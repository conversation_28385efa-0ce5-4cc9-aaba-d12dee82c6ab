#!/usr/bin/env python3
"""
Web scraper for iMedical Doctor database archives.
Fetches medical database listings and stores them in SQLite for analysis.
"""

import sqlite3
import requests
from bs4 import BeautifulSoup
from urllib.parse import quote

URL = "https://en.imedicaldoctor.net/dbs.php?query=&limit=99000&orderby=dateadded&order=desc"
DB_FILE = "imd_archives.sqlite"

def fetch_data():
    """Retrieve HTML content from iMedical Doctor database"""
    try:
        response = requests.get(URL, timeout=30)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"Failed to fetch data: {e}")
        return None

def parse_table(html):
    """Extract table data into structured format"""
    soup = BeautifulSoup(html, 'html.parser')
    table = soup.find('table', class_='table')
    
    if not table:
        return []
    
    # Get headers
    headers = [th.get_text(strip=True) for th in table.find('thead').find('tr').find_all('th')]
    
    # Parse rows
    data = []
    for row in table.find('tbody').find_all('tr'):
        cells = row.find_all(['th', 'td'])
        entry = {}
        
        for i, cell in enumerate(cells):
            if i >= len(headers):
                continue
                
            if cell.name == 'th':
                # Extract title from span or text
                span = cell.find('span')
                entry[headers[i]] = span.get_text(strip=True) if span else cell.get_text(strip=True)
            else:
                # Handle download links
                link = cell.find('a')
                if link and link.get('href'):
                    entry[headers[i]] = quote(link['href'], safe=':/')
                else:
                    entry[headers[i]] = cell.get_text(strip=True)
        
        if entry:
            data.append(entry)
    
    return data

def parse_size(size_str):
    """Convert size string like '1.23 MB' to bytes as integer"""
    if not size_str:
        return 0
    
    size_str = size_str.strip()
    if not size_str:
        return 0
    
    # Extract number and unit
    parts = size_str.split()
    if len(parts) < 2:
        return 0
    
    try:
        size_value = float(parts[0])
        unit = parts[1].upper()
        
        # Convert to bytes
        unit_multipliers = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024
        }
        
        multiplier = unit_multipliers.get(unit, 1)
        return int(size_value * multiplier)
    except (ValueError, IndexError):
        return 0

def save_database(data):
    """Store parsed data in SQLite database"""
    if not data:
        return
    
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # Single-run optimization for large dataset
    cursor.execute("PRAGMA journal_mode=OFF")  # No transaction log needed
    cursor.execute("PRAGMA synchronous=OFF")   # Fastest writes
    cursor.execute("PRAGMA cache_size=50000")  # Larger cache for 27K+ records
    cursor.execute("PRAGMA temp_store=memory")
    cursor.execute("PRAGMA mmap_size=536870912")  # 512MB memory map
    
    # Create table with computed column for human-readable sizes
    columns = [h.replace(' ', '_').lower() for h in data[0].keys()]
    schema_parts = []
    for col in columns:
        if col == 'size':
            schema_parts.append(f"{col} INTEGER")
        else:
            schema_parts.append(f"{col} TEXT")
    
    # Add computed column that automatically calculates human-readable size
    schema_parts.append("size_human TEXT GENERATED ALWAYS AS (CASE WHEN size >= 1073741824 THEN ROUND(size/1073741824.0, 2) || ' GB' WHEN size >= 1048576 THEN ROUND(size/1048576.0, 2) || ' MB' WHEN size >= 1024 THEN ROUND(size/1024.0, 2) || ' KB' ELSE size || ' B' END) STORED")
    
    schema = ', '.join(schema_parts)
    cursor.execute(f"CREATE TABLE IF NOT EXISTS imd_archives ({schema}, UNIQUE(title, version))")
    
    # Process and insert data
    processed_rows = []
    for row in data:
        processed_row = []
        for header in data[0].keys():
            value = row.get(header, '')
            # Parse size field
            if header.lower() == 'size':
                value = parse_size(value)
            processed_row.append(value)
        processed_rows.append(processed_row)
    
    placeholders = ', '.join('?' * len(columns))
    cursor.executemany(f"INSERT OR REPLACE INTO imd_archives VALUES ({placeholders})", processed_rows)
    
    # Essential indexes only for single-run
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_title_version ON imd_archives(title, version)")
    
    conn.commit()
    conn.close()

def main():
    print("Fetching database listings...")
    html = fetch_data()
    if not html:
        return
    
    print("Parsing data...")
    data = parse_table(html)
    if not data:
        print("No data found")
        return
    
    print(f"Processing {len(data)} entries...")
    save_database(data)
    print(f"Database saved to {DB_FILE}")

if __name__ == "__main__":
    main()
import sqlite3
import json
import os
import requests
import logging
import zipfile
import shutil
from urllib.parse import urlparse, unquote

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(funcName)s - %(message)s')
logger = logging.getLogger(__name__)

# disable urllib3 debug logging
logging.getLogger("urllib3").setLevel(logging.ERROR)

def find_archives_by_title(title):
    """Search archives by title with fuzzy matching.
    
    Args:
        title (str): The title to search for in the archive database.
                    Uses fuzzy matching to find partial matches.
    
    Returns:
        str: JSON string containing a list of matching archive records.
             Each record includes fields like title, download URL, etc.
    """
    logger.debug(f"Searching for: {title}")
    conn = sqlite3.connect('imd_archives.sqlite')
    conn.row_factory = sqlite3.Row
    
    cursor = conn.cursor()
    query = "SELECT * FROM imd_archives WHERE title LIKE ? COLLATE NOCASE"
    search_pattern = "%" + "%".join(title.split()) + "%"
    
    cursor.execute(query, (search_pattern,))
    results = [dict(row) for row in cursor.fetchall()]
    logger.debug(f"Search results: {json.dumps(results, indent=2)}")
    
    conn.close()
    return json.dumps(results, indent=2)

def download_and_extract_archive(link, location=None, keep_archive=False, ):
    """Download archive to downloaded-archives folder and unzip it.
    
    Args:
        link (str): URL to download the archive from
        location (str, optional): Directory to download the archive to. Defaults to "downloaded-archives" folder.
        keep_archive (bool, optional): Whether to keep the archive after unzipping. Defaults to False.
    
    Returns:
        str: Path to the downloaded archive or the unzipped directory
    """
    logger.debug(f"Downloading from: {link}")
    if location:
        download_dir = location
    else:
        download_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "downloaded-archives")
    os.makedirs(download_dir, exist_ok=True)
    
    filename = os.path.basename(unquote(urlparse(link).path)) or f"archive_{hash(link) % 1000000}.zip"
    filepath = os.path.join(download_dir, filename)
    
    response = requests.get(link, stream=True)
    response.raise_for_status()
    # Response headers
    logger.debug(f"Response headers: {json.dumps(dict(response.headers), indent=2)}")
    
    with open(filepath, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            f.write(chunk)
        logger.debug(f"Downloaded to: {filepath}")
    
    # Unzip the archive
    try:
        # Create a directory with the same name as the archive (without extension)
        extract_dir = os.path.splitext(filepath)[0]
        
        # Check if directory already exists and warn about overwriting
        if os.path.exists(extract_dir):
            logger.warning(f"Directory {extract_dir} already exists. Removing existing directory for clean extraction.")
            shutil.rmtree(extract_dir)
        
        os.makedirs(extract_dir, exist_ok=True)
        
        logger.debug(f"Extracting archive to: {extract_dir}")
        with zipfile.ZipFile(filepath, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
        
        logger.debug(f"Archive extracted to: {extract_dir}")
        
        # Delete the archive if keep_archive is False
        if not keep_archive:
            logger.debug(f"Deleting archive: {filepath}")
            os.remove(filepath)
            return extract_dir
        else:
            return extract_dir
    except zipfile.BadZipFile:
        logger.error(f"Failed to unzip {filepath}. File is not a valid zip archive.")
        return filepath
    except Exception as e:
        logger.error(f"Error unzipping archive: {str(e)}")
        return filepath

def decrypt_data_folder(unzipped_folder_path):
    """Decrypt the data folder contents, specifically the info.vbe file.
    
    Args:
        unzipped_folder_path (str): Path to the unzipped folder containing the data
    
    Returns:
        bool: True if decryption was successful, False otherwise
    """
    logger.debug(f"Decrypting data folder: {unzipped_folder_path}")
    
    # Find the actual content directory (might be nested) search for subdir which has file info.vbe
    content_dir = None
    for root, dirs, files in os.walk(unzipped_folder_path):
        if "info.vbe" in files:
            content_dir = root
            break
    if not content_dir:
        logger.warning(f"info.vbe file not found in {unzipped_folder_path}")
        return False
    
    # Look for info.vbe file in the data folder
    data_folder = os.path.join(content_dir, "data")
    info_vbe_path = os.path.join(data_folder, "info.vbe")
    
    if not os.path.exists(info_vbe_path):
        logger.warning(f"info.vbe file not found at: {info_vbe_path}")
        return False
    
    try:
        with open(info_vbe_path, 'r', encoding='utf-8') as f:
            encoded_content = f.read()
        
        logger.debug(f"Found info.vbe file, size: {len(encoded_content)} characters")
        
        logger.debug(f"info.vbe content: {encoded_content}")
        # Decrypt the VBE content
        decrypted_content = decrypt_vbe_content(encoded_content)
        logger.debug(f"Decrypted info.vbe content: {decrypted_content}")
        # Write the decrypted content to info.vbs
        info_vbs_path = os.path.join(data_folder, "info.vbs")
        with open(info_vbs_path, 'w', encoding='utf-8') as f:
            f.write(decrypted_content)
        
        logger.info(f"Successfully decrypted info.vbe to: {info_vbs_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error decrypting info.vbe: {str(e)}")
        return False

def decrypt_vbe_content(encoded_content):
    """Decrypt VBScript Encoded (.vbe) content.
    
    Args:
        encoded_content (str): The encoded VBScript content
    
    Returns:
        str: The decrypted VBScript content
    """
    # VBE decoding table
    decode_table = [
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x57, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x2E, 0x47, 0x7A, 0x56, 0x42, 0x6A, 0x2F, 0x26, 0x49, 0x41, 0x34, 0x32, 0x5B, 0x76, 0x72, 0x43,
        0x38, 0x39, 0x70, 0x45, 0x74, 0x71, 0x33, 0x37, 0x73, 0x35, 0x7C, 0x46, 0x79, 0x40, 0x4E, 0x77,
        0x3A, 0x59, 0x64, 0x5E, 0x52, 0x54, 0x75, 0x7D, 0x29, 0x36, 0x20, 0x2C, 0x30, 0x7F, 0x69, 0x65,
        0x50, 0x2A, 0x2D, 0x4A, 0x4D, 0x68, 0x56, 0x44, 0x23, 0x7E, 0x00, 0x2B, 0x5A, 0x3E, 0x5C, 0x4F,
        0x51, 0x67, 0x6C, 0x49, 0x58, 0x4B, 0x62, 0x5D, 0x42, 0x53, 0x31, 0x29, 0x28, 0x66, 0x24, 0x6D,
        0x48, 0x27, 0x21, 0x60, 0x63, 0x55, 0x3D, 0x4C, 0x3C, 0x61, 0x78, 0x25, 0x22, 0x3B, 0x6B, 0x6F
    ]
    
    if not encoded_content.startswith("#@~^"):
        logger.warning("Content doesn't appear to be VBE encoded")
        return encoded_content
    
    try:
        # Find the start and end markers
        start_marker = "#@~^"
        end_marker = "^#~@"
        
        start_pos = encoded_content.find(start_marker)
        end_pos = encoded_content.find(end_marker)
        
        if start_pos == -1 or end_pos == -1:
            logger.warning("VBE markers not found properly")
            return encoded_content
        
        # Extract the encoded section
        encoded_section = encoded_content[start_pos + len(start_marker):end_pos]
        
        # Skip the first 8 characters (they contain encoding info)
        if len(encoded_section) < 8:
            logger.warning("Encoded section too short")
            return encoded_content
            
        encoded_data = encoded_section[8:]
        
        # Decode the content
        decoded_chars = []
        for i, char in enumerate(encoded_data):
            if char == '@':
                decoded_chars.append('\n')
            elif char == '_':
                decoded_chars.append('\0')
            else:
                char_code = ord(char)
                if 0 <= char_code < len(decode_table):
                    decoded_char = chr(decode_table[char_code])
                    decoded_chars.append(decoded_char)
                else:
                    decoded_chars.append(char)
        
        decoded_content = ''.join(decoded_chars).rstrip('\0')
        logger.debug(f"Successfully decoded VBE content, length: {len(decoded_content)}")
        return decoded_content
        
    except Exception as e:
        logger.error(f"Error in VBE decoding: {str(e)}")
        return encoded_content

def main():
    query = "AceQBank 2025"
    logger.info(f"Searchng and downloading : {query}")
    results = json.loads(find_archives_by_title(title=query))
    logger.info(f"Found {len(results)} results.")
    if results:
        first_result = results[0]
        logger.info(f"Downloading first result: {first_result['title']}")
        extracted_folder = download_and_extract_archive(link=first_result['download'])
        
        # Decrypt the data folder
        logger.info("Attempting to decrypt data folder...")
        decrypt_success = decrypt_data_folder(unzipped_folder_path=extracted_folder)
        if decrypt_success:
            logger.info("Data folder decryption completed successfully.")
        else:
            logger.warning("Data folder decryption failed or was not needed.")
    else:
        logger.info("No results found.")

if __name__ == "__main__":
    main()
    
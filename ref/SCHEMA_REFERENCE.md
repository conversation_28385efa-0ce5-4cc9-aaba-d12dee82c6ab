# Sample Bundle Database Schema – Reference

> This document supersedes all previous schema notes.  
> It merges the physical database layout **and** the application-level integrity logic discovered in the de-compiled APK (`net.imedicaldoctor.imd`).

---

## Table of Contents
1. [Scope & Sources](#1-scope--sources)  
2. [High-Level Design](#2-high-level-design)  
3. [Entity–Relationship Diagram](#3-entity-relationship-diagram)  
4. [Detailed Table Reference](#4-detailed-table-reference)  
5. [Application-Level Referential Integrity](#5-application-level-referential-integrity)  
6. [Media File Management](#6-media-file-management)
7. [MCQ Test Flow](#7-mcq-test-flow)
8. [Design Observations](#8-design-observations)  
9. [Appendix A – Raw DDL Excerpt](#9-appendix-a--raw-ddl-excerpt)

---

## 1. Scope & Sources
| Bundle | SQLite file | Rows in `Questions`* |
|--------|-------------|-----------------------|
| aceqbank-cdm-2024 | `aceqbank-cdm-2024.db` | ≈ 187 |
| cqb-amcmcq-2024   | `cqb-amcmcq-2024.db`   | ≈ 3 575 |

\*Counts taken with `sqlite3` on 22 Jul 2025.  
The two files share **identical schemas**; only data differs.

---

## 2. High-Level Design
1. **Core domain** – questions, answers, references, taxonomy.  
2. **User-generated data** – tests, logs, flags.  
3. **Infrastructure** – FTS4 + spellfix1 virtual tables (search & spell).
4. **Media assets** – stored separately in `media-E/` directory, referenced by filename in database.

👉 _No `FOREIGN KEY` constraints are declared; relationships are enforced by application code (see § 5)._

---

## 3. Entity–Relationship Diagram
```mermaid
erDiagram
    Subjects ||--o{ SubjectsSystems : categorises
    Systems  ||--o{ SubjectsSystems : categorises

    Subjects ||--o{ Questions : "primary subject"
    Systems  ||--o{ Questions : "primary system"

    Questions ||--|{ Answers : has
    Questions ||--|{ Refs    : cites

    Questions ||--o{ logs   : answered_in
    Tests     ||--o{ logs   : contains
    Questions ||--o{ flags  : bookmarked
    
    Questions ||--o{ MediaFiles : "referenced by filename in mediaName/otherMedias"
```
Legend:  `||` PK, `|{` mandatory 1-to-many, `o{` optional 1-to-many.  
(Search & spell tables omitted – they hold only derived index data.)

---

## 4. Detailed Table Reference

### 4.1 Core Domain
| Table | Column | Type | PK | Description |
|-------|--------|------|----|-------------|
| **Subjects** | id | INTEGER | ✔ | Subject ID |
| | name | VARCHAR(255) |  | Title |
| | count | INTEGER |  | Cached #questions |
| **Systems** | id | INTEGER | ✔ | System/organ ID |
| | name | VARCHAR(255) |  | Title |
| | count | INTEGER |  | Cached #questions |
| **SubjectsSystems** | subId | INTEGER | (✓) | → Subjects.id |
| | sysId | INTEGER | (✓) | → Systems.id |
| | count | INTEGER |  | #questions with that pair |
| **Questions** | id | INTEGER | ✔ | Question UID |
| | question / explanation | TEXT |  | Stem & rationale (encrypted) |
| | corrAns | INTEGER |  | Correct option index |
| | subId / sysId | INTEGER |  | Taxonomy FK |
| | mediaName | TEXT |  | Primary image filename (often empty) |
| | otherMedias | TEXT |  | Pipe-separated list of additional media filenames |
| | parentQId | INTEGER |  | Self-reference for grouped questions |
| | … *plus stats & media fields – see DDL* |
| **Answers** | id | INTEGER | ✔ | Row ID |
| | qId | INTEGER |  | → Questions.id |
| | answerId | INTEGER |  | Ordinal |
| | answerText | TEXT |  | Choice text (encrypted) |
| **Refs** | id | INTEGER | ✔ | Row ID |
| | qId | INTEGER |  | → Questions.id |
| | refTitle/refLink/refType | TEXT/INTEGER |  | Citation data |

### 4.2 User-Generated Data
| Table | Key Columns | What it Stores |
|-------|-------------|----------------|
| **Tests** (`id`) | Comma-separated list of question ids plus progress & score fields |
| **logs** (`id`) | One row per answered question – `qid` & `testId` link back |
| **flags** (`id`) | Bookmarks – `qid` → Questions |

### 4.3 Search & Spell Infrastructure
`Search` (FTS4 virtual), `Search_content/segments/…`, `spell` (spellfix1), `spell_vocab`.  
These hold **only** index data and can be rebuilt from primary tables.

---

## 5. Application-Level Referential Integrity

Physical FKs ‒ **disabled**. Integrity is upheld by how the APK issues SQL.

### 5.1 I/O Layer
```java
// in net.imedicaldoctor.imd.Data.iMDProvider
return open(dbPath).rawQuery(sql, null);   // no FK enforcement
```
`CompressHelper.query()` constructs SQL strings and calls this provider.

### 5.2 Typical Runtime Flow
```java
SELECT * FROM Questions WHERE id = ?          // show question
SELECT * FROM Answers   WHERE qId = ?         // load choices
INSERT INTO logs(qid, …, testId) VALUES (…)   // record answer
```
All ids used in subsequent statements come from rows just read, so the app **assumes** they are valid.

### 5.3 Implications
* deleting or renaming ids outside the app will crash it later  
* migrations must either:  
  a) reproduce the same read-then-write invariants, **or**  
  b) turn on `PRAGMA foreign_keys = ON` and add real FK constraints

---

## 6. Media File Management

### 6.1 Storage Structure
Media files are stored in the `media-E/` directory alongside each database:
```
aceqbank-cdm-2024/
├── aceqbank-cdm-2024.db
├── aceqbank.jpg
├── info.vbe
└── media-E/
    ├── 101_cdm_exp_0a4e3af8ac01dfd244334c6ac7ea9519.png
    ├── 103_cdm_d69c927b709f12191df71975e5e61d23.jpg
    └── ...
```

### 6.2 Database References to Media
Media files are referenced **directly by filename** in the `Questions` table:

- **`mediaName`**: Contains the primary image filename (often empty for text-only questions)
- **`otherMedias`**: Contains a pipe-separated list of additional media filenames

**Example from actual data:**
```sql
-- aceqbank-cdm-2024.db
ID: 17, mediaName: "", otherMedias: "21_cdm_exp_22e57ca27a93a8767179bcbcf440565b.png"
ID: 37, mediaName: "", otherMedias: "44_cdm_exp_5360eda671af4fc2cbb44c589e25cc49.jpg"

-- cqb-amcmcq-2024.db  
ID: 11828, mediaName: "", otherMedias: "gadolinium induced nephrogenic systemic fibrosis.jpeg"
ID: 11944, mediaName: "", otherMedias: "q183.jpg"
```

### 6.3 File Naming Conventions
Different content providers use different naming standards:

**AceQBank (CDM format):**
- Pattern: `{questionId}_{type}_{md5hash}.{ext}`
- Example: `101_cdm_exp_0a4e3af8ac01dfd244334c6ac7ea9519.png`
- Where:
  - `questionId`: Often (but not always) matches Questions.id
  - `type`: "cdm" (question) or "cdm_exp" (explanation)
  - `hash`: MD5 hash for uniqueness

**CanadaQBank (CQB format):**
- Descriptive names: `gadolinium induced nephrogenic systemic fibrosis.jpeg`
- Generic patterns: `q183.jpg`, `Medical - 657.jpg`

The app handles both transparently – it only cares about the filenames stored in the database fields.

### 6.4 Runtime Loading Process
From `UWTestViewerActivityFragment.java`:

1. **Extract media list**: Parse `mediaName` + split `otherMedias` by pipe
2. **Locate files**: For each filename, build path `{bundle}/media-E/{filename}`
3. **Decrypt files**: Read encrypted file → decrypt with key "127" → save to temp "base" directory
4. **WebView access**: HTML `<img>` tags reference the temporary decrypted files
5. **Cleanup**: Temp files are marked `deleteOnExit()` when fragment is destroyed

---

## 7. MCQ Test Flow

Based on analysis of `UWMainActivityFragment.java` and `UWTestViewerActivityFragment.java`:

### 7.1 Test Creation (`UWMainActivityFragment.m72608v3()`)
1. User selects filters:
   - Subjects (multi-select from `Subjects` table)
   - Systems (multi-select from `Systems` table)  
   - Difficulty level (based on `corrTaken` percentage)
   - Number of questions
   - Test mode: "Testing (Timed)", "Tutor", "Exam"

2. Build question pool:
   ```sql
   SELECT id FROM Questions 
   WHERE subId IN (selected subjects) 
   AND sysId IN (selected systems)
   AND corrTaken BETWEEN (difficulty range)
   ```

3. Handle grouped questions:
   - If a question has `parentQId`, include all related questions
   - Groups are kept together (not split across tests)

4. Create test:
   ```sql
   INSERT INTO Tests (qIds, createdDate, mode, ...) 
   VALUES ('comma,separated,question,ids', ...)
   ```

5. Navigate to test viewer with the new test ID

### 7.2 Question Display
1. Load question from `Questions` table by ID
2. Load answers from `Answers` table where `qId = ?`
3. Decrypt question/answer text using hardcoded key
4. Process media files:
   - Extract filenames from `mediaName` and `otherMedias`
   - Decrypt each file from `media-E/` to temporary directory
5. Build HTML with JavaScript for interactivity
6. Load into WebView with access to temporary media files

### 7.3 Answer Recording
1. User selects answer(s) - supports multi-select for questions with comma-separated `corrAns`
2. Create `logs` entry with:
   - `qid`: current question
   - `selectedAnswer`: user's choice
   - `corrAnswer`: from question record
   - `time`: seconds spent
   - `testId`: current test session

### 7.4 Progress Tracking
- Timer runs for timed tests (updates every 5 seconds)
- Progress saved incrementally to allow resume
- Test marked `done = 1` when completed
- Score calculated from `logs` entries

---

## 8. Design Observations
1. **Flat & lightweight** – ideal for offline mobile use.  
2. **Integrity by convention** – simpler at runtime, but risky for external tooling.  
3. **Pre-computed `count` columns** speed taxonomy lists.  
4. **Search as side-car** – FTS4 & spellfix1 allow self-contained full-text search.
5. **Media separation** – keeps DB size small, media files encrypted separately.
6. **Direct filename references** – no junction table needed, just store filenames in text fields.
7. **Flexible question types** – supports both single and multiple correct answers.
8. **Content provider agnostic** – handles different naming conventions transparently.

---

## 9. Appendix A – Raw DDL Excerpt
<details>
<summary>Click to expand</summary>

```sql
CREATE TABLE Subjects (
    id INTEGER PRIMARY KEY NOT NULL,
    name VARCHAR(255),
    count INTEGER
);

CREATE TABLE Systems (
    id INTEGER PRIMARY KEY NOT NULL,
    name VARCHAR(255),
    count INTEGER
);

CREATE TABLE Questions (
    id INTEGER PRIMARY KEY NOT NULL,
    question TEXT,
    explanation TEXT,
    corrAns INTEGER,
    subId INTEGER,
    sysId INTEGER,
    pplTaken FLOAT,
    corrTaken FLOAT,
    lastUpdated VARCHAR(255),
    title TEXT,
    questionType INTEGER,
    questionFormatType INTEGER,
    mediaName TEXT,
    otherMedias TEXT,
    parentQId INTEGER
);

CREATE TABLE Answers (
    id INTEGER PRIMARY KEY AUTOINCREMENT UNIQUE NOT NULL,
    qId INTEGER,
    answerId INTEGER,
    answerText TEXT,
    correctPercentage INTEGER
);

CREATE TABLE Tests (
    id INTEGER PRIMARY KEY AUTOINCREMENT UNIQUE NOT NULL,
    qIds TEXT,
    createdDate TEXT,
    qIndex INTEGER,
    done INTEGER,
    mode VARCHAR(50),
    right INTEGER,
    wrong INTEGER,
    subject TEXT,
    system TEXT,
    score VARCHAR(255),
    hard VARCHAR(255)
);

CREATE TABLE logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT UNIQUE NOT NULL,
    qid INTEGER,
    selectedAnswer INT,
    corrAnswer INT,
    time INT,
    answerDate TEXT,
    testId INTEGER
);
-- further CREATE statements omitted for brevity
```
</details>

---

#!/usr/bin/env python3
"""
iMedicalDoctor Decryption Proof-of-Concept

This script demonstrates the successful decryption of iMedicalDoctor's
database and metadata files. It is the result of a reverse-engineering
effort that discovered a hardcoded password within the application,
allowing for universal decryption without device-specific information.

Usage:
    python imedical_decryption_poc.py <path_to_database.db>
"""

import sys
import sqlite3
import base64
import gzip
import json
import plistlib
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
from Crypto.Protocol.KDF import PBKDF2

# -- Constants discovered during reverse-engineering --

# Hardcoded password found in multiple methods in VBHelper.java
DECRYPTION_PASSWORD = "hs;d,hghdk[;ak"

# Hardcoded IV used for AES-128-CBC decryption of database content
DB_CONTENT_IV = bytes([
    17, 115, 105, 102, 103, 104, 111, 107, 108, 122, 120, 119, 118, 98, 110, 109
])

# Hardcoded salt used for info.vbe file decryption
INFO_VBE_SALT = "info.vb ".encode('utf-8')

# Number of iterations for PBKDF2 key derivation
PBKDF2_ITERATIONS = 19


def decrypt_info_vbe(vbe_file_path):
    """
    Decrypts an info.vbe file.

    This algorithm was reverse-engineered from VBHelper.m73443e().
    """
    if not vbe_file_path.exists():
        return None, f"VBE file not found: {vbe_file_path}"

    try:
        vbe_data = vbe_file_path.read_bytes()

        key = PBKDF2(DECRYPTION_PASSWORD.encode('utf-8'),
                     INFO_VBE_SALT,
                     dkLen=16,
                     count=PBKDF2_ITERATIONS)

        cipher = AES.new(key, AES.MODE_CBC, DB_CONTENT_IV)
        plaintext = unpad(cipher.decrypt(vbe_data), AES.block_size)

        # The app replaces '&' before parsing, so we replicate that.
        text = plaintext.decode('utf-8').replace('&', '&amp;')
        plist_data = plistlib.loads(text.encode('utf-8'))

        return plist_data, None
    except Exception as e:
        return None, f"Failed to decrypt {vbe_file_path.name}: {e}"


def decrypt_db_content(encrypted_data, question_id):
    """
    Decrypts a single field from the Questions table.

    This algorithm was reverse-engineered from CompressHelper.m71773B().
    """
    try:
        encrypted_bytes = base64.b64decode(encrypted_data)
        salt = str(question_id).ljust(8)[:8].encode('utf-8')

        key = PBKDF2(DECRYPTION_PASSWORD.encode('utf-8'),
                     salt,
                     dkLen=16,
                     count=PBKDF2_ITERATIONS)

        cipher = AES.new(key, AES.MODE_CBC, DB_CONTENT_IV)
        decrypted_padded = cipher.decrypt(encrypted_bytes)

        decompressed = gzip.decompress(unpad(decrypted_padded, AES.block_size))
        return decompressed.decode('utf-8'), None
    except Exception as e:
        return None, f"Decryption failed: {e}"


def run_poc(db_path):
    """
    Runs the proof-of-concept decryption on the specified database.
    """
    db_file = Path(db_path)
    if not db_file.exists():
        print(f"Error: Database file not found at '{db_path}'")
        sys.exit(1)

    print(f"iMedicalDoctor Decryption PoC")
    print("-" * 40)
    print(f"Target Database: {db_file.name}")

    # --- Decrypt associated info.vbe file ---
    vbe_file = db_file.with_name("info.vbe")
    print(f"Metadata Target: {vbe_file.name}")
    info_data, error = decrypt_info_vbe(vbe_file)
    if error:
        print(f"  Status: Failed")
        print(f"  Reason: {error}")
    else:
        print(f"  Status: Decrypted Successfully")
        print(f"  Title: {info_data.get('Title', 'N/A')}")
        print(f"  Version: {info_data.get('Version', 'N/A')}")
        print(f"  Type: {info_data.get('Type', 'N/A')}")

    print("-" * 40)

    # --- Decrypt sample content from the database ---
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        cursor.execute("SELECT id, question, explanation, title FROM Questions LIMIT 3")
        sample_questions = cursor.fetchall()
        conn.close()

        print(f"Found {len(sample_questions)} sample questions for PoC decryption.")

        for i, (q_id, enc_q, enc_e, title) in enumerate(sample_questions):
            print(f"\n--- Sample #{i+1} (ID: {q_id}) ---")
            print(f"Title: {title or 'N/A'}")

            # Decrypt question
            dec_q, error_q = decrypt_db_content(enc_q, q_id)
            if error_q:
                print(f"  Question Decryption: FAILED ({error_q})")
            else:
                print(f"  Question Decryption: SUCCESS")
                # Print a snippet of the decrypted content
                print(f"  Content Snippet: {dec_q.strip()[:100]}...")

            # Decrypt explanation
            if enc_e:
                dec_e, error_e = decrypt_db_content(enc_e, q_id)
                if error_e:
                    print(f"  Explanation Decryption: FAILED ({error_e})")
                else:
                    print(f"  Explanation Decryption: SUCCESS")
            else:
                print(f"  Explanation Decryption: SKIPPED (no content)")

    except Exception as e:
        print(f"\nError processing database: {e}")
        sys.exit(1)

    print("-" * 40)
    print("Proof-of-Concept complete.")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(__doc__)
        sys.exit(1)
    run_poc(sys.argv[1]) 
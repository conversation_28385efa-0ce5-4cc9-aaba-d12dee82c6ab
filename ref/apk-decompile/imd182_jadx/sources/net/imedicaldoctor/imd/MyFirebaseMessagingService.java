package net.imedicaldoctor.imd;

import android.content.Intent;
import android.util.Log;
import androidx.annotation.NonNull;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import java.util.Map;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class MyFirebaseMessagingService extends FirebaseMessagingService {
    @Override // com.google.firebase.messaging.FirebaseMessagingService
    /* renamed from: r */
    public void mo49641r(RemoteMessage remoteMessage) {
        super.mo49641r(remoteMessage);
        Log.d("PushNotification", "From: " + remoteMessage.m49759I());
        Map<String, String> mapM49758H = remoteMessage.m49758H();
        String str = mapM49758H.get("title");
        String str2 = mapM49758H.get(Annotation.f68283i3);
        String str3 = mapM49758H.get(HTML.Tag.f74331C);
        Intent intent = new Intent(this, (Class<?>) NotificationActivity.class);
        intent.putExtra("title", str);
        intent.putExtra(Annotation.f68283i3, str2);
        intent.putExtra(HTML.Tag.f74331C, str3);
        intent.addFlags(268435456);
        startActivity(intent);
    }

    @Override // com.google.firebase.messaging.FirebaseMessagingService
    /* renamed from: t */
    public void mo49643t(@NonNull String str) {
        new CompressHelper(this).m71864l0();
    }
}

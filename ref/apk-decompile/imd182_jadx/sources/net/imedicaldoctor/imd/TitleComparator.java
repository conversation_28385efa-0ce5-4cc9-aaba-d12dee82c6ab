package net.imedicaldoctor.imd;

import android.os.Bundle;
import java.util.Comparator;

/* loaded from: classes3.dex */
public class TitleComparator implements Comparator<Bundle> {

    /* renamed from: s */
    private String f101329s;

    public TitleComparator(String str) {
        this.f101329s = str;
    }

    /* renamed from: b */
    private int m73389b(String str) {
        String[] strArrSplit = str.split("\\.");
        if (strArrSplit.length > 0) {
            try {
                return Integer.parseInt(strArrSplit[0].trim());
            } catch (NumberFormatException unused) {
            }
        }
        return -1;
    }

    @Override // java.util.Comparator
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public int compare(Bundle bundle, Bundle bundle2) {
        String string = bundle.getString(this.f101329s);
        String string2 = bundle2.getString(this.f101329s);
        if (string != null && string2 != null) {
            int iM73389b = m73389b(string);
            int iM73389b2 = m73389b(string2);
            if (iM73389b != -1 && iM73389b2 != -1) {
                return Integer.compare(iM73389b, iM73389b2);
            }
            if (iM73389b != -1) {
                return -1;
            }
            if (iM73389b2 != -1) {
                return 1;
            }
        }
        return 0;
    }
}

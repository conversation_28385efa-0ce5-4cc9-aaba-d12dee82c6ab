package net.imedicaldoctor.imd;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import net.imedicaldoctor.imd.Fragments.accountFragment;
import net.imedicaldoctor.imd.Fragments.contentFragment;
import net.imedicaldoctor.imd.Fragments.databasesFragment;
import net.imedicaldoctor.imd.Fragments.downloadFragment;
import net.imedicaldoctor.imd.Fragments.favoritesFragment;
import net.imedicaldoctor.imd.Fragments.searchFragment;

/* loaded from: classes3.dex */
public class TabsPagerAdapter extends FragmentPagerAdapter {

    /* renamed from: n */
    public FragmentManager f101328n;

    public TabsPagerAdapter(FragmentManager fragmentManager) {
        super(fragmentManager);
        this.f101328n = fragmentManager;
    }

    /* renamed from: z */
    private static String m73387z(int i2, int i3) {
        return "android:switcher:" + i2 + ":" + i3;
    }

    @Override // androidx.viewpager.widget.PagerAdapter
    /* renamed from: e */
    public int mo28926e() {
        return 6;
    }

    @Override // androidx.viewpager.widget.PagerAdapter
    /* renamed from: g */
    public CharSequence mo28928g(int i2) {
        return "";
    }

    @Override // androidx.fragment.app.FragmentPagerAdapter
    /* renamed from: v */
    public Fragment mo15734v(int i2) {
        if (i2 == 0) {
            return new searchFragment();
        }
        if (i2 == 1) {
            return new databasesFragment();
        }
        if (i2 == 2) {
            return new favoritesFragment();
        }
        if (i2 == 3) {
            return new contentFragment();
        }
        if (i2 == 4) {
            return new downloadFragment();
        }
        if (i2 != 5) {
            return null;
        }
        return new accountFragment();
    }

    /* renamed from: y */
    public Fragment m73388y(ViewPager viewPager, int i2) {
        return this.f101328n.m15659s0(m73387z(viewPager.getId(), i2));
    }
}

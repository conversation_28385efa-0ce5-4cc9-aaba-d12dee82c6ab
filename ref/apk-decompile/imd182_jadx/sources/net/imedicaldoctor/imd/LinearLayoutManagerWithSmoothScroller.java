package net.imedicaldoctor.imd;

import android.content.Context;
import android.graphics.PointF;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

/* loaded from: classes3.dex */
public class LinearLayoutManagerWithSmoothScroller extends LinearLayoutManager {

    private class TopSnappedSmoothScroller extends LinearSmoothScroller {
        public TopSnappedSmoothScroller(Context context) {
            super(context);
        }

        @Override // androidx.recyclerview.widget.LinearSmoothScroller
        /* renamed from: C */
        protected int mo27241C() {
            return -1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.SmoothScroller
        /* renamed from: a */
        public PointF mo27755a(int i2) {
            return LinearLayoutManagerWithSmoothScroller.this.mo27192a(i2);
        }
    }

    public LinearLayoutManagerWithSmoothScroller(Context context, int i2, boolean z) {
        super(context, i2, z);
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.LayoutManager
    /* renamed from: j2 */
    public void mo27200j2(RecyclerView recyclerView, RecyclerView.State state, int i2) {
        TopSnappedSmoothScroller topSnappedSmoothScroller = new TopSnappedSmoothScroller(recyclerView.getContext());
        topSnappedSmoothScroller.m27767q(i2);
        m27644k2(topSnappedSmoothScroller);
    }
}

package net.imedicaldoctor.imd;

import androidx.media3.common.MimeTypes;
import com.itextpdf.tool.xml.html.HTML;
import fi.iki.elonen.NanoHTTPD;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import org.apache.commons.httpclient.methods.multipart.FilePart;

/* loaded from: classes3.dex */
public class LocalServer extends NanoHTTPD {

    /* renamed from: t */
    private String f90910t;

    /* renamed from: u */
    private String f90911u;

    public LocalServer(int i2, String str, String str2) {
        super(i2);
        this.f90910t = str;
        this.f90911u = str2;
    }

    /* renamed from: R */
    private String m73385R(String str) {
        return str.endsWith(".css") ? HTML.Attribute.Value.f74318a : str.endsWith(".js") ? "application/javascript" : str.endsWith(".png") ? MimeTypes.f19867R0 : (str.endsWith(".jpg") || str.endsWith(".jpeg")) ? MimeTypes.f19865Q0 : str.endsWith(".gif") ? "image/gif" : FilePart.DEFAULT_CONTENT_TYPE;
    }

    @Override // fi.iki.elonen.NanoHTTPD
    /* renamed from: G */
    public NanoHTTPD.Response mo58313G(NanoHTTPD.IHTTPSession iHTTPSession) {
        if ("/content".equals(iHTTPSession.mo58365c())) {
            return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.OK, NanoHTTPD.f77082p, this.f90910t);
        }
        File file = new File(this.f90911u, iHTTPSession.mo58365c().substring(1));
        if (!file.exists()) {
            return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.NOT_FOUND, "text/plain", "File not found");
        }
        file.setReadable(true, false);
        try {
            return NanoHTTPD.m58292C(NanoHTTPD.Response.Status.OK, m73385R(file.getName()), new FileInputStream(file), file.length());
        } catch (FileNotFoundException unused) {
            return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.NOT_FOUND, "text/plain", "File not found");
        }
    }
}

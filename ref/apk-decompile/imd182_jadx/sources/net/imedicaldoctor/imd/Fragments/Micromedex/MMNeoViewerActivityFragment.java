package net.imedicaldoctor.imd.Fragments.Micromedex;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MMNeoViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88614X4;

    /* renamed from: Y4 */
    public ArrayList<String> f88615Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88616Z4;

    /* renamed from: a5 */
    public int f88617a5;

    /* renamed from: O4 */
    private void m72398O4(String str) {
        ArrayList<String> arrayList = this.f88615Y4;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f88615Y4.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", next);
            bundle.putString("Description", "");
            bundle.putString("id", next);
            if (new File(next).length() > 5000) {
                arrayList2.add(bundle);
            }
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public void m72399I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f88616Z4.add(bundle);
    }

    /* renamed from: J4 */
    public String m72400J4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88617a5 + 1;
        this.f88617a5 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded3\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded3(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: K4 */
    public String m72401K4(String str, String str2, String str3, String str4) {
        int i2 = this.f88617a5 + 1;
        this.f88617a5 = i2;
        return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + String.valueOf(i2) + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
    }

    /* renamed from: L4 */
    public String m72402L4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88617a5 + 1;
        this.f88617a5 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: M4 */
    public String m72403M4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88617a5 + 1;
        this.f88617a5 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded2\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded2(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: N4 */
    public String m72404N4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return m72840w3(this.f88615Y4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                String str;
                try {
                    String str2 = MMNeoViewerActivityFragment.this.f89563A4;
                    if (str2 == null || str2.length() == 0) {
                        iMDLogger.m73550f("Loading Document", MMNeoViewerActivityFragment.this.f89567E4);
                        String[] strArrSplit = MMNeoViewerActivityFragment.this.f89567E4.split("-");
                        MMNeoViewerActivityFragment.this.f88616Z4 = new ArrayList<>();
                        MMNeoViewerActivityFragment.this.f88617a5 = 0;
                        if (strArrSplit[0].equals("drug")) {
                            MMNeoViewerActivityFragment mMNeoViewerActivityFragment = MMNeoViewerActivityFragment.this;
                            ArrayList<Bundle> arrayListM71817V = mMNeoViewerActivityFragment.f89579Q4.m71817V(mMNeoViewerActivityFragment.f89566D4, "Select * from drug_idx where drug_id=" + strArrSplit[1] + " and has_generic=0");
                            if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                                MMNeoViewerActivityFragment.this.f88614X4 = arrayListM71817V.get(0);
                                MMNeoViewerActivityFragment mMNeoViewerActivityFragment2 = MMNeoViewerActivityFragment.this;
                                mMNeoViewerActivityFragment2.f89568F4 = mMNeoViewerActivityFragment2.f88614X4.getString("name");
                                MMNeoViewerActivityFragment mMNeoViewerActivityFragment3 = MMNeoViewerActivityFragment.this;
                                ArrayList<Bundle> arrayListM71817V2 = mMNeoViewerActivityFragment3.f89579Q4.m71817V(mMNeoViewerActivityFragment3.f89566D4, "select drug_section.title as sectionTitle, drug_sub_section.title as subSectionTitle, monograph from (drug_mono inner join drug_sub_section on drug_sub_section.sub_section_id=drug_mono.sub_section_id) inner join drug_section on drug_section.section_id=drug_sub_section.section_id where drug_id=" + strArrSplit[1] + " AND monograph<>'' AND drug_section.section_id<>0 order by drug_section.section_id, drug_sub_section.sub_section_id");
                                if (arrayListM71817V2 == null) {
                                    arrayListM71817V2 = new ArrayList<>();
                                }
                                Iterator<Bundle> it2 = arrayListM71817V2.iterator();
                                str = "";
                                while (it2.hasNext()) {
                                    Bundle next = it2.next();
                                    str = str + MMNeoViewerActivityFragment.this.m72402L4(next.getString("sectionTitle"), "", "LTR", next.getString("monograph"), "", "margin-left:10px;margin-top:5px", "");
                                    MMNeoViewerActivityFragment.this.m72399I4(next.getString("sectionTitle"), MMNeoViewerActivityFragment.this.f88617a5);
                                }
                            }
                            MMNeoViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                            return;
                        }
                        if (strArrSplit[0].equals("formula")) {
                            MMNeoViewerActivityFragment mMNeoViewerActivityFragment4 = MMNeoViewerActivityFragment.this;
                            ArrayList<Bundle> arrayListM71817V3 = mMNeoViewerActivityFragment4.f89579Q4.m71817V(mMNeoViewerActivityFragment4.f89566D4, "Select * from formula_idx where formula_id=" + strArrSplit[1]);
                            if (arrayListM71817V3 != null && arrayListM71817V3.size() != 0) {
                                MMNeoViewerActivityFragment.this.f88614X4 = arrayListM71817V3.get(0);
                                MMNeoViewerActivityFragment mMNeoViewerActivityFragment5 = MMNeoViewerActivityFragment.this;
                                mMNeoViewerActivityFragment5.f89568F4 = mMNeoViewerActivityFragment5.f88614X4.getString("title");
                                MMNeoViewerActivityFragment mMNeoViewerActivityFragment6 = MMNeoViewerActivityFragment.this;
                                ArrayList<Bundle> arrayListM71817V4 = mMNeoViewerActivityFragment6.f89579Q4.m71817V(mMNeoViewerActivityFragment6.f89566D4, "SELECT (  CASE WHEN (fs.sorter = 0) THEN '<th>'  WHEN (fs.sorter % 2 != 0) THEN '<tr class=\"\"odd\"\"><td>'   ELSE '<tr><td>' END ||   fs.value ||   CASE WHEN (fs.sorter = 0) THEN '</th><th>' ELSE '</td><td>' END || (CASE WHEN (ft.per_100_cal_value = '') THEN '&nbsp;' ELSE ft.per_100_cal_value END) || CASE WHEN (fs.sorter = 0) THEN '</th><th>' ELSE '</td><td>' END || (CASE WHEN (ft.per_liter_value = '') THEN '&nbsp;' ELSE ft.per_liter_value END) || CASE WHEN (fs.sorter = 0) THEN '</th>'  ELSE '</td></tr>' END  ) formatted_formula_data FROM formula_table ft, formula_section fs WHERE formula_id = " + strArrSplit[1] + " AND ft.section_id = fs.section_id ORDER BY fs.sorter");
                                MMNeoViewerActivityFragment mMNeoViewerActivityFragment7 = MMNeoViewerActivityFragment.this;
                                ArrayList<Bundle> arrayListM71817V5 = mMNeoViewerActivityFragment7.f89579Q4.m71817V(mMNeoViewerActivityFragment7.f89566D4, "SELECT ((CASE note_id WHEN 5 THEN X'e280a0' || ' Protein Source: ' WHEN 6 THEN X'e280a1' || ' Fat Source: ' WHEN 7 THEN X'c2a7' || ' Carbohydrate Source: ' ELSE '' END) || value) as formatted_product_note FROM product_notes WHERE formula_id = " + strArrSplit[1] + "  AND note_id != 1 ORDER BY note_id");
                                Iterator<Bundle> it3 = arrayListM71817V4.iterator();
                                String str3 = "";
                                while (it3.hasNext()) {
                                    str3 = str3 + it3.next().getString("formatted_formula_data");
                                }
                                String str4 = "<table>" + str3 + "</table>";
                                Iterator<Bundle> it4 = arrayListM71817V5.iterator();
                                String str5 = "";
                                while (it4.hasNext()) {
                                    str5 = str5 + it4.next().getString("formatted_product_note");
                                }
                                String str6 = "" + MMNeoViewerActivityFragment.this.m72402L4("Table", "", "LTR", str4, "", "margin-left:10px;margin-top:5px", "");
                                MMNeoViewerActivityFragment mMNeoViewerActivityFragment8 = MMNeoViewerActivityFragment.this;
                                mMNeoViewerActivityFragment8.m72399I4("Table", mMNeoViewerActivityFragment8.f88617a5);
                                str = str6 + MMNeoViewerActivityFragment.this.m72402L4("Product Notes", "", "LTR", str5, "", "margin-left:10px;margin-top:5px", "");
                                MMNeoViewerActivityFragment mMNeoViewerActivityFragment9 = MMNeoViewerActivityFragment.this;
                                mMNeoViewerActivityFragment9.m72399I4("Product Notes", mMNeoViewerActivityFragment9.f88617a5);
                            }
                            MMNeoViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                            return;
                        }
                        str = "";
                        MMNeoViewerActivityFragment mMNeoViewerActivityFragment10 = MMNeoViewerActivityFragment.this;
                        String strM72817d4 = mMNeoViewerActivityFragment10.m72817d4(mMNeoViewerActivityFragment10.m15366r(), "MMHeader.css");
                        MMNeoViewerActivityFragment mMNeoViewerActivityFragment11 = MMNeoViewerActivityFragment.this;
                        String strM72817d42 = mMNeoViewerActivityFragment11.m72817d4(mMNeoViewerActivityFragment11.m15366r(), "MMFooter.css");
                        String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", MMNeoViewerActivityFragment.this.f89568F4).replace("[include]", "");
                        MMNeoViewerActivityFragment.this.f89563A4 = strReplace + str + strM72817d42;
                    }
                    MMNeoViewerActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    MMNeoViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = MMNeoViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    MMNeoViewerActivityFragment mMNeoViewerActivityFragment = MMNeoViewerActivityFragment.this;
                    mMNeoViewerActivityFragment.m72780C4(mMNeoViewerActivityFragment.f89595p4);
                    return;
                }
                String strM71753g1 = CompressHelper.m71753g1(MMNeoViewerActivityFragment.this.f89566D4, "base");
                MMNeoViewerActivityFragment mMNeoViewerActivityFragment2 = MMNeoViewerActivityFragment.this;
                mMNeoViewerActivityFragment2.m72795O3(mMNeoViewerActivityFragment2.f89563A4, strM71753g1);
                MMNeoViewerActivityFragment.this.m72836s4();
                MMNeoViewerActivityFragment.this.m72831p4();
                MMNeoViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                MMNeoViewerActivityFragment.this.m15358o2(false);
                MMNeoViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = strM71753g1.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72404N4(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = strM71753g1 + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f88615Y4 = arrayList;
            mo71972o4();
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            m72398O4("asdfafdsaf");
            return true;
        }
        if (itemId == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f88616Z4);
            bundle.putString("TitleProperty", "label");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        if (str2.equals("image")) {
            m72398O4(str3);
            return true;
        }
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

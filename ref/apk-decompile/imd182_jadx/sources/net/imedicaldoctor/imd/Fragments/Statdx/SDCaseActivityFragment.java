package net.imedicaldoctor.imd.Fragments.Statdx;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.palette.graphics.Palette;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.GridAutoFitLayoutManager;
import net.imedicaldoctor.imd.ViewHolders.ImageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class SDCaseActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f88928X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88929Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88930Z4;

    /* renamed from: a5 */
    public DiagnosisAdapter f88931a5;

    /* renamed from: b5 */
    public Bundle f88932b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f88933c5;

    /* renamed from: d5 */
    public String f88934d5;

    /* renamed from: e5 */
    public ArrayList<String> f88935e5;

    /* renamed from: f5 */
    public ChaptersAdapter f88936f5;

    /* renamed from: g5 */
    public ArrayList<Bundle> f88937g5;

    /* renamed from: h5 */
    public String f88938h5;

    /* renamed from: i5 */
    private Bundle f88939i5;

    /* renamed from: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment$1 */
    class RunnableC50421 implements Runnable {
        RunnableC50421() {
        }

        @Override // java.lang.Runnable
        public void run() {
            SDCaseActivityFragment.this.f88933c5 = new ArrayList<>();
            Bundle bundle = new Bundle();
            bundle.putString("fieldTitle", "Description");
            bundle.putString("fieldId", "");
            SDCaseActivityFragment.this.f88933c5.add(bundle);
            SDCaseActivityFragment sDCaseActivityFragment = SDCaseActivityFragment.this;
            sDCaseActivityFragment.f88929Y4 = sDCaseActivityFragment.f89579Q4.m71817V(sDCaseActivityFragment.f89566D4, "Select * from cases_images where caseId='" + SDCaseActivityFragment.this.f88934d5 + "'");
            SDCaseActivityFragment sDCaseActivityFragment2 = SDCaseActivityFragment.this;
            if (sDCaseActivityFragment2.f88929Y4 == null) {
                sDCaseActivityFragment2.f88929Y4 = new ArrayList<>();
            }
            if (SDCaseActivityFragment.this.f88929Y4.size() > 0) {
                SDCaseActivityFragment.this.f88935e5.add("Images");
                Iterator<Bundle> it2 = SDCaseActivityFragment.this.f88929Y4.iterator();
                while (it2.hasNext()) {
                    Bundle next = it2.next();
                    Bundle bundle2 = new Bundle();
                    String strM71754h1 = CompressHelper.m71754h1(SDCaseActivityFragment.this.f89566D4, next.getString("imageId") + ".jpg", "images-E");
                    SDCaseActivityFragment.this.m72804T3(next.getString("imageId"), "images-E");
                    bundle2.putString("ImagePath", strM71754h1);
                    bundle2.putString("id", next.getString("imageId"));
                    bundle2.putString("Encrypted", IcyHeaders.f28171a3);
                    bundle2.putBundle("db", SDCaseActivityFragment.this.f89566D4);
                    SDCaseActivityFragment.this.f88930Z4.add(bundle2);
                }
                SDCaseActivityFragment sDCaseActivityFragment3 = SDCaseActivityFragment.this;
                sDCaseActivityFragment3.f88936f5 = new ChaptersAdapter(sDCaseActivityFragment3.m15366r(), SDCaseActivityFragment.this.f88929Y4, "title", C5562R.layout.list_view_item_image) { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.1.1
                    @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                    /* renamed from: e0 */
                    public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle3, int i2) {
                        final ImageViewHolder imageViewHolder = (ImageViewHolder) viewHolder;
                        final String strM71754h12 = CompressHelper.m71754h1(SDCaseActivityFragment.this.f89566D4, bundle3.getString("imageId") + ".jpg", "small");
                        SDCaseActivityFragment.this.m72804T3(bundle3.getString("imageId"), "small");
                        Glide.m30041G(SDCaseActivityFragment.this.m15366r()).mo30124i(new File(strM71754h12)).m30165B2(imageViewHolder.f101461I);
                        if (SDCaseActivityFragment.this.f88939i5.containsKey(strM71754h12)) {
                            imageViewHolder.f101462J.setRippleColor(SDCaseActivityFragment.this.f88939i5.getInt(strM71754h12));
                        } else {
                            SDCaseActivityFragment.this.m72832q3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.1.1.1
                                @Override // java.lang.Runnable
                                public void run() {
                                    Palette.Swatch swatchM26489C = Palette.m26478b(BitmapFactory.decodeFile(strM71754h12)).m26518g().m26489C();
                                    if (swatchM26489C == null) {
                                        return;
                                    }
                                    int iM26530e = swatchM26489C.m26530e();
                                    if (SDCaseActivityFragment.this.f88939i5.containsKey(strM71754h12)) {
                                        return;
                                    }
                                    SDCaseActivityFragment.this.f88939i5.putInt(strM71754h12, iM26530e);
                                }
                            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.1.1.2
                                @Override // java.lang.Runnable
                                public void run() {
                                    imageViewHolder.f101462J.setRippleColor(SDCaseActivityFragment.this.f88939i5.getInt(strM71754h12));
                                }
                            });
                        }
                        imageViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.1.1.3
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                SDCaseActivityFragment.this.m72497O4(bundle3.getString("imageId"));
                            }
                        });
                    }

                    @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                    /* renamed from: h0 */
                    public RecyclerView.ViewHolder mo71986h0(View view) {
                        return new ImageViewHolder(view);
                    }
                };
            }
            SDCaseActivityFragment sDCaseActivityFragment4 = SDCaseActivityFragment.this;
            sDCaseActivityFragment4.f88937g5 = sDCaseActivityFragment4.f89579Q4.m71817V(sDCaseActivityFragment4.f89566D4, "Select * from cases_docs where caseId='" + SDCaseActivityFragment.this.f88934d5 + "'");
            ArrayList<Bundle> arrayList = SDCaseActivityFragment.this.f88937g5;
            if (arrayList != null && arrayList.size() > 0) {
                SDCaseActivityFragment.this.f88935e5.add("ddx");
            }
            SDCaseActivityFragment.this.f88935e5.add("Document");
        }
    }

    public class DiagnosisAdapter extends RecyclerView.Adapter {

        /* renamed from: f */
        private static final int f88956f = 0;

        /* renamed from: g */
        private static final int f88957g = 1;

        /* renamed from: h */
        private static final int f88958h = 2;

        /* renamed from: d */
        public Context f88959d;

        public DiagnosisAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            SDCaseActivityFragment sDCaseActivityFragment = SDCaseActivityFragment.this;
            Bundle bundleM72498I4 = sDCaseActivityFragment.m72498I4(i2, sDCaseActivityFragment.f88935e5);
            String string = bundleM72498I4.getString("Type");
            if (string.equals("Header")) {
                return 0;
            }
            if (string.equals("Item")) {
                return bundleM72498I4.getString("Section").equals("Images") ? 2 : 1;
            }
            return -1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            MaterialRippleLayout materialRippleLayout;
            View.OnClickListener onClickListener;
            SDCaseActivityFragment sDCaseActivityFragment = SDCaseActivityFragment.this;
            Bundle bundleM72498I4 = sDCaseActivityFragment.m72498I4(i2, sDCaseActivityFragment.f88935e5);
            int iM27811F = viewHolder.m27811F();
            if (iM27811F == 0) {
                ((HeaderCellViewHolder) viewHolder).f88965I.setText(SDCaseActivityFragment.this.m72500M4(bundleM72498I4.getString("Text")));
                return;
            }
            if (iM27811F == 2) {
                RecyclerViewViewHolder recyclerViewViewHolder = (RecyclerViewViewHolder) viewHolder;
                recyclerViewViewHolder.f88966I.setAdapter(SDCaseActivityFragment.this.f88936f5);
                recyclerViewViewHolder.f88966I.setLayoutManager(new GridAutoFitLayoutManager(SDCaseActivityFragment.this.m15366r(), (int) (SDCaseActivityFragment.this.m15320b0().getDisplayMetrics().density * 100.0f)));
                return;
            }
            if (iM27811F == 1) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                String string = bundleM72498I4.getString("Section");
                int i3 = bundleM72498I4.getInt("Index");
                rippleTextFullViewHolder.f101500J.setVisibility(8);
                if (string.equals("Images")) {
                    return;
                }
                if (string.equals("Document")) {
                    final Bundle bundle = SDCaseActivityFragment.this.f88933c5.get(i3);
                    rippleTextFullViewHolder.f101499I.setText(bundle.getString("fieldTitle"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.DiagnosisAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDCaseActivityFragment sDCaseActivityFragment2 = SDCaseActivityFragment.this;
                            sDCaseActivityFragment2.f89579Q4.m71772A1(sDCaseActivityFragment2.f89566D4, "doc,,,case,,," + SDCaseActivityFragment.this.f88934d5, null, bundle.getString("fieldId"));
                        }
                    };
                } else {
                    if (!string.equals("ddx")) {
                        return;
                    }
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    final Bundle bundle2 = SDCaseActivityFragment.this.f88937g5.get(i3);
                    rippleTextFullViewHolder.f101499I.setText(bundle2.getString("docTitle"));
                    rippleTextFullViewHolder.f101500J.setText(bundle2.getString("docSection"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.DiagnosisAdapter.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDCaseActivityFragment sDCaseActivityFragment2 = SDCaseActivityFragment.this;
                            sDCaseActivityFragment2.f89579Q4.m71772A1(sDCaseActivityFragment2.f89566D4, "menu,,," + bundle2.getString("docId"), null, null);
                        }
                    };
                }
                materialRippleLayout.setOnClickListener(onClickListener);
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new HeaderCellViewHolder(LayoutInflater.from(SDCaseActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 2) {
                return new RecyclerViewViewHolder(LayoutInflater.from(SDCaseActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_recyclerview, viewGroup, false));
            }
            if (i2 != 1) {
                return null;
            }
            RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(LayoutInflater.from(SDCaseActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_full, viewGroup, false));
            rippleTextFullViewHolder.f101501K.setVisibility(8);
            return rippleTextFullViewHolder;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            SDCaseActivityFragment sDCaseActivityFragment = SDCaseActivityFragment.this;
            return sDCaseActivityFragment.m72502P4(sDCaseActivityFragment.f88935e5);
        }
    }

    public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f88965I;

        public HeaderCellViewHolder(View view) {
            super(view);
            this.f88965I = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    public static class RecyclerViewViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public RecyclerView f88966I;

        public RecyclerViewViewHolder(View view) {
            super(view);
            this.f88966I = (RecyclerView) view.findViewById(C5562R.id.recycler_view);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: O4 */
    public void m72497O4(String str) {
        int i2 = 0;
        int i3 = 0;
        while (true) {
            if (i3 >= this.f88930Z4.size() - 1) {
                break;
            }
            if (this.f88930Z4.get(i3).getString("id").equals(str)) {
                i2 = i3;
                break;
            }
            i3++;
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", this.f88930Z4);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public Bundle m72498I4(int i2, ArrayList<String> arrayList) {
        Iterator<String> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            String next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Text", next);
                bundle.putString("Type", "Header");
                return bundle;
            }
            int iM72501N4 = i3 + m72501N4(next);
            if (i2 <= iM72501N4) {
                int iM72501N42 = (i2 - (iM72501N4 - m72501N4(next))) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putString("Section", next);
                bundle2.putInt("Index", iM72501N42);
                bundle2.putString("Type", "Item");
                return bundle2;
            }
            i3 = iM72501N4 + 1;
        }
        return null;
    }

    /* renamed from: L4 */
    public void m72499L4() {
        this.f88928X4.setItemAnimator(new DefaultItemAnimator());
        this.f88928X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88928X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: M4 */
    public String m72500M4(String str) {
        if (!str.equals("Images")) {
            return str.equals("Document") ? "Document" : str.equals("ddx") ? "Related Dx" : "";
        }
        return this.f88929Y4.size() + " Images";
    }

    /* renamed from: N4 */
    public int m72501N4(String str) {
        ArrayList<Bundle> arrayList;
        if (str.equals("Images")) {
            return 1;
        }
        if (str.equals("Document")) {
            arrayList = this.f88933c5;
        } else {
            if (!str.equals("ddx")) {
                return 0;
            }
            arrayList = this.f88937g5;
        }
        return arrayList.size();
    }

    /* renamed from: P4 */
    public int m72502P4(ArrayList<String> arrayList) {
        int iM72501N4 = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            iM72501N4 = iM72501N4 + m72501N4(it2.next()) + 1;
        }
        return iM72501N4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList = this.f88929Y4;
        if (arrayList == null || arrayList.size() <= 0) {
            return null;
        }
        Bundle bundleM72839v3 = m72839v3(this.f88929Y4);
        String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, bundleM72839v3.getString("imageId") + ".jpg", "images-E");
        m72804T3(bundleM72839v3.getString("imageId"), "images-E");
        return strM71754h1;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88928X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        this.f88939i5 = new Bundle();
        String[] strArrSplit = this.f89567E4.split(",,,");
        if (strArrSplit.length == 2) {
            this.f88934d5 = strArrSplit[1];
        } else if (strArrSplit.length == 3) {
            this.f88934d5 = strArrSplit[1];
            this.f88938h5 = strArrSplit[2];
            this.f89567E4 = strArrSplit[0] + ",,," + strArrSplit[1];
        }
        this.f88935e5 = new ArrayList<>();
        this.f88930Z4 = new ArrayList<>();
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from cases where id='" + this.f88934d5 + "'");
        if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "Can't find the document, sorry", 1);
        } else {
            Bundle bundle2 = arrayListM71817V.get(0);
            this.f88932b5 = bundle2;
            this.f89568F4 = bundle2.getString("title");
            m72834r3(new RunnableC50421(), new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    SDCaseActivityFragment sDCaseActivityFragment = SDCaseActivityFragment.this;
                    if (sDCaseActivityFragment.f88929Y4 == null) {
                        sDCaseActivityFragment.f88929Y4 = new ArrayList<>();
                    }
                    SDCaseActivityFragment sDCaseActivityFragment2 = SDCaseActivityFragment.this;
                    final String str = sDCaseActivityFragment2.f88938h5;
                    if (str != null) {
                        sDCaseActivityFragment2.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.2.1
                            @Override // java.lang.Runnable
                            public void run() {
                                SDCaseActivityFragment.this.m72497O4(str);
                            }
                        }, 1000L);
                    }
                    if (SDCaseActivityFragment.this.m15387y().containsKey("SEARCH") && SDCaseActivityFragment.this.m15387y().getStringArray("SEARCH") != null) {
                        new Timer().schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.2.2
                            @Override // java.util.TimerTask, java.lang.Runnable
                            public void run() {
                                SDCaseActivityFragment sDCaseActivityFragment3 = SDCaseActivityFragment.this;
                                sDCaseActivityFragment3.f89579Q4.m71772A1(sDCaseActivityFragment3.f89566D4, "doc,,,case,,," + SDCaseActivityFragment.this.f88934d5, SDCaseActivityFragment.this.m15387y().getStringArray("SEARCH"), null);
                                SDCaseActivityFragment.this.m15387y().remove("SEARCH");
                            }
                        }, ExoPlayer.f21773a1);
                    }
                    SDCaseActivityFragment sDCaseActivityFragment3 = SDCaseActivityFragment.this;
                    sDCaseActivityFragment3.f88931a5 = sDCaseActivityFragment3.new DiagnosisAdapter();
                    SDCaseActivityFragment sDCaseActivityFragment4 = SDCaseActivityFragment.this;
                    sDCaseActivityFragment4.f88928X4.setAdapter(sDCaseActivityFragment4.f88931a5);
                    SDCaseActivityFragment.this.m72499L4();
                    SDCaseActivityFragment.this.mo72642f3(C5562R.menu.favorite);
                    SDCaseActivityFragment.this.m15358o2(false);
                    SDCaseActivityFragment.this.m72786G3();
                }
            });
        }
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        super.mo71957e3(menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.4

            /* renamed from: a */
            byte[] f88954a;

            @Override // android.os.AsyncTask
            protected Object doInBackground(Object[] objArr) {
                try {
                    File file = new File(SDCaseActivityFragment.this.mo71955R2());
                    this.f88954a = new CompressHelper(SDCaseActivityFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127");
                    return null;
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                    return null;
                }
            }

            @Override // android.os.AsyncTask
            protected void onPostExecute(Object obj) {
                super.onPostExecute(obj);
                Glide.m30041G(SDCaseActivityFragment.this.m15366r()).mo30123h(this.f88954a).m30165B2(SDCaseActivityFragment.this.f89575M4);
            }
        }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
    }

    @Override // androidx.fragment.app.Fragment, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDCaseActivityFragment.3
            @Override // java.lang.Runnable
            public void run() {
                SDCaseActivityFragment.this.f88931a5.m27491G();
            }
        }, 500L);
    }
}

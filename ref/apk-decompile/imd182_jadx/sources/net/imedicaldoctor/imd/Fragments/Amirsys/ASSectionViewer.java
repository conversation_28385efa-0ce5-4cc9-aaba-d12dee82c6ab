package net.imedicaldoctor.imd.Fragments.Amirsys;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.Facts.FTViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.IranDaru.IDViewerActivity;
import net.imedicaldoctor.imd.Fragments.LWW.LWWViewerFragment;
import net.imedicaldoctor.imd.Fragments.Martindale.MDViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMIVViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Statdx.SDDocActivityFragment;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;

/* loaded from: classes3.dex */
public class ASSectionViewer extends DialogFragment {
    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_new_section_viewer, (ViewGroup) null);
        RecyclerView recyclerView = (RecyclerView) viewInflate.findViewById(C5562R.id.recycler_view);
        recyclerView.setAdapter(new ChaptersAdapter(m15366r(), m15387y().getParcelableArrayList("Items"), m15387y().getString("TitleProperty"), C5562R.layout.list_view_item_ripple_text) { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer.1
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: f0 */
            public void mo71975f0(Bundle bundle2, int i2) {
                ASSectionViewer.this.mo15205N2();
                ASSectionViewer.this.m71987g3(bundle2);
            }
        });
        recyclerView.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        recyclerView.m27459p(new CustomItemDecoration(m15366r()));
        builder.setView(viewInflate);
        return builder.create();
    }

    /* renamed from: g3 */
    public void m71987g3(Bundle bundle) {
        ViewerHelperFragment viewerHelperFragment;
        StringBuilder sb;
        String str;
        String string;
        if (m15351l0() instanceof LWWViewerFragment) {
            ((LWWViewerFragment) m15351l0()).mo71967C3(bundle.getString("id"));
            return;
        }
        if (!(m15351l0() instanceof MMViewerActivityFragment)) {
            if (!(m15351l0() instanceof ASDocActivityFragment)) {
                if (!(m15351l0() instanceof FTViewerActivityFragment)) {
                    if (m15351l0() instanceof IDViewerActivity.IDViewerFragment) {
                        viewerHelperFragment = (IDViewerActivity.IDViewerFragment) m15351l0();
                        str = "fDrugGenericID";
                    } else if (m15351l0() instanceof MDViewerActivityFragment) {
                        viewerHelperFragment = (MDViewerActivityFragment) m15351l0();
                    } else if (m15351l0() instanceof MMInteractViewerActivityFragment) {
                        viewerHelperFragment = (MMInteractViewerActivityFragment) m15351l0();
                        str = "f" + bundle.getString("sequence");
                    } else if (m15351l0() instanceof MMIVViewerActivityFragment) {
                        viewerHelperFragment = (MMIVViewerActivityFragment) m15351l0();
                        sb = new StringBuilder();
                    } else if (m15351l0() instanceof MMNeoViewerActivityFragment) {
                        viewerHelperFragment = (MMNeoViewerActivityFragment) m15351l0();
                        sb = new StringBuilder();
                    } else if (!(m15351l0() instanceof SDDocActivityFragment)) {
                        return;
                    } else {
                        viewerHelperFragment = (SDDocActivityFragment) m15351l0();
                    }
                    string = bundle.getString(str);
                    viewerHelperFragment.mo71967C3(string);
                }
                viewerHelperFragment = (FTViewerActivityFragment) m15351l0();
                string = bundle.getString("fId");
                viewerHelperFragment.mo71967C3(string);
            }
            viewerHelperFragment = (ASDocActivityFragment) m15351l0();
            string = bundle.getString("fieldId");
            viewerHelperFragment.mo71967C3(string);
        }
        viewerHelperFragment = (MMViewerActivityFragment) m15351l0();
        sb = new StringBuilder();
        sb.append("f");
        sb.append(bundle.getString("sequence"));
        string = sb.toString();
        viewerHelperFragment.mo71967C3(string);
    }
}

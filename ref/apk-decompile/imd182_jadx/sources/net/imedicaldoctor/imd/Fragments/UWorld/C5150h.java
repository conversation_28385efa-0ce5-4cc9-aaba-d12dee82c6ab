package net.imedicaldoctor.imd.Fragments.UWorld;

import androidx.media3.extractor.text.ttml.TtmlNode;
import java.util.Iterator;

/* renamed from: net.imedicaldoctor.imd.Fragments.UWorld.h */
/* loaded from: classes3.dex */
public final /* synthetic */ class C5150h {
    /* renamed from: a */
    public static /* synthetic */ String m72696a(CharSequence charSequence, Iterable iterable) {
        if (charSequence == null) {
            throw new NullPointerException(TtmlNode.f29717b0);
        }
        StringBuilder sb = new StringBuilder();
        Iterator it2 = iterable.iterator();
        if (it2.hasNext()) {
            while (true) {
                sb.append((CharSequence) it2.next());
                if (!it2.hasNext()) {
                    break;
                }
                sb.append(charSequence);
            }
        }
        return sb.toString();
    }
}

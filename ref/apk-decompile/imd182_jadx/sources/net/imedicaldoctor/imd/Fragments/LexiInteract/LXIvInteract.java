package net.imedicaldoctor.imd.Fragments.LexiInteract;

import android.content.res.Resources;
import android.database.DataSetObserver;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.FragmentManager;
import com.google.common.net.HttpHeaders;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.LexiInteract.LXIVInteractResult;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Views.ButtonSmall;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class LXIvInteract extends iMDActivity {

    public static class LXIvInteractFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private ArrayList<Bundle> f88414A4;

        /* renamed from: B4 */
        private ArrayList<Bundle> f88415B4;

        /* renamed from: C4 */
        private ArrayList<Bundle> f88416C4;

        /* renamed from: D4 */
        public ButtonSmall f88417D4;

        /* renamed from: E4 */
        private ArrayList<Bundle> f88418E4;

        /* renamed from: F4 */
        private ArrayList<Bundle> f88419F4;

        public class HeaderViewHolder {

            /* renamed from: a */
            public final TextView f88430a;

            public HeaderViewHolder(View view) {
                this.f88430a = (TextView) view.findViewById(C5562R.id.header_text);
            }
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            ArrayList<Bundle> arrayList;
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_lxiv_interact, viewGroup, false);
            this.f88797q4 = viewInflate;
            super.mo15303U0(layoutInflater, viewGroup, bundle);
            this.f88798r4 = (Toolbar) this.f88797q4.findViewById(C5562R.id.toolbar);
            this.f88417D4 = (ButtonSmall) this.f88797q4.findViewById(C5562R.id.back_button);
            this.f88798r4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract.LXIvInteractFragment.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    LXIvInteractFragment.this.f88791k4.m71821W1(true);
                }
            });
            ButtonSmall buttonSmall = this.f88417D4;
            if (buttonSmall != null) {
                buttonSmall.setDrawableIcon(m15366r().getResources().getDrawable(C5562R.drawable.back_icon));
                this.f88417D4.setRippleColor(m15366r().getResources().getColor(C5562R.color.toolbar_item_ripple_color));
                this.f88417D4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract.LXIvInteractFragment.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        LXIvInteractFragment.this.f88791k4.m71821W1(true);
                    }
                });
                this.f88417D4.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract.LXIvInteractFragment.3
                    @Override // android.view.View.OnLongClickListener
                    public boolean onLongClick(View view) {
                        LXIvInteractFragment.this.f88791k4.m71830Z1(true);
                        return true;
                    }
                });
            }
            mo72338d3();
            if (bundle == null || !bundle.containsKey("mDrugs")) {
                this.f88414A4 = new ArrayList<>();
                this.f88415B4 = new ArrayList<>();
                arrayList = new ArrayList<>();
            } else {
                this.f88414A4 = bundle.getParcelableArrayList("mIVDrugs");
                this.f88415B4 = bundle.getParcelableArrayList("mSites");
                arrayList = bundle.getParcelableArrayList("mSolutions");
            }
            this.f88416C4 = arrayList;
            final CompressHelper compressHelper = new CompressHelper(m15366r());
            this.f88418E4 = compressHelper.m71817V(this.f88788h4, "Select * from site order by name");
            this.f88419F4 = compressHelper.m71817V(this.f88788h4, "Select * from solution order by name");
            ListAdapter listAdapter = new ListAdapter() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract.LXIvInteractFragment.4
                @Override // android.widget.Adapter
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public Bundle getItem(int i2) {
                    ArrayList arrayList2;
                    int size;
                    int i3;
                    String str;
                    Bundle bundle2 = new Bundle();
                    if (i2 == 0) {
                        bundle2.putString("Type", "Header");
                        str = "DRUGS";
                    } else if (i2 == 1) {
                        bundle2.putString("Type", "Add");
                        str = "Add Drug";
                    } else if (i2 == LXIvInteractFragment.this.f88414A4.size() + 2) {
                        bundle2.putString("Type", "Header");
                        str = "AT SITES";
                    } else if (i2 == LXIvInteractFragment.this.f88414A4.size() + 3) {
                        bundle2.putString("Type", "Add");
                        str = "All Sites";
                    } else if (i2 == LXIvInteractFragment.this.f88414A4.size() + 4 + LXIvInteractFragment.this.f88415B4.size()) {
                        bundle2.putString("Type", "Header");
                        str = "IN SOLUTIONS";
                    } else {
                        if (i2 != LXIvInteractFragment.this.f88414A4.size() + 5 + LXIvInteractFragment.this.f88415B4.size()) {
                            if (i2 <= 1 || i2 >= LXIvInteractFragment.this.f88414A4.size() + 2) {
                                if (i2 <= LXIvInteractFragment.this.f88414A4.size() + 3 || i2 >= LXIvInteractFragment.this.f88414A4.size() + 4 + LXIvInteractFragment.this.f88415B4.size()) {
                                    if (i2 > LXIvInteractFragment.this.f88414A4.size() + 5 + LXIvInteractFragment.this.f88415B4.size()) {
                                        bundle2.putString("Type", "SolutionItem");
                                        arrayList2 = LXIvInteractFragment.this.f88416C4;
                                        size = LXIvInteractFragment.this.f88414A4.size() + 6 + LXIvInteractFragment.this.f88415B4.size();
                                    }
                                    return bundle2;
                                }
                                bundle2.putString("Type", "SiteItem");
                                arrayList2 = LXIvInteractFragment.this.f88415B4;
                                size = LXIvInteractFragment.this.f88414A4.size() + 4;
                                i3 = i2 - size;
                            } else {
                                bundle2.putString("Type", "DrugItem");
                                arrayList2 = LXIvInteractFragment.this.f88414A4;
                                i3 = i2 - 2;
                            }
                            bundle2.putBundle("Item", (Bundle) arrayList2.get(i3));
                            return bundle2;
                        }
                        bundle2.putString("Type", "Add");
                        str = "All Solutions";
                    }
                    bundle2.putString("Text", str);
                    return bundle2;
                }

                @Override // android.widget.ListAdapter
                public boolean areAllItemsEnabled() {
                    return false;
                }

                @Override // android.widget.Adapter
                public int getCount() {
                    return LXIvInteractFragment.this.f88414A4.size() + 4 + LXIvInteractFragment.this.f88415B4.size() + 2 + LXIvInteractFragment.this.f88416C4.size();
                }

                @Override // android.widget.Adapter
                public long getItemId(int i2) {
                    return 0L;
                }

                @Override // android.widget.Adapter
                public int getItemViewType(int i2) {
                    Bundle item = getItem(i2);
                    if (item.getString("Type").equals("Header")) {
                        return 0;
                    }
                    return item.getString("Type").equals("Add") ? 1 : 2;
                }

                @Override // android.widget.Adapter
                public View getView(int i2, View view, ViewGroup viewGroup2) {
                    TextView textView;
                    Bundle item = getItem(i2);
                    final String string = item.getString("Type");
                    if (string.equals("Header")) {
                        if (view == null) {
                            view = LayoutInflater.from(LXIvInteractFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup2, false);
                            view.setTag(LXIvInteractFragment.this.new HeaderViewHolder(view));
                        }
                        textView = ((HeaderViewHolder) view.getTag()).f88430a;
                    } else {
                        if (!string.equals("Add")) {
                            if (view == null) {
                                view = LayoutInflater.from(LXIvInteractFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_delete, viewGroup2, false);
                                view.setTag(view.findViewById(C5562R.id.text));
                            }
                            TextView textView2 = (TextView) view.getTag();
                            final Bundle bundle2 = item.getBundle("Item");
                            textView2.setText(bundle2.getString("name"));
                            ((TextView) view.findViewById(C5562R.id.delete_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract.LXIvInteractFragment.4.1
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view2) {
                                    ArrayList arrayList2;
                                    if (string.equals("DrugItem")) {
                                        arrayList2 = LXIvInteractFragment.this.f88414A4;
                                    } else {
                                        if (!string.equals("SiteItem")) {
                                            if (string.equals("SolutionItem")) {
                                                arrayList2 = LXIvInteractFragment.this.f88416C4;
                                            }
                                            LXIvInteractFragment.this.f88787g4.setAdapter(LXIvInteractFragment.this.f88787g4.getAdapter());
                                        }
                                        arrayList2 = LXIvInteractFragment.this.f88415B4;
                                    }
                                    arrayList2.remove(bundle2);
                                    LXIvInteractFragment.this.f88787g4.setAdapter(LXIvInteractFragment.this.f88787g4.getAdapter());
                                }
                            });
                            return view;
                        }
                        if (view == null) {
                            view = LayoutInflater.from(LXIvInteractFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_add, viewGroup2, false);
                            view.setTag(view.findViewById(C5562R.id.text));
                        }
                        textView = (TextView) view.getTag();
                    }
                    textView.setText(item.getString("Text"));
                    return view;
                }

                @Override // android.widget.Adapter
                public int getViewTypeCount() {
                    return 3;
                }

                @Override // android.widget.Adapter
                public boolean hasStableIds() {
                    return false;
                }

                @Override // android.widget.Adapter
                public boolean isEmpty() {
                    return false;
                }

                @Override // android.widget.ListAdapter
                public boolean isEnabled(int i2) {
                    String string = getItem(i2).getString("Type");
                    return string.equals("Add") || string.equals("DrugItem");
                }

                @Override // android.widget.Adapter
                public void registerDataSetObserver(DataSetObserver dataSetObserver) {
                }

                @Override // android.widget.Adapter
                public void unregisterDataSetObserver(DataSetObserver dataSetObserver) {
                }
            };
            ((Button) this.f88797q4.findViewById(C5562R.id.interac_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract.LXIvInteractFragment.5
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    CompressHelper compressHelper2 = new CompressHelper(LXIvInteractFragment.this.m15366r());
                    if (LXIvInteractFragment.this.f88414A4.size() == 0) {
                        CompressHelper.m71767x2(LXIvInteractFragment.this.m15366r(), "You must at least add one drug", 1);
                        return;
                    }
                    if (LXIvInteractFragment.this.f88414A4.size() != 1) {
                        Bundle bundle2 = new Bundle();
                        bundle2.putParcelableArrayList("Sites", LXIvInteractFragment.this.m72344s3());
                        bundle2.putParcelableArrayList("Solutions", LXIvInteractFragment.this.m72346u3());
                        bundle2.putParcelableArrayList("Drugs", LXIvInteractFragment.this.f88414A4);
                        bundle2.putBundle("DB", LXIvInteractFragment.this.f88788h4);
                        if (LXIvInteractFragment.this.f88415B4.size() != 0 || LXIvInteractFragment.this.f88416C4.size() != 0) {
                            bundle2.putString(HttpHeaders.f62930g, "Note: Information shown represents only the sites and solutions you have chosen.");
                        }
                        new CompressHelper(LXIvInteractFragment.this.m15366r()).m71798N(LXIVInteractResult.class, LXIVInteractResult.LXIvInteractResultFragment.class, bundle2);
                        return;
                    }
                    Bundle bundle3 = (Bundle) LXIvInteractFragment.this.f88414A4.get(0);
                    String string = bundle3.getString("rowid");
                    ArrayList<Bundle> arrayListM71817V = compressHelper2.m71817V(LXIvInteractFragment.this.f88788h4, "Select d.id, vf.sequence, vf.label, f.content, f.fieldtype_id from document d join field f on f.document_id = d.id join chapter c on c.id = d.chapter_id join view v on v.id = c.view_id join viewfield vf on vf.view_id = v.id and vf.fieldtype_id = f.fieldtype_id join fieldtypesite s on vf.fieldtype_id = s.fieldtype_id join generic g on d.globalid = g.global_id  join item i on i.generic_id = g.id left join ivsolution l on f.document_id = l.document_id and f.fieldtype_id = l.fieldtype_id where i.rowid = " + string + " and f.fieldtype_id != 38 and f.fieldtype_id != 42 and s.site_id in (" + LXIvInteractFragment.this.m72343r3() + ")  and (l.solution_id is null or l.solution_id in (" + LXIvInteractFragment.this.m72345t3() + ")) union select d.id, vf.sequence, vf.label, iv.content, iv.fieldtype_id from document d join ivfield iv on iv.document_id = d.id join chapter c on c.id = d.chapter_id join view v on v.id = c.view_id join viewfield vf on vf.view_id = v.id and vf.fieldtype_id = iv.fieldtype_id join fieldtypesite s on vf.fieldtype_id = s.fieldtype_id join generic g on d.globalid = g.global_id join item i on i.generic_id = g.id  left join ivsolution l on iv.document_id = l.document_id and iv.fieldtype_id = l.fieldtype_id  left join solution sol on l.solution_id = sol.id  where i.rowid = " + string + " and iv.fieldtype_id != 38 and iv.fieldtype_id != 42  order by vf.sequence");
                    Bundle bundle4 = new Bundle();
                    bundle4.putParcelableArrayList("ivMonograph", arrayListM71817V);
                    bundle4.putParcelableArrayList("Solutions", LXIvInteractFragment.this.m72346u3());
                    bundle4.putParcelableArrayList("Sites", LXIvInteractFragment.this.m72344s3());
                    bundle4.putInt("Mode", 2);
                    if (LXIvInteractFragment.this.f88415B4.size() != 0 || LXIvInteractFragment.this.f88416C4.size() != 0) {
                        bundle4.putString(HttpHeaders.f62930g, "Note: Information shown represents only the sites and solutions you have chosen.");
                    }
                    compressHelper2.m71775B1(LXIvInteractFragment.this.f88788h4, bundle3.getString("rowid"), null, null, bundle4);
                }
            });
            this.f88787g4.setDivider(new GradientDrawable(GradientDrawable.Orientation.RIGHT_LEFT, new int[]{-1, -**********}));
            this.f88787g4.setDividerHeight(1);
            this.f88787g4.setAdapter(listAdapter);
            this.f88787g4.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract.LXIvInteractFragment.6
                @Override // android.widget.AdapterView.OnItemClickListener
                public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                    LXIvInteractList lXIvInteractList;
                    FragmentManager fragmentManagerM15283M;
                    String str;
                    LXIvInteractFragment lXIvInteractFragment = LXIvInteractFragment.this;
                    lXIvInteractFragment.f88785e4 = i2;
                    Bundle bundle2 = (Bundle) lXIvInteractFragment.f88787g4.getAdapter().getItem(i2);
                    String string = bundle2.getString("Type");
                    String string2 = bundle2.getString("Text");
                    if (!string.equals("Add")) {
                        if (string.equals("DrugItem")) {
                            String string3 = bundle2.getBundle("Item").getString("rowid");
                            ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(LXIvInteractFragment.this.f88788h4, "Select d.id, vf.sequence, vf.label, f.content, f.fieldtype_id from document d join field f on f.document_id = d.id join chapter c on c.id = d.chapter_id join view v on v.id = c.view_id join viewfield vf on vf.view_id = v.id and vf.fieldtype_id = f.fieldtype_id join fieldtypesite s on vf.fieldtype_id = s.fieldtype_id join generic g on d.globalid = g.global_id  join item i on i.generic_id = g.id left join ivsolution l on f.document_id = l.document_id and f.fieldtype_id = l.fieldtype_id where i.rowid = " + string3 + " and f.fieldtype_id != 38 and f.fieldtype_id != 42 and s.site_id in (" + LXIvInteractFragment.this.m72343r3() + ")  and (l.solution_id is null or l.solution_id in (" + LXIvInteractFragment.this.m72345t3() + ")) union select d.id, vf.sequence, vf.label, iv.content, iv.fieldtype_id from document d join ivfield iv on iv.document_id = d.id join chapter c on c.id = d.chapter_id join view v on v.id = c.view_id join viewfield vf on vf.view_id = v.id and vf.fieldtype_id = iv.fieldtype_id join fieldtypesite s on vf.fieldtype_id = s.fieldtype_id join generic g on d.globalid = g.global_id join item i on i.generic_id = g.id  left join ivsolution l on iv.document_id = l.document_id and iv.fieldtype_id = l.fieldtype_id  left join solution sol on l.solution_id = sol.id  where i.rowid = " + string3 + " and iv.fieldtype_id != 38 and iv.fieldtype_id != 42  order by vf.sequence");
                            Bundle bundle3 = new Bundle();
                            bundle3.putParcelableArrayList("ivMonograph", arrayListM71817V);
                            bundle3.putParcelableArrayList("Solutions", LXIvInteractFragment.this.m72346u3());
                            bundle3.putParcelableArrayList("Sites", LXIvInteractFragment.this.m72344s3());
                            bundle3.putInt("Mode", 2);
                            if (LXIvInteractFragment.this.f88415B4.size() != 0 || LXIvInteractFragment.this.f88416C4.size() != 0) {
                                bundle3.putString(HttpHeaders.f62930g, "Note: Information shown represents only the sites and solutions you have chosen.");
                            }
                            compressHelper.m71775B1(LXIvInteractFragment.this.f88788h4, bundle2.getBundle("Item").getString("rowid"), null, null, bundle3);
                            return;
                        }
                        return;
                    }
                    if (string2.contains("Drug")) {
                        LXIvInteractDrugList lXIvInteractDrugList = new LXIvInteractDrugList();
                        Bundle bundle4 = new Bundle();
                        bundle4.putBundle("db", LXIvInteractFragment.this.f88788h4);
                        bundle4.putString("Drugs", CompressHelper.m71735J1(LXIvInteractFragment.this.f88414A4, "generic_id", ",", "'", "'"));
                        lXIvInteractDrugList.m15342i2(bundle4);
                        lXIvInteractDrugList.mo15218Z2(true);
                        lXIvInteractDrugList.m15245A2(LXIvInteractFragment.this, 0);
                        lXIvInteractDrugList.mo15222e3(LXIvInteractFragment.this.m15283M(), "LXIVInteractDrugsList");
                        return;
                    }
                    if (string2.contains("Solution")) {
                        lXIvInteractList = new LXIvInteractList();
                        Bundle bundle5 = new Bundle();
                        bundle5.putBundle("db", LXIvInteractFragment.this.f88788h4);
                        ArrayList<? extends Parcelable> arrayList2 = new ArrayList<>();
                        arrayList2.addAll(LXIvInteractFragment.this.f88419F4);
                        arrayList2.removeAll(LXIvInteractFragment.this.f88416C4);
                        if (arrayList2.size() == 0) {
                            CompressHelper.m71767x2(LXIvInteractFragment.this.m15366r(), "There is nothing to select", 1);
                        }
                        bundle5.putParcelableArrayList("items", arrayList2);
                        bundle5.putString("titleProperty", "name");
                        bundle5.putString("type", "Solution");
                        lXIvInteractList.m15342i2(bundle5);
                        lXIvInteractList.mo15218Z2(true);
                        lXIvInteractList.m15245A2(LXIvInteractFragment.this, 0);
                        fragmentManagerM15283M = LXIvInteractFragment.this.m15283M();
                        str = "LXIVInteractListSolution";
                    } else {
                        if (!string2.contains("Site")) {
                            return;
                        }
                        lXIvInteractList = new LXIvInteractList();
                        Bundle bundle6 = new Bundle();
                        bundle6.putBundle("db", LXIvInteractFragment.this.f88788h4);
                        ArrayList<? extends Parcelable> arrayList3 = new ArrayList<>();
                        arrayList3.addAll(LXIvInteractFragment.this.f88418E4);
                        arrayList3.removeAll(LXIvInteractFragment.this.f88415B4);
                        if (arrayList3.size() == 0) {
                            CompressHelper.m71767x2(LXIvInteractFragment.this.m15366r(), "There is nothing to select", 1);
                        }
                        bundle6.putParcelableArrayList("items", arrayList3);
                        bundle6.putString("titleProperty", "name");
                        bundle6.putString("type", "Site");
                        lXIvInteractList.m15342i2(bundle6);
                        lXIvInteractList.mo15218Z2(true);
                        lXIvInteractList.m15245A2(LXIvInteractFragment.this, 0);
                        fragmentManagerM15283M = LXIvInteractFragment.this.m15283M();
                        str = "LXIVInteractListSite";
                    }
                    lXIvInteractList.mo15222e3(fragmentManagerM15283M, str);
                }
            });
            this.f88787g4.setSelection(this.f88785e4);
            m15358o2(true);
            mo72472e3();
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: d3 */
        public void mo72338d3() throws Resources.NotFoundException {
            if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
                float dimension = m15320b0().getDimension(C5562R.dimen.toolbar_padding);
                Toolbar toolbar = this.f88798r4;
                if (toolbar != null) {
                    toolbar.setPadding(0, (int) dimension, 0, 0);
                }
            }
        }

        /* renamed from: i3 */
        public String m72339i3() {
            new ArrayList();
            return CompressHelper.m71734I1(this.f88418E4, "id");
        }

        /* renamed from: j3 */
        public String m72340j3() {
            new ArrayList();
            return CompressHelper.m71734I1(this.f88419F4, "id");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        /* renamed from: p3 */
        public void m72341p3(Bundle bundle) {
            if (this.f88414A4.contains(bundle)) {
                CompressHelper.m71767x2(m15366r(), "You Already added this drug", 1);
                return;
            }
            this.f88414A4.add(bundle);
            ListView listView = this.f88787g4;
            listView.setAdapter(listView.getAdapter());
        }

        /* renamed from: q3 */
        public void m72342q3(Bundle bundle, String str) {
            ArrayList<Bundle> arrayList;
            if (!str.equals("Solution")) {
                if (str.equals("Site")) {
                    if (this.f88415B4.contains(bundle)) {
                        CompressHelper.m71767x2(m15366r(), "You Already added this Site", 1);
                        return;
                    }
                    arrayList = this.f88415B4;
                }
                ListView listView = this.f88787g4;
                listView.setAdapter(listView.getAdapter());
            }
            if (this.f88416C4.contains(bundle)) {
                CompressHelper.m71767x2(m15366r(), "You Already added this Solution", 1);
                return;
            }
            arrayList = this.f88416C4;
            arrayList.add(bundle);
            ListView listView2 = this.f88787g4;
            listView2.setAdapter(listView2.getAdapter());
        }

        /* renamed from: r3 */
        public String m72343r3() {
            return CompressHelper.m71734I1(m72344s3(), "id");
        }

        /* renamed from: s3 */
        public ArrayList<Bundle> m72344s3() {
            ArrayList<Bundle> arrayList = this.f88415B4.size() == 0 ? this.f88418E4 : this.f88415B4;
            Bundle bundle = new Bundle();
            bundle.putString("id", "0");
            arrayList.add(bundle);
            return arrayList;
        }

        /* renamed from: t3 */
        public String m72345t3() {
            return CompressHelper.m71734I1(m72346u3(), "id");
        }

        /* renamed from: u3 */
        public ArrayList<Bundle> m72346u3() {
            return this.f88416C4.size() == 0 ? this.f88419F4 : this.f88416C4;
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new LXIvInteractFragment());
    }
}

package net.imedicaldoctor.imd.Fragments.UWorld;

import android.os.Bundle;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes3.dex */
public class USMLECalculator {
    /* renamed from: a */
    public static double m72570a(int i2, int i3, int i4, ArrayList<Bundle> arrayList, double d2, double d3) {
        Iterator<Bundle> it2 = arrayList.iterator();
        double d4 = 0.0d;
        double d5 = 0.0d;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            d5 += next.getDouble("ppltaken");
            d4 += next.getDouble("corrTaken");
        }
        return (d4 / d5) * 100.0d;
    }

    /* renamed from: b */
    public static double m72571b(int i2, int i3, int i4, ArrayList<Bundle> arrayList, double d2, double d3) {
        String str;
        int i5 = i2 + i3 + i4;
        Iterator<Bundle> it2 = arrayList.iterator();
        double dPow = 0.0d;
        double d4 = 0.0d;
        double d5 = 0.0d;
        while (true) {
            str = "corrTaken";
            if (!it2.hasNext()) {
                break;
            }
            Bundle next = it2.next();
            d5 += next.getDouble("ppltaken");
            d4 += next.getDouble("corrTaken");
        }
        double d6 = d4 / d5;
        double d7 = i2 / i5;
        Iterator<Bundle> it3 = arrayList.iterator();
        while (it3.hasNext()) {
            Bundle next2 = it3.next();
            double d8 = next2.getDouble("ppltaken");
            dPow += d8 * Math.pow((next2.getDouble(str) / d8) - d6, 2.0d);
            str = str;
        }
        return d2 + (((d7 - d6) / Math.sqrt(dPow / d5)) * d3);
    }

    /* renamed from: c */
    public static void m72572c(String[] strArr) {
        ArrayList arrayList = new ArrayList();
        for (int i2 = 0; i2 < 100; i2++) {
            Bundle bundle = new Bundle();
            bundle.putDouble("ppltaken", 100.0d);
            bundle.putDouble("corrTaken", 60.0d);
            arrayList.add(bundle);
        }
        double dM72571b = m72571b(70, 20, 10, arrayList, 500.0d, 100.0d);
        System.out.println("USMLE Score: " + dM72571b);
    }
}

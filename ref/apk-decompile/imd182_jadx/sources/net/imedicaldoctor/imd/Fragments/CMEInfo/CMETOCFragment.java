package net.imedicaldoctor.imd.Fragments.CMEInfo;

import android.R;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.text.Html;
import android.text.Spanned;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;
import at.grabner.circleprogress.CircleProgressView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.common.net.HttpHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import com.p008dd.CircularProgressButton;
import java.io.File;
import java.io.FilenameFilter;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.Timer;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Fragments.downloadFragment;
import net.imedicaldoctor.imd.TitleComparator;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.iMD;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class CMETOCFragment extends SearchHelperFragment {

    /* renamed from: J4 */
    public static final String f87559J4 = "bytesDownloaded";

    /* renamed from: K4 */
    public static final String f87560K4 = "bytesTotal";

    /* renamed from: L4 */
    public static final String f87561L4 = "avgSpeed";

    /* renamed from: M4 */
    public static final String f87562M4 = "remaining";

    /* renamed from: N4 */
    public static final String f87563N4 = "Progress";

    /* renamed from: O4 */
    public static final String f87564O4 = "Title";

    /* renamed from: P4 */
    public static final String f87565P4 = "URL";

    /* renamed from: Q4 */
    public static final String f87566Q4 = "FileName";

    /* renamed from: R4 */
    public static final String f87567R4 = "MD5";

    /* renamed from: S4 */
    public static final String f87568S4 = "PartFileSize";

    /* renamed from: T4 */
    public static final String f87569T4 = "price";

    /* renamed from: U4 */
    public static final String f87570U4 = "Buy";

    /* renamed from: V4 */
    public static final String f87571V4 = "downloader";

    /* renamed from: W4 */
    public static final String f87572W4 = "retry";

    /* renamed from: X4 */
    public static final String f87573X4 = "completed";

    /* renamed from: Y4 */
    public static final String f87574Y4 = "error";

    /* renamed from: Z4 */
    public static final String f87575Z4 = "fileSize";

    /* renamed from: a5 */
    public static final String f87576a5 = "Icon";

    /* renamed from: b5 */
    public static final String f87577b5 = "name";

    /* renamed from: c5 */
    public static final String f87578c5 = "type";

    /* renamed from: d5 */
    public static final String f87579d5 = "version";

    /* renamed from: e5 */
    public static final String f87580e5 = "Delta";

    /* renamed from: f5 */
    public static final String f87581f5 = "Update";

    /* renamed from: g5 */
    public static final String f87582g5 = "Rebuilding";

    /* renamed from: h5 */
    public static final String f87583h5 = "Parts";

    /* renamed from: i5 */
    public static final String f87584i5 = "folderSizeKey";

    /* renamed from: j5 */
    public static final String f87585j5 = "videoIdKey";

    /* renamed from: k5 */
    public static final String f87586k5 = "savePathKey";

    /* renamed from: l5 */
    public static final String f87587l5 = "LatestKey";

    /* renamed from: A4 */
    public DownloadsAdapter f87588A4;

    /* renamed from: B4 */
    public ArrayList<Bundle> f87589B4;

    /* renamed from: C4 */
    public String f87590C4;

    /* renamed from: D4 */
    public ArrayList<Bundle> f87591D4;

    /* renamed from: E4 */
    public Typeface f87592E4;

    /* renamed from: F4 */
    private Activity f87593F4;

    /* renamed from: G4 */
    private Timer f87594G4;

    /* renamed from: H4 */
    public downloadFragment f87595H4;

    /* renamed from: I4 */
    public int f87596I4;

    public class DownloadCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f87615I;

        /* renamed from: J */
        private final TextView f87616J;

        /* renamed from: K */
        private final TextView f87617K;

        /* renamed from: L */
        private final Button f87618L;

        /* renamed from: M */
        private final CircleProgressView f87619M;

        public DownloadCellViewHolder(View view) {
            super(view);
            this.f87615I = (TextView) view.findViewById(C5562R.id.title);
            this.f87616J = (TextView) view.findViewById(C5562R.id.subtitle);
            this.f87617K = (TextView) view.findViewById(C5562R.id.subtitle2);
            Button button = (Button) view.findViewById(C5562R.id.download_button);
            this.f87618L = button;
            button.setTypeface(CMETOCFragment.this.f87592E4);
            this.f87619M = (CircleProgressView) view.findViewById(C5562R.id.circleView);
        }
    }

    public class DownloadsAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public ArrayList<Bundle> f87621d;

        /* renamed from: e */
        public ArrayList<Bundle> f87622e;

        public DownloadsAdapter(ArrayList<Bundle> arrayList, ArrayList<Bundle> arrayList2) {
            this.f87621d = arrayList;
            this.f87622e = arrayList2;
        }

        /* renamed from: d0 */
        private void m72006d0(final CircularProgressButton circularProgressButton) {
            circularProgressButton.setProgress(1);
            circularProgressButton.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.9
                @Override // java.lang.Runnable
                public void run() {
                    circularProgressButton.setProgress(0);
                    circularProgressButton.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.9.1
                        @Override // java.lang.Runnable
                        public void run() {
                            circularProgressButton.setProgress(1);
                        }
                    }, 100L);
                }
            }, 100L);
        }

        /* renamed from: e0 */
        private double m72007e0(Bundle bundle, String str) {
            try {
                if (bundle.containsKey(str)) {
                    return bundle.getDouble(str);
                }
                return 0.0d;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                return 0.0d;
            }
        }

        /* renamed from: g0 */
        private long m72008g0(Bundle bundle, String str) {
            try {
                if (bundle.containsKey(str)) {
                    return bundle.getLong(str);
                }
                return 0L;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                return 0L;
            }
        }

        /* renamed from: h0 */
        private String m72009h0(int i2) {
            return m72010k0(i2 / 3600) + " : " + m72010k0((i2 % 3600) / 60) + " : " + m72010k0(i2 % 60);
        }

        /* renamed from: k0 */
        private String m72010k0(int i2) {
            if (i2 == 0) {
                return "00";
            }
            if (i2 / 10 != 0) {
                return String.valueOf(i2);
            }
            return "0" + i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            if (m72013j0() == 0) {
                return 0;
            }
            ArrayList<Bundle> arrayList = this.f87621d;
            if (arrayList == null || arrayList.size() <= 0) {
                return CMETOCFragment.this.m72000t3(this.f87622e.get(i2)) ? 1 : 2;
            }
            if (i2 < this.f87621d.size()) {
                return 3;
            }
            return CMETOCFragment.this.m72000t3(this.f87622e.get(i2 - this.f87621d.size())) ? 1 : 2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) throws Resources.NotFoundException, NumberFormatException {
            TextView textView;
            int color;
            if (viewHolder.m27811F() == 3) {
                RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                rippleTextViewHolder.f101515I.setText(this.f87621d.get(i2).getString("name"));
                rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.4
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CMETOCFragment.this.m72468V2();
                        Bundle bundle = new Bundle();
                        bundle.putBundle("DB", CMETOCFragment.this.f88788h4);
                        bundle.putString("ParentId", DownloadsAdapter.this.f87621d.get(i2).getString("id"));
                        CMETOCFragment.this.f88791k4.m71798N(CMETOC.class, CMETOCFragment.class, bundle);
                    }
                });
                return;
            }
            final Bundle bundle = this.f87622e.get(i2 - this.f87621d.size());
            if (CMETOCFragment.this.m72000t3(bundle)) {
                VideoCellViewHolder videoCellViewHolder = (VideoCellViewHolder) viewHolder;
                videoCellViewHolder.f87649I.setText(CMETOCFragment.this.m71998q3(bundle));
                String string = bundle.getString(CSS.Property.f74043m0);
                if (string == "") {
                    videoCellViewHolder.f87650J.setProgress(0);
                } else {
                    long jLongValue = Long.valueOf(string).longValue();
                    Long lValueOf = Long.valueOf(bundle.getString("dur"));
                    Log.e("Progress bar", "Duration " + bundle.getString("dur") + ", position : " + bundle.getString(CSS.Property.f74043m0));
                    videoCellViewHolder.f87650J.setMax(10000);
                    videoCellViewHolder.f87650J.setProgress((int) (((double) (((float) jLongValue) / ((float) lValueOf.longValue()))) * 10000.0d));
                }
                if (bundle.containsKey("subText")) {
                    videoCellViewHolder.f87652L.setVisibility(0);
                    videoCellViewHolder.f87652L.setText(bundle.getString("subText"));
                } else {
                    videoCellViewHolder.f87652L.setVisibility(8);
                }
                videoCellViewHolder.f87651K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.5
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CMETOCFragment cMETOCFragment = CMETOCFragment.this;
                        cMETOCFragment.f87596I4 = i2;
                        String strM71754h1 = CompressHelper.m71754h1(cMETOCFragment.f88788h4, bundle.getString("name"), "temp");
                        if (bundle.containsKey("dbname")) {
                            strM71754h1 = CompressHelper.m71754h1(CMETOCFragment.this.f88788h4, bundle.getString("dbname") + "-" + bundle.getString("name"), "temp");
                        }
                        if (!CMETOCFragment.this.f88788h4.getString("Type").equals("cme")) {
                            strM71754h1 = CompressHelper.m71754h1(CMETOCFragment.this.f88788h4, bundle.getString("name"), "videos");
                        }
                        Intent intent = new Intent(CMETOCFragment.this.m71997p3(), (Class<?>) Player.class);
                        intent.putExtra("Address", strM71754h1);
                        intent.putExtra("DB", CMETOCFragment.this.f88788h4);
                        String string2 = bundle.getString(CSS.Property.f74043m0);
                        if (string2 == "") {
                            string2 = "0";
                        }
                        intent.putExtra(HttpHeaders.f62970t0, Long.valueOf(string2));
                        intent.putExtra("VideoID", bundle.getString("id"));
                        CMETOCFragment.this.m71997p3().startActivity(intent);
                    }
                });
                return;
            }
            DownloadCellViewHolder downloadCellViewHolder = (DownloadCellViewHolder) viewHolder;
            downloadCellViewHolder.f87615I.setText(CMETOCFragment.this.m71998q3(bundle));
            if (bundle.containsKey("subText")) {
                downloadCellViewHolder.f87617K.setVisibility(0);
                downloadCellViewHolder.f87617K.setText(bundle.getString("subText"));
            } else {
                downloadCellViewHolder.f87617K.setVisibility(8);
            }
            Bundle bundleM71996o3 = CMETOCFragment.this.m71996o3(bundle);
            if (bundleM71996o3 == null) {
                bundleM71996o3 = new Bundle();
                bundleM71996o3.putString("fileSize", bundle.getString("fileSize"));
            }
            if (bundleM71996o3.containsKey("completed")) {
                downloadCellViewHolder.f87616J.setText("Download Completed");
                downloadCellViewHolder.f87616J.setTextColor(CMETOCFragment.this.m15320b0().getColor(C5562R.color.green_real));
                if (bundleM71996o3.containsKey("Rebuilding")) {
                    downloadCellViewHolder.f87616J.setText("Rebuilding ...");
                }
                downloadCellViewHolder.f87618L.setVisibility(8);
                downloadCellViewHolder.f87619M.setVisibility(8);
                return;
            }
            if (!bundleM71996o3.containsKey("downloader")) {
                downloadCellViewHolder.f87618L.setVisibility(0);
                downloadCellViewHolder.f87619M.setVisibility(8);
                try {
                    if (bundleM71996o3.containsKey("error")) {
                        downloadCellViewHolder.f87616J.setText(bundleM71996o3.getString("error"));
                        textView = downloadCellViewHolder.f87616J;
                        color = CMETOCFragment.this.m15320b0().getColor(C5562R.color.red);
                    } else {
                        downloadCellViewHolder.f87616J.setText(CMETOCFragment.this.m71999s3(Long.valueOf(bundleM71996o3.getString("fileSize")).longValue()));
                        textView = downloadCellViewHolder.f87616J;
                        color = CMETOCFragment.this.m15320b0().getColor(C5562R.color.darkGrey);
                    }
                    textView.setTextColor(color);
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    downloadCellViewHolder.f87616J.setText("Error occured, try again");
                    e2.printStackTrace();
                }
                downloadCellViewHolder.f87618L.setText("Download");
                downloadCellViewHolder.f87618L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.6
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CMETOCFragment.this.m71994l3(bundle);
                        CMETOCFragment.this.m71991i3();
                    }
                });
                downloadCellViewHolder.f87618L.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.7
                    @Override // android.view.View.OnLongClickListener
                    public boolean onLongClick(View view) {
                        String strM71904y = CMETOCFragment.this.f88791k4.m71904y();
                        ArrayList arrayList = new ArrayList();
                        String str = strM71904y + "/" + bundle.getString("name");
                        arrayList.add(str);
                        for (int i3 = 1; i3 < 11; i3++) {
                            arrayList.add(str + "." + i3);
                            arrayList.add(str + "." + i3 + ".download");
                        }
                        Iterator it2 = arrayList.iterator();
                        while (it2.hasNext()) {
                            String str2 = (String) it2.next();
                            if (new File(str2).exists()) {
                                new File(str2).delete();
                            }
                        }
                        CompressHelper.m71767x2(CMETOCFragment.this.m71997p3(), "All Temp Files Deleted", 1);
                        return true;
                    }
                });
                return;
            }
            DecimalFormat decimalFormat = new DecimalFormat("#,##0.#");
            downloadCellViewHolder.f87618L.setVisibility(8);
            downloadCellViewHolder.f87619M.setVisibility(0);
            double dM72007e0 = m72007e0(bundleM71996o3, "bytesDownloaded");
            double dM72007e02 = m72007e0(bundleM71996o3, "bytesTotal");
            long jM72008g0 = m72008g0(bundleM71996o3, "avgSpeed");
            long jM72008g02 = m72008g0(bundleM71996o3, "remaining");
            int iM72011f0 = m72011f0(bundleM71996o3, "Progress");
            String str = decimalFormat.format((dM72007e0 / 1024.0d) / 1024.0d) + " of " + decimalFormat.format((dM72007e02 / 1024.0d) / 1024.0d) + " MB(" + CMETOCFragment.this.m71999s3(jM72008g0) + "/s), " + m72009h0((int) jM72008g02) + " remaining";
            downloadCellViewHolder.f87616J.setTextColor(CMETOCFragment.this.m15320b0().getColor(C5562R.color.darkGrey));
            if (dM72007e0 == 0.0d) {
                downloadCellViewHolder.f87619M.m29176u();
                str = "Preparing Download";
            } else {
                downloadCellViewHolder.f87619M.m29177v();
            }
            downloadCellViewHolder.f87616J.setText(str);
            if (iM72011f0 == 0) {
                downloadCellViewHolder.f87619M.setValue(1.0f);
            } else {
                downloadCellViewHolder.f87619M.setValue(iM72011f0);
            }
            downloadCellViewHolder.f87619M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.8
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    CMETOCFragment.this.m71992j3(bundle);
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 1) {
                View viewInflate = LayoutInflater.from(CMETOCFragment.this.m71997p3()).inflate(C5562R.layout.list_view_item_video, viewGroup, false);
                viewInflate.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CMETOCFragment.this.m72468V2();
                    }
                });
                return CMETOCFragment.this.new VideoCellViewHolder(viewInflate);
            }
            if (i2 == 2) {
                View viewInflate2 = LayoutInflater.from(CMETOCFragment.this.m71997p3()).inflate(C5562R.layout.list_view_item_cme_download, viewGroup, false);
                viewInflate2.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CMETOCFragment.this.m72468V2();
                    }
                });
                return CMETOCFragment.this.new DownloadCellViewHolder(viewInflate2);
            }
            if (i2 != 3) {
                return null;
            }
            View viewInflate3 = LayoutInflater.from(CMETOCFragment.this.m71997p3()).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false);
            viewInflate3.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.DownloadsAdapter.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    CMETOCFragment.this.m72468V2();
                }
            });
            return new RippleTextViewHolder(viewInflate3);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return m72013j0();
        }

        /* renamed from: f0 */
        public int m72011f0(Bundle bundle, String str) {
            if (bundle.containsKey(str)) {
                return bundle.getInt(str);
            }
            return 0;
        }

        /* renamed from: i0 */
        public Bundle m72012i0(int i2) {
            return this.f87622e.get(i2 - this.f87621d.size());
        }

        /* renamed from: j0 */
        public int m72013j0() {
            ArrayList<Bundle> arrayList = this.f87621d;
            int size = arrayList != null ? arrayList.size() : 0;
            ArrayList<Bundle> arrayList2 = this.f87622e;
            return arrayList2 != null ? size + arrayList2.size() : size;
        }
    }

    public abstract class UIActionClass extends ItemTouchHelper.Callback {

        /* renamed from: i */
        Context f87641i;

        /* renamed from: j */
        private final Paint f87642j;

        /* renamed from: k */
        private final ColorDrawable f87643k = new ColorDrawable();

        /* renamed from: l */
        private final int f87644l = Color.parseColor("#b80f0a");

        /* renamed from: m */
        private final Drawable f87645m;

        /* renamed from: n */
        private final int f87646n;

        /* renamed from: o */
        private final int f87647o;

        UIActionClass(Context context) {
            this.f87641i = context;
            Paint paint = new Paint();
            this.f87642j = paint;
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
            Drawable drawableM6692l = ContextCompat.m6692l(this.f87641i, R.drawable.ic_menu_delete);
            this.f87645m = drawableM6692l;
            this.f87646n = drawableM6692l.getIntrinsicWidth();
            this.f87647o = drawableM6692l.getIntrinsicHeight();
        }

        /* renamed from: E */
        private void m72014E(Canvas canvas, Float f2, Float f3, Float f4, Float f5) {
            canvas.drawRect(f2.floatValue(), f3.floatValue(), f4.floatValue(), f5.floatValue(), this.f87642j);
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: A */
        public boolean mo27105A(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder viewHolder2) {
            return false;
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: l */
        public int mo27117l(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
            return viewHolder.m27811F() == 1 ? ItemTouchHelper.Callback.m27104v(0, 4) : ItemTouchHelper.Callback.m27104v(0, 0);
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: n */
        public float mo27119n(@NonNull RecyclerView.ViewHolder viewHolder) {
            return 0.7f;
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: w */
        public void mo27126w(@NonNull Canvas canvas, @NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, float f2, float f3, int i2, boolean z) {
            super.mo27126w(canvas, recyclerView, viewHolder, f2, f3, i2, z);
            View view = viewHolder.f33076a;
            int height = view.getHeight();
            if (viewHolder.m27811F() == 1 && (f2 != 0.0f || z)) {
                this.f87643k.setColor(this.f87644l);
                this.f87643k.setBounds(view.getRight() + ((int) f2), view.getTop(), view.getRight(), view.getBottom());
                this.f87643k.draw(canvas);
                int top = view.getTop();
                int i3 = this.f87647o;
                int i4 = top + ((height - i3) / 2);
                int i5 = (height - i3) / 2;
                this.f87645m.setBounds((view.getRight() - i5) - this.f87646n, i4, view.getRight() - i5, this.f87647o + i4);
                this.f87645m.draw(canvas);
            } else {
                m72014E(canvas, Float.valueOf(view.getRight() + f2), Float.valueOf(view.getTop()), Float.valueOf(view.getRight()), Float.valueOf(view.getBottom()));
            }
            super.mo27126w(canvas, recyclerView, viewHolder, f2, f3, i2, z);
        }
    }

    public class VideoCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f87649I;

        /* renamed from: J */
        private final ProgressBar f87650J;

        /* renamed from: K */
        private final MaterialRippleLayout f87651K;

        /* renamed from: L */
        private final TextView f87652L;

        public VideoCellViewHolder(View view) {
            super(view);
            this.f87649I = (TextView) view.findViewById(C5562R.id.title);
            this.f87650J = (ProgressBar) view.findViewById(C5562R.id.progress_bar);
            this.f87651K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f87652L = (TextView) view.findViewById(C5562R.id.subtitle);
        }
    }

    /* renamed from: n3 */
    private void m71988n3() {
        new ItemTouchHelper(new UIActionClass(m15366r()) { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.6
            @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
            /* renamed from: D */
            public void mo27108D(@NonNull RecyclerView.ViewHolder viewHolder, int i2) {
                try {
                    final int iM27807B = viewHolder.m27807B();
                    new AlertDialog.Builder(CMETOCFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you really want to delete this video ?").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.6.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i3) {
                            Bundle bundleM72012i0 = ((DownloadsAdapter) CMETOCFragment.this.f88803w4.getAdapter()).m72012i0(iM27807B);
                            String strM71754h1 = CompressHelper.m71754h1(CMETOCFragment.this.f88788h4, bundleM72012i0.getString("name"), "temp");
                            if (bundleM72012i0.containsKey("dbname")) {
                                strM71754h1 = CompressHelper.m71754h1(CMETOCFragment.this.f88788h4, bundleM72012i0.getString("dbname") + "-" + bundleM72012i0.getString("name"), "temp");
                            }
                            if (!CMETOCFragment.this.f88788h4.getString("Type").equals("cme")) {
                                strM71754h1 = CompressHelper.m71754h1(CMETOCFragment.this.f88788h4, bundleM72012i0.getString("name"), "videos");
                            }
                            new File(strM71754h1).delete();
                            CMETOCFragment.this.f88803w4.getAdapter().m27492H(iM27807B);
                        }
                    }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.6.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i3) {
                            CMETOCFragment.this.f88803w4.getAdapter().m27492H(iM27807B);
                        }
                    }).m1090I();
                } catch (Exception unused) {
                }
            }
        }).m27092m(this.f88803w4);
    }

    /* renamed from: r3 */
    private void m71989r3() {
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: M0 */
    public void mo15284M0(Activity activity) {
        super.mo15284M0(activity);
        this.f87593F4 = activity;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: Q2 */
    public void mo71990Q2() {
        if (this.f88788h4.containsKey("Demo")) {
            m72459K2();
            return;
        }
        SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88799s4.setIconifiedByDefault(false);
        this.f88799s4.setQueryHint("Search");
        this.f88789i4 = true;
        ((ImageView) this.f88799s4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.7
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                CMETOCFragment.this.f88799s4.m2508k0("", false);
                CMETOCFragment.this.f88799s4.clearFocus();
                CMETOCFragment cMETOCFragment = CMETOCFragment.this;
                cMETOCFragment.f88803w4.setAdapter(cMETOCFragment.f88792l4);
                CMETOCFragment.this.m72468V2();
                CMETOCFragment.this.mo72211Z2();
            }
        });
        this.f88799s4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.8
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                CMETOCFragment cMETOCFragment = CMETOCFragment.this;
                if (!cMETOCFragment.f88789i4) {
                    return true;
                }
                cMETOCFragment.f88786f4 = str;
                if (str.length() > 1) {
                    new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.8.1
                        @Override // android.os.AsyncTask
                        protected Object doInBackground(Object[] objArr) {
                            CMETOCFragment cMETOCFragment2 = CMETOCFragment.this;
                            cMETOCFragment2.f87589B4 = cMETOCFragment2.f88791k4.m71817V(cMETOCFragment2.f88788h4, "select * from toc where name like '%" + CMETOCFragment.this.f88799s4.getQuery().toString() + "%' AND NOT (id=999) COLLATE utf8_general_ci");
                            CMETOCFragment cMETOCFragment3 = CMETOCFragment.this;
                            cMETOCFragment3.f88795o4 = cMETOCFragment3.mo71950a3(str);
                            return null;
                        }

                        @Override // android.os.AsyncTask
                        protected void onPostExecute(Object obj) {
                            CMETOCFragment.this.mo71973X2();
                        }

                        @Override // android.os.AsyncTask
                        protected void onPreExecute() {
                        }
                    }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                    return true;
                }
                CMETOCFragment cMETOCFragment2 = CMETOCFragment.this;
                cMETOCFragment2.f88803w4.setAdapter(cMETOCFragment2.f88792l4);
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str) {
                return false;
            }
        });
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        CompressHelper compressHelper;
        Bundle bundle2;
        StringBuilder sb;
        String str;
        ArrayList<Bundle> arrayListM71817V;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        this.f87592E4 = Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf");
        this.f87595H4 = ((iMD) m15366r().getApplicationContext()).f101675c3;
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            this.f87590C4 = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.3
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            this.f87590C4 = m15387y().getString("ParentId");
        }
        if (this.f87590C4 == null) {
            this.f87590C4 = "0";
        }
        if (this.f87590C4.equals("11111")) {
            String[] list = new File(CompressHelper.m71753g1(this.f88788h4, "temp")).list(new FilenameFilter() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.4
                @Override // java.io.FilenameFilter
                public boolean accept(File file, String str2) {
                    return str2.toLowerCase().endsWith("mp4");
                }
            });
            ArrayList arrayList = new ArrayList();
            for (String str2 : list) {
                arrayList.add("'" + str2 + "'");
            }
            arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "select *,medias.id as id ,logs.duration as dur from medias left outer join logs on medias.id=logs.id where dbname || '-' || name in (" + StringUtils.join(arrayList, ",") + ") ");
        } else {
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select * from toc where parentId = " + this.f87590C4);
            if (this.f88788h4.getString("Type").equals("cme")) {
                compressHelper = this.f88791k4;
                bundle2 = this.f88788h4;
                sb = new StringBuilder();
                str = "Select medias.*, logs.position, logs.vDate ,logs.duration as dur from medias left outer join logs  on medias.id=logs.id where tocId = ";
            } else {
                compressHelper = this.f88791k4;
                bundle2 = this.f88788h4;
                sb = new StringBuilder();
                str = "Select videos.id, videos.title, videos.path, videos.name, videos.tocId, videos.purpose, videos.fileSize, logs.position, logs.vDate ,logs.duration as dur from videos left outer join logs  on videos.id=logs.id where tocId =";
            }
            sb.append(str);
            sb.append(this.f87590C4);
            arrayListM71817V = compressHelper.m71817V(bundle2, sb.toString());
        }
        this.f87591D4 = arrayListM71817V;
        if (this.f88794n4 == null) {
            this.f88794n4 = new ArrayList<>();
        }
        if (this.f87591D4 == null) {
            this.f87591D4 = new ArrayList<>();
        }
        if (this.f88788h4.getString("Name").equals("imdvideos.db") && this.f87590C4.equals("0")) {
            String[] list2 = new File(CompressHelper.m71753g1(this.f88788h4, "temp")).list(new FilenameFilter() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.5
                @Override // java.io.FilenameFilter
                public boolean accept(File file, String str3) {
                    return str3.toLowerCase().endsWith("mp4");
                }
            });
            Bundle bundle3 = new Bundle();
            bundle3.putString("id", "11111");
            bundle3.putString("name", "Downloaded");
            bundle3.putString("videoCount", list2.length + "");
            this.f88794n4.add(0, bundle3);
        }
        Collections.sort(this.f87591D4, new TitleComparator("title"));
        Collections.sort(this.f88794n4, new TitleComparator("name"));
        this.f88792l4 = new DownloadsAdapter(this.f88794n4, this.f87591D4);
        m71995m3();
        this.f87588A4 = new DownloadsAdapter(this.f87589B4, this.f88795o4);
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        this.f88803w4.setItemAnimator(null);
        m15358o2(false);
        m71988n3();
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        if (this.f88795o4 == null) {
            this.f88795o4 = new ArrayList<>();
        }
        if (this.f87589B4 == null) {
            this.f87589B4 = new ArrayList<>();
        }
        DownloadsAdapter downloadsAdapter = new DownloadsAdapter(this.f87589B4, this.f88795o4);
        this.f87588A4 = downloadsAdapter;
        this.f88803w4.setAdapter(downloadsAdapter);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, StringUtils.SPACE);
        ArrayList arrayList = new ArrayList();
        for (String str2 : strArrSplitByWholeSeparator) {
            arrayList.add("title like '%" + str2 + "%'");
        }
        ArrayList<Bundle> arrayListM71817V = this.f88788h4.getString("Type").equals("cme") ? this.f88791k4.m71817V(this.f88788h4, "SELECT medias.*, logs.position, logs.vDate, logs.duration AS dur, REPLACE(REPLACE(COALESCE(toc5.name || ' / ', '') || COALESCE(toc4.name || ' / ', '') || COALESCE(toc3.name || ' / ', '') || COALESCE(toc2.name || ' / ', '') || toc1.name, 'Videos / ', ''), 'Audios / ', '') AS subText FROM medias LEFT OUTER JOIN logs ON medias.id = logs.id LEFT OUTER JOIN TOC toc1 ON medias.tocId = toc1.id LEFT OUTER JOIN TOC toc2 ON toc1.parentId = toc2.id LEFT OUTER JOIN TOC toc3 ON toc2.parentId = toc3.id LEFT OUTER JOIN TOC toc4 ON toc3.parentId = toc4.id LEFT OUTER JOIN TOC toc5 ON toc4.parentId = toc5.id WHERE " + StringUtils.join(arrayList, " AND ")) : this.f88791k4.m71817V(this.f88788h4, "Select videos.id, videos.title, videos.path, videos.name, videos.tocId, videos.purpose, videos.fileSize, logs.position, logs.vDate ,logs.duration as dur from videos left outer join logs  on videos.id=logs.id where title like '%" + str + "%'");
        if (arrayListM71817V == null) {
            arrayListM71817V = new ArrayList<>();
        }
        Collections.sort(arrayListM71817V, new TitleComparator("title"));
        return arrayListM71817V;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return null;
    }

    /* renamed from: i3 */
    public void m71991i3() {
    }

    /* renamed from: j3 */
    public void m71992j3(Bundle bundle) {
        String str = this.f88788h4.getString("Name") + " - " + bundle.getString("name");
        Log.e("StopDownload", str);
        if (this.f87595H4.m73238x3(str) != null) {
            this.f87595H4.m73233a4(str);
        }
    }

    /* renamed from: k3 */
    public void m71993k3(String str) {
        int i2 = 0;
        if (this.f88803w4.getAdapter() == this.f88792l4) {
            ArrayList<Bundle> arrayList = this.f88794n4;
            int size = arrayList != null ? arrayList.size() : 0;
            while (i2 < this.f87591D4.size()) {
                if (str.equals(this.f88788h4.getString("Name") + " - " + this.f87591D4.get(i2).getString("name"))) {
                    this.f88803w4.getAdapter().m27492H(size + i2);
                }
                i2++;
            }
            return;
        }
        ArrayList<Bundle> arrayList2 = this.f87589B4;
        int size2 = arrayList2 != null ? arrayList2.size() : 0;
        while (i2 < this.f88795o4.size()) {
            if (str.equals(this.f88788h4.getString("Name") + " - " + this.f88795o4.get(i2).getString("name"))) {
                this.f88803w4.getAdapter().m27492H(size2 + i2);
            }
            i2++;
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: l1 */
    public void mo15352l1() {
        CompressHelper compressHelper;
        Bundle bundle;
        StringBuilder sb;
        String str;
        ArrayList<Bundle> arrayListM71817V;
        super.mo15352l1();
        this.f87595H4 = ((iMD) m15366r().getApplicationContext()).f101675c3;
        if (this.f87590C4.equals("11111")) {
            String[] list = new File(CompressHelper.m71753g1(this.f88788h4, "temp")).list(new FilenameFilter() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.1
                @Override // java.io.FilenameFilter
                public boolean accept(File file, String str2) {
                    return str2.toLowerCase().endsWith("mp4");
                }
            });
            ArrayList arrayList = new ArrayList();
            for (String str2 : list) {
                arrayList.add("'" + str2 + "'");
            }
            arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "select *,medias.id as id ,logs.duration as dur from medias left outer join logs on medias.id=logs.id where dbname || '-' || name in (" + StringUtils.join(arrayList, ",") + ") ");
        } else {
            if (this.f88788h4.getString("Type").equals("cme")) {
                compressHelper = this.f88791k4;
                bundle = this.f88788h4;
                sb = new StringBuilder();
                str = "Select medias.*, logs.position, logs.vDate ,logs.duration as dur from medias left outer join logs  on medias.id=logs.id where tocId = ";
            } else {
                compressHelper = this.f88791k4;
                bundle = this.f88788h4;
                sb = new StringBuilder();
                str = "Select videos.id, videos.title, videos.path, videos.name, videos.tocId, videos.purpose, videos.fileSize, logs.position, logs.vDate ,logs.duration as dur from videos left outer join logs  on videos.id=logs.id where tocId =";
            }
            sb.append(str);
            sb.append(this.f87590C4);
            arrayListM71817V = compressHelper.m71817V(bundle, sb.toString());
        }
        this.f87591D4 = arrayListM71817V;
        if (this.f87591D4 == null) {
            this.f87591D4 = new ArrayList<>();
        }
        Collections.sort(this.f87591D4, new TitleComparator("title"));
        RecyclerView.Adapter adapter = this.f88803w4.getAdapter();
        RecyclerView.Adapter adapter2 = this.f88792l4;
        if (adapter == adapter2) {
            ((DownloadsAdapter) adapter2).f87622e = this.f87591D4;
        } else {
            ArrayList<Bundle> arrayListMo71950a3 = mo71950a3(this.f88799s4.getQuery().toString());
            this.f88795o4 = arrayListMo71950a3;
            if (arrayListMo71950a3 == null) {
                this.f88795o4 = new ArrayList<>();
            }
            Collections.sort(this.f88795o4, new TitleComparator("name"));
            ArrayList<Bundle> arrayListM71817V2 = this.f88791k4.m71817V(this.f88788h4, "select * from toc where name like '%" + this.f88799s4.getQuery().toString() + "%' AND NOT (id=999) COLLATE utf8_general_ci");
            this.f87589B4 = arrayListM71817V2;
            if (arrayListM71817V2 == null) {
                this.f87589B4 = new ArrayList<>();
            }
            DownloadsAdapter downloadsAdapter = this.f87588A4;
            downloadsAdapter.f87621d = this.f87589B4;
            downloadsAdapter.f87622e = this.f88795o4;
        }
        this.f88803w4.getAdapter().m27491G();
    }

    /* renamed from: l3 */
    public void m71994l3(Bundle bundle) {
        try {
            String strReplace = this.f88788h4.getString("Name").replace(".db", "");
            if (bundle.containsKey("dbname")) {
                strReplace = bundle.getString("dbname").replace(".db", "");
            }
            String strM71813T1 = this.f88791k4.m71813T1("http://" + (m15307V1().getSharedPreferences("default_preferences", 0).getString("DownloadServer", "dl").equals("idl") ? "ivideos" : "videos") + ".imedicaldoctor.net/cmeinfo/" + strReplace + "/" + bundle.getString("name"));
            String strM71754h1 = CompressHelper.m71754h1(this.f88788h4, bundle.getString("name"), "temp");
            if (bundle.containsKey("dbname")) {
                strM71754h1 = CompressHelper.m71754h1(this.f88788h4, bundle.getString("dbname") + "-" + bundle.getString("name"), "temp");
            }
            if (!this.f88788h4.getString("Type").equals("cme")) {
                strM71813T1 = this.f88791k4.m71813T1(this.f88791k4.m71790J() + "/dbs/usmle/" + this.f88788h4.getString("Name").replace(".db", "") + "/videos/" + bundle.getString("name") + ".mp4");
                strM71754h1 = CompressHelper.m71754h1(this.f88788h4, bundle.getString("name"), "videos");
            }
            String str = strM71813T1;
            String str2 = strM71754h1;
            String str3 = StringUtils.splitByWholeSeparator(str2, "/")[r2.length - 1];
            final String str4 = this.f88788h4.getString("Name") + " - " + bundle.getString("name");
            Log.e("AddDownloadForVideo", "Downloading " + str4 + " , " + str + " To " + str2);
            Bundle bundleM73238x3 = this.f87595H4.m73238x3(str4);
            if (bundleM73238x3 != null && bundleM73238x3.containsKey("downloader")) {
                this.f87595H4.m73232Z3(str4);
                return;
            }
            this.f87595H4.m73235u3(bundle.getString("title"), str, str2, bundle.getString("fileSize"), str3, str4, "");
            this.f87595H4.m73222T3(str4, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.9
                @Override // java.lang.Runnable
                public void run() {
                    CMETOCFragment.this.m71993k3(str4);
                }
            });
            this.f87595H4.m73232Z3(str4);
        } catch (Exception unused) {
        }
    }

    /* renamed from: m3 */
    public void m71995m3() {
        try {
            Iterator<Bundle> it2 = this.f87591D4.iterator();
            while (it2.hasNext()) {
                final String str = this.f88788h4.getString("Name") + " - " + it2.next().getString("name");
                Log.e("Find Resource VideoID", str);
                Bundle bundleM73238x3 = this.f87595H4.m73238x3(str);
                if (bundleM73238x3 != null && bundleM73238x3.containsKey("downloader")) {
                    this.f87595H4.m73222T3(str, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment.2
                        @Override // java.lang.Runnable
                        public void run() {
                            CMETOCFragment.this.m71993k3(str);
                        }
                    });
                }
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: o3 */
    public Bundle m71996o3(Bundle bundle) {
        String str = this.f88788h4.getString("Name") + " - " + bundle.getString("name");
        downloadFragment downloadfragment = this.f87595H4;
        if (downloadfragment == null) {
            return null;
        }
        return downloadfragment.m73238x3(str);
    }

    /* renamed from: p3 */
    public Activity m71997p3() {
        return this.f87593F4;
    }

    /* renamed from: q3 */
    public Spanned m71998q3(Bundle bundle) {
        String string;
        if (!bundle.containsKey("dbtitle") || (this.f88799s4.getQuery().toString().length() <= 0 && this.f87590C4 != "11111")) {
            string = bundle.getString("title");
        } else {
            string = bundle.getString("title") + "<br/><font color=\"Gray\"><small>" + bundle.getString("dbtitle") + "</small></font>";
        }
        return Html.fromHtml(string);
    }

    /* renamed from: s3 */
    public String m71999s3(long j2) {
        if (j2 <= 0) {
            return "0";
        }
        double d2 = j2;
        int iLog10 = (int) (Math.log10(d2) / Math.log10(1024.0d));
        return new DecimalFormat("#,##0.#").format(d2 / Math.pow(1024.0d, iLog10)) + StringUtils.SPACE + new String[]{"B", "KB", "MB", "GB", "TB"}[iLog10];
    }

    /* renamed from: t3 */
    public boolean m72000t3(Bundle bundle) {
        String strM71754h1 = CompressHelper.m71754h1(this.f88788h4, bundle.getString("name"), "temp");
        if (bundle.containsKey("dbname")) {
            strM71754h1 = CompressHelper.m71754h1(this.f88788h4, bundle.getString("dbname") + "-" + bundle.getString("name"), "temp");
        }
        if (!this.f88788h4.getString("Type").equals("cme")) {
            strM71754h1 = CompressHelper.m71754h1(this.f88788h4, bundle.getString("name"), "videos");
        }
        return new File(strM71754h1).exists();
    }
}

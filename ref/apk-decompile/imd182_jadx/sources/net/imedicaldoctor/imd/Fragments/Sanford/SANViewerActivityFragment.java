package net.imedicaldoctor.imd.Fragments.Sanford;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class SANViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88735X4;

    /* renamed from: Y4 */
    public ArrayList<String> f88736Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88737Z4;

    /* renamed from: a5 */
    public boolean f88738a5;

    /* renamed from: J4 */
    private void m72438J4(String str) {
        ArrayList<String> arrayList = this.f88736Y4;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f88736Y4.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", next);
            bundle.putString("Description", "");
            bundle.putString("id", next);
            if (new File(next).length() > 5000) {
                arrayList2.add(bundle);
            }
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public String m72439I4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return m72840w3(this.f88736Y4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        this.f88738a5 = true;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Sanford.SANViewerActivityFragment.1
            /* JADX WARN: Removed duplicated region for block: B:26:0x00be  */
            @Override // java.lang.Runnable
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public void run() {
                /*
                    Method dump skipped, instructions count: 265
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Sanford.SANViewerActivityFragment.RunnableC49821.run():void");
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Sanford.SANViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = SANViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    SANViewerActivityFragment sANViewerActivityFragment = SANViewerActivityFragment.this;
                    sANViewerActivityFragment.m72780C4(sANViewerActivityFragment.f89595p4);
                    return;
                }
                String string = SANViewerActivityFragment.this.f89566D4.getString("Path");
                SANViewerActivityFragment sANViewerActivityFragment2 = SANViewerActivityFragment.this;
                sANViewerActivityFragment2.m72795O3(sANViewerActivityFragment2.f89563A4, string);
                SANViewerActivityFragment.this.m72836s4();
                SANViewerActivityFragment.this.m72831p4();
                SANViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                SANViewerActivityFragment.this.m15358o2(false);
                SANViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String string = this.f89566D4.getString("Path");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = string.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72439I4(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = string + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f88736Y4 = arrayList;
            mo71972o4();
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("fixAllImages2();");
        this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() != C5562R.id.action_gallery) {
            return super.mo15329e1(menuItem);
        }
        m72438J4("asdfafdsaf");
        return true;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_menu);
    }

    /* JADX WARN: Removed duplicated region for block: B:29:0x0129 A[PHI: r12
      0x0129: PHI (r12v5 java.lang.String) = (r12v4 java.lang.String), (r12v8 java.lang.String) binds: [B:23:0x00f7, B:28:0x0127] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean mo71960y4(android.webkit.WebView r10, java.lang.String r11, java.lang.String r12, java.lang.String r13) {
        /*
            Method dump skipped, instructions count: 313
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Sanford.SANViewerActivityFragment.mo71960y4(android.webkit.WebView, java.lang.String, java.lang.String, java.lang.String):boolean");
    }
}

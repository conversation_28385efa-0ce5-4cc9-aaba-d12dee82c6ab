package net.imedicaldoctor.imd.Fragments;

import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Typeface;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.Views.ButtonSmall;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class SearchDialogHelperFragment extends DialogFragment {

    /* renamed from: F4 */
    public int f88741F4;

    /* renamed from: G4 */
    public String f88742G4;

    /* renamed from: H4 */
    public ListView f88743H4;

    /* renamed from: I4 */
    public Bundle f88744I4;

    /* renamed from: J4 */
    public boolean f88745J4;

    /* renamed from: K4 */
    public CompressHelper f88746K4;

    /* renamed from: L4 */
    public RecyclerView.Adapter f88747L4;

    /* renamed from: M4 */
    public ContentSearchAdapter f88748M4;

    /* renamed from: N4 */
    public ArrayList<Bundle> f88749N4;

    /* renamed from: O4 */
    public ArrayList<Bundle> f88750O4;

    /* renamed from: P4 */
    public ArrayList<Bundle> f88751P4;

    /* renamed from: Q4 */
    public ImageButton f88752Q4;

    /* renamed from: R4 */
    public View f88753R4;

    /* renamed from: S4 */
    public Toolbar f88754S4;

    /* renamed from: T4 */
    public SearchView f88755T4;

    /* renamed from: U4 */
    public ImageView f88756U4;

    /* renamed from: V4 */
    public TextView f88757V4;

    /* renamed from: W4 */
    public ButtonSmall f88758W4;

    /* renamed from: X4 */
    public RecyclerView f88759X4;

    public class ContentSearchViewHolder {

        /* renamed from: a */
        public final TextView f88782a;

        /* renamed from: b */
        public final TextView f88783b;

        public ContentSearchViewHolder(View view) {
            this.f88782a = (TextView) view.findViewById(C5562R.id.title_text);
            this.f88783b = (TextView) view.findViewById(C5562R.id.subtitle_text);
        }
    }

    @Override // androidx.fragment.app.Fragment
    @Nullable
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        this.f88746K4 = new CompressHelper(m15366r());
        if (bundle != null && bundle.containsKey("Position")) {
            this.f88741F4 = bundle.getInt("Position");
        }
        if (bundle != null && bundle.containsKey("Query")) {
            this.f88742G4 = bundle.getString("Query");
        }
        this.f88743H4 = (ListView) this.f88753R4.findViewById(C5562R.id.list_view);
        this.f88744I4 = (m15387y() == null || !m15387y().containsKey("DB")) ? null : m15387y().getBundle("DB");
        try {
            m72452t3("");
        } catch (Exception unused) {
        }
        LinearLayout linearLayout = (LinearLayout) this.f88753R4.findViewById(C5562R.id.status_layout);
        if (linearLayout != null) {
            linearLayout.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.13
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    SearchDialogHelperFragment.this.m72446n3();
                }
            });
        }
        return super.mo15303U0(layoutInflater, viewGroup, bundle);
    }

    /* renamed from: g3 */
    public void mo72091g3() {
        this.f88759X4.setItemAnimator(new DefaultItemAnimator());
        this.f88759X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88759X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: h3 */
    public void m72440h3() {
        SearchView searchView = (SearchView) this.f88753R4.findViewById(C5562R.id.search_view);
        this.f88755T4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88755T4.setIconifiedByDefault(false);
        this.f88755T4.setQueryHint("Search All");
        m72449q3();
        this.f88755T4.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.9
            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: a */
            public boolean mo2516a(int i2) {
                Cursor cursorMo10512c = SearchDialogHelperFragment.this.f88755T4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (SearchDialogHelperFragment.this.f88755T4.getTag(1) != null && ((String) SearchDialogHelperFragment.this.f88755T4.getTag(1)).length() > 0) {
                    string = SearchDialogHelperFragment.this.f88755T4.getTag() + StringUtils.SPACE + string;
                }
                SearchDialogHelperFragment.this.f88755T4.m2508k0(string, true);
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: b */
            public boolean mo2517b(int i2) {
                Cursor cursorMo10512c = SearchDialogHelperFragment.this.f88755T4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (SearchDialogHelperFragment.this.f88755T4.getTag() != null && ((String) SearchDialogHelperFragment.this.f88755T4.getTag()).length() > 0) {
                    string = SearchDialogHelperFragment.this.f88755T4.getTag() + StringUtils.SPACE + string;
                }
                SearchDialogHelperFragment.this.f88755T4.m2508k0(string, true);
                return false;
            }
        });
        ((ImageView) this.f88755T4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.10
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchDialogHelperFragment.this.f88755T4.m2508k0("", false);
                SearchDialogHelperFragment.this.f88755T4.clearFocus();
                SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                searchDialogHelperFragment.f88759X4.setAdapter(searchDialogHelperFragment.f88747L4);
                SearchDialogHelperFragment.this.m72446n3();
                SearchDialogHelperFragment.this.m72450r3();
            }
        });
        this.f88755T4.setSuggestionsAdapter(new CursorAdapter(m15366r(), null, 0) { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.11
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("word")));
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell, viewGroup, false);
                viewInflate.setTag(viewInflate.findViewById(C5562R.id.text));
                return viewInflate;
            }
        });
        this.f88755T4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.12
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                if (!searchDialogHelperFragment.f88745J4) {
                    return true;
                }
                searchDialogHelperFragment.f88742G4 = str;
                if (str.length() <= 1) {
                    return false;
                }
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.12.2
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        String[] strArrSplit = str.trim().split(StringUtils.SPACE);
                        String str2 = strArrSplit[strArrSplit.length - 1];
                        String str3 = "";
                        for (int i2 = 0; i2 < strArrSplit.length - 1; i2++) {
                            str3 = str3 + StringUtils.SPACE + strArrSplit[i2];
                        }
                        str3.trim();
                        return SearchDialogHelperFragment.this.m72455y3(str2);
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchDialogHelperFragment.this.f88755T4.getSuggestionsAdapter().mo10519m(SearchDialogHelperFragment.this.f88746K4.m71850h((ArrayList) obj));
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(final String str) {
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.12.1
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                        searchDialogHelperFragment.f88750O4 = searchDialogHelperFragment.m72451s3(str);
                        return null;
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchDialogHelperFragment.this.m72448p3();
                    }

                    @Override // android.os.AsyncTask
                    protected void onPreExecute() {
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return false;
            }
        });
    }

    /* renamed from: i3 */
    public void m72441i3() {
        SearchView searchView = (SearchView) this.f88753R4.findViewById(C5562R.id.search_view);
        this.f88755T4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88755T4.setIconifiedByDefault(false);
        this.f88755T4.setQueryHint("Search");
        m72449q3();
        ((ImageView) this.f88755T4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.7
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchDialogHelperFragment.this.f88755T4.m2508k0("", false);
                SearchDialogHelperFragment.this.f88755T4.clearFocus();
                SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                searchDialogHelperFragment.f88759X4.setAdapter(searchDialogHelperFragment.f88747L4);
                SearchDialogHelperFragment.this.m72446n3();
                SearchDialogHelperFragment.this.m72450r3();
            }
        });
        this.f88755T4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.8
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                if (!searchDialogHelperFragment.f88745J4) {
                    return true;
                }
                searchDialogHelperFragment.f88742G4 = str;
                if (str.length() <= 1) {
                    return false;
                }
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.8.1
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        SearchDialogHelperFragment searchDialogHelperFragment2 = SearchDialogHelperFragment.this;
                        searchDialogHelperFragment2.f88750O4 = searchDialogHelperFragment2.m72451s3(str);
                        return null;
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchDialogHelperFragment.this.m72448p3();
                    }

                    @Override // android.os.AsyncTask
                    protected void onPreExecute() {
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str) {
                return false;
            }
        });
    }

    /* renamed from: j3 */
    public void m72442j3() {
        SearchView searchView = (SearchView) this.f88753R4.findViewById(C5562R.id.search_view);
        this.f88755T4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88755T4.setIconifiedByDefault(false);
        this.f88755T4.setQueryHint("Search");
        m72449q3();
        ((ImageView) this.f88755T4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchDialogHelperFragment.this.f88755T4.m2508k0("", false);
                SearchDialogHelperFragment.this.f88755T4.clearFocus();
                SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                searchDialogHelperFragment.f88759X4.setAdapter(searchDialogHelperFragment.f88747L4);
                SearchDialogHelperFragment.this.m72446n3();
                SearchDialogHelperFragment.this.m72450r3();
            }
        });
        this.f88755T4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.6
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                if (!searchDialogHelperFragment.f88745J4) {
                    return true;
                }
                searchDialogHelperFragment.f88742G4 = str;
                if (str.length() <= 1) {
                    return false;
                }
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.6.1
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        SearchDialogHelperFragment searchDialogHelperFragment2 = SearchDialogHelperFragment.this;
                        searchDialogHelperFragment2.f88750O4 = searchDialogHelperFragment2.m72451s3(str);
                        SearchDialogHelperFragment searchDialogHelperFragment3 = SearchDialogHelperFragment.this;
                        searchDialogHelperFragment3.f88751P4 = searchDialogHelperFragment3.m72455y3(str);
                        return null;
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchDialogHelperFragment.this.m72448p3();
                    }

                    @Override // android.os.AsyncTask
                    protected void onPreExecute() {
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str) {
                return false;
            }
        });
    }

    /* renamed from: k3 */
    public void m72443k3() {
        Toolbar toolbar = (Toolbar) this.f88753R4.findViewById(C5562R.id.toolbar);
        this.f88754S4 = toolbar;
        if (toolbar == null) {
            return;
        }
        this.f88758W4 = (ButtonSmall) this.f88753R4.findViewById(C5562R.id.back_button);
        this.f88754S4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchDialogHelperFragment.this.f88746K4.m71821W1(true);
            }
        });
        ButtonSmall buttonSmall = this.f88758W4;
        if (buttonSmall != null) {
            buttonSmall.setDrawableIcon(m15366r().getResources().getDrawable(C5562R.drawable.back_icon));
            this.f88758W4.setRippleColor(m15366r().getResources().getColor(C5562R.color.toolbar_item_ripple_color));
            this.f88758W4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.2
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    SearchDialogHelperFragment.this.f88746K4.m71821W1(true);
                }
            });
            this.f88758W4.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.3
                @Override // android.view.View.OnLongClickListener
                public boolean onLongClick(View view) {
                    SearchDialogHelperFragment.this.f88746K4.m71830Z1(true);
                    return true;
                }
            });
        }
        ImageButton imageButton = (ImageButton) this.f88753R4.findViewById(C5562R.id.action_home);
        this.f88752Q4 = imageButton;
        if (imageButton != null) {
            imageButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    SearchDialogHelperFragment.this.f88746K4.m71830Z1(true);
                }
            });
        }
        this.f88756U4 = (ImageView) this.f88753R4.findViewById(C5562R.id.toolbar_image_view);
        TextView textView = (TextView) this.f88753R4.findViewById(C5562R.id.toolbar_text_view);
        this.f88757V4 = textView;
        if (textView != null) {
            this.f88757V4.setTypeface(Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf"));
            this.f88757V4.setText(m72456z3());
        }
        if (this.f88756U4 != null) {
            m72453u3();
        }
        m72454v3();
    }

    /* renamed from: l3 */
    public String[] m72444l3(String str) {
        ArrayList arrayList = new ArrayList();
        for (String str2 : str.split("</b>")) {
            String[] strArrSplit = str2.split("<b>");
            if (strArrSplit.length == 2 && !arrayList.contains(strArrSplit[1].toLowerCase()) && !strArrSplit[1].equals("...")) {
                arrayList.add(strArrSplit[1].toLowerCase());
            }
        }
        if (str.contains("</b> <b>")) {
            for (String str3 : str.replace("</b> <b>", StringUtils.SPACE).split("</b>")) {
                String[] strArrSplit2 = str3.split("<b>");
                if (strArrSplit2.length == 2 && !arrayList.contains(strArrSplit2[1].toLowerCase()) && !strArrSplit2[1].equals("...") && !arrayList.contains(strArrSplit2[1].toLowerCase())) {
                    arrayList.add(0, strArrSplit2[1]);
                }
            }
        }
        return (String[]) arrayList.toArray(new String[arrayList.size()]);
    }

    @Override // androidx.fragment.app.DialogFragment, androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    /* renamed from: m3 */
    public int m72445m3() {
        int identifier = m15320b0().getIdentifier("status_bar_height", "dimen", "android");
        if (identifier > 0) {
            return m15320b0().getDimensionPixelSize(identifier);
        }
        return 0;
    }

    /* renamed from: n3 */
    public void m72446n3() {
        try {
            InputMethodManager inputMethodManager = (InputMethodManager) m15366r().getSystemService("input_method");
            if (m15366r().getCurrentFocus() != null) {
                inputMethodManager.hideSoftInputFromWindow(m15366r().getCurrentFocus().getWindowToken(), 0);
            }
            SearchView searchView = this.f88755T4;
            if (searchView != null) {
                searchView.clearFocus();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* renamed from: o3 */
    public void m72447o3(Bundle bundle) {
        this.f88746K4 = new CompressHelper(m15366r());
        if (bundle != null && bundle.containsKey("Position")) {
            this.f88741F4 = bundle.getInt("Position");
        }
        if (bundle != null && bundle.containsKey("Query")) {
            this.f88742G4 = bundle.getString("Query");
        }
        this.f88744I4 = (m15387y() == null || !m15387y().containsKey("DB")) ? null : m15387y().getBundle("DB");
    }

    /* renamed from: p3 */
    public void m72448p3() {
        this.f88748M4.m73469f0(this.f88750O4);
        this.f88759X4.setAdapter(this.f88748M4);
    }

    /* renamed from: q3 */
    public void m72449q3() {
        this.f88755T4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment.14
            @Override // java.lang.Runnable
            public void run() {
                SearchDialogHelperFragment searchDialogHelperFragment = SearchDialogHelperFragment.this;
                searchDialogHelperFragment.f88745J4 = true;
                searchDialogHelperFragment.f88755T4.m2508k0(searchDialogHelperFragment.f88742G4, false);
                SearchDialogHelperFragment.this.m72446n3();
            }
        }, 10L);
        this.f88745J4 = false;
    }

    /* renamed from: r3 */
    public void m72450r3() {
    }

    /* renamed from: s3 */
    public ArrayList<Bundle> m72451s3(String str) {
        return null;
    }

    /* renamed from: t3 */
    public void m72452t3(String str) {
        try {
            ((AppCompatActivity) m15366r()).m1122F0().mo910A0(str);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* renamed from: u3 */
    public void m72453u3() {
        Glide.m30040F(this).mo30129t(CompressHelper.m71724C(this.f88744I4)).mo30182a(new RequestOptions().m31361u()).m30165B2(this.f88756U4);
    }

    /* renamed from: v3 */
    public void m72454v3() throws Resources.NotFoundException {
        if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
            float dimension = m15320b0().getDimension(C5562R.dimen.toolbar_padding);
            Toolbar toolbar = this.f88754S4;
            if (toolbar != null) {
                toolbar.setPadding(0, (int) dimension, 0, 0);
            }
        }
    }

    /* renamed from: w3 */
    public void mo72092w3() {
        ListView listView = (ListView) this.f88753R4.findViewById(C5562R.id.list_view);
        TextView textView = (TextView) this.f88753R4.findViewById(C5562R.id.status_label);
        LinearLayout linearLayout = (LinearLayout) this.f88753R4.findViewById(C5562R.id.status_layout);
        listView.setVisibility(0);
        textView.setVisibility(8);
        linearLayout.setVisibility(8);
    }

    /* renamed from: x3 */
    public void mo72093x3(String str) {
        ListView listView = (ListView) this.f88753R4.findViewById(C5562R.id.list_view);
        TextView textView = (TextView) this.f88753R4.findViewById(C5562R.id.status_label);
        LinearLayout linearLayout = (LinearLayout) this.f88753R4.findViewById(C5562R.id.status_layout);
        listView.setVisibility(8);
        textView.setVisibility(0);
        linearLayout.setVisibility(0);
        textView.setText(str);
    }

    /* renamed from: y3 */
    public ArrayList<Bundle> m72455y3(String str) {
        return null;
    }

    /* renamed from: z3 */
    public String m72456z3() {
        return this.f88744I4.getString("Title");
    }
}

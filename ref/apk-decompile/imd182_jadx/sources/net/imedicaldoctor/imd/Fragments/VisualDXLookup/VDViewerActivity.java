package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.content.res.Resources;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class VDViewerActivity extends ViewerHelperActivity {

    public static class VDViewerFragment extends ViewerHelperFragment implements VDDialogListInterface {

        /* renamed from: X4 */
        public ArrayList<Bundle> f89902X4;

        /* renamed from: I4 */
        public void m72931I4() {
            VDDialogList vDDialogList = new VDDialogList();
            Bundle bundle = new Bundle();
            bundle.putBundle("db", this.f89566D4);
            bundle.putParcelableArrayList("items", this.f89902X4);
            bundle.putString("titleProperty", "fieldName");
            bundle.putString("type", "Field");
            vDDialogList.m15342i2(bundle);
            vDDialogList.mo15218Z2(true);
            vDDialogList.m15245A2(this, 0);
            vDDialogList.mo15222e3(m15283M(), "VDDialogFragment");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            String str = this.f89567E4.split("-")[1];
            ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "select imageId ,copyRight from Images where diagnosesModulesid=" + str);
            if (arrayListM71817V == null || arrayListM71817V.size() <= 0) {
                return null;
            }
            Bundle bundleM72839v3 = m72839v3(arrayListM71817V);
            return CompressHelper.m71754h1(this.f89566D4, bundleM72839v3.getString("imageId") + ".jpg", "Large-Encrypted");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_vdviewer, menu);
            m72833q4(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f89902X4 = bundle.getParcelableArrayList("mFields");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            iMDLogger.m73554j("VDViewer", "Loading VD Document with mDocAddress = " + this.f89567E4);
            try {
                CompressHelper compressHelper = new CompressHelper(m15366r());
                String str = this.f89563A4;
                if (str == null || str.length() == 0) {
                    String str2 = this.f89567E4.split("-")[1];
                    Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from DiagnosesModules where id =" + str2));
                    this.f89568F4 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from Diagnoses where id =" + bundleM71890s1.getString("diagnosisId"))).getString("dName");
                    ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(this.f89566D4, "select fieldId, fieldName,doc  from Documents, fields where DiagnosesModulesId=" + str2 + " and documents.fieldId = fields.id");
                    this.f89902X4 = arrayListM71817V;
                    String str3 = "";
                    Iterator<Bundle> it2 = arrayListM71817V.iterator();
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        str3 = str3 + "<span class=\"h1\" id=\"field" + next.getString("fieldId") + "\">" + next.getString("fieldName") + "</span>" + compressHelper.m71773B(next.getString("doc"), str2, "127");
                    }
                    String strM72817d4 = m72817d4(m15366r(), "VDHeader.css");
                    String strM72817d42 = m72817d4(m15366r(), "VDFooter.css");
                    String str4 = strM72817d4.replace("[size]", "200").replace("[title]", this.f89568F4) + str3 + strM72817d42;
                    m72826m3();
                    this.f89563A4 = str4;
                    if (!compressHelper.m71903x1()) {
                        m72827m4("Diagnosis");
                    }
                    m72795O3(this.f89563A4, CompressHelper.m71753g1(this.f89566D4, "base"));
                    m72836s4();
                }
                m72831p4();
                mo72642f3(C5562R.menu.menu_vdviewer);
                m15358o2(false);
                m72786G3();
                return viewInflate;
            } catch (Exception e2) {
                m72779B4(e2);
                return viewInflate;
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            if (menuItem.getItemId() == C5562R.id.action_menu) {
                m72931I4();
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDialogListInterface
        /* renamed from: h */
        public void mo72891h(Bundle bundle, String str) {
            new CompressHelper(m15366r());
            if (str.equals("Field")) {
                mo71967C3("field" + bundle.getString("fieldId"));
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: o4 */
        public void mo71972o4() {
            new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDViewerActivity.VDViewerFragment.1

                /* renamed from: a */
                byte[] f89903a;

                @Override // android.os.AsyncTask
                protected Object doInBackground(Object[] objArr) {
                    try {
                        File file = new File(VDViewerFragment.this.mo71955R2());
                        this.f89903a = new CompressHelper(VDViewerFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127");
                        return null;
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                        return null;
                    }
                }

                @Override // android.os.AsyncTask
                protected void onPostExecute(Object obj) {
                    super.onPostExecute(obj);
                    Glide.m30041G(VDViewerFragment.this.m15366r()).mo30123h(this.f89903a).m30165B2(VDViewerFragment.this.f89575M4);
                }
            }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new VDViewerFragment(), bundle);
    }
}

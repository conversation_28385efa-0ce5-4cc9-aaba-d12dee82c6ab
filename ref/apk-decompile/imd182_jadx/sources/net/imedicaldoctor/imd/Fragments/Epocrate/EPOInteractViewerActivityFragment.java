package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;

/* loaded from: classes3.dex */
public class EPOInteractViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public ArrayList<Bundle> f88118X4;

    /* renamed from: Y4 */
    public String f88119Y4;

    /* renamed from: Z4 */
    public int f88120Z4;

    /* renamed from: I4 */
    public String m72207I4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88120Z4 + 1;
        this.f88120Z4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded2\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded2(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: J4 */
    public String m72208J4(String str, String str2) {
        if (str != null && str.length() != 0) {
            CompressHelper compressHelper = this.f89579Q4;
            Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71819W(this.f89566D4, "select * from " + str2 + "_string where id=" + str, this.f88119Y4));
            if (bundleM71890s1 != null && bundleM71890s1.size() != 0) {
                return bundleM71890s1.getString("STRING");
            }
        }
        return "";
    }

    /* renamed from: K4 */
    public String m72209K4(String str) {
        return m72208J4(str, "general");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        this.f88119Y4 = "RX.sqlite";
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                Bundle bundleM71890s1;
                String string;
                String str;
                try {
                    String str2 = EPOInteractViewerActivityFragment.this.f89563A4;
                    if (str2 != null && str2.length() != 0) {
                        return;
                    }
                    EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment = EPOInteractViewerActivityFragment.this;
                    ePOInteractViewerActivityFragment.f88120Z4 = 0;
                    String[] strArrSplit = ePOInteractViewerActivityFragment.f89567E4.split("-");
                    if (strArrSplit.length == 3) {
                        String str3 = strArrSplit[1];
                        String str4 = strArrSplit[2];
                        EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment2 = EPOInteractViewerActivityFragment.this;
                        CompressHelper compressHelper = ePOInteractViewerActivityFragment2.f89579Q4;
                        Bundle bundleM71890s12 = compressHelper.m71890s1(compressHelper.m71819W(ePOInteractViewerActivityFragment2.f89566D4, "SELECT                     ID,                     DRUG_ID AS DRUG_0_ID,                     INTERACTING_DRUG_ID AS DRUG_1_ID,                     DDI_ID,                     GROUP_0_ID,                     GROUP_1_ID                     FROM (                     SELECT DISTINCT                     tDID.ID,                     MIN(d1.ID, d2.ID) AS DRUG_ID,                     MAX(d1.ID, d2.ID) AS INTERACTING_DRUG_ID,                     tDID.DDI_ID,                     DDI.GROUP_0_ID,                     DDI.GROUP_1_ID                     FROM                     DRUG_TO_INTERACTING_DRUG tDID                     JOIN DDI ON tDID.DDI_ID = DDI.ID                     JOIN DRUG d1 ON d1.ID = tDID.DRUG_0_ID OR d1.GENERIC_ID = tDID.DRUG_0_ID OR d1.ID = tDID.DRUG_1_ID OR d1.GENERIC_ID = tDID.DRUG_1_ID                     JOIN DRUG d2 ON                     CASE WHEN d1.ID = tDID.DRUG_0_ID OR d1.GENERIC_ID = tDID.DRUG_0_ID                     THEN d2.ID = tDID.DRUG_1_ID OR d2.GENERIC_ID = tDID.DRUG_1_ID                     ELSE d2.ID = tDID.DRUG_0_ID OR d2.GENERIC_ID = tDID.DRUG_0_ID                     END                     WHERE                     tDID.DRUG_0_ID IN (" + str3 + ", " + str4 + ")                     AND                     tDID.DRUG_1_ID IN (" + str3 + ", " + str4 + ")                     AND                     DRUG_0_ID <> DRUG_1_ID                     AND                     d1.ID IN (" + str3 + ", " + str4 + ")                     AND                     d2.ID IN (" + str3 + ", " + str4 + ")                     ORDER BY CATEGORY_ID, d1.name, d2.name                     )", EPOInteractViewerActivityFragment.this.f88119Y4));
                        String string2 = bundleM71890s12.getString("DRUG_0_ID");
                        String string3 = bundleM71890s12.getString("DRUG_1_ID");
                        String string4 = bundleM71890s12.getString("DDI_ID");
                        EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment3 = EPOInteractViewerActivityFragment.this;
                        CompressHelper compressHelper2 = ePOInteractViewerActivityFragment3.f89579Q4;
                        Bundle bundle2 = ePOInteractViewerActivityFragment3.f89566D4;
                        StringBuilder sb = new StringBuilder();
                        sb.append("Select * from Drug where ID=");
                        sb.append(string2);
                        string = compressHelper2.m71890s1(compressHelper2.m71819W(bundle2, sb.toString(), EPOInteractViewerActivityFragment.this.f88119Y4)).getString("NAME");
                        EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment4 = EPOInteractViewerActivityFragment.this;
                        CompressHelper compressHelper3 = ePOInteractViewerActivityFragment4.f89579Q4;
                        Bundle bundle3 = ePOInteractViewerActivityFragment4.f89566D4;
                        StringBuilder sb2 = new StringBuilder();
                        sb2.append("Select * from Drug where ID=");
                        sb2.append(string3);
                        String string5 = compressHelper3.m71890s1(compressHelper3.m71819W(bundle3, sb2.toString(), EPOInteractViewerActivityFragment.this.f88119Y4)).getString("NAME");
                        EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment5 = EPOInteractViewerActivityFragment.this;
                        CompressHelper compressHelper4 = ePOInteractViewerActivityFragment5.f89579Q4;
                        Bundle bundle4 = ePOInteractViewerActivityFragment5.f89566D4;
                        StringBuilder sb3 = new StringBuilder();
                        str = string5;
                        sb3.append("SELECT  DDI.ID ,  DDI.GROUP_0_ID ,  DDI.GROUP_1_ID ,  DDI.CATEGORY_ID ,  DDI.ACTION_STRING_ID ,  DDI.EFFECT_STRING_ID ,  DDI.MECHANISM_STRING_ID   FROM DDI   WHERE  ID =  ");
                        sb3.append(string4);
                        bundleM71890s1 = compressHelper4.m71890s1(compressHelper4.m71819W(bundle4, sb3.toString(), EPOInteractViewerActivityFragment.this.f88119Y4));
                        EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment6 = EPOInteractViewerActivityFragment.this;
                        ePOInteractViewerActivityFragment6.f88118X4 = ePOInteractViewerActivityFragment6.f89579Q4.m71822X(ePOInteractViewerActivityFragment6.f89566D4, "Select * from pill_pictures where drug_id=" + string2 + " OR drug_id=" + string3, EPOInteractViewerActivityFragment.this.f88119Y4, true);
                    } else {
                        String str5 = strArrSplit[1];
                        EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment7 = EPOInteractViewerActivityFragment.this;
                        CompressHelper compressHelper5 = ePOInteractViewerActivityFragment7.f89579Q4;
                        bundleM71890s1 = compressHelper5.m71890s1(compressHelper5.m71819W(ePOInteractViewerActivityFragment7.f89566D4, "SELECT  DDI.ID ,  DDI.GROUP_0_ID ,  DDI.GROUP_1_ID ,  DDI.CATEGORY_ID ,  DDI.ACTION_STRING_ID ,  DDI.EFFECT_STRING_ID ,  DDI.MECHANISM_STRING_ID   FROM DDI   WHERE  ID = " + str5, EPOInteractViewerActivityFragment.this.f88119Y4));
                        EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment8 = EPOInteractViewerActivityFragment.this;
                        ePOInteractViewerActivityFragment8.f88118X4 = ePOInteractViewerActivityFragment8.f89579Q4.m71822X(ePOInteractViewerActivityFragment8.f89566D4, "Select * from pill_pictures where drug_id=" + bundleM71890s1.getString("GROUP_0_ID") + " OR drug_id=" + bundleM71890s1.getString("GROUP_1_ID"), EPOInteractViewerActivityFragment.this.f88119Y4, true);
                        string = null;
                        str = null;
                    }
                    String string6 = bundleM71890s1.getString("GROUP_0_ID");
                    String string7 = bundleM71890s1.getString("GROUP_1_ID");
                    EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment9 = EPOInteractViewerActivityFragment.this;
                    CompressHelper compressHelper6 = ePOInteractViewerActivityFragment9.f89579Q4;
                    String string8 = compressHelper6.m71890s1(compressHelper6.m71819W(ePOInteractViewerActivityFragment9.f89566D4, "SELECT  DDI_GROUP.ID ,  DDI_GROUP.NAME   FROM DDI_GROUP   WHERE  ID =  " + string6, EPOInteractViewerActivityFragment.this.f88119Y4)).getString("NAME");
                    EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment10 = EPOInteractViewerActivityFragment.this;
                    CompressHelper compressHelper7 = ePOInteractViewerActivityFragment10.f89579Q4;
                    String string9 = compressHelper7.m71890s1(compressHelper7.m71819W(ePOInteractViewerActivityFragment10.f89566D4, "SELECT  DDI_GROUP.ID ,  DDI_GROUP.NAME   FROM DDI_GROUP   WHERE  ID =  " + string7, EPOInteractViewerActivityFragment.this.f88119Y4)).getString("NAME");
                    if (string == null) {
                        string = string8;
                    }
                    if (str != null) {
                        string9 = str;
                    }
                    EPOInteractViewerActivityFragment.this.f89568F4 = string9 + " - " + string;
                    EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment11 = EPOInteractViewerActivityFragment.this;
                    CompressHelper compressHelper8 = ePOInteractViewerActivityFragment11.f89579Q4;
                    String string10 = compressHelper8.m71890s1(compressHelper8.m71819W(ePOInteractViewerActivityFragment11.f89566D4, "Select * from DDI_Category where id = " + bundleM71890s1.getString("CATEGORY_ID"), EPOInteractViewerActivityFragment.this.f88119Y4)).getString("NAME");
                    String strM72209K4 = EPOInteractViewerActivityFragment.this.m72209K4(bundleM71890s1.getString("ACTION_STRING_ID"));
                    String strM72209K42 = EPOInteractViewerActivityFragment.this.m72209K4(bundleM71890s1.getString("EFFECT_STRING_ID"));
                    String strM72209K43 = EPOInteractViewerActivityFragment.this.m72209K4(bundleM71890s1.getString("MECHANISM_STRING_ID"));
                    EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment12 = EPOInteractViewerActivityFragment.this;
                    String strM72817d4 = ePOInteractViewerActivityFragment12.m72817d4(ePOInteractViewerActivityFragment12.m15366r(), "EPOHeader.css");
                    EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment13 = EPOInteractViewerActivityFragment.this;
                    String strM72817d42 = ePOInteractViewerActivityFragment13.m72817d4(ePOInteractViewerActivityFragment13.m15366r(), "EPOFooter.css");
                    String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", EPOInteractViewerActivityFragment.this.f89568F4).replace("[include]", "");
                    String strReplace2 = ((((("<div class=\"cellTitle\" style=\"margin-left:15px;margin-top:15px\">" + string9 + " + " + string + "</div>") + "<div style=\"color:red;font-weight:bold;margin-left:15px\">" + string10 + "</div>") + EPOInteractViewerActivityFragment.this.m72207I4("Action", "", "", strM72209K4, "", "margin-left:15px", "")) + EPOInteractViewerActivityFragment.this.m72207I4("Effect", "", "", strM72209K42, "", "margin-left:15px", "")) + EPOInteractViewerActivityFragment.this.m72207I4("Mechanism", "", "", strM72209K43, "", "margin-left:15px", "")).replace("..", ".");
                    EPOInteractViewerActivityFragment.this.f89563A4 = strReplace + strReplace2 + strM72817d42;
                } catch (Exception e2) {
                    e2.printStackTrace();
                    EPOInteractViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = EPOInteractViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment = EPOInteractViewerActivityFragment.this;
                    ePOInteractViewerActivityFragment.m72780C4(ePOInteractViewerActivityFragment.f89595p4);
                    return;
                }
                String strM71752f1 = CompressHelper.m71752f1(EPOInteractViewerActivityFragment.this.f89566D4);
                EPOInteractViewerActivityFragment ePOInteractViewerActivityFragment2 = EPOInteractViewerActivityFragment.this;
                ePOInteractViewerActivityFragment2.m72795O3(ePOInteractViewerActivityFragment2.f89563A4, strM71752f1);
                EPOInteractViewerActivityFragment.this.m72836s4();
                EPOInteractViewerActivityFragment.this.m72831p4();
                EPOInteractViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                EPOInteractViewerActivityFragment.this.m15358o2(false);
                EPOInteractViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
        menu.removeItem(C5562R.id.action_menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3;
        ArrayList<Bundle> arrayList = this.f88118X4;
        if (arrayList == null || arrayList.size() == 0 || (bundleM72839v3 = m72839v3(this.f88118X4)) == null) {
            return;
        }
        Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        this.f89579Q4.m71800N1(this.f89566D4, str);
        return true;
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Lexi.LXSectionsViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class EPOIDViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public ArrayList<Bundle> f88063X4;

    /* renamed from: Y4 */
    public int f88064Y4;

    /* renamed from: Z4 */
    public String f88065Z4;

    /* renamed from: a5 */
    public JSONObject f88066a5;

    /* renamed from: I4 */
    public void m72191I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f88063X4.add(bundle);
    }

    /* renamed from: J4 */
    public String m72192J4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88064Y4 + 1;
        this.f88064Y4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws JSONException, Resources.NotFoundException {
        String str;
        String str2;
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        int i2 = 0;
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        try {
            String str3 = this.f89563A4;
            if (str3 == null || str3.length() == 0) {
                this.f88064Y4 = 0;
                this.f88063X4 = new ArrayList<>();
                iMDLogger.m73550f("Loading Document", this.f89567E4);
                String[] strArrSplit = this.f89567E4.split("-");
                int i3 = 1;
                String str4 = strArrSplit[1];
                if (strArrSplit.length == 3) {
                    this.f88065Z4 = strArrSplit[2];
                }
                ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from id_monographs where id=" + str4);
                if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                    Bundle bundle2 = arrayListM71817V.get(0);
                    this.f89568F4 = bundle2.getString("title");
                    JSONObject jSONObject = new JSONObject(this.f89579Q4.m71773B(bundle2.getString("monograph"), str4, "127")).getJSONObject(Annotation.f68283i3);
                    this.f88066a5 = jSONObject;
                    String[] strArr = {"empiric", "specific", "other"};
                    String str5 = "";
                    if (this.f88065Z4 == null) {
                        str2 = "";
                        int i4 = 0;
                        for (int i5 = 3; i4 < i5; i5 = 3) {
                            String str6 = strArr[i4];
                            String string = this.f89579Q4.m71886r1(this.f88066a5.getJSONArray("views"), "id", str6).getString(Annotation.f68283i3);
                            String str7 = str6.substring(i2, i3).toUpperCase() + str6.substring(i3).toLowerCase();
                            str2 = str2 + m72192J4(str7, "", "LTR", string.replace("<html>", str5).replace("</html>", str5), "", "", "");
                            m72191I4(str7, this.f88064Y4);
                            i4++;
                            str5 = str5;
                            i2 = 0;
                            i3 = 1;
                        }
                        str = str5;
                    } else {
                        str = "";
                        str2 = "<div style=\"margin:0px\">" + this.f89579Q4.m71886r1(jSONObject.getJSONArray("views"), "id", this.f88065Z4).getString(Annotation.f68283i3) + "</div>";
                    }
                    String strM72817d4 = m72817d4(m15366r(), "EPOHeader.css");
                    String strM72817d42 = m72817d4(m15366r(), "EPOFooter.css");
                    this.f89563A4 = strM72817d4.replace("[size]", "200").replace("[title]", this.f89568F4).replace("[include]", str) + str2.replace("..", ".") + strM72817d42;
                }
                CompressHelper.m71767x2(m15366r(), "Document doesn't exist", 1);
                return this.f89565C4;
            }
            m72795O3(this.f89563A4, CompressHelper.m71752f1(this.f89566D4));
            m72836s4();
            m72831p4();
            mo72642f3(C5562R.menu.elsviewer2);
            m15358o2(false);
            m72786G3();
        } catch (Exception e2) {
            m72779B4(e2);
        }
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_menu) {
            LXSectionsViewer lXSectionsViewer = new LXSectionsViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("fields", this.f88063X4);
            lXSectionsViewer.m15342i2(bundle);
            lXSectionsViewer.mo15218Z2(true);
            lXSectionsViewer.m15245A2(this, 0);
            lXSectionsViewer.mo15222e3(m15283M(), "LXSectionsViewer");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        if (!this.f89579Q4.m71800N1(this.f89566D4, str) && str3.contains("//current/")) {
            String str4 = StringUtils.splitByWholeSeparator(str3, "//current/")[1];
            this.f89579Q4.m71772A1(this.f89566D4, this.f89567E4 + "-" + str4, null, null);
        }
        return true;
    }
}

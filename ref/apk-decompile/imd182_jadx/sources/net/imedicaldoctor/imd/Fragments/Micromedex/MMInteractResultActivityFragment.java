package net.imedicaldoctor.imd.Fragments.Micromedex;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MMInteractResultActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f88555X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88556Y4;

    /* renamed from: Z4 */
    public Bundle f88557Z4;

    /* renamed from: a5 */
    public ArrayList<String> f88558a5;

    /* renamed from: b5 */
    public NotStickySectionAdapter f88559b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f88560c5;

    /* renamed from: d5 */
    public ArrayList<Bundle> f88561d5;

    /* renamed from: I4 */
    public void m72386I4() {
        this.f88555X4.setItemAnimator(new DefaultItemAnimator());
        this.f88555X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88555X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88555X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(this.f89567E4.split("-")[1], ";;;;;");
        this.f89579Q4.m71866m(this.f89566D4, "delete from selected_drugs");
        for (String str : strArrSplitByWholeSeparator) {
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str, ",,,,,");
            this.f89579Q4.m71866m(this.f89566D4, "Insert into selected_drugs values (" + strArrSplitByWholeSeparator2[0] + ", '" + strArrSplitByWholeSeparator2[1] + "', 0)");
        }
        this.f88560c5 = this.f89579Q4.m71817V(this.f89566D4, "SELECT title, doc_id, severity, (SELECT descrip FROM dict_severity WHERE id = severity) severity_descr FROM v_interactions");
        this.f89568F4 = "Found " + this.f88560c5.size() + " Interactions";
        this.f88561d5 = this.f89579Q4.m71887r2(this.f88560c5, "severity_descr");
        NotStickySectionAdapter notStickySectionAdapter = new NotStickySectionAdapter(m15366r(), this.f88561d5, "title", C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractResultActivityFragment.1
            @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
            /* renamed from: f0 */
            public void mo72200f0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                String[] strArrSplitByWholeSeparator3 = StringUtils.splitByWholeSeparator(bundle2.getString("title"), " : ");
                int i3 = 0;
                String str2 = strArrSplitByWholeSeparator3[0];
                String str3 = strArrSplitByWholeSeparator3[1];
                rippleTextFullViewHolder.f101499I.setText(str2);
                rippleTextFullViewHolder.f101500J.setText(str3);
                String string = bundle2.getString("severity");
                if (string.equals(IcyHeaders.f28171a3)) {
                    i3 = C5562R.drawable.xinteraction;
                } else if (string.equals(ExifInterface.f16317Y4)) {
                    i3 = C5562R.drawable.dinteraction;
                } else if (string.equals(ExifInterface.f16326Z4)) {
                    i3 = C5562R.drawable.cinteraction;
                } else if (string.equals("4")) {
                    i3 = C5562R.drawable.binteraction;
                }
                rippleTextFullViewHolder.f101501K.setImageDrawable(MMInteractResultActivityFragment.this.m15320b0().getDrawable(i3));
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractResultActivityFragment.1.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        MMInteractResultActivityFragment mMInteractResultActivityFragment = MMInteractResultActivityFragment.this;
                        mMInteractResultActivityFragment.f89579Q4.m71772A1(mMInteractResultActivityFragment.f89566D4, "doc-" + bundle2.getString("doc_id") + ",,,,," + bundle2.getString("title"), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
            /* renamed from: i0 */
            public String mo72201i0(String str2) {
                return str2;
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
            /* renamed from: k0 */
            public RecyclerView.ViewHolder mo72202k0(View view2) {
                return new RippleTextFullViewHolder(view2);
            }
        };
        this.f88559b5 = notStickySectionAdapter;
        this.f88555X4.setAdapter(notStickySectionAdapter);
        m72386I4();
        mo72642f3(C5562R.menu.favorite);
        m15358o2(false);
        m72786G3();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3;
        ArrayList<Bundle> arrayList = this.f88556Y4;
        if (arrayList == null || arrayList.size() == 0 || (bundleM72839v3 = m72839v3(this.f88556Y4)) == null) {
            return;
        }
        Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
    }
}

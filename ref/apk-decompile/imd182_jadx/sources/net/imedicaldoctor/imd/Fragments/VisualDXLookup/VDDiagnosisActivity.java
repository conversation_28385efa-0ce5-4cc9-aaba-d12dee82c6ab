package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TabHost;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.palette.graphics.Palette;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.pipeline.end.PdfWriterPipeline;
import java.io.File;
import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.GridAutoFitLayoutManager;
import net.imedicaldoctor.imd.ViewHolders.ImageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class VDDiagnosisActivity extends ViewerHelperActivity {

    public static class VDDiagnosisFragment extends ViewerHelperFragment implements VDDialogListInterface {

        /* renamed from: X4 */
        private Bundle f89802X4;

        /* renamed from: Y4 */
        private ArrayList<Bundle> f89803Y4;

        /* renamed from: Z4 */
        private ArrayList<Bundle> f89804Z4;

        /* renamed from: a5 */
        private ArrayList<Bundle> f89805a5;

        /* renamed from: b5 */
        private int f89806b5;

        /* renamed from: c5 */
        private Bundle f89807c5;

        /* renamed from: d5 */
        public RecyclerView f89808d5;

        public class DatabaseHeaderViewHolder {

            /* renamed from: a */
            public final TextView f89816a;

            public DatabaseHeaderViewHolder(View view) {
                this.f89816a = (TextView) view.findViewById(C5562R.id.header_text);
            }
        }

        public static class EmergencyTextViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f89818I;

            /* renamed from: J */
            public TextView f89819J;

            public EmergencyTextViewHolder(View view) {
                super(view);
                this.f89818I = (TextView) view.findViewById(C5562R.id.text);
                this.f89819J = (TextView) view.findViewById(C5562R.id.emergency_text);
            }
        }

        public static class GridViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public GridView f89820I;

            public GridViewHolder(View view) {
                super(view);
                this.f89820I = (GridView) view.findViewById(C5562R.id.grid_view);
            }
        }

        public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f89821I;

            public HeaderCellViewHolder(View view) {
                super(view);
                this.f89821I = (TextView) view.findViewById(C5562R.id.header_text);
            }
        }

        public static class RecyclerViewViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public RecyclerView f89822I;

            public RecyclerViewViewHolder(View view) {
                super(view);
                this.f89822I = (RecyclerView) view.findViewById(C5562R.id.recycler_view);
            }
        }

        public static class TextViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f89823I;

            public TextViewHolder(View view) {
                super(view);
                this.f89823I = (TextView) view.findViewById(C5562R.id.text);
            }
        }

        public class VDDiagnosisAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f89824d;

            public VDDiagnosisAdapter(Context context) {
                this.f89824d = context;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: C */
            public int mo26845C(int i2) {
                String string = m72909d0(i2).getString("Type");
                if (string.equals("Header")) {
                    return 0;
                }
                if (string.equals("SimpleTextEmergency")) {
                    return 1;
                }
                if (string.equals("TextList")) {
                    return 2;
                }
                if (string.equals("GridView")) {
                    return 3;
                }
                return string.equals("Item") ? 4 : -1;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                Bundle bundleM72909d0 = m72909d0(i2);
                int iM27811F = viewHolder.m27811F();
                if (iM27811F == 0) {
                    ((HeaderCellViewHolder) viewHolder).f89821I.setText(bundleM72909d0.getString("Text"));
                    return;
                }
                if (iM27811F == 1) {
                    EmergencyTextViewHolder emergencyTextViewHolder = (EmergencyTextViewHolder) viewHolder;
                    emergencyTextViewHolder.f89818I.setText(bundleM72909d0.getString("Text"));
                    emergencyTextViewHolder.f89819J.setVisibility(bundleM72909d0.getInt("Emergency") == 0 ? 8 : 0);
                    return;
                }
                if (iM27811F == 2) {
                    TextViewHolder textViewHolder = (TextViewHolder) viewHolder;
                    textViewHolder.f89823I.setText(bundleM72909d0.getString("Text"));
                    textViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.VDDiagnosisAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            VDDialogList vDDialogList = new VDDialogList();
                            Bundle bundle = new Bundle();
                            bundle.putBundle("db", VDDiagnosisFragment.this.f89566D4);
                            bundle.putParcelableArrayList("items", VDDiagnosisFragment.this.f89803Y4);
                            bundle.putString("titleProperty", "longName");
                            bundle.putString("type", "Module");
                            vDDialogList.m15342i2(bundle);
                            vDDialogList.mo15218Z2(true);
                            vDDialogList.m15245A2(VDDiagnosisFragment.this, 0);
                            vDDialogList.mo15222e3(VDDiagnosisFragment.this.m15283M(), "VDDialogFragment");
                        }
                    });
                } else {
                    if (iM27811F != 3) {
                        if (iM27811F == 4) {
                            RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                            rippleTextViewHolder.f101515I.setText(bundleM72909d0.getBundle("Item").getString("fieldName"));
                            rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.VDDiagnosisAdapter.4
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    Bundle bundle = VDDiagnosisAdapter.this.m72909d0(i2).getBundle("Item");
                                    VDDiagnosisFragment vDDiagnosisFragment = VDDiagnosisFragment.this;
                                    vDDiagnosisFragment.f89579Q4.m71772A1(vDDiagnosisFragment.f89566D4, "Doc-" + ((Bundle) VDDiagnosisFragment.this.f89803Y4.get(VDDiagnosisFragment.this.f89806b5)).getString("id"), null, "field" + bundle.getString("fieldId"));
                                }
                            });
                            return;
                        }
                        return;
                    }
                    RecyclerViewViewHolder recyclerViewViewHolder = (RecyclerViewViewHolder) viewHolder;
                    final ChaptersAdapter chaptersAdapter = new ChaptersAdapter(VDDiagnosisFragment.this.m15366r(), VDDiagnosisFragment.this.f89805a5, "title", C5562R.layout.list_view_item_image) { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.VDDiagnosisAdapter.2
                        @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                        /* renamed from: e0 */
                        public void mo71985e0(RecyclerView.ViewHolder viewHolder2, final Bundle bundle, int i3) {
                            final ImageViewHolder imageViewHolder = (ImageViewHolder) viewHolder2;
                            final String strM71754h1 = CompressHelper.m71754h1(VDDiagnosisFragment.this.f89566D4, bundle.getString("imageId") + ".jpg", "Medium");
                            Glide.m30041G(VDDiagnosisFragment.this.m15366r()).mo30124i(new File(strM71754h1)).m30165B2(imageViewHolder.f101461I);
                            if (VDDiagnosisFragment.this.f89807c5.containsKey(strM71754h1)) {
                                imageViewHolder.f101462J.setRippleColor(VDDiagnosisFragment.this.f89807c5.getInt(strM71754h1));
                            } else {
                                VDDiagnosisFragment.this.m72832q3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.VDDiagnosisAdapter.2.1
                                    @Override // java.lang.Runnable
                                    public void run() {
                                        Palette.Swatch swatchM26489C = Palette.m26478b(BitmapFactory.decodeFile(strM71754h1)).m26518g().m26489C();
                                        if (swatchM26489C == null) {
                                            return;
                                        }
                                        int iM26530e = swatchM26489C.m26530e();
                                        if (VDDiagnosisFragment.this.f89807c5.containsKey(strM71754h1)) {
                                            return;
                                        }
                                        VDDiagnosisFragment.this.f89807c5.putInt(strM71754h1, iM26530e);
                                    }
                                }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.VDDiagnosisAdapter.2.2
                                    @Override // java.lang.Runnable
                                    public void run() {
                                        imageViewHolder.f101462J.setRippleColor(VDDiagnosisFragment.this.f89807c5.getInt(strM71754h1));
                                    }
                                });
                            }
                            imageViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.VDDiagnosisAdapter.2.3
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    VDDiagnosisFragment.this.m72903U4(bundle.getString("imageId"));
                                }
                            });
                        }

                        @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                        /* renamed from: h0 */
                        public RecyclerView.ViewHolder mo71986h0(View view) {
                            return new ImageViewHolder(view);
                        }
                    };
                    recyclerViewViewHolder.f89822I.setAdapter(chaptersAdapter);
                    final GridAutoFitLayoutManager gridAutoFitLayoutManager = new GridAutoFitLayoutManager(VDDiagnosisFragment.this.m15366r(), (int) (VDDiagnosisFragment.this.m15320b0().getDisplayMetrics().density * 100.0f));
                    gridAutoFitLayoutManager.m27031R3(new GridLayoutManager.SpanSizeLookup() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.VDDiagnosisAdapter.3
                        @Override // androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
                        /* renamed from: f */
                        public int mo27056f(int i3) {
                            if (chaptersAdapter.mo26845C(i3) == 1) {
                                return gridAutoFitLayoutManager.f101458b0;
                            }
                            return 1;
                        }
                    });
                    recyclerViewViewHolder.f89822I.setLayoutManager(gridAutoFitLayoutManager);
                }
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                if (i2 == 0) {
                    return new HeaderCellViewHolder(LayoutInflater.from(VDDiagnosisFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
                }
                if (i2 == 1) {
                    return new EmergencyTextViewHolder(LayoutInflater.from(VDDiagnosisFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_emergency, viewGroup, false));
                }
                if (i2 == 2) {
                    return new TextViewHolder(LayoutInflater.from(VDDiagnosisFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_list_icon, viewGroup, false));
                }
                if (i2 == 3) {
                    return new RecyclerViewViewHolder(LayoutInflater.from(VDDiagnosisFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_recyclerview, viewGroup, false));
                }
                if (i2 == 4) {
                    return new RippleTextViewHolder(LayoutInflater.from(VDDiagnosisFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
                }
                return null;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                return VDDiagnosisFragment.this.f89804Z4.size() + 6;
            }

            /* renamed from: d0 */
            public Bundle m72909d0(int i2) {
                String str;
                String string;
                Bundle bundle = new Bundle();
                String str2 = "TypeInteger";
                if (i2 == 0) {
                    bundle.putString("Type", "SimpleTextEmergency");
                    VDDiagnosisFragment vDDiagnosisFragment = VDDiagnosisFragment.this;
                    bundle.putString("Text", vDDiagnosisFragment.m72902T4(Integer.valueOf(vDDiagnosisFragment.f89802X4.getString("sortPriority")).intValue()));
                    bundle.putInt("TypeInteger", 0);
                    str2 = "Emergency";
                    if (VDDiagnosisFragment.this.f89802X4.getString("severityLevel").equals(ExifInterface.f16317Y4)) {
                        bundle.putInt(str2, 1);
                    } else {
                        bundle.putInt("Emergency", 0);
                    }
                } else {
                    if (i2 == 1) {
                        bundle.putString("Type", "Header");
                        str = "CLINICAL SCENARIO";
                    } else {
                        int i3 = 2;
                        if (i2 == 2) {
                            bundle.putString("Type", "TextList");
                            string = ((Bundle) VDDiagnosisFragment.this.f89803Y4.get(VDDiagnosisFragment.this.f89806b5)).getString("longName");
                        } else {
                            i3 = 3;
                            if (i2 == 3) {
                                bundle.putString("Type", "Header");
                                str = VDDiagnosisFragment.this.f89805a5.size() + " IMAGES";
                            } else if (i2 == 4) {
                                bundle.putString("Type", "GridView");
                                string = "";
                            } else if (i2 == 5) {
                                bundle.putString("Type", "Header");
                                str = PdfWriterPipeline.f74582f;
                            } else if (i2 > 5) {
                                bundle.putString("Type", "Item");
                                bundle.putBundle("Item", (Bundle) VDDiagnosisFragment.this.f89804Z4.get(i2 - 6));
                                bundle.putInt("TypeInteger", 4);
                            }
                        }
                        bundle.putString("Text", string);
                        bundle.putInt("TypeInteger", i3);
                    }
                    bundle.putString("Text", str);
                    bundle.putInt(str2, 1);
                }
                return bundle;
            }

            /* renamed from: e0 */
            public void m72910e0(Bundle bundle, int i2) {
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: T4 */
        public String m72902T4(int i2) {
            return i2 == 1 ? "Very common or important" : i2 == 2 ? "Common" : i2 == 3 ? "Uncommon" : i2 == 4 ? "Extremely rare" : "";
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: U4 */
        public void m72903U4(String str) {
            ArrayList<Bundle> arrayList = this.f89805a5;
            if (arrayList == null || arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
                return;
            }
            ArrayList arrayList2 = new ArrayList();
            int i2 = 0;
            for (int i3 = 0; i3 < this.f89805a5.size(); i3++) {
                Bundle bundle = this.f89805a5.get(i3);
                String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, bundle.getString("imageId") + ".jpg", "Large-Encrypted");
                Bundle bundle2 = new Bundle();
                bundle2.putString("ImagePath", strM71754h1);
                bundle2.putString("id", bundle.getString("imageId"));
                bundle2.putString("Encrypted", IcyHeaders.f28171a3);
                if (bundle.getString("imageId").startsWith(str)) {
                    i2 = i3;
                }
                arrayList2.add(bundle2);
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList2);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        /* renamed from: Q4 */
        public void m72904Q4() {
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            ArrayList<Bundle> arrayList = this.f89805a5;
            if (arrayList == null || arrayList.size() <= 0) {
                return null;
            }
            Bundle bundleM72839v3 = m72839v3(this.f89805a5);
            return CompressHelper.m71754h1(this.f89566D4, bundleM72839v3.getString("imageId") + ".jpg", "Large-Encrypted");
        }

        /* renamed from: R4 */
        public void m72905R4() {
            this.f89808d5.setItemAnimator(new DefaultItemAnimator());
            this.f89808d5.m27459p(new CustomItemDecoration(m15366r()));
            this.f89808d5.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        }

        /* renamed from: S4 */
        public int m72906S4(int i2) {
            return (int) ((i2 * m15320b0().getDisplayMetrics().density) + 0.5f);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
            this.f89579Q4 = new CompressHelper(m15366r());
            this.f89807c5 = new Bundle();
            if (bundle != null && bundle.containsKey("Restoring")) {
                this.f89584e4 = true;
                if (bundle.containsKey("Find")) {
                    this.f89585f4 = bundle.getString("Find");
                    this.f89594o4 = bundle.getInt("FindIndex");
                }
                if (bundle.containsKey("mFinalHTML")) {
                    this.f89563A4 = bundle.getString("mFinalHTML");
                }
                if (bundle.containsKey("mTitle")) {
                    this.f89568F4 = bundle.getString("mTitle");
                }
                this.f89803Y4 = bundle.getParcelableArrayList("mModules");
                this.f89804Z4 = bundle.getParcelableArrayList("mFields");
                this.f89805a5 = bundle.getParcelableArrayList("mImages");
                this.f89806b5 = bundle.getInt("mSelectedModule");
                this.f89802X4 = bundle.getBundle("mDiagnosis");
            }
            this.f89565C4 = viewInflate;
            this.f89808d5 = (RecyclerView) viewInflate.findViewById(C5562R.id.recycler_view);
            this.f89566D4 = m15387y().getBundle("DB");
            this.f89567E4 = m15387y().getString("URL");
            this.f89574L4 = (Toolbar) this.f89565C4.findViewById(C5562R.id.toolbar);
            TabHost tabHost = (TabHost) viewInflate.findViewById(C5562R.id.findtabhost);
            this.f89603x4 = tabHost;
            if (tabHost != null) {
                tabHost.setup();
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            try {
                final CompressHelper compressHelper = new CompressHelper(m15366r());
                if (this.f89802X4 == null) {
                    this.f89802X4 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from diagnoses where id = " + this.f89567E4));
                    this.f89803Y4 = compressHelper.m71817V(this.f89566D4, "SELECT DiagnosesModules.id,moduleId, imageCount,modules.longName FROM DiagnosesModules,modules where diagnosisId=" + this.f89567E4 + " And diagnosesModules.moduleId=modules.id");
                    final int i2 = m15387y().containsKey("defaultModule") ? m15387y().getInt("defaultModule") : Integer.valueOf(this.f89802X4.getString("defaultModule")).intValue();
                    this.f89806b5 = this.f89803Y4.indexOf((Bundle) new ArrayList(Collections2.m42365d(this.f89803Y4, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.1
                        @Override // com.google.common.base.Predicate
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public boolean apply(Bundle bundle2) {
                            return bundle2.getString("moduleId").equals(String.valueOf(i2));
                        }
                    })).get(0));
                    this.f89805a5 = compressHelper.m71817V(this.f89566D4, "select imageId ,copyRight from Images where diagnosesModulesid=" + this.f89803Y4.get(this.f89806b5).getString("id"));
                    this.f89804Z4 = compressHelper.m71817V(this.f89566D4, "select fieldId, fieldName,doc  from Documents, fields where DiagnosesModulesId=" + this.f89803Y4.get(this.f89806b5).getString("id") + " and documents.fieldId = fields.id");
                    this.f89568F4 = this.f89802X4.getString("dName");
                    this.f89563A4 = "";
                    if (m15387y().containsKey("SEARCH") && m15387y().getStringArray("SEARCH") != null) {
                        new Timer().schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.2
                            @Override // java.util.TimerTask, java.lang.Runnable
                            public void run() {
                                compressHelper.m71772A1(VDDiagnosisFragment.this.f89566D4, "Doc-" + ((Bundle) VDDiagnosisFragment.this.f89803Y4.get(VDDiagnosisFragment.this.f89806b5)).getString("id"), VDDiagnosisFragment.this.m15387y().getStringArray("SEARCH"), null);
                                VDDiagnosisFragment.this.m15387y().remove("SEARCH");
                            }
                        }, ExoPlayer.f21773a1);
                    }
                }
                if (!compressHelper.m71903x1()) {
                    m72827m4(this.f89568F4);
                }
                m15366r().setTitle(this.f89568F4);
                this.f89808d5.setAdapter(new VDDiagnosisAdapter(m15366r()));
                m72905R4();
                mo72642f3(C5562R.menu.favorite);
                m15358o2(false);
                m72818g3();
                m72786G3();
                return viewInflate;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                m72779B4(e2);
                return viewInflate;
            }
        }

        /* renamed from: V4 */
        public void m72907V4() {
            ListView listView = (ListView) this.f89565C4.findViewById(C5562R.id.list_view);
            TextView textView = (TextView) this.f89565C4.findViewById(C5562R.id.status_label);
            LinearLayout linearLayout = (LinearLayout) this.f89565C4.findViewById(C5562R.id.status_layout);
            listView.setVisibility(0);
            textView.setVisibility(8);
            linearLayout.setVisibility(8);
        }

        @Override // net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDialogListInterface
        /* renamed from: h */
        public void mo72891h(Bundle bundle, String str) {
            CompressHelper compressHelper = new CompressHelper(m15366r());
            if (str.equals("Module")) {
                this.f89806b5 = this.f89803Y4.indexOf(bundle);
                this.f89805a5 = compressHelper.m71817V(this.f89566D4, "select imageId ,copyRight from Images where diagnosesModulesid=" + this.f89803Y4.get(this.f89806b5).getString("id"));
                this.f89804Z4 = compressHelper.m71817V(this.f89566D4, "select fieldId, fieldName,doc  from Documents, fields where DiagnosesModulesId=" + this.f89803Y4.get(this.f89806b5).getString("id") + " and documents.fieldId = fields.id");
                this.f89808d5.setAdapter(new VDDiagnosisAdapter(m15366r()));
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: l1 */
        public void mo15352l1() {
            super.mo15352l1();
            m72786G3();
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: o4 */
        public void mo71972o4() {
            new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.3

                /* renamed from: a */
                byte[] f89813a;

                @Override // android.os.AsyncTask
                protected Object doInBackground(Object[] objArr) {
                    try {
                        File file = new File(VDDiagnosisFragment.this.mo71955R2());
                        this.f89813a = new CompressHelper(VDDiagnosisFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127");
                        return null;
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                        return null;
                    }
                }

                @Override // android.os.AsyncTask
                protected void onPostExecute(Object obj) {
                    super.onPostExecute(obj);
                    Glide.m30041G(VDDiagnosisFragment.this.m15366r()).mo30123h(this.f89813a).m30165B2(VDDiagnosisFragment.this.f89575M4);
                }
            }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
        }

        @Override // androidx.fragment.app.Fragment, android.content.ComponentCallbacks
        public void onConfigurationChanged(Configuration configuration) {
            super.onConfigurationChanged(configuration);
            this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDiagnosisActivity.VDDiagnosisFragment.4
                @Override // java.lang.Runnable
                public void run() {
                    VDDiagnosisFragment.this.f89808d5.getAdapter().m27491G();
                }
            }, 500L);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new VDDiagnosisFragment(), bundle);
    }
}

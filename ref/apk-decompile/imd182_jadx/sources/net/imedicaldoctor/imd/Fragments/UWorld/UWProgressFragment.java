package net.imedicaldoctor.imd.Fragments.UWorld;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.components.Description;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.LegendEntry;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.itextpdf.tool.xml.css.CSS;
import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.DayViewDecorator;
import com.prolificinteractive.materialcalendarview.DayViewFacade;
import com.prolificinteractive.materialcalendarview.spans.DotSpan;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Random;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Views.ButtonSmall;

/* loaded from: classes3.dex */
public class UWProgressFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    private ProgressBar f89296X4;

    /* renamed from: Y4 */
    private TextView f89297Y4;

    /* renamed from: Z4 */
    private TextView f89298Z4;

    /* renamed from: a5 */
    private TextView f89299a5;

    /* renamed from: b5 */
    private TextView f89300b5;

    /* renamed from: c5 */
    private TextView f89301c5;

    /* renamed from: d5 */
    private PieChart f89302d5;

    /* renamed from: e5 */
    private BarChart f89303e5;

    /* renamed from: f5 */
    private BarChart f89304f5;

    /* renamed from: g5 */
    private BarChart f89305g5;

    /* renamed from: h5 */
    private BarChart f89306h5;

    /* renamed from: i5 */
    public ButtonSmall f89307i5;

    private static class EventDecorator implements DayViewDecorator {

        /* renamed from: a */
        private final int f89311a;

        /* renamed from: b */
        private final HashSet<CalendarDay> f89312b;

        public EventDecorator(int i2, Collection<CalendarDay> collection) {
            this.f89311a = i2;
            this.f89312b = new HashSet<>(collection);
        }

        @Override // com.prolificinteractive.materialcalendarview.DayViewDecorator
        /* renamed from: a */
        public void mo58040a(DayViewFacade dayViewFacade) {
            dayViewFacade.m58042a(new DotSpan(5.0f, this.f89311a));
        }

        @Override // com.prolificinteractive.materialcalendarview.DayViewDecorator
        /* renamed from: b */
        public boolean mo58041b(CalendarDay calendarDay) {
            return this.f89312b.contains(calendarDay);
        }
    }

    /* renamed from: I4 */
    private int[] m72627I4(int i2) {
        int[] iArr = new int[i2];
        Random random = new Random();
        for (int i3 = 0; i3 < i2; i3++) {
            iArr[i3] = Color.rgb(random.nextInt(256), random.nextInt(256), random.nextInt(256));
        }
        return iArr;
    }

    /* renamed from: J4 */
    private void m72628J4() {
        try {
            int iIntValue = Integer.valueOf(this.f89579Q4.m71817V(this.f89566D4, "select count(*) c from questions").get(0).getString("c")).intValue();
            int iIntValue2 = Integer.valueOf(this.f89579Q4.m71817V(this.f89566D4, "select count(distinct(qid)) c from logs").get(0).getString("c")).intValue();
            int iIntValue3 = Integer.valueOf(this.f89579Q4.m71817V(this.f89566D4, "SELECT COUNT(*)  AS c FROM (SELECT l1.* FROM logs l1 JOIN (SELECT qid, MAX(id) AS latestId FROM logs GROUP BY qid) l2 ON l1.qid = l2.qid AND l1.id = l2.latestId) subquery WHERE selectedAnswer = corrAnswer;").get(0).getString("c")).intValue();
            int iIntValue4 = Integer.valueOf(this.f89579Q4.m71817V(this.f89566D4, "SELECT COUNT(*)  AS c FROM (SELECT l1.* FROM logs l1 JOIN (SELECT qid, MAX(id) AS latestId FROM logs GROUP BY qid) l2 ON l1.qid = l2.qid AND l1.id = l2.latestId) subquery WHERE selectedAnswer <> corrAnswer;").get(0).getString("c")).intValue();
            int i2 = iIntValue - iIntValue2;
            this.f89296X4.setProgress((int) ((iIntValue2 / iIntValue) * 100.0d));
            this.f89297Y4.setText("Overall Progress: " + this.f89296X4.getProgress() + CSS.Value.f74136n0);
            this.f89298Z4.setText(String.valueOf(iIntValue));
            this.f89299a5.setText(String.valueOf(iIntValue2));
            this.f89300b5.setText(String.valueOf(iIntValue3));
            this.f89301c5.setText(String.valueOf(iIntValue4));
            ArrayList arrayList = new ArrayList();
            arrayList.add(new PieEntry(iIntValue3, "Correct"));
            arrayList.add(new PieEntry(iIntValue4, "Incorrect"));
            arrayList.add(new PieEntry(i2, "Unused"));
            PieDataSet pieDataSet = new PieDataSet(arrayList, "");
            pieDataSet.m32402D1(new int[]{C5562R.color.correct, C5562R.color.incorrect, C5562R.color.omitted, C5562R.color.unused}, m15246B());
            PieData pieData = new PieData(pieDataSet);
            pieData.m32510J(false);
            this.f89302d5.setData(pieData);
            Description description = new Description();
            description.m32246q("");
            this.f89302d5.setDescription(description);
            this.f89302d5.invalidate();
        } catch (Exception unused) {
            CompressHelper.m71767x2(m15366r(), "Error in Overall Progress Calculations", 0);
        }
    }

    /* renamed from: K4 */
    private void m72629K4() {
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "SELECT s.subjectName, s.ids, CAST((COUNT(DISTINCT l.qid) * 100.0 / COUNT(DISTINCT q.id)) AS INTEGER) AS percentage FROM (SELECT CASE WHEN instr(name, '(') > 0 THEN substr(name, 1, instr(name, '(') - 1) ELSE name END AS subjectName, GROUP_CONCAT(id) AS ids FROM Subjects GROUP BY subjectName) s JOIN Questions q ON ',' || s.ids || ',' LIKE '%,' || q.subId || ',%' LEFT JOIN logs l ON q.id = l.qid GROUP BY s.subjectName, s.ids ORDER BY percentage DESC;");
        ArrayList arrayList = new ArrayList();
        ArrayList arrayList2 = new ArrayList();
        if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
            this.f89303e5.setVisibility(8);
            return;
        }
        for (int i2 = 0; i2 < arrayListM71817V.size(); i2++) {
            arrayList.add(new BarEntry(i2, Integer.valueOf(arrayListM71817V.get(i2).getString("percentage")).intValue()));
            arrayList2.add(arrayListM71817V.get(i2).getString("subjectName") + "(" + arrayListM71817V.get(i2).getString("percentage") + "%)");
        }
        BarDataSet barDataSet = new BarDataSet(arrayList, "Subjects Progress");
        barDataSet.m32398B1(m72627I4(arrayList.size()));
        this.f89303e5.setData(new BarData(barDataSet));
        Description description = new Description();
        description.m32246q("");
        this.f89303e5.setDescription(description);
        Legend legend = this.f89303e5.getLegend();
        legend.m32270T(Legend.LegendForm.SQUARE);
        legend.m32238i(12.0f);
        legend.m32281e0(10.0f);
        legend.m32282f0(5.0f);
        legend.m32279c0(Legend.LegendVerticalAlignment.BOTTOM);
        legend.m32275Y(Legend.LegendHorizontalAlignment.CENTER);
        legend.m32277a0(Legend.LegendOrientation.HORIZONTAL);
        legend.m32280d0(true);
        legend.m32265O(false);
        LegendEntry[] legendEntryArr = new LegendEntry[arrayList2.size()];
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            LegendEntry legendEntry = new LegendEntry();
            legendEntry.f46535a = (String) arrayList2.get(i3);
            legendEntry.f46540f = barDataSet.mo32426d0(i3);
            legendEntryArr[i3] = legendEntry;
        }
        legend.m32263M(legendEntryArr);
        int size = ((int) (m15320b0().getDisplayMetrics().density * 300.0f)) + (arrayList2.size() * ((int) (m15320b0().getDisplayMetrics().density * 20.0f)));
        ViewGroup.LayoutParams layoutParams = this.f89303e5.getLayoutParams();
        layoutParams.height = size;
        this.f89303e5.setLayoutParams(layoutParams);
        this.f89303e5.invalidate();
    }

    /* renamed from: L4 */
    private void m72630L4() {
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "SELECT s.subjectName, s.ids, COUNT(DISTINCT l.qid) AS answeredQuestions, COUNT(DISTINCT CASE WHEN l.selectedAnswer = l.corrAnswer THEN l.qid END) AS correctAnswers, COUNT(DISTINCT CASE WHEN l.selectedAnswer != l.corrAnswer THEN l.qid END) AS incorrectAnswers, CAST((COUNT(DISTINCT CASE WHEN l.selectedAnswer = l.corrAnswer THEN l.qid END) * 100.0 / COUNT(DISTINCT l.qid)) AS INTEGER) AS correctPercentage FROM (SELECT CASE WHEN instr(name, '(') > 0 THEN substr(name, 1, instr(name, '(') - 1) ELSE name END AS subjectName, GROUP_CONCAT(id) AS ids FROM Subjects GROUP BY subjectName) s JOIN Questions q ON ',' || s.ids || ',' LIKE '%,' || q.subId || ',%' LEFT JOIN logs l ON q.id = l.qid GROUP BY s.subjectName, s.ids HAVING correctPercentage IS NOT NULL ORDER BY correctPercentage DESC;");
        ArrayList arrayList = new ArrayList();
        ArrayList arrayList2 = new ArrayList();
        if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
            this.f89304f5.setVisibility(8);
            return;
        }
        for (int i2 = 0; i2 < arrayListM71817V.size(); i2++) {
            arrayList.add(new BarEntry(i2, Integer.valueOf(arrayListM71817V.get(i2).getString("correctPercentage")).intValue()));
            arrayList2.add(arrayListM71817V.get(i2).getString("subjectName") + "(" + arrayListM71817V.get(i2).getString("correctPercentage") + "%)");
        }
        BarDataSet barDataSet = new BarDataSet(arrayList, "Subjects Correct Percentage");
        barDataSet.m32398B1(m72627I4(arrayList.size()));
        this.f89304f5.setData(new BarData(barDataSet));
        Description description = new Description();
        description.m32246q("");
        this.f89304f5.setDescription(description);
        Legend legend = this.f89304f5.getLegend();
        legend.m32270T(Legend.LegendForm.SQUARE);
        legend.m32238i(12.0f);
        legend.m32281e0(10.0f);
        legend.m32282f0(5.0f);
        legend.m32279c0(Legend.LegendVerticalAlignment.BOTTOM);
        legend.m32275Y(Legend.LegendHorizontalAlignment.CENTER);
        legend.m32277a0(Legend.LegendOrientation.HORIZONTAL);
        legend.m32280d0(true);
        legend.m32265O(false);
        LegendEntry[] legendEntryArr = new LegendEntry[arrayList2.size()];
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            LegendEntry legendEntry = new LegendEntry();
            legendEntry.f46535a = (String) arrayList2.get(i3);
            legendEntry.f46540f = barDataSet.mo32426d0(i3);
            legendEntryArr[i3] = legendEntry;
        }
        legend.m32263M(legendEntryArr);
        int size = ((int) (m15320b0().getDisplayMetrics().density * 300.0f)) + (arrayList2.size() * ((int) (m15320b0().getDisplayMetrics().density * 20.0f)));
        ViewGroup.LayoutParams layoutParams = this.f89304f5.getLayoutParams();
        layoutParams.height = size;
        this.f89304f5.setLayoutParams(layoutParams);
        this.f89304f5.invalidate();
    }

    /* renamed from: M4 */
    private void m72631M4() {
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "SELECT s.subjectName, s.ids, CAST((COUNT(DISTINCT l.qid) * 100.0 / COUNT(DISTINCT q.id)) AS INTEGER) AS percentage FROM (SELECT CASE WHEN instr(name, '(') > 0 THEN substr(name, 1, instr(name, '(') - 1) ELSE name END AS subjectName, GROUP_CONCAT(id) AS ids FROM Systems GROUP BY subjectName) s JOIN Questions q ON ',' || s.ids || ',' LIKE '%,' || q.sysId || ',%' LEFT JOIN logs l ON q.id = l.qid GROUP BY s.subjectName, s.ids ORDER BY percentage DESC;");
        ArrayList arrayList = new ArrayList();
        ArrayList arrayList2 = new ArrayList();
        if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
            this.f89305g5.setVisibility(8);
            return;
        }
        for (int i2 = 0; i2 < arrayListM71817V.size(); i2++) {
            arrayList.add(new BarEntry(i2, Integer.valueOf(arrayListM71817V.get(i2).getString("percentage")).intValue()));
            arrayList2.add(arrayListM71817V.get(i2).getString("subjectName") + "(" + arrayListM71817V.get(i2).getString("percentage") + "%)");
        }
        BarDataSet barDataSet = new BarDataSet(arrayList, "Systems Progress");
        barDataSet.m32398B1(m72627I4(arrayList.size()));
        this.f89305g5.setData(new BarData(barDataSet));
        Description description = new Description();
        description.m32246q("");
        this.f89305g5.setDescription(description);
        Legend legend = this.f89305g5.getLegend();
        legend.m32270T(Legend.LegendForm.SQUARE);
        legend.m32238i(12.0f);
        legend.m32281e0(10.0f);
        legend.m32282f0(5.0f);
        legend.m32279c0(Legend.LegendVerticalAlignment.BOTTOM);
        legend.m32275Y(Legend.LegendHorizontalAlignment.CENTER);
        legend.m32277a0(Legend.LegendOrientation.HORIZONTAL);
        legend.m32280d0(true);
        legend.m32265O(false);
        LegendEntry[] legendEntryArr = new LegendEntry[arrayList2.size()];
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            LegendEntry legendEntry = new LegendEntry();
            legendEntry.f46535a = (String) arrayList2.get(i3);
            legendEntry.f46540f = barDataSet.mo32426d0(i3);
            legendEntryArr[i3] = legendEntry;
        }
        legend.m32263M(legendEntryArr);
        int size = ((int) (m15320b0().getDisplayMetrics().density * 300.0f)) + (arrayList2.size() * ((int) (m15320b0().getDisplayMetrics().density * 20.0f)));
        ViewGroup.LayoutParams layoutParams = this.f89305g5.getLayoutParams();
        layoutParams.height = size;
        this.f89305g5.setLayoutParams(layoutParams);
        this.f89305g5.invalidate();
    }

    /* renamed from: N4 */
    private void m72632N4() {
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "SELECT s.subjectName, s.ids, COUNT(DISTINCT l.qid) AS answeredQuestions, COUNT(DISTINCT CASE WHEN l.selectedAnswer = l.corrAnswer THEN l.qid END) AS correctAnswers, COUNT(DISTINCT CASE WHEN l.selectedAnswer != l.corrAnswer THEN l.qid END) AS incorrectAnswers, CAST((COUNT(DISTINCT CASE WHEN l.selectedAnswer = l.corrAnswer THEN l.qid END) * 100.0 / COUNT(DISTINCT l.qid)) AS INTEGER) AS correctPercentage FROM (SELECT CASE WHEN instr(name, '(') > 0 THEN substr(name, 1, instr(name, '(') - 1) ELSE name END AS subjectName, GROUP_CONCAT(id) AS ids FROM Systems GROUP BY subjectName) s JOIN Questions q ON ',' || s.ids || ',' LIKE '%,' || q.sysId || ',%' LEFT JOIN logs l ON q.id = l.qid WHERE l.qid IS NOT NULL GROUP BY s.subjectName, s.ids HAVING correctPercentage IS NOT NULL ORDER BY correctPercentage DESC;");
        ArrayList arrayList = new ArrayList();
        ArrayList arrayList2 = new ArrayList();
        if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
            this.f89306h5.setVisibility(8);
            return;
        }
        for (int i2 = 0; i2 < arrayListM71817V.size(); i2++) {
            arrayList.add(new BarEntry(i2, Integer.valueOf(arrayListM71817V.get(i2).getString("correctPercentage")).intValue()));
            arrayList2.add(arrayListM71817V.get(i2).getString("subjectName") + "(" + arrayListM71817V.get(i2).getString("correctPercentage") + "%)");
        }
        BarDataSet barDataSet = new BarDataSet(arrayList, "Systems Correct Percentage");
        barDataSet.m32398B1(m72627I4(arrayList.size()));
        this.f89306h5.setData(new BarData(barDataSet));
        Description description = new Description();
        description.m32246q("");
        this.f89306h5.setDescription(description);
        Legend legend = this.f89306h5.getLegend();
        legend.m32270T(Legend.LegendForm.SQUARE);
        legend.m32238i(12.0f);
        legend.m32281e0(10.0f);
        legend.m32282f0(5.0f);
        legend.m32279c0(Legend.LegendVerticalAlignment.BOTTOM);
        legend.m32275Y(Legend.LegendHorizontalAlignment.CENTER);
        legend.m32277a0(Legend.LegendOrientation.HORIZONTAL);
        legend.m32280d0(true);
        legend.m32265O(false);
        LegendEntry[] legendEntryArr = new LegendEntry[arrayList2.size()];
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            LegendEntry legendEntry = new LegendEntry();
            legendEntry.f46535a = (String) arrayList2.get(i3);
            legendEntry.f46540f = barDataSet.mo32426d0(i3);
            legendEntryArr[i3] = legendEntry;
        }
        legend.m32263M(legendEntryArr);
        int size = ((int) (m15320b0().getDisplayMetrics().density * 300.0f)) + (arrayList2.size() * ((int) (m15320b0().getDisplayMetrics().density * 20.0f)));
        ViewGroup.LayoutParams layoutParams = this.f89306h5.getLayoutParams();
        layoutParams.height = size;
        this.f89306h5.setLayoutParams(layoutParams);
        this.f89306h5.invalidate();
    }

    @Override // androidx.fragment.app.Fragment
    @Nullable
    /* renamed from: U0 */
    public View mo15303U0(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_progress, viewGroup, false);
        this.f89565C4 = viewInflate;
        Toolbar toolbar = (Toolbar) viewInflate.findViewById(C5562R.id.toolbar);
        this.f89574L4 = toolbar;
        AppCompatActivity appCompatActivity = (AppCompatActivity) m15366r();
        if (appCompatActivity != null) {
            appCompatActivity.m1129P0(toolbar);
            appCompatActivity.m1122F0().mo937Y(true);
            appCompatActivity.m1122F0().mo941c0(true);
        }
        this.f89566D4 = (m15387y() == null || !m15387y().containsKey("DB")) ? null : m15387y().getBundle("DB");
        this.f89307i5 = (ButtonSmall) this.f89565C4.findViewById(C5562R.id.back_button);
        this.f89574L4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWProgressFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                UWProgressFragment.this.f89579Q4.m71821W1(false);
            }
        });
        if (this.f89307i5 != null) {
            this.f89307i5.setDrawableIcon(m15366r().getResources().getDrawable(C5562R.drawable.back_icon_white));
            this.f89307i5.setRippleColor(m15366r().getResources().getColor(C5562R.color.toolbar_item_ripple_color));
            this.f89307i5.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWProgressFragment.2
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    UWProgressFragment.this.f89579Q4.m71821W1(true);
                }
            });
            this.f89307i5.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWProgressFragment.3
                @Override // android.view.View.OnLongClickListener
                public boolean onLongClick(View view) {
                    UWProgressFragment.this.f89579Q4.m71830Z1(true);
                    return true;
                }
            });
        }
        this.f89296X4 = (ProgressBar) viewInflate.findViewById(C5562R.id.overall_progress_bar);
        this.f89297Y4 = (TextView) viewInflate.findViewById(C5562R.id.overall_progress_text);
        this.f89298Z4 = (TextView) viewInflate.findViewById(C5562R.id.total_questions_value);
        this.f89299a5 = (TextView) viewInflate.findViewById(C5562R.id.used_questions_value);
        this.f89300b5 = (TextView) viewInflate.findViewById(C5562R.id.correct_questions_value);
        this.f89301c5 = (TextView) viewInflate.findViewById(C5562R.id.incorrect_questions_value);
        this.f89302d5 = (PieChart) viewInflate.findViewById(C5562R.id.question_distribution_pie_chart);
        this.f89303e5 = (BarChart) viewInflate.findViewById(C5562R.id.subjects_bar_chart);
        this.f89304f5 = (BarChart) viewInflate.findViewById(C5562R.id.subjects_correct_chart);
        this.f89305g5 = (BarChart) viewInflate.findViewById(C5562R.id.systems_bar_chart);
        this.f89306h5 = (BarChart) viewInflate.findViewById(C5562R.id.systems_correct_chart);
        m72628J4();
        m72629K4();
        m72630L4();
        m72631M4();
        m72632N4();
        return viewInflate;
    }
}

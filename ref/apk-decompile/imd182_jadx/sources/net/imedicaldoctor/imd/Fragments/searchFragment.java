package net.imedicaldoctor.imd.Fragments;

import android.content.Context;
import android.database.Cursor;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import com.itextpdf.tool.xml.html.HTML;
import com.timehop.stickyheadersrecyclerview.ItemVisibilityAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersDecoration;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersTouchListener;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.observers.DisposableObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class searchFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    private SearchAdapter f90809A4;

    /* renamed from: B4 */
    private boolean f90810B4;

    /* renamed from: C4 */
    private ArrayList<Bundle> f90811C4;

    /* renamed from: D4 */
    private RecyclerView f90812D4;

    /* renamed from: E4 */
    private AsyncTask f90813E4;

    /* renamed from: F4 */
    private boolean f90814F4;

    /* renamed from: G4 */
    private Observable<Bundle> f90815G4;

    /* renamed from: H4 */
    private DisposableObserver<Bundle> f90816H4;

    /* renamed from: I4 */
    private ProgressBar f90817I4;

    /* renamed from: J4 */
    private MenuItem f90818J4;

    /* renamed from: K4 */
    private String f90819K4;

    /* renamed from: L4 */
    private StickyRecyclerHeadersDecoration f90820L4;

    /* renamed from: M4 */
    private LinearLayoutManager f90821M4;

    /* renamed from: N4 */
    private ArrayList<String> f90822N4;

    /* renamed from: O4 */
    StickyRecyclerHeadersTouchListener f90823O4;

    /* renamed from: P4 */
    public CompressHelper f90824P4;

    /* renamed from: net.imedicaldoctor.imd.Fragments.searchFragment$6 */
    class C55376 implements ObservableOnSubscribe<String> {
        C55376() {
        }

        @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
        /* renamed from: a */
        public void mo59827a(@NonNull final ObservableEmitter<String> observableEmitter) throws Throwable {
            searchFragment.this.f88799s4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.6.1

                /* renamed from: a */
                private DisposableObserver<Bundle> f90832a;

                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: a */
                public boolean mo2514a(String str) {
                    observableEmitter.onNext(str);
                    return true;
                }

                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: b */
                public boolean mo2515b(final String str) {
                    iMDLogger.m73554j("OnQueryTextSubmit", "OnQueryTextSubmit");
                    searchFragment.this.f90819K4 = str;
                    searchFragment.this.f88786f4 = str;
                    DisposableObserver<Bundle> disposableObserver = this.f90832a;
                    if (disposableObserver != null) {
                        disposableObserver.onComplete();
                    }
                    observableEmitter.onNext("SoheilvbSoheilvbSoheilvb");
                    searchFragment.this.f90824P4.m71788I(str, "SearchAll");
                    iMDLogger.m73554j("OnQueryTextSubmit", "Building search observable");
                    Observable observableM59468A4 = Observable.m59451w1(new ObservableOnSubscribe<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.6.1.1
                        @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
                        /* renamed from: a */
                        public void mo59827a(@NonNull ObservableEmitter<Bundle> observableEmitter2) throws Throwable {
                            Iterator<Bundle> it2 = CompressHelper.f87345t.iterator();
                            while (it2.hasNext()) {
                                Bundle next = it2.next();
                                ArrayList<Bundle> arrayListM73370k3 = searchFragment.this.m73370k3(next, str);
                                if (arrayListM73370k3 != null && arrayListM73370k3.size() != 0) {
                                    iMDLogger.m73554j("Search", "Result from " + next.getString("Title") + " : " + arrayListM73370k3.size());
                                    Bundle bundle = new Bundle();
                                    bundle.putBundle("database", next);
                                    bundle.putParcelableArrayList("items", arrayListM73370k3);
                                    observableEmitter2.onNext(bundle);
                                }
                            }
                            observableEmitter2.onComplete();
                        }
                    }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59468A4(new Function<Throwable, Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.6.1.2
                        @Override // io.reactivex.rxjava3.functions.Function
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public Bundle apply(Throwable th) throws Throwable {
                            return null;
                        }
                    });
                    searchFragment.this.f90815G4 = observableM59468A4;
                    DisposableObserver<Bundle> disposableObserver2 = new DisposableObserver<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.6.1.3
                        @Override // io.reactivex.rxjava3.observers.DisposableObserver
                        /* renamed from: a */
                        protected void mo61331a() {
                            super.mo61331a();
                            searchFragment.this.f90818J4.setVisible(true);
                            searchFragment.this.f90817I4.setIndeterminate(true);
                            iMDLogger.m73554j("SearchSubscriber", "On Start");
                            searchFragment.this.f90811C4 = new ArrayList();
                            searchFragment.this.m73366C3();
                            searchFragment.this.m72468V2();
                            searchFragment.this.f88799s4.getSuggestionsAdapter().mo10519m(null);
                            searchFragment.this.mo72473f3("Searching");
                        }

                        @Override // io.reactivex.rxjava3.core.Observer
                        /* renamed from: c, reason: merged with bridge method [inline-methods] */
                        public void onNext(@NonNull Bundle bundle) {
                            searchFragment.this.f90811C4.add(bundle);
                            searchFragment.this.mo72472e3();
                            StringBuilder sb = new StringBuilder();
                            sb.append("On Next - ");
                            sb.append(bundle.getBundle("database").getString("Title"));
                            sb.append(" - ");
                            searchFragment searchfragment = searchFragment.this;
                            sb.append(searchfragment.m73367D3(searchfragment.f90811C4));
                            sb.append(" - IN Thread :");
                            sb.append(Thread.currentThread());
                            iMDLogger.m73554j("SearchSubscriber", sb.toString());
                        }

                        @Override // io.reactivex.rxjava3.core.Observer
                        public void onComplete() {
                            searchFragment.this.f90818J4.setVisible(false);
                            iMDLogger.m73554j("SearchSubscriber", "On Complete");
                            if (searchFragment.this.f90811C4 == null || searchFragment.this.f90811C4.size() == 0) {
                                searchFragment.this.mo72473f3("Nothing Found");
                            }
                            if (searchFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("SearchCollapsed", false)) {
                                searchFragment.this.m73372z3();
                            }
                        }

                        @Override // io.reactivex.rxjava3.core.Observer
                        public void onError(@NonNull Throwable th) {
                            searchFragment.this.f90818J4.setVisible(false);
                            iMDLogger.m73554j("SearchSubscriber", "On Error");
                            iMDLogger.m73550f("onQueryTextSubmit", "Some error on SearchObservable");
                        }
                    };
                    this.f90832a = disposableObserver2;
                    searchFragment.this.f90816H4 = disposableObserver2;
                    observableM59468A4.mo59651a(this.f90832a);
                    return true;
                }
            });
        }
    }

    public class EmptyViewHolder extends RecyclerView.ViewHolder {
        public EmptyViewHolder(View view) {
            super(view);
        }
    }

    public class SearchAdapter extends RecyclerView.Adapter implements StickyRecyclerHeadersAdapter {

        /* renamed from: d */
        private final Context f90844d;

        public SearchAdapter(Context context) {
            this.f90844d = context;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            searchFragment searchfragment = searchFragment.this;
            return searchfragment.m73369j3(i2, searchfragment.f90811C4).containsKey("Item") ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            if (viewHolder.m27811F() == 1) {
                return;
            }
            SearchItemViewHolder searchItemViewHolder = (SearchItemViewHolder) viewHolder;
            searchFragment searchfragment = searchFragment.this;
            Bundle bundle = searchfragment.m73369j3(i2, searchfragment.f90811C4).getBundle("Item");
            searchItemViewHolder.f90852I.setText(bundle.getString("text"));
            if (bundle.containsKey("subText") && bundle.getString("subText").length() == 0) {
                searchItemViewHolder.f90853J.setText((CharSequence) null);
                searchItemViewHolder.f90853J.setVisibility(8);
            } else {
                searchItemViewHolder.f90853J.setVisibility(0);
                searchItemViewHolder.f90853J.setText(bundle.getString("subText"));
            }
            searchItemViewHolder.f90854K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.SearchAdapter.1
                /* JADX WARN: Multi-variable type inference failed */
                /* JADX WARN: Removed duplicated region for block: B:115:0x03db A[PHI: r1 r2
                  0x03db: PHI (r1v55 java.lang.String) = (r1v54 java.lang.String), (r1v58 java.lang.String) binds: [B:120:0x0408, B:114:0x03d9] A[DONT_GENERATE, DONT_INLINE]
                  0x03db: PHI (r2v94 java.lang.String) = (r2v93 java.lang.String), (r2v95 java.lang.String) binds: [B:120:0x0408, B:114:0x03d9] A[DONT_GENERATE, DONT_INLINE]] */
                /* JADX WARN: Type inference failed for: r4v30, types: [java.lang.String, java.lang.String[]] */
                /* JADX WARN: Type inference failed for: r4v31 */
                /* JADX WARN: Type inference failed for: r4v7 */
                @Override // android.view.View.OnClickListener
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public void onClick(android.view.View r23) {
                    /*
                        Method dump skipped, instructions count: 1861
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.searchFragment.SearchAdapter.ViewOnClickListenerC55411.onClick(android.view.View):void");
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 1) {
                return searchFragment.this.new EmptyViewHolder(LayoutInflater.from(this.f90844d).inflate(C5562R.layout.list_view_item_header_keeper, viewGroup, false));
            }
            View viewInflate = LayoutInflater.from(this.f90844d).inflate(C5562R.layout.list_view_item_search_ripple, viewGroup, false);
            searchFragment searchfragment = searchFragment.this;
            return searchfragment.new SearchItemViewHolder(searchfragment.m15366r(), viewInflate);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            searchFragment searchfragment = searchFragment.this;
            return searchfragment.m73367D3(searchfragment.f90811C4);
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: o */
        public RecyclerView.ViewHolder mo58201o(ViewGroup viewGroup) {
            View viewInflate = LayoutInflater.from(this.f90844d).inflate(C5562R.layout.list_view_item_search_header, viewGroup, false);
            searchFragment searchfragment = searchFragment.this;
            return searchfragment.new SearchHeaderViewHolder(searchfragment.m15366r(), viewInflate);
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: p */
        public void mo58202p(RecyclerView.ViewHolder viewHolder, int i2) throws IOException {
            SearchHeaderViewHolder searchHeaderViewHolder = (SearchHeaderViewHolder) viewHolder;
            if (searchFragment.this.f90811C4 == null) {
                return;
            }
            viewHolder.f33076a.setTag(Integer.valueOf(i2));
            searchFragment searchfragment = searchFragment.this;
            Bundle bundle = searchfragment.m73369j3(i2, searchfragment.f90811C4).getBundle("Database");
            searchHeaderViewHolder.f90849J.setText(bundle.getString("Title"));
            String strM71724C = CompressHelper.m71724C(bundle);
            if (!strM71724C.contains("file:///android_asset/")) {
                searchHeaderViewHolder.f90848I.setImageURI(Uri.parse(strM71724C));
                return;
            }
            try {
                InputStream inputStreamOpen = searchFragment.this.m15366r().getAssets().open(strM71724C.replace("file:///android_asset/", ""));
                searchHeaderViewHolder.f90848I.setImageBitmap(BitmapFactory.decodeStream(inputStreamOpen));
                inputStreamOpen.close();
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
            }
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: r */
        public long mo58203r(int i2) {
            searchFragment searchfragment = searchFragment.this;
            return searchfragment.m73368i3(i2, searchfragment.f90811C4);
        }
    }

    public class SearchHeaderViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public ImageView f90848I;

        /* renamed from: J */
        public TextView f90849J;

        /* renamed from: K */
        public ImageView f90850K;

        public SearchHeaderViewHolder(Context context, View view) {
            super(view);
            this.f90849J = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90848I = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90850K = (ImageView) view.findViewById(C5562R.id.icon);
        }
    }

    public class SearchItemViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90852I;

        /* renamed from: J */
        public TextView f90853J;

        /* renamed from: K */
        public MaterialRippleLayout f90854K;

        public SearchItemViewHolder(Context context, View view) {
            super(view);
            this.f90852I = (TextView) view.findViewById(C5562R.id.title_text);
            this.f90853J = (TextView) view.findViewById(C5562R.id.subtitle_text);
            this.f90854K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: A3 */
    public Bundle m73351A3(Bundle bundle) {
        String str;
        String str2;
        Bundle bundle2 = new Bundle();
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(bundle.getString("docId"), "|");
        String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(bundle.getString(HTML.Tag.f74369V), "|");
        for (int i2 = 0; i2 < strArrSplitByWholeSeparator.length; i2++) {
            if (strArrSplitByWholeSeparator2.length > i2) {
                str = strArrSplitByWholeSeparator[i2];
                str2 = strArrSplitByWholeSeparator2[i2];
            } else {
                str = strArrSplitByWholeSeparator[i2];
                str2 = "";
            }
            bundle2.putString(str, str2);
        }
        return bundle2;
    }

    /* renamed from: B3 */
    public void m73365B3() {
        if (this.f90810B4) {
            this.f90812D4.m27380A1(this.f90820L4);
            this.f90810B4 = false;
        }
    }

    /* renamed from: C3 */
    public void m73366C3() {
        StickyRecyclerHeadersTouchListener stickyRecyclerHeadersTouchListener = new StickyRecyclerHeadersTouchListener(this.f90812D4, this.f90820L4);
        this.f90823O4 = stickyRecyclerHeadersTouchListener;
        stickyRecyclerHeadersTouchListener.m58212h(new StickyRecyclerHeadersTouchListener.OnHeaderClickListener() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.8
            @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersTouchListener.OnHeaderClickListener
            /* renamed from: a */
            public void mo58213a(View view, int i2, long j2) {
                String string = ((Bundle) searchFragment.this.f90811C4.get((int) j2)).getBundle("database").getString("Name");
                if (searchFragment.this.f90822N4.contains(string)) {
                    searchFragment.this.f90822N4.remove(string);
                } else {
                    searchFragment.this.f90822N4.add(string);
                }
                searchFragment.this.f90812D4.getAdapter().m27491G();
            }
        });
        this.f90812D4.m27466s(this.f90823O4);
        m73371y3();
        this.f90812D4.setAdapter(this.f90809A4);
        this.f90809A4.m27502Z(new RecyclerView.AdapterDataObserver() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.9
            @Override // androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
            /* renamed from: a */
            public void mo27287a() {
                searchFragment.this.f90820L4.m58207n();
            }
        });
    }

    /* renamed from: D3 */
    public int m73367D3(ArrayList<Bundle> arrayList) {
        int size = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            size += this.f90822N4.contains(next.getBundle("database").getString("Name")) ? 1 : next.getParcelableArrayList("items").size();
        }
        return size;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        try {
            m15366r().setTitle("");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        menuInflater.inflate(C5562R.menu.search, menu);
        SearchView searchView = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        MenuItem menuItemFindItem = menu.findItem(C5562R.id.progress_menu);
        this.f90818J4 = menuItemFindItem;
        this.f90817I4 = (ProgressBar) menuItemFindItem.getActionView();
        searchView.setIconifiedByDefault(false);
        searchView.setQueryHint("Search Anything");
        final String str = this.f90819K4;
        this.f88799s4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.2
            @Override // java.lang.Runnable
            public void run() {
                searchFragment.this.f88799s4.m2508k0(str, false);
                String str2 = searchFragment.this.f88786f4;
                if (str2 == null || str2.length() <= 0) {
                    return;
                }
                if (searchFragment.this.f90811C4 == null || searchFragment.this.f90811C4.size() == 0) {
                    searchFragment searchfragment = searchFragment.this;
                    searchfragment.f88799s4.m2508k0(searchfragment.f88786f4, true);
                } else {
                    searchFragment searchfragment2 = searchFragment.this;
                    searchfragment2.f88799s4.m2508k0(searchfragment2.f88786f4, false);
                    searchFragment.this.mo72472e3();
                }
                searchFragment.this.m72468V2();
            }
        }, 10L);
        searchView.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.3
            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: a */
            public boolean mo2516a(int i2) {
                Cursor cursorMo10512c = searchFragment.this.f88799s4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (searchFragment.this.f88799s4.getTag(1) != null && ((String) searchFragment.this.f88799s4.getTag(1)).length() > 0) {
                    string = searchFragment.this.f88799s4.getTag() + StringUtils.SPACE + string;
                }
                searchFragment.this.f88799s4.m2508k0(string, true);
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: b */
            public boolean mo2517b(int i2) {
                Cursor cursorMo10512c = searchFragment.this.f88799s4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (searchFragment.this.f88799s4.getTag() != null && ((String) searchFragment.this.f88799s4.getTag()).length() > 0) {
                    string = searchFragment.this.f88799s4.getTag() + StringUtils.SPACE + string;
                }
                searchFragment.this.f88799s4.m2508k0(string, true);
                return false;
            }
        });
        ((ImageView) searchView.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                searchFragment.this.f88799s4.m2508k0("", false);
                searchFragment.this.f88799s4.clearFocus();
                searchFragment.this.mo72473f3("Search Anything");
                searchFragment.this.m72468V2();
            }
        });
        searchView.setSuggestionsAdapter(new CursorAdapter(m15366r(), null, 0) { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.5
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("word")));
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell, viewGroup, false);
                viewInflate.setTag(viewInflate.findViewById(C5562R.id.text));
                return viewInflate;
            }
        });
        Observable.m59451w1(new C55376()).m59804x1(500L, TimeUnit.MILLISECONDS).mo59651a(new DisposableObserver<String>() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.7
            @Override // io.reactivex.rxjava3.core.Observer
            /* renamed from: c, reason: merged with bridge method [inline-methods] */
            public void onNext(@NonNull String str2) throws IOException {
                if (str2.equals("SoheilvbSoheilvbSoheilvb")) {
                    searchFragment.this.f88799s4.getSuggestionsAdapter().mo10519m(null);
                    return;
                }
                if (str2.length() > 1) {
                    String[] strArrSplit = str2.trim().split(StringUtils.SPACE);
                    String str3 = strArrSplit[strArrSplit.length - 1];
                    String str4 = "";
                    for (int i2 = 0; i2 < strArrSplit.length - 1; i2++) {
                        str4 = str4 + StringUtils.SPACE + strArrSplit[i2];
                    }
                    searchFragment.this.f88799s4.setTag(str4.trim());
                    CompressHelper compressHelper = searchFragment.this.f90824P4;
                    compressHelper.m71839c0(compressHelper.m71770A(), "Select rowid as _id,word from spell where word match '" + str3 + "*'").m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<ArrayList<Bundle>>() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.7.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(ArrayList<Bundle> arrayList) throws Throwable {
                            searchFragment.this.f88799s4.getSuggestionsAdapter().mo10519m(searchFragment.this.f90824P4.m71850h(arrayList));
                        }
                    });
                }
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onComplete() {
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onError(@NonNull Throwable th) {
            }
        });
        m15366r().setTitle("");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View view = this.f88797q4;
        if (view != null) {
            return view;
        }
        this.f90824P4 = new CompressHelper(m15366r());
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_search, viewGroup, false);
        this.f88797q4 = viewInflate;
        if (bundle != null && bundle.containsKey("Position")) {
            this.f88785e4 = bundle.getInt("Position");
        }
        if (bundle != null && bundle.containsKey("Query")) {
            this.f88786f4 = bundle.getString("Query");
        }
        if (bundle != null && bundle.containsKey("mIsSubmitted")) {
            this.f90814F4 = bundle.getBoolean("mIsSubmitted");
        }
        this.f90822N4 = new ArrayList<>();
        this.f90812D4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f90811C4 = new ArrayList<>();
        if (bundle != null && bundle.containsKey("mResults")) {
            this.f90811C4 = bundle.getParcelableArrayList("mResults");
        }
        SearchAdapter searchAdapter = new SearchAdapter(m15366r());
        this.f90809A4 = searchAdapter;
        this.f90820L4 = new StickyRecyclerHeadersDecoration(searchAdapter, new ItemVisibilityAdapter() { // from class: net.imedicaldoctor.imd.Fragments.searchFragment.1
            @Override // com.timehop.stickyheadersrecyclerview.ItemVisibilityAdapter
            /* renamed from: a */
            public boolean mo58199a(int i2) {
                searchFragment searchfragment = searchFragment.this;
                searchfragment.f90821M4 = (LinearLayoutManager) searchfragment.f90812D4.getLayoutManager();
                searchFragment.this.f90821M4.m27176B2();
                searchFragment.this.f90821M4.m27178E2();
                boolean z = searchFragment.this.f90821M4.m27176B2() <= i2 && searchFragment.this.f90821M4.m27178E2() >= i2;
                iMDLogger.m73550f(CSS.Property.f74043m0, i2 + " visible + " + Boolean.valueOf(z));
                return z;
            }
        });
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(m15366r());
        this.f90821M4 = linearLayoutManager;
        this.f90812D4.setLayoutManager(linearLayoutManager);
        this.f90812D4.setItemAnimator(new DefaultItemAnimator());
        this.f90812D4.m27459p(new CustomItemDecoration(m15366r()));
        m15358o2(true);
        mo72473f3("Search Titles");
        return viewInflate;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: e3 */
    public void mo72472e3() {
        RecyclerView.Adapter adapter = this.f90812D4.getAdapter();
        SearchAdapter searchAdapter = this.f90809A4;
        if (adapter == searchAdapter) {
            searchAdapter.m27491G();
        } else {
            this.f90820L4.m58207n();
            this.f90812D4.setAdapter(this.f90809A4);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: f3 */
    public void mo72473f3(String str) {
        try {
            if (!str.equals("Searching")) {
                this.f90820L4.m58207n();
                m73365B3();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        this.f90812D4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        this.f90812D4.setAdapter(new StatusAdapter(m15366r(), str));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: g1 */
    public void mo15335g1() {
        super.mo15335g1();
    }

    /* renamed from: i3 */
    public int m73368i3(int i2, ArrayList<Bundle> arrayList) {
        int size = 0;
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            Bundle bundle = arrayList.get(i3);
            size += this.f90822N4.contains(bundle.getBundle("database").getString("Name")) ? 1 : bundle.getParcelableArrayList("items").size();
            if (i2 < size) {
                return i3;
            }
        }
        return 0;
    }

    /* renamed from: j3 */
    public Bundle m73369j3(int i2, ArrayList<Bundle> arrayList) {
        Iterator<Bundle> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String string = next.getBundle("database").getString("Name");
            int size = this.f90822N4.contains(string) ? 1 : next.getParcelableArrayList("items").size();
            i3 += size;
            if (i2 < i3) {
                int i4 = i2 - (i3 - size);
                Bundle bundle = new Bundle();
                bundle.putBundle("Database", next.getBundle("database"));
                if (this.f90822N4.contains(string)) {
                    if (i4 == 0) {
                        return bundle;
                    }
                    i4--;
                }
                bundle.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(i4));
                return bundle;
            }
        }
        return null;
    }

    /* renamed from: k3 */
    public ArrayList<Bundle> m73370k3(Bundle bundle, String str) {
        CompressHelper compressHelper;
        String str2;
        CompressHelper compressHelper2;
        String str3;
        CompressHelper compressHelper3;
        String str4;
        String strReplace = str.replace("'", "''");
        String string = bundle.getString("Type");
        try {
            if (!string.equals("lexi")) {
                if (string.equals("skyscape")) {
                    compressHelper = this.f90824P4;
                    str2 = "Select id as id, indexName as text,indexType as subText,section  from search where indexName match '" + strReplace + "'";
                } else if (string.equals("medhand")) {
                    compressHelper3 = this.f90824P4;
                    str4 = "select Text as text, \"table\" as subText, URL from search where text match '" + strReplace + "'";
                } else {
                    if (string.equals("infopoems")) {
                        return null;
                    }
                    if (string.equals("irandarou")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select drugId, drug as text, '' as subText from Search where drug match '" + strReplace + "' order by drug asc";
                    } else if (string.equals("uptodateddx")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select id, diagnosis as text, isMain from search where diagnosis match '" + strReplace + "' order by isMain desc";
                    } else if (string.equals("labvalues")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select id,shortName, longName, subtitle, value, shortName as text, subtitle as subText from search where search match 'shortName:" + strReplace + " OR longName:" + strReplace + "'";
                    } else if (string.equals("visualdx")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select id,dName as text,'' as subText from DiagnosesSearch where dNameSearch match '" + strReplace + "' order by dName asc";
                    } else if (string.equals("uptodate")) {
                        compressHelper3 = this.f90824P4;
                        str4 = "select Text as text, \"table\" as subText,URL, related_topic from search where search match 'text:" + strReplace + " OR subText:" + strReplace + "'";
                    } else if (string.equals("accessmedicine")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text, typeText as subText, type, contentId from Search where search match 'text:" + strReplace + " NOT type:5'";
                    } else if (string.equals("lww")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text, typeText as subText, type, contentId from Search where search match 'text:" + strReplace + " NOT (type:5 OR type:0)'";
                    } else if (string.equals("elsevier") || string.equals("elseviernew")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text, typeText as subText, type, contentId from Search where search match 'text:" + strReplace + " NOT type:5'";
                    } else if (string.equals("ovid")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text, typeText as subText, type, contentId from Search where search match 'text:" + strReplace + " NOT type:5 NOT type:0'";
                    } else if (string.equals("epub")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text, typeText as subText, type, contentId, section from Search where search match 'text:" + strReplace + " NOT type:5'";
                    } else if (string.equals("nejm")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text, typeText as subText, type, contentId, section from Search where search match 'text:" + strReplace + " AND type:1'";
                    } else if (string.equals("epocrate")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text, typeText as subText,typeText, type, contentId from Search where search match 'text:" + strReplace + " NOT type:5'";
                    } else if (string.equals("amirsys")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text  || ' - ' || content as text, typeText as subText, type, contentId from Search where search match 'text:" + strReplace + " AND type:1' order by type asc";
                    } else if (string.equals("statdx")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select text  as text,content as subText, typeText, type, contentId from Search where search match '(text:" + strReplace + "*) AND (type:1 OR type:3)' order by type asc";
                    } else if (string.equals("martindale")) {
                        compressHelper = this.f90824P4;
                        str2 = "Select distinct(text)  as text,content as subText, typeText, type, contentId from Search where search match '(text:" + strReplace + "*) AND (type:1)' ";
                    } else {
                        if (!string.equals("facts")) {
                            if (string.equals("micromedex-drug")) {
                                compressHelper2 = this.f90824P4;
                                str3 = "Select text, content as subText, type, contentId from Search where search match 'text:" + strReplace + " NOT type:5'";
                            } else if (string.equals("micromedex-neofax")) {
                                compressHelper2 = this.f90824P4;
                                str3 = "Select text, typeText as subText, typeText, type, contentId from Search where search match 'text:" + strReplace + " NOT type:5'";
                            } else {
                                if (string.equals("sanford")) {
                                    return this.f90824P4.m71819W(bundle, "Select title as text, '' as subText,path as contentId from Search_base where Search_base match 'title:" + strReplace + " OR subject:" + strReplace + "'", "fts.db");
                                }
                                if (string.equals("noskhe")) {
                                    compressHelper = this.f90824P4;
                                    str2 = "Select text  as text,content as subText, typeText, type, contentId from Search where search match '(text:" + strReplace + "*) AND (type:1)'";
                                } else if (string.equals("stockley")) {
                                    compressHelper = this.f90824P4;
                                    str2 = "Select text  as text,content as subText, typeText, type, contentId from Search where search match '(text:" + strReplace + "*) AND (type:1)'";
                                } else {
                                    if (!string.equals("mksap")) {
                                        if (!string.equals("cme")) {
                                            return null;
                                        }
                                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strReplace, StringUtils.SPACE);
                                        ArrayList arrayList = new ArrayList();
                                        for (String str5 : strArrSplitByWholeSeparator) {
                                            arrayList.add("title like '%" + str5 + "%'");
                                        }
                                        return this.f90824P4.m71817V(bundle, "SELECT medias.*, medias.title AS text, logs.position, logs.vDate, logs.duration AS dur, REPLACE(REPLACE(COALESCE(toc5.name || ' / ', '') || COALESCE(toc4.name || ' / ', '') || COALESCE(toc3.name || ' / ', '') || COALESCE(toc2.name || ' / ', '') || toc1.name, 'Videos / ', ''), 'Audios / ', '') AS subText FROM medias LEFT OUTER JOIN logs ON medias.id = logs.id LEFT OUTER JOIN TOC toc1 ON medias.tocId = toc1.id LEFT OUTER JOIN TOC toc2 ON toc1.parentId = toc2.id LEFT OUTER JOIN TOC toc3 ON toc2.parentId = toc3.id LEFT OUTER JOIN TOC toc4 ON toc3.parentId = toc4.id LEFT OUTER JOIN TOC toc5 ON toc4.parentId = toc5.id WHERE " + StringUtils.join(arrayList, " AND "));
                                    }
                                    compressHelper = this.f90824P4;
                                    str2 = "Select text  as text,content as subText, typeText, type, contentId from Search where search match '(text:" + strReplace + "*) AND (type:1) AND (typeText:Topic OR typeText:Question)'";
                                }
                            }
                            return compressHelper2.m71819W(bundle, str3, "fsearch.sqlite");
                        }
                        compressHelper = this.f90824P4;
                        str2 = "Select text  as text,content as subText, typeText, type, contentId from Search where search match '(text:" + strReplace + "*) AND (type:1)'";
                    }
                }
                return compressHelper.m71817V(bundle, str2);
            }
            if (!new File(CompressHelper.m71753g1(bundle, "fsearch.db")).exists()) {
                return null;
            }
            compressHelper3 = this.f90824P4;
            str4 = "Select id, name as text, type as subText from search where name match '" + strReplace + "'";
            return compressHelper3.m71819W(bundle, str4, "fsearch.db");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            iMDLogger.m73550f("SearchInDB", "Error in searching " + bundle.getString("Title") + " : " + e2);
            return null;
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: l1 */
    public void mo15352l1() {
        super.mo15352l1();
        m72468V2();
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    /* renamed from: y3 */
    public void m73371y3() {
        if (this.f90810B4) {
            return;
        }
        this.f90812D4.m27459p(this.f90820L4);
        this.f90810B4 = true;
    }

    /* renamed from: z3 */
    public void m73372z3() {
        Bundle bundle;
        this.f90822N4 = new ArrayList<>();
        Iterator<Bundle> it2 = this.f90811C4.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (next != null && (bundle = next.getBundle("database")) != null) {
                this.f90822N4.add(bundle.getString("Name"));
            }
        }
        this.f90812D4.getAdapter().m27491G();
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.os.Bundle;
import android.view.MenuItem;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class payActivity extends iMDActivity {
    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new payActivityFragment());
        setContentView(C5562R.layout.activity_pay);
        if (bundle == null) {
            payActivityFragment payactivityfragment = new payActivityFragment();
            payactivityfragment.m15342i2(getIntent().getExtras());
            m15416k0().m15664u().m15827o("something").m15818f(C5562R.id.container, payactivityfragment).mo15164r();
        }
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == 16908332) {
            finish();
        }
        return super.onOptionsItemSelected(menuItem);
    }
}

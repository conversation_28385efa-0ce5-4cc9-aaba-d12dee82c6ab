package net.imedicaldoctor.imd.Fragments.Amirsys;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridView;
import android.widget.TextView;
import androidx.palette.graphics.Palette;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.pipeline.end.PdfWriterPipeline;
import java.io.File;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.GridAutoFitLayoutManager;
import net.imedicaldoctor.imd.ViewHolders.ImageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class ASMenuActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f87525X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f87526Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f87527Z4;

    /* renamed from: a5 */
    public DiagnosisAdapter f87528a5;

    /* renamed from: b5 */
    public Bundle f87529b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f87530c5;

    /* renamed from: d5 */
    private Bundle f87531d5;

    /* renamed from: e5 */
    public String f87532e5;

    /* renamed from: f5 */
    private boolean f87533f5;

    public class DiagnosisAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public Context f87540d;

        public DiagnosisAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            String string = m71983d0(i2).getString("Type");
            if (string.equals("Header")) {
                return 0;
            }
            if (string.equals("GridView")) {
                return 1;
            }
            return string.equals("Item") ? 2 : -1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            Bundle bundleM71983d0 = m71983d0(i2);
            int iM27811F = viewHolder.m27811F();
            if (iM27811F == 0) {
                ((HeaderCellViewHolder) viewHolder).f87556I.setText(bundleM71983d0.getString("Text"));
                return;
            }
            if (iM27811F == 1) {
                RecyclerViewViewHolder recyclerViewViewHolder = (RecyclerViewViewHolder) viewHolder;
                final ChaptersAdapter chaptersAdapter = new ChaptersAdapter(ASMenuActivityFragment.this.m15366r(), ASMenuActivityFragment.this.f87526Y4, "title", C5562R.layout.list_view_item_image) { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.DiagnosisAdapter.1
                    @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                    /* renamed from: e0 */
                    public void mo71985e0(RecyclerView.ViewHolder viewHolder2, final Bundle bundle, int i3) {
                        final ImageViewHolder imageViewHolder = (ImageViewHolder) viewHolder2;
                        final String strM71754h1 = CompressHelper.m71754h1(ASMenuActivityFragment.this.f89566D4, bundle.getString("id") + ".jpg", "small");
                        Glide.m30041G(ASMenuActivityFragment.this.m15366r()).mo30124i(new File(strM71754h1)).m30165B2(imageViewHolder.f101461I);
                        if (ASMenuActivityFragment.this.f87533f5) {
                            if (ASMenuActivityFragment.this.f87531d5.containsKey(strM71754h1)) {
                                imageViewHolder.f101462J.setRippleColor(ASMenuActivityFragment.this.f87531d5.getInt(strM71754h1));
                            } else {
                                ASMenuActivityFragment.this.m72832q3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.DiagnosisAdapter.1.1
                                    @Override // java.lang.Runnable
                                    public void run() {
                                        Palette.Swatch swatchM26489C = Palette.m26478b(BitmapFactory.decodeFile(strM71754h1)).m26518g().m26489C();
                                        if (swatchM26489C == null) {
                                            return;
                                        }
                                        int iM26530e = swatchM26489C.m26530e();
                                        if (ASMenuActivityFragment.this.f87531d5.containsKey(strM71754h1)) {
                                            return;
                                        }
                                        ASMenuActivityFragment.this.f87531d5.putInt(strM71754h1, iM26530e);
                                    }
                                }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.DiagnosisAdapter.1.2
                                    @Override // java.lang.Runnable
                                    public void run() {
                                        imageViewHolder.f101462J.setRippleColor(ASMenuActivityFragment.this.f87531d5.getInt(strM71754h1));
                                    }
                                });
                            }
                        }
                        imageViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.DiagnosisAdapter.1.3
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                ASMenuActivityFragment.this.m71981M4(bundle.getString("id"));
                            }
                        });
                    }

                    @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                    /* renamed from: h0 */
                    public RecyclerView.ViewHolder mo71986h0(View view) {
                        return new ImageViewHolder(view);
                    }
                };
                recyclerViewViewHolder.f87557I.setAdapter(chaptersAdapter);
                final GridAutoFitLayoutManager gridAutoFitLayoutManager = new GridAutoFitLayoutManager(ASMenuActivityFragment.this.m15366r(), (int) (ASMenuActivityFragment.this.m15320b0().getDisplayMetrics().density * 100.0f));
                gridAutoFitLayoutManager.m27031R3(new GridLayoutManager.SpanSizeLookup() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.DiagnosisAdapter.2
                    @Override // androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
                    /* renamed from: f */
                    public int mo27056f(int i3) {
                        if (chaptersAdapter.mo26845C(i3) == 1) {
                            return gridAutoFitLayoutManager.f101458b0;
                        }
                        return 1;
                    }
                });
                recyclerViewViewHolder.f87557I.setLayoutManager(gridAutoFitLayoutManager);
                return;
            }
            if (iM27811F == 2) {
                RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                rippleTextViewHolder.f101515I.setText(bundleM71983d0.getBundle("Item").getString("fieldTitle"));
                rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.DiagnosisAdapter.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        Bundle bundle = DiagnosisAdapter.this.m71983d0(i2).getBundle("Item");
                        ASMenuActivityFragment aSMenuActivityFragment = ASMenuActivityFragment.this;
                        aSMenuActivityFragment.f89579Q4.m71772A1(aSMenuActivityFragment.f89566D4, "doc,,," + ASMenuActivityFragment.this.f87532e5, null, bundle.getString("fieldId"));
                    }
                });
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new HeaderCellViewHolder(LayoutInflater.from(ASMenuActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 1) {
                return new RecyclerViewViewHolder(LayoutInflater.from(ASMenuActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_recyclerview, viewGroup, false));
            }
            if (i2 == 2) {
                return new RippleTextViewHolder(LayoutInflater.from(ASMenuActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return ASMenuActivityFragment.this.f87530c5.size() + 3;
        }

        /* renamed from: d0 */
        public Bundle m71983d0(int i2) {
            String str;
            Bundle bundle = new Bundle();
            if (i2 == 0) {
                bundle.putString("Type", "Header");
                str = ASMenuActivityFragment.this.f87526Y4.size() + " IMAGES";
            } else if (i2 == 1) {
                bundle.putString("Type", "GridView");
                str = "";
            } else {
                if (i2 != 2) {
                    if (i2 > 2) {
                        bundle.putString("Type", "Item");
                        bundle.putBundle("Item", ASMenuActivityFragment.this.f87530c5.get(i2 - 3));
                    }
                    return bundle;
                }
                bundle.putString("Type", "Header");
                str = PdfWriterPipeline.f74582f;
            }
            bundle.putString("Text", str);
            return bundle;
        }

        /* renamed from: e0 */
        public void m71984e0(Bundle bundle, int i2) {
        }
    }

    public static class GridViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public GridView f87555I;

        public GridViewHolder(View view) {
            super(view);
            this.f87555I = (GridView) view.findViewById(C5562R.id.grid_view);
        }
    }

    public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f87556I;

        public HeaderCellViewHolder(View view) {
            super(view);
            this.f87556I = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    public static class RecyclerViewViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public RecyclerView f87557I;

        public RecyclerViewViewHolder(View view) {
            super(view);
            this.f87557I = (RecyclerView) view.findViewById(C5562R.id.recycler_view);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: M4 */
    public void m71981M4(String str) {
        int i2 = 0;
        int i3 = 0;
        while (true) {
            if (i3 >= this.f87527Z4.size()) {
                break;
            }
            if (this.f87527Z4.get(i3).getString("id").equals(str)) {
                i2 = i3;
                break;
            }
            i3++;
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", this.f87527Z4);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: L4 */
    public void m71982L4() {
        this.f87525X4.setItemAnimator(new DefaultItemAnimator());
        this.f87525X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f87525X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList = this.f87526Y4;
        if (arrayList == null || arrayList.size() <= 0) {
            return null;
        }
        Bundle bundleM72839v3 = m72839v3(this.f87526Y4);
        return CompressHelper.m71754h1(this.f89566D4, bundleM72839v3.getString("id") + ".jpg", "images-E");
    }

    /* JADX WARN: Removed duplicated region for block: B:38:0x01df  */
    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public android.view.View mo15303U0(android.view.LayoutInflater r7, android.view.ViewGroup r8, android.os.Bundle r9) throws android.content.res.Resources.NotFoundException {
        /*
            Method dump skipped, instructions count: 489
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.mo15303U0(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.4

            /* renamed from: a */
            byte[] f87538a;

            @Override // android.os.AsyncTask
            protected Object doInBackground(Object[] objArr) {
                try {
                    File file = new File(ASMenuActivityFragment.this.mo71955R2());
                    this.f87538a = new CompressHelper(ASMenuActivityFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127");
                    return null;
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                    return null;
                }
            }

            @Override // android.os.AsyncTask
            protected void onPostExecute(Object obj) {
                super.onPostExecute(obj);
                Glide.m30041G(ASMenuActivityFragment.this.m15366r()).mo30123h(this.f87538a).m30165B2(ASMenuActivityFragment.this.f89575M4);
            }
        }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
    }

    @Override // androidx.fragment.app.Fragment, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASMenuActivityFragment.3
            @Override // java.lang.Runnable
            public void run() {
                ASMenuActivityFragment.this.f87528a5.m27491G();
            }
        }, 500L);
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class EPOLabListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88123A4;

    /* renamed from: B4 */
    public String f88124B4;

    /* renamed from: C4 */
    public TabLayout f88125C4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        CompressHelper compressHelper;
        Bundle bundle2;
        String str;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_lab_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        this.f88125C4 = (TabLayout) this.f88797q4.findViewById(C5562R.id.tabs);
        String[] strArr = {"Specimen Type", "Panel Type"};
        for (int i2 = 0; i2 < 2; i2++) {
            TabLayout.Tab tabM40228I = this.f88125C4.m40228I();
            tabM40228I.m40276D(strArr[i2]);
            this.f88125C4.m40248i(tabM40228I);
        }
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            this.f88125C4.setVisibility(0);
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            this.f88124B4 = null;
            if (this.f88125C4.getSelectedTabPosition() == 0) {
                compressHelper = this.f88791k4;
                bundle2 = this.f88788h4;
                str = "Select * from lab_specimen";
            } else {
                compressHelper = this.f88791k4;
                bundle2 = this.f88788h4;
                str = "Select * from lab_panel";
            }
        } else {
            appBarLayout.m35746D(false, false);
            appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOLabListActivityFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    relativeLayout.setVisibility(0);
                }
            }, 800L);
            this.f88125C4.setVisibility(8);
            CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) this.f88803w4.getLayoutParams();
            layoutParams.setMargins(0, 0, 0, 0);
            this.f88803w4.setLayoutParams(layoutParams);
            this.f88124B4 = m15387y().getString("ParentId");
            compressHelper = this.f88791k4;
            bundle2 = this.f88788h4;
            str = "SELECT * FROM lab_topics where id in ( select topicid from lab_cats_topics where catId=" + this.f88124B4 + ")";
        }
        this.f88794n4 = compressHelper.m71817V(bundle2, str);
        this.f88125C4.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOLabListActivityFragment.2
            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: a */
            public void mo40255a(TabLayout.Tab tab) {
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: b */
            public void mo40256b(TabLayout.Tab tab) {
                EPOLabListActivityFragment ePOLabListActivityFragment;
                CompressHelper compressHelper2;
                Bundle bundle3;
                String str2;
                if (EPOLabListActivityFragment.this.f88125C4.getSelectedTabPosition() == 0) {
                    ePOLabListActivityFragment = EPOLabListActivityFragment.this;
                    compressHelper2 = ePOLabListActivityFragment.f88791k4;
                    bundle3 = ePOLabListActivityFragment.f88788h4;
                    str2 = "Select * from lab_specimen";
                } else {
                    ePOLabListActivityFragment = EPOLabListActivityFragment.this;
                    compressHelper2 = ePOLabListActivityFragment.f88791k4;
                    bundle3 = ePOLabListActivityFragment.f88788h4;
                    str2 = "Select * from lab_panel";
                }
                ePOLabListActivityFragment.f88794n4 = compressHelper2.m71817V(bundle3, str2);
                EPOLabListActivityFragment ePOLabListActivityFragment2 = EPOLabListActivityFragment.this;
                ((ChaptersAdapter) ePOLabListActivityFragment2.f88792l4).m73465g0(ePOLabListActivityFragment2.f88794n4);
                EPOLabListActivityFragment ePOLabListActivityFragment3 = EPOLabListActivityFragment.this;
                ePOLabListActivityFragment3.f88803w4.setAdapter(ePOLabListActivityFragment3.f88792l4);
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: c */
            public void mo40257c(TabLayout.Tab tab) {
            }
        });
        this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOLabListActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: f0 */
            public void mo71975f0(Bundle bundle3, int i3) {
                EPOLabListActivityFragment.this.m72468V2();
                EPOLabListActivityFragment ePOLabListActivityFragment = EPOLabListActivityFragment.this;
                if (ePOLabListActivityFragment.f88124B4 == null) {
                    Bundle bundle4 = new Bundle();
                    bundle4.putBundle("DB", EPOLabListActivityFragment.this.f88788h4);
                    bundle4.putString("ParentId", bundle3.getString("id"));
                    EPOLabListActivityFragment.this.f88791k4.m71798N(EPOLabListActivity.class, EPOLabListActivityFragment.class, bundle4);
                    return;
                }
                ePOLabListActivityFragment.f88791k4.m71772A1(ePOLabListActivityFragment.f88788h4, "lab-" + bundle3.getString("mId"), null, null);
            }
        };
        this.f88123A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOLabListActivityFragment.4
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: g0 */
            public void mo71976g0(Bundle bundle3, int i3) {
                EPOLabListActivityFragment.this.m72468V2();
                EPOLabListActivityFragment ePOLabListActivityFragment = EPOLabListActivityFragment.this;
                ePOLabListActivityFragment.f88791k4.m71772A1(ePOLabListActivityFragment.f88788h4, "lab-" + bundle3.getString("contentId"), null, null);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle3) {
                EPOLabListActivityFragment.this.m72468V2();
                EPOLabListActivityFragment.this.f88799s4.m2508k0(bundle3.getString("word"), true);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88123A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88123A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match 'text:" + str + "* AND typeText:Lab AND type:1'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: c3 */
    public void mo72171c3() {
        this.f88800t4.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.labs_icon));
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "Labs";
    }
}

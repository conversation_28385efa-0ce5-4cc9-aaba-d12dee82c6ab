package net.imedicaldoctor.imd.Fragments.VisualDDX;

import android.content.Context;
import android.database.Cursor;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TabHost;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDialogListInterface;
import net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxFindingsDialog;
import net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxKeyQuestionsDialog;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class VDDxBuilderActivity extends ViewerHelperActivity {

    public static class VDDXBuilderFragment extends ViewerHelperFragment implements VDDialogListInterface {

        /* renamed from: X4 */
        private boolean f89741X4;

        /* renamed from: Y4 */
        private Bundle f89742Y4;

        /* renamed from: Z4 */
        private int f89743Z4;

        /* renamed from: a5 */
        private ArrayList<Bundle> f89744a5;

        /* renamed from: b5 */
        private Bundle f89745b5;

        /* renamed from: c5 */
        private ArrayList<String> f89746c5;

        /* renamed from: d5 */
        private ArrayList<Bundle> f89747d5;

        /* renamed from: e5 */
        private ArrayList<Bundle> f89748e5;

        /* renamed from: f5 */
        private ArrayList<String> f89749f5;

        /* renamed from: g5 */
        private ArrayList<String> f89750g5;

        /* renamed from: h5 */
        private ArrayList<String> f89751h5;

        /* renamed from: i5 */
        private int f89752i5;

        /* renamed from: j5 */
        private ListView f89753j5;

        /* renamed from: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity$VDDXBuilderFragment$1 */
        class C52461 extends BaseAdapter {
            C52461() {
            }

            @Override // android.widget.BaseAdapter, android.widget.ListAdapter
            public boolean areAllItemsEnabled() {
                return false;
            }

            @Override // android.widget.Adapter
            public int getCount() {
                VDDXBuilderFragment vDDXBuilderFragment = VDDXBuilderFragment.this;
                return vDDXBuilderFragment.m72890b5(vDDXBuilderFragment.f89746c5);
            }

            @Override // android.widget.Adapter
            public Object getItem(int i2) {
                VDDXBuilderFragment vDDXBuilderFragment = VDDXBuilderFragment.this;
                return vDDXBuilderFragment.m72884I4(i2, vDDXBuilderFragment.f89746c5);
            }

            @Override // android.widget.Adapter
            public long getItemId(int i2) {
                return 0L;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getItemViewType(int i2) {
                Bundle bundle = (Bundle) getItem(i2);
                String string = bundle.getString("Type");
                if (!string.equals("Header") && string.equals("Item")) {
                    String string2 = bundle.getString("Section");
                    int i3 = bundle.getInt("Index");
                    if (string2.equals("Visual Findings")) {
                        if (i3 == 0) {
                            return 1;
                        }
                        if (i3 == 1) {
                            return 2;
                        }
                    } else {
                        if (string2.equals("Body Location Findings")) {
                            return i3 == 0 ? 3 : 4;
                        }
                        if (string2.equals("Distribution Findings")) {
                            return 5;
                        }
                        if (string2.equals("Key Questions")) {
                            return i3 == 0 ? 3 : 4;
                        }
                        if (string2.equals("Add Other Findings")) {
                            return i3 == 0 ? 3 : 4;
                        }
                    }
                }
                return 0;
            }

            @Override // android.widget.Adapter
            public View getView(int i2, View view, ViewGroup viewGroup) {
                TextView textView;
                View.OnClickListener onClickListener;
                TextView textView2;
                String str;
                ArrayList arrayList;
                int size;
                RecyclerView.Adapter adapter;
                Bundle bundle = (Bundle) getItem(i2);
                if (bundle.getString("Type").equals("Header")) {
                    if (view == null) {
                        view = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false);
                        view.setTag(view.findViewById(C5562R.id.header_text));
                    }
                    ((TextView) view.getTag()).setText(bundle.getString("Text"));
                    return view;
                }
                if (!bundle.getString("Type").equals("Item")) {
                    return view;
                }
                String string = bundle.getString("Section");
                final int i3 = bundle.getInt("Index");
                if (string.equals("Visual Findings")) {
                    if (view != null) {
                        ((RecyclerView) view.getTag()).getAdapter().m27491G();
                        return view;
                    }
                    View viewInflate = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_recycleview, viewGroup, false);
                    viewInflate.setTag(viewInflate.findViewById(C5562R.id.recycler_view));
                    final RecyclerView recyclerView = (RecyclerView) viewInflate.getTag();
                    recyclerView.setLayoutManager(new LinearLayoutManager(VDDXBuilderFragment.this.m15366r(), 0, false));
                    recyclerView.setItemAnimator(new DefaultItemAnimator());
                    recyclerView.setOnTouchListener(new View.OnTouchListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.1
                        @Override // android.view.View.OnTouchListener
                        public boolean onTouch(View view2, MotionEvent motionEvent) {
                            VDDXBuilderFragment.this.f89753j5.requestDisallowInterceptTouchEvent(true);
                            if (motionEvent.getActionMasked() == 1) {
                                VDDXBuilderFragment.this.f89753j5.requestDisallowInterceptTouchEvent(false);
                            }
                            return false;
                        }
                    });
                    if (i3 == 0) {
                        adapter = new RecyclerView.Adapter() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.2
                            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                            /* renamed from: R */
                            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i4) {
                                PhotoCaptionPlaceHolder photoCaptionPlaceHolder = (PhotoCaptionPlaceHolder) viewHolder;
                                final Bundle bundle2 = (Bundle) VDDXBuilderFragment.this.f89742Y4.getParcelableArrayList("visualFindingIconImageList").get(i4);
                                String strM71753g1 = CompressHelper.m71753g1(VDDXBuilderFragment.this.f89566D4, bundle2.getString("imageName"));
                                String strM71753g12 = CompressHelper.m71753g1(VDDXBuilderFragment.this.f89566D4, bundle2.getString("mouseoverImageName"));
                                photoCaptionPlaceHolder.f89787I.setText(VDDXBuilderFragment.this.f89745b5.getString(bundle2.getString("findingId")));
                                if (VDDXBuilderFragment.this.f89751h5.contains(bundle2.getString("findingId"))) {
                                    photoCaptionPlaceHolder.f89789K.setVisibility(0);
                                } else {
                                    photoCaptionPlaceHolder.f89789K.setVisibility(8);
                                }
                                AnimationDrawable animationDrawable = new AnimationDrawable();
                                animationDrawable.addFrame(new BitmapDrawable(BitmapFactory.decodeFile(strM71753g1)), 1000);
                                animationDrawable.addFrame(new BitmapDrawable(BitmapFactory.decodeFile(strM71753g12)), 1000);
                                animationDrawable.setOneShot(false);
                                photoCaptionPlaceHolder.f89788J.setImageDrawable(animationDrawable);
                                photoCaptionPlaceHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.2.1
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view2) {
                                        int i5 = i4;
                                        if (i5 != VDDXBuilderFragment.this.f89752i5) {
                                            VDDXBuilderFragment.this.f89752i5 = i5;
                                            if (bundle2.getParcelableArrayList("childIcons").size() > 0) {
                                                VDDXBuilderFragment.this.f89743Z4 = 2;
                                                ((BaseAdapter) VDDXBuilderFragment.this.f89753j5.getAdapter()).notifyDataSetChanged();
                                                return;
                                            } else {
                                                VDDXBuilderFragment.this.f89743Z4 = 1;
                                                ((BaseAdapter) VDDXBuilderFragment.this.f89753j5.getAdapter()).notifyDataSetChanged();
                                            }
                                        }
                                        String string2 = bundle2.getString("findingId");
                                        if (VDDXBuilderFragment.this.f89751h5.contains(string2)) {
                                            VDDXBuilderFragment.this.f89751h5.remove(string2);
                                        } else {
                                            VDDXBuilderFragment.this.f89751h5.add(string2);
                                        }
                                        recyclerView.getAdapter().m27492H(i4);
                                    }
                                });
                            }

                            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                            /* renamed from: T */
                            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup2, int i4) {
                                return new PhotoCaptionPlaceHolder(LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.grid_view_item_image_caption_checkmark, viewGroup2, false));
                            }

                            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                            /* renamed from: b */
                            public int mo26171b() {
                                return VDDXBuilderFragment.this.f89742Y4.getParcelableArrayList("visualFindingIconImageList").size();
                            }
                        };
                    } else {
                        if (i3 != 1) {
                            return viewInflate;
                        }
                        adapter = new RecyclerView.Adapter() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.3
                            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                            /* renamed from: R */
                            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i4) {
                                PhotoCaptionPlaceHolder photoCaptionPlaceHolder = (PhotoCaptionPlaceHolder) viewHolder;
                                final Bundle bundle2 = (Bundle) ((Bundle) VDDXBuilderFragment.this.f89742Y4.getParcelableArrayList("visualFindingIconImageList").get(VDDXBuilderFragment.this.f89752i5)).getParcelableArrayList("childIcons").get(i4);
                                String strM71753g1 = CompressHelper.m71753g1(VDDXBuilderFragment.this.f89566D4, bundle2.getString("imageName"));
                                String strM71753g12 = CompressHelper.m71753g1(VDDXBuilderFragment.this.f89566D4, bundle2.getString("mouseoverImageName"));
                                photoCaptionPlaceHolder.f89787I.setText(VDDXBuilderFragment.this.f89745b5.getString(bundle2.getString("findingId")));
                                if (VDDXBuilderFragment.this.f89751h5.contains(bundle2.getString("findingId"))) {
                                    photoCaptionPlaceHolder.f89789K.setVisibility(0);
                                } else {
                                    photoCaptionPlaceHolder.f89789K.setVisibility(8);
                                }
                                AnimationDrawable animationDrawable = new AnimationDrawable();
                                animationDrawable.addFrame(new BitmapDrawable(BitmapFactory.decodeFile(strM71753g1)), 1000);
                                animationDrawable.addFrame(new BitmapDrawable(BitmapFactory.decodeFile(strM71753g12)), 1000);
                                animationDrawable.setOneShot(false);
                                photoCaptionPlaceHolder.f89788J.setImageDrawable(animationDrawable);
                                photoCaptionPlaceHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.3.1
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view2) {
                                        String string2 = bundle2.getString("findingId");
                                        if (VDDXBuilderFragment.this.f89751h5.contains(string2)) {
                                            VDDXBuilderFragment.this.f89751h5.remove(string2);
                                        } else {
                                            VDDXBuilderFragment.this.f89751h5.add(string2);
                                        }
                                        recyclerView.getAdapter().m27492H(i4);
                                    }
                                });
                            }

                            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                            /* renamed from: T */
                            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup2, int i4) {
                                return new PhotoCaptionPlaceHolder(LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.grid_view_item_image_caption_checkmark, viewGroup2, false));
                            }

                            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                            /* renamed from: b */
                            public int mo26171b() {
                                return ((Bundle) VDDXBuilderFragment.this.f89742Y4.getParcelableArrayList("visualFindingIconImageList").get(VDDXBuilderFragment.this.f89752i5)).getParcelableArrayList("childIcons").size();
                            }
                        };
                    }
                    recyclerView.setAdapter(adapter);
                    return viewInflate;
                }
                if (string.equals("Body Location Findings")) {
                    if (i3 == 0) {
                        if (view == null) {
                            view = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_add, viewGroup, false);
                            view.setTag(view.findViewById(C5562R.id.text));
                        }
                        textView2 = (TextView) view.getTag();
                        str = "Add Body Location";
                        textView2.setText(str);
                        return view;
                    }
                    if (i3 <= VDDXBuilderFragment.this.f89747d5.size()) {
                        arrayList = VDDXBuilderFragment.this.f89747d5;
                        size = i3 - 1;
                    } else {
                        arrayList = VDDXBuilderFragment.this.f89748e5;
                        size = (i3 - VDDXBuilderFragment.this.f89747d5.size()) - 1;
                    }
                    Bundle bundle2 = (Bundle) arrayList.get(size);
                    String string2 = bundle2.getString("title");
                    if (string2.length() == 0) {
                        string2 = VDDXBuilderFragment.this.f89745b5.getString(bundle2.getStringArrayList("findingIds").get(0));
                    }
                    if (view == null) {
                        view = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_delete, viewGroup, false);
                        view.setTag(view.findViewById(C5562R.id.text));
                    }
                    textView = (TextView) view.findViewById(C5562R.id.delete_button);
                    ((TextView) view.getTag()).setText(string2);
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.4
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            ArrayList arrayList2;
                            int size2;
                            if (i3 <= VDDXBuilderFragment.this.f89747d5.size()) {
                                arrayList2 = VDDXBuilderFragment.this.f89747d5;
                                size2 = i3;
                            } else {
                                arrayList2 = VDDXBuilderFragment.this.f89748e5;
                                size2 = i3 - VDDXBuilderFragment.this.f89747d5.size();
                            }
                            arrayList2.remove(size2 - 1);
                            ((BaseAdapter) VDDXBuilderFragment.this.f89753j5.getAdapter()).notifyDataSetChanged();
                        }
                    };
                    textView.setOnClickListener(onClickListener);
                    return view;
                }
                if (string.equals("Distribution Findings")) {
                    if (view != null) {
                        ((RecyclerView) view.getTag()).getAdapter().m27491G();
                        return view;
                    }
                    View viewInflate2 = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_recycleview, viewGroup, false);
                    viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.recycler_view));
                    final RecyclerView recyclerView2 = (RecyclerView) viewInflate2.getTag();
                    recyclerView2.setLayoutManager(new LinearLayoutManager(VDDXBuilderFragment.this.m15366r(), 0, false));
                    recyclerView2.setItemAnimator(new DefaultItemAnimator());
                    recyclerView2.setOnTouchListener(new View.OnTouchListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.5
                        @Override // android.view.View.OnTouchListener
                        public boolean onTouch(View view2, MotionEvent motionEvent) {
                            VDDXBuilderFragment.this.f89753j5.requestDisallowInterceptTouchEvent(true);
                            if (motionEvent.getActionMasked() == 1) {
                                VDDXBuilderFragment.this.f89753j5.requestDisallowInterceptTouchEvent(false);
                            }
                            return false;
                        }
                    });
                    recyclerView2.setAdapter(new RecyclerView.Adapter() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.6
                        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                        /* renamed from: R */
                        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i4) {
                            PhotoCaptionPlaceHolder photoCaptionPlaceHolder = (PhotoCaptionPlaceHolder) viewHolder;
                            final Bundle bundle3 = (Bundle) VDDXBuilderFragment.this.f89742Y4.getParcelableArrayList("distributionFindingIconImageList").get(i4);
                            String strM71753g1 = CompressHelper.m71753g1(VDDXBuilderFragment.this.f89566D4, bundle3.getString("imageName"));
                            String strM71753g12 = CompressHelper.m71753g1(VDDXBuilderFragment.this.f89566D4, bundle3.getString("mouseoverImageName"));
                            photoCaptionPlaceHolder.f89787I.setText(VDDXBuilderFragment.this.f89745b5.getString(bundle3.getString("findingId")));
                            if (VDDXBuilderFragment.this.f89751h5.contains(bundle3.getString("findingId"))) {
                                photoCaptionPlaceHolder.f89789K.setVisibility(0);
                            } else {
                                photoCaptionPlaceHolder.f89789K.setVisibility(8);
                            }
                            AnimationDrawable animationDrawable = new AnimationDrawable();
                            animationDrawable.addFrame(new BitmapDrawable(BitmapFactory.decodeFile(strM71753g1)), 1000);
                            animationDrawable.addFrame(new BitmapDrawable(BitmapFactory.decodeFile(strM71753g12)), 1000);
                            animationDrawable.setOneShot(false);
                            photoCaptionPlaceHolder.f89788J.setImageDrawable(animationDrawable);
                            photoCaptionPlaceHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.6.1
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view2) {
                                    String string3 = bundle3.getString("findingId");
                                    if (VDDXBuilderFragment.this.f89751h5.contains(string3)) {
                                        VDDXBuilderFragment.this.f89751h5.remove(string3);
                                    } else {
                                        VDDXBuilderFragment.this.f89751h5.add(string3);
                                    }
                                    recyclerView2.getAdapter().m27492H(i4);
                                }
                            });
                        }

                        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                        /* renamed from: T */
                        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup2, int i4) {
                            return new PhotoCaptionPlaceHolder(LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.grid_view_item_image_caption_checkmark, viewGroup2, false));
                        }

                        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                        /* renamed from: b */
                        public int mo26171b() {
                            return VDDXBuilderFragment.this.f89742Y4.getParcelableArrayList("distributionFindingIconImageList").size();
                        }
                    });
                    return viewInflate2;
                }
                if (string.equals("Key Questions")) {
                    if (i3 == 0) {
                        if (view == null) {
                            view = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_add, viewGroup, false);
                            view.setTag(view.findViewById(C5562R.id.text));
                        }
                        textView2 = (TextView) view.getTag();
                        str = "Add Key Questions";
                        textView2.setText(str);
                        return view;
                    }
                    final String str2 = (String) VDDXBuilderFragment.this.f89749f5.get(i3 - 1);
                    String string3 = VDDXBuilderFragment.this.f89745b5.getString(str2);
                    if (view == null) {
                        view = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_delete, viewGroup, false);
                        view.setTag(view.findViewById(C5562R.id.text));
                    }
                    textView = (TextView) view.findViewById(C5562R.id.delete_button);
                    ((TextView) view.getTag()).setText(string3);
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.7
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            VDDXBuilderFragment.this.f89749f5.remove(str2);
                            ((BaseAdapter) VDDXBuilderFragment.this.f89753j5.getAdapter()).notifyDataSetChanged();
                        }
                    };
                } else {
                    if (!string.equals("Add Other Findings")) {
                        return view;
                    }
                    if (i3 == 0) {
                        if (view == null) {
                            view = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_add, viewGroup, false);
                            view.setTag(view.findViewById(C5562R.id.text));
                        }
                        ((TextView) view.getTag()).setText("Add Other Findings");
                        return view;
                    }
                    final String str3 = (String) VDDXBuilderFragment.this.f89750g5.get(i3 - 1);
                    String string4 = VDDXBuilderFragment.this.f89745b5.getString(str3);
                    if (view == null) {
                        view = LayoutInflater.from(VDDXBuilderFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_delete, viewGroup, false);
                        view.setTag(view.findViewById(C5562R.id.text));
                    }
                    textView = (TextView) view.findViewById(C5562R.id.delete_button);
                    ((TextView) view.getTag()).setText(string4);
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.1.8
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            VDDXBuilderFragment.this.f89750g5.remove(str3);
                            ((BaseAdapter) VDDXBuilderFragment.this.f89753j5.getAdapter()).notifyDataSetChanged();
                        }
                    };
                }
                textView.setOnClickListener(onClickListener);
                return view;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getViewTypeCount() {
                return 6;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public boolean hasStableIds() {
                return false;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public boolean isEmpty() {
                return false;
            }

            @Override // android.widget.BaseAdapter, android.widget.ListAdapter
            public boolean isEnabled(int i2) {
                Bundle bundle = (Bundle) getItem(i2);
                return bundle.getString("Type").equals("Item") && bundle.getInt("Index") == 0;
            }
        }

        public static class PhotoCaptionPlaceHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f89787I;

            /* renamed from: J */
            public ImageView f89788J;

            /* renamed from: K */
            public ImageView f89789K;

            public PhotoCaptionPlaceHolder(View view) {
                super(view);
                this.f89787I = (TextView) view.findViewById(C5562R.id.caption);
                this.f89788J = (ImageView) view.findViewById(C5562R.id.image_view);
                this.f89789K = (ImageView) view.findViewById(C5562R.id.checkmark);
            }
        }

        /* renamed from: I4 */
        public Bundle m72884I4(int i2, ArrayList<String> arrayList) {
            Iterator<String> it2 = arrayList.iterator();
            int i3 = 0;
            while (it2.hasNext()) {
                String next = it2.next();
                if (i2 == i3) {
                    Bundle bundle = new Bundle();
                    bundle.putString("Text", next);
                    bundle.putString("Type", "Header");
                    return bundle;
                }
                int iM72888Z4 = i3 + m72888Z4(next);
                if (i2 <= iM72888Z4) {
                    int iM72888Z42 = (i2 - (iM72888Z4 - m72888Z4(next))) - 1;
                    Bundle bundle2 = new Bundle();
                    bundle2.putString("Section", next);
                    bundle2.putInt("Index", iM72888Z42);
                    bundle2.putString("Type", "Item");
                    return bundle2;
                }
                i3 = iM72888Z4 + 1;
            }
            return null;
        }

        /* renamed from: J4 */
        public void m72885J4() {
            ListView listView = this.f89753j5;
            listView.setAdapter(listView.getAdapter());
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f89597r4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72886X4();
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_vddx_builder, viewGroup, false);
            if (bundle != null && bundle.containsKey("Restoring")) {
                this.f89584e4 = true;
                if (bundle.containsKey("Find")) {
                    this.f89585f4 = bundle.getString("Find");
                    this.f89594o4 = bundle.getInt("FindIndex");
                }
                if (bundle.containsKey("mFinalHTML")) {
                    this.f89563A4 = bundle.getString("mFinalHTML");
                }
                if (bundle.containsKey("mTitle")) {
                    this.f89568F4 = bundle.getString("mTitle");
                }
                this.f89744a5 = bundle.getParcelableArrayList("mAllFindings");
                this.f89742Y4 = bundle.getBundle("mModule");
                this.f89743Z4 = bundle.getInt("mGridViewCount");
                this.f89746c5 = bundle.getStringArrayList("mSections");
                this.f89748e5 = bundle.getParcelableArrayList("mDistributionSelectedItems");
                this.f89747d5 = bundle.getParcelableArrayList("mLocationSelectedItems");
                this.f89749f5 = bundle.getStringArrayList("mKeyQuestionSelectedItems");
                this.f89750g5 = bundle.getStringArrayList("mOtherFindingsSelectedItems");
                this.f89751h5 = bundle.getStringArrayList("mLesionsSelected");
                this.f89752i5 = bundle.getInt("mVisualIndex");
                this.f89745b5 = bundle.getBundle("mAllFindingsDic");
            }
            this.f89565C4 = viewInflate;
            this.f89566D4 = m15387y().getBundle("DB");
            this.f89567E4 = m15387y().getString("URL");
            TabHost tabHost = (TabHost) viewInflate.findViewById(C5562R.id.findtabhost);
            this.f89603x4 = tabHost;
            if (tabHost != null) {
                tabHost.setup();
            }
            this.f89753j5 = (ListView) viewInflate.findViewById(C5562R.id.list_view);
            if (m15387y() == null) {
                return viewInflate;
            }
            try {
                this.f89579Q4 = new CompressHelper(m15366r());
                if (this.f89742Y4 == null) {
                    this.f89742Y4 = this.f89579Q4.m71784G(new JSONObject(CompressHelper.m71750e2(new File(CompressHelper.m71754h1(this.f89566D4, this.f89567E4 + ".JS", "modules")))));
                    this.f89744a5 = this.f89579Q4.m71817V(this.f89566D4, "SELECT id, shortName, longName, children, leaf, ',' || supportedModules || ',' as modules FROM Findings where modules like '%," + this.f89567E4 + ",%'");
                    this.f89745b5 = new Bundle();
                    Iterator<Bundle> it2 = this.f89744a5.iterator();
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        this.f89745b5.putString(next.getString("id"), next.getString("shortName"));
                    }
                    this.f89743Z4 = 1;
                    ArrayList<String> arrayList = new ArrayList<>();
                    this.f89746c5 = arrayList;
                    arrayList.add("Visual Findings");
                    this.f89746c5.add("Body Location Findings");
                    if (this.f89742Y4.getParcelableArrayList("distributionFindingIconImageList").size() > 0) {
                        this.f89746c5.add("Distribution Findings");
                    }
                    if (this.f89742Y4.getParcelableArrayList("keyQuestionList").size() > 0) {
                        this.f89746c5.add("Key Questions");
                    }
                    this.f89746c5.add("Add Other Findings");
                    this.f89568F4 = "";
                    this.f89563A4 = "";
                    this.f89751h5 = new ArrayList<>();
                    this.f89747d5 = new ArrayList<>();
                    this.f89748e5 = new ArrayList<>();
                    this.f89749f5 = new ArrayList<>();
                    this.f89750g5 = new ArrayList<>();
                }
                this.f89753j5.setAdapter((ListAdapter) new C52461());
                this.f89753j5.setDivider(new ColorDrawable(Color.parseColor("#e5e5e5")));
                this.f89753j5.setDividerHeight(1);
                if (!this.f89579Q4.m71903x1()) {
                    m72827m4(this.f89568F4);
                }
                this.f89753j5.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.2
                    @Override // android.widget.AdapterView.OnItemClickListener
                    public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                        DialogFragment vDDxFindingsDialog;
                        Bundle bundle2;
                        Bundle bundle3 = (Bundle) adapterView.getItemAtPosition(i2);
                        if (bundle3.getString("Type").equals("Item")) {
                            String string = bundle3.getString("Section");
                            int i3 = bundle3.getInt("Index");
                            if (string.equals("Visual Findings")) {
                                return;
                            }
                            if (string.equals("Body Location Findings")) {
                                if (i3 != 0) {
                                    return;
                                }
                                vDDxFindingsDialog = new VDDxBodyPartDialog();
                                bundle2 = new Bundle();
                                bundle2.putBundle("db", VDDXBuilderFragment.this.f89566D4);
                                bundle2.putBundle("allFindings", VDDXBuilderFragment.this.f89745b5);
                                bundle2.putParcelableArrayList("distribution", VDDXBuilderFragment.this.f89748e5);
                                bundle2.putParcelableArrayList("location", VDDXBuilderFragment.this.f89747d5);
                                bundle2.putString("bodyFolder", VDDXBuilderFragment.this.m15387y().getBundle("moduleInfo").getString("bodyLocation"));
                            } else {
                                if (string.equals("Distribution Findings")) {
                                    return;
                                }
                                if (string.equals("Key Questions")) {
                                    if (i3 != 0) {
                                        return;
                                    }
                                    vDDxFindingsDialog = new VDDxKeyQuestionsDialog();
                                    bundle2 = new Bundle();
                                    bundle2.putBundle("db", VDDXBuilderFragment.this.f89566D4);
                                    bundle2.putBundle("allFindings", VDDXBuilderFragment.this.f89745b5);
                                    bundle2.putStringArrayList("selectedKeyQuestions", VDDXBuilderFragment.this.f89749f5);
                                    bundle2.putParcelableArrayList("allKeyQuestions", VDDXBuilderFragment.this.f89742Y4.getParcelableArrayList("keyQuestionList"));
                                } else {
                                    if (!string.equals("Add Other Findings") || i3 != 0) {
                                        return;
                                    }
                                    vDDxFindingsDialog = new VDDxFindingsDialog();
                                    bundle2 = new Bundle();
                                    bundle2.putBundle("db", VDDXBuilderFragment.this.f89566D4);
                                    bundle2.putBundle("allFindings", VDDXBuilderFragment.this.f89745b5);
                                    bundle2.putStringArrayList("selectedFindings", VDDXBuilderFragment.this.f89750g5);
                                    bundle2.putBundle("parent", new Bundle());
                                    bundle2.putString("moduleId", VDDXBuilderFragment.this.f89567E4);
                                    bundle2.putStringArrayList("disabledItems", VDDXBuilderFragment.this.m72887Y4());
                                }
                            }
                            vDDxFindingsDialog.m15342i2(bundle2);
                            vDDxFindingsDialog.mo15218Z2(true);
                            vDDxFindingsDialog.m15245A2(VDDXBuilderFragment.this, 0);
                            vDDxFindingsDialog.mo15222e3(VDDXBuilderFragment.this.m15283M(), "VDDialogFragment");
                        }
                    }
                });
                m15366r().setTitle(this.f89568F4);
                ((Button) this.f89565C4.findViewById(C5562R.id.ddx_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CompressHelper compressHelper;
                        Bundle bundle2;
                        String[] strArr;
                        String str;
                        String str2;
                        ArrayList<String> arrayListM72887Y4 = VDDXBuilderFragment.this.m72887Y4();
                        Bundle bundle3 = new Bundle();
                        bundle3.putString("moduleId", VDDXBuilderFragment.this.f89567E4);
                        if (arrayListM72887Y4.size() > 0) {
                            VDDXBuilderFragment vDDXBuilderFragment = VDDXBuilderFragment.this;
                            CompressHelper compressHelper2 = vDDXBuilderFragment.f89579Q4;
                            Bundle bundle4 = vDDXBuilderFragment.f89566D4;
                            strArr = null;
                            str = null;
                            compressHelper = compressHelper2;
                            bundle2 = bundle4;
                            str2 = "ddx-" + StringUtils.join(arrayListM72887Y4, ",");
                        } else {
                            VDDXBuilderFragment vDDXBuilderFragment2 = VDDXBuilderFragment.this;
                            compressHelper = vDDXBuilderFragment2.f89579Q4;
                            bundle2 = vDDXBuilderFragment2.f89566D4;
                            strArr = null;
                            str = null;
                            str2 = "ddx-";
                        }
                        compressHelper.m71775B1(bundle2, str2, strArr, str, bundle3);
                    }
                });
                m15358o2(false);
                Toolbar toolbar = (Toolbar) this.f89565C4.findViewById(C5562R.id.toolbar);
                this.f89574L4 = toolbar;
                toolbar.mo2678z(C5562R.menu.search);
                SearchView searchView = (SearchView) this.f89574L4.getMenu().findItem(C5562R.id.action_search).getActionView();
                this.f89574L4.setNavigationIcon(C5562R.drawable.back_icon_small);
                this.f89574L4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.4
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        if (!VDDXBuilderFragment.this.m15387y().containsKey("Dialog")) {
                            VDDXBuilderFragment.this.f89579Q4.m71821W1(false);
                            return;
                        }
                        try {
                            VDDXBuilderFragment.this.m15391z().m15664u().m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out).mo15144B(VDDXBuilderFragment.this).mo15164r();
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                            e2.printStackTrace();
                        }
                    }
                });
                this.f89597r4 = searchView;
                m72886X4();
                m72889a5();
                m72786G3();
                return viewInflate;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                m72779B4(e2);
                return viewInflate;
            }
        }

        /* renamed from: X4 */
        public void m72886X4() {
            if (Build.VERSION.SDK_INT >= 26) {
                this.f89597r4.setImportantForAutofill(8);
            }
            this.f89597r4.setIconifiedByDefault(false);
            this.f89597r4.setQueryHint("Add Findings");
            this.f89597r4.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.5
                @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
                /* renamed from: a */
                public boolean mo2516a(int i2) {
                    Cursor cursorMo10512c = VDDXBuilderFragment.this.f89597r4.getSuggestionsAdapter().mo10512c();
                    if (!cursorMo10512c.moveToPosition(i2)) {
                        return false;
                    }
                    String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("id"));
                    if (VDDXBuilderFragment.this.f89744a5.contains(string)) {
                        VDDXBuilderFragment.this.f89597r4.m2508k0("", true);
                        VDDXBuilderFragment.this.m72786G3();
                        return true;
                    }
                    VDDXBuilderFragment.this.f89750g5.add(string);
                    VDDXBuilderFragment.this.f89597r4.m2508k0("", true);
                    VDDXBuilderFragment.this.m72786G3();
                    ((BaseAdapter) VDDXBuilderFragment.this.f89753j5.getAdapter()).notifyDataSetChanged();
                    return false;
                }

                @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
                /* renamed from: b */
                public boolean mo2517b(int i2) {
                    mo2516a(i2);
                    return false;
                }
            });
            ((ImageView) this.f89597r4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.6
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    VDDXBuilderFragment.this.f89597r4.m2508k0("", false);
                    VDDXBuilderFragment.this.f89597r4.clearFocus();
                    VDDXBuilderFragment.this.m72786G3();
                }
            });
            this.f89597r4.setSuggestionsAdapter(new CursorAdapter(m15366r(), null, 0) { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.7
                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: e */
                public void mo2556e(View view, Context context, Cursor cursor) {
                    TextView textView = (TextView) view.getTag();
                    if (VDDXBuilderFragment.this.f89750g5.contains(cursor.getString(cursor.getColumnIndex("id")))) {
                        textView.setTextColor(-7829368);
                    }
                    textView.setText(cursor.getString(cursor.getColumnIndex("longName")));
                }

                @Override // android.widget.BaseAdapter, android.widget.ListAdapter
                public boolean isEnabled(int i2) {
                    Cursor cursor = (Cursor) getItem(i2);
                    return !VDDXBuilderFragment.this.f89750g5.contains(cursor.getString(cursor.getColumnIndex("id")));
                }

                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: j */
                public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                    View viewInflate = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell_drug, viewGroup, false);
                    viewInflate.setTag(viewInflate.findViewById(C5562R.id.text));
                    return viewInflate;
                }
            });
            this.f89597r4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.8
                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: a */
                public boolean mo2514a(final String str) {
                    if (str.length() <= 1) {
                        return false;
                    }
                    new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity.VDDXBuilderFragment.8.1
                        @Override // android.os.AsyncTask
                        protected Object doInBackground(Object[] objArr) {
                            String str2 = "Select rowid as _id,* from FindingsSearch where longNameSearch match '" + str + "*' order by longName asc";
                            VDDXBuilderFragment vDDXBuilderFragment = VDDXBuilderFragment.this;
                            return vDDXBuilderFragment.f89579Q4.m71817V(vDDXBuilderFragment.f89566D4, str2);
                        }

                        @Override // android.os.AsyncTask
                        protected void onPostExecute(Object obj) {
                            VDDXBuilderFragment.this.f89597r4.getSuggestionsAdapter().mo10519m(VDDXBuilderFragment.this.f89579Q4.m71850h((ArrayList) obj));
                        }
                    }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                    return true;
                }

                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: b */
                public boolean mo2515b(String str) {
                    return false;
                }
            });
            this.f89597r4.clearFocus();
        }

        /* renamed from: Y4 */
        public ArrayList<String> m72887Y4() {
            ArrayList<String> arrayList = new ArrayList<>();
            arrayList.addAll(this.f89751h5);
            Iterator<Bundle> it2 = this.f89747d5.iterator();
            while (it2.hasNext()) {
                arrayList.addAll(it2.next().getStringArrayList("findingIds"));
            }
            Iterator<Bundle> it3 = this.f89748e5.iterator();
            while (it3.hasNext()) {
                arrayList.addAll(it3.next().getStringArrayList("findingIds"));
            }
            arrayList.addAll(this.f89749f5);
            return arrayList;
        }

        /* renamed from: Z4 */
        public int m72888Z4(String str) {
            ArrayList<String> arrayList;
            int size;
            if (str.equals("Visual Findings")) {
                return this.f89743Z4;
            }
            if (str.equals("Body Location Findings")) {
                size = this.f89747d5.size() + this.f89748e5.size();
            } else {
                if (str.equals("Distribution Findings")) {
                    return 1;
                }
                if (str.equals("Key Questions")) {
                    arrayList = this.f89749f5;
                } else {
                    if (!str.equals("Add Other Findings")) {
                        return 0;
                    }
                    arrayList = this.f89750g5;
                }
                size = arrayList.size();
            }
            return size + 1;
        }

        /* renamed from: a5 */
        public void m72889a5() {
            ListView listView = (ListView) this.f89565C4.findViewById(C5562R.id.list_view);
            TextView textView = (TextView) this.f89565C4.findViewById(C5562R.id.status_label);
            LinearLayout linearLayout = (LinearLayout) this.f89565C4.findViewById(C5562R.id.status_layout);
            listView.setVisibility(0);
            textView.setVisibility(8);
            linearLayout.setVisibility(8);
        }

        /* renamed from: b5 */
        public int m72890b5(ArrayList<String> arrayList) {
            int iM72888Z4 = 0;
            if (arrayList == null) {
                return 0;
            }
            Iterator<String> it2 = arrayList.iterator();
            while (it2.hasNext()) {
                iM72888Z4 = iM72888Z4 + m72888Z4(it2.next()) + 1;
            }
            return iM72888Z4;
        }

        @Override // net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDialogListInterface
        /* renamed from: h */
        public void mo72891h(Bundle bundle, String str) {
            str.equals("Module");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: l1 */
        public void mo15352l1() {
            super.mo15352l1();
            m72786G3();
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new VDDXBuilderFragment(), bundle);
    }
}

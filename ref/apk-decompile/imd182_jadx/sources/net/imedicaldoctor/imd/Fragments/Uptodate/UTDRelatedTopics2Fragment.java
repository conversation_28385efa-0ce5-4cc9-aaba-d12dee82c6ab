package net.imedicaldoctor.imd.Fragments.Uptodate;

import android.app.Dialog;
import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class UTDRelatedTopics2Fragment extends DialogFragment {
    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_utdrelated_topics2, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        m15366r().setFinishOnTouchOutside(true);
        try {
            String string = m15387y().getString("RELATED");
            CompressHelper compressHelper = new CompressHelper(m15366r());
            listView.setAdapter((ListAdapter) new CursorAdapter(m15366r(), compressHelper.m71850h(compressHelper.m71819W(m15387y().getBundle("db"), "select topic_id as _id, title from topic where topic_id in (" + string + ")", "unidex.en.sqlite"))) { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDRelatedTopics2Fragment.1
                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: e */
                public void mo2556e(View view, Context context, Cursor cursor) {
                    ((TextView) view.findViewById(C5562R.id.title_text)).setText(cursor.getString(cursor.getColumnIndex("title")));
                }

                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: j */
                public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                    UTDRelatedTopics2Fragment.this.m15366r().getLayoutInflater();
                    return LayoutInflater.from(UTDRelatedTopics2Fragment.this.m15366r()).inflate(C5562R.layout.list_view_item_related_topics, viewGroup, false);
                }
            });
            listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDRelatedTopics2Fragment.2
                @Override // android.widget.AdapterView.OnItemClickListener
                public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                    Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                    iMDLogger.m73554j("relatedTopicsFragment", "clicked : " + cursorMo10512c.getString(cursorMo10512c.getColumnIndex("title")));
                    ((UTDGraphicActivity.UTDGraphicFragment) UTDRelatedTopics2Fragment.this.m15351l0()).m72721e5(cursorMo10512c.getString(cursorMo10512c.getColumnIndex("_id")));
                    UTDRelatedTopics2Fragment.this.mo15203M2();
                }
            });
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f(getClass().toString(), "Error in parsing related Topics " + e2);
        }
        builder.setView(viewInflate);
        return builder.create();
    }
}

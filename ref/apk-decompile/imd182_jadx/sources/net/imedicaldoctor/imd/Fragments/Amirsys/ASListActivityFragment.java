package net.imedicaldoctor.imd.Fragments.Amirsys;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class ASListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f87519A4;

    /* renamed from: B4 */
    public String f87520B4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String string;
        CompressHelper compressHelper;
        Bundle bundle2;
        String str;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            string = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASListActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            string = m15387y().getString("ParentId");
        }
        this.f87520B4 = string;
        if (this.f87520B4 == null) {
            compressHelper = this.f88791k4;
            bundle2 = this.f88788h4;
            str = "SELECT * FROM categories";
        } else {
            compressHelper = this.f88791k4;
            bundle2 = this.f88788h4;
            str = "select * from topics where id in (select topicId from cats_topics where catId = '" + this.f87520B4 + "') order by title collate nocase asc";
        }
        this.f88794n4 = compressHelper.m71817V(bundle2, str);
        this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASListActivityFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: f0 */
            public void mo71975f0(Bundle bundle3, int i2) {
                ASListActivityFragment.this.m71974i3(bundle3, i2);
            }
        };
        this.f87519A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", Annotation.f68283i3) { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASListActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: g0 */
            public void mo71976g0(Bundle bundle3, int i2) {
                ASListActivityFragment.this.m72468V2();
                ASListActivityFragment aSListActivityFragment = ASListActivityFragment.this;
                aSListActivityFragment.f88791k4.m71772A1(aSListActivityFragment.f88788h4, "menu,,," + bundle3.getString("contentId"), null, null);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle3) {
                ASListActivityFragment.this.m72468V2();
                ASListActivityFragment.this.f88799s4.m2508k0(bundle3.getString("word"), true);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f87519A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f87519A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match '(text:" + str + "* OR content:" + str + "*) AND type:1'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    /* renamed from: i3 */
    public void m71974i3(Bundle bundle, int i2) {
        m72468V2();
        if (this.f87520B4 == null) {
            Bundle bundle2 = new Bundle();
            bundle2.putBundle("DB", this.f88788h4);
            bundle2.putString("ParentId", bundle.getString("id"));
            this.f88791k4.m71798N(ASListActivity.class, ASListActivityFragment.class, bundle2);
            return;
        }
        this.f88791k4.m71772A1(this.f88788h4, "menu,,," + bundle.getString("id"), null, null);
    }
}

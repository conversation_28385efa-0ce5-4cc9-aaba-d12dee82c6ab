package net.imedicaldoctor.imd.Fragments.EPUB;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.JsResult;
import android.webkit.WebView;
import androidx.appcompat.app.AlertDialog;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class EPUBViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    private String f87987X4;

    /* renamed from: Y4 */
    private MenuItem f87988Y4;

    /* renamed from: Z4 */
    public ArrayList<String> f87989Z4;

    /* renamed from: a5 */
    public Bundle f87990a5;

    /* renamed from: b5 */
    public String f87991b5;

    /* renamed from: c5 */
    public String f87992c5;

    /* renamed from: d5 */
    public boolean f87993d5;

    /* renamed from: e5 */
    public long f87994e5;

    /* renamed from: J4 */
    private void m72156J4(String str) {
        ArrayList<String> arrayList = this.f87989Z4;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f87989Z4.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", next);
            try {
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(next, "/");
                String str2 = strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1];
                bundle.putString("Description", this.f87990a5.containsKey(str2) ? this.f87990a5.getString(str2) : "");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
            bundle.putString("id", next);
            if (new File(next).length() > 5000) {
                arrayList2.add(bundle);
            }
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: C3 */
    public void mo71967C3(String str) {
        this.f89569G4.m73433g("document.getElementById(\"" + str + "\").scrollIntoView(true);");
        this.f89569G4.m73433g("document.getElementsByName(\"" + str + "\")[0].scrollIntoView(true);");
    }

    /* renamed from: I4 */
    public String m72157I4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: J3 */
    public boolean mo72158J3(Context context) {
        if (this.f89566D4.getString("Name").equals("utdpathways.db")) {
            return false;
        }
        return super.mo72158J3(context);
    }

    /* renamed from: K4 */
    public void m72159K4(String str) {
        ArrayList arrayList = new ArrayList();
        Bundle bundle = new Bundle();
        bundle.putString("ImagePath", str);
        bundle.putString("isVideo", IcyHeaders.f28171a3);
        arrayList.add(bundle);
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList);
        intent.putExtra("Start", 0);
        mo15256D2(intent);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<String> arrayList = this.f87989Z4;
        if (arrayList == null || arrayList.size() <= 0) {
            return null;
        }
        return m72840w3(this.f87989Z4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(C5562R.menu.menu_epubviewer, menu);
        m72833q4(menu);
        mo71957e3(menu);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        this.f87990a5 = new Bundle();
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        m72835r4(viewInflate, bundle);
        if (bundle != null) {
            this.f87989Z4 = bundle.getStringArrayList("mImages");
            this.f87991b5 = bundle.getString("mBasePath");
            this.f87992c5 = bundle.getString("mPath");
        }
        if (m15387y() == null) {
            return viewInflate;
        }
        iMDLogger.m73554j("AMViewer", "Loading EPUB Document with mDocAddress = " + this.f89567E4);
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBViewerActivityFragment.1
            /* JADX WARN: Removed duplicated region for block: B:40:0x014e  */
            /* JADX WARN: Removed duplicated region for block: B:43:0x01ae A[Catch: Exception -> 0x0052, TryCatch #4 {Exception -> 0x0052, blocks: (B:3:0x000c, B:15:0x0045, B:17:0x004b, B:45:0x01ba, B:47:0x01c0, B:22:0x0055, B:24:0x0074, B:26:0x007b, B:38:0x0120, B:41:0x014f, B:43:0x01ae, B:44:0x01b2, B:37:0x0102, B:14:0x003e, B:5:0x0019, B:8:0x002b, B:9:0x0031, B:12:0x0037), top: B:59:0x000c, inners: #3 }] */
            /* JADX WARN: Removed duplicated region for block: B:47:0x01c0 A[Catch: Exception -> 0x0052, TRY_LEAVE, TryCatch #4 {Exception -> 0x0052, blocks: (B:3:0x000c, B:15:0x0045, B:17:0x004b, B:45:0x01ba, B:47:0x01c0, B:22:0x0055, B:24:0x0074, B:26:0x007b, B:38:0x0120, B:41:0x014f, B:43:0x01ae, B:44:0x01b2, B:37:0x0102, B:14:0x003e, B:5:0x0019, B:8:0x002b, B:9:0x0031, B:12:0x0037), top: B:59:0x000c, inners: #3 }] */
            /* JADX WARN: Removed duplicated region for block: B:60:? A[RETURN, SYNTHETIC] */
            @Override // java.lang.Runnable
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public void run() {
                /*
                    Method dump skipped, instructions count: 475
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.EPUB.EPUBViewerActivityFragment.RunnableC47811.run():void");
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = EPUBViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    EPUBViewerActivityFragment ePUBViewerActivityFragment = EPUBViewerActivityFragment.this;
                    ePUBViewerActivityFragment.m72780C4(ePUBViewerActivityFragment.f89595p4);
                    return;
                }
                EPUBViewerActivityFragment ePUBViewerActivityFragment2 = EPUBViewerActivityFragment.this;
                ePUBViewerActivityFragment2.m72795O3(ePUBViewerActivityFragment2.f89563A4, ePUBViewerActivityFragment2.f87991b5);
                EPUBViewerActivityFragment.this.m72836s4();
                EPUBViewerActivityFragment.this.m72831p4();
                EPUBViewerActivityFragment.this.mo72642f3(C5562R.menu.menu_epubviewer);
                EPUBViewerActivityFragment.this.m15358o2(false);
                EPUBViewerActivityFragment.this.m72786G3();
            }
        });
        return viewInflate;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                String strReplace = this.f87991b5.replace("file://", "");
                if (strReplace.endsWith("/")) {
                    strReplace = strReplace.substring(0, strReplace.length() - 1);
                }
                String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str, "/");
                for (String str2 : strArrSplitByWholeSeparator2) {
                    strReplace = str2.equals("..") ? m72157I4(strReplace) : strReplace + "/" + str2;
                }
                try {
                    if (this.f87993d5 && strArrSplitByWholeSeparator2.length > 0) {
                        String str3 = strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1];
                        CompressHelper compressHelper = this.f89579Q4;
                        Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(this.f89566D4, "Select * from images where imageName='" + str3 + "'"));
                        if (bundleM71907z != null) {
                            String string = bundleM71907z.getString("desc");
                            if (!this.f87990a5.containsKey(str3)) {
                                this.f87990a5.putString(str3, string);
                            }
                        }
                    }
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
                File file = new File(strReplace);
                file.length();
                if (file.length() > ExoPlayer.f21773a1) {
                    arrayList.add(strReplace);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strReplace);
            }
            this.f87989Z4 = arrayList;
            mo71972o4();
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: X3 */
    public boolean mo72160X3(WebView webView, String str, String str2, JsResult jsResult) {
        if (System.currentTimeMillis() - this.f87994e5 > 1000) {
            this.f87994e5 = System.currentTimeMillis();
            new AlertDialog.Builder(m15366r()).mo1102l(str2).mo1109s("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBViewerActivityFragment.3
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                    dialogInterface.dismiss();
                }
            }).m1090I();
        } else {
            Log.e("e-Anatomy", "too many alerts");
        }
        jsResult.cancel();
        return true;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        super.mo71956Z3(webView, str);
        this.f89569G4.m73433g("IgnoreSmallImages();");
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("fixAllImages2();");
        if (!this.f89566D4.getString("Name").contains("auntminni") && !this.f89566D4.getString("Name").contains("student-")) {
            this.f89569G4.m73433g("fixAllTables();");
        }
        this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_gallery) {
            m72156J4("soheilvb");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        this.f87988Y4 = menu.findItem(C5562R.id.action_gallery);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        String str4;
        String strReplace = str3;
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + strReplace);
        if (str2.equals("image")) {
            m72156J4(strReplace);
            return true;
        }
        if (str2.equals("utdpathway")) {
            ArrayList arrayList = new ArrayList(Collections2.m42365d(((iMD) m15366r().getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBViewerActivityFragment.4
                @Override // com.google.common.base.Predicate
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public boolean apply(Bundle bundle) {
                    return bundle.getString("Name").equals("uptodate");
                }
            }));
            if (arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "you must install Uptodate", 1);
                return false;
            }
            Bundle bundle = (Bundle) arrayList.get(0);
            strReplace = strReplace.replace(".html", "");
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strReplace, "-");
            if (strArrSplitByWholeSeparator.length < 3) {
                return false;
            }
            if (strArrSplitByWholeSeparator[1].equals("MED") || strArrSplitByWholeSeparator[1].equals("PAT") || strArrSplitByWholeSeparator[1].equals("Society_Guidelines") || strArrSplitByWholeSeparator[1].equals("DRG")) {
                String str5 = strArrSplitByWholeSeparator[3];
                if (str5.contains("#")) {
                    str5 = StringUtils.splitByWholeSeparator(str5, "#")[0];
                }
                this.f89579Q4.m71772A1(bundle, "Topic-" + strArrSplitByWholeSeparator[2], null, str5);
            } else if (strArrSplitByWholeSeparator[1].equals("Graphic")) {
                this.f89579Q4.m71772A1(bundle, "Graphic-" + strArrSplitByWholeSeparator[2], null, "");
            }
        }
        if (str2.equals("svv")) {
            m72159K4(this.f89566D4.getString("Path") + strReplace.replace("//", ""));
            return true;
        }
        if (str2.equals(Annotation.f68285k3) || (str2.equals("http") & strReplace.contains("localhost:"))) {
            CompressHelper compressHelper = new CompressHelper(m15366r());
            String str6 = "//" + this.f89566D4.getString("Path") + "/";
            if (this.f89566D4.getString("Name").contains("student-")) {
                strReplace = strReplace.replace("///student/content/book", "base");
            }
            if (str.contains("#")) {
                str4 = StringUtils.splitByWholeSeparator(str, "#")[1];
                iMDLogger.m73550f("Testing", "BasePath : " + str6 + ", Resource : " + strReplace + ", mPath : " + this.f87992c5);
                strReplace = strReplace.replace(str6, "");
                if (this.f89566D4.getString("Name").contains("student-")) {
                    strReplace = strReplace + ".html";
                }
                if (this.f87992c5.equalsIgnoreCase(strReplace)) {
                    mo71967C3(str4);
                    return true;
                }
            } else {
                if (this.f89566D4.getString("Name").contains("student-")) {
                    strReplace = strReplace + ".html";
                }
                str4 = "";
            }
            iMDLogger.m73550f("Testing", "BasePath : " + str6 + ", Resource : " + strReplace + ", mPath : " + this.f87992c5);
            String strReplace2 = strReplace.replace(str6, "");
            Bundle bundle2 = this.f89566D4;
            StringBuilder sb = new StringBuilder();
            sb.append("Select * from docs where path = '");
            sb.append(strReplace2);
            sb.append("'");
            ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(bundle2, sb.toString());
            if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "Sorry, Document not available", 1);
                return true;
            }
            compressHelper.m71772A1(this.f89566D4, arrayListM71817V.get(0).getString("id"), null, str4);
        }
        return true;
    }
}

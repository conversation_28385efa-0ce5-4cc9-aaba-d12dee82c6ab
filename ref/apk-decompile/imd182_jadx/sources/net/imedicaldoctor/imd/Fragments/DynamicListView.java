package net.imedicaldoctor.imd.Fragments;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.TypeEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.util.AttributeSet;
import android.util.Property;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListView;
import androidx.core.view.ViewCompat;
import java.util.ArrayList;

/* loaded from: classes3.dex */
public class DynamicListView extends ListView {

    /* renamed from: u3 */
    private static final TypeEvaluator<Rect> f87911u3 = new TypeEvaluator<Rect>() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.5
        @Override // android.animation.TypeEvaluator
        /* renamed from: a, reason: merged with bridge method [inline-methods] */
        public Rect evaluate(float f2, Rect rect, Rect rect2) {
            return new Rect(m72136b(rect.left, rect2.left, f2), m72136b(rect.top, rect2.top, f2), m72136b(rect.right, rect2.right, f2), m72136b(rect.bottom, rect2.bottom, f2));
        }

        /* renamed from: b */
        public int m72136b(int i2, int i3, float f2) {
            return (int) (i2 + (f2 * (i3 - i2)));
        }
    };

    /* renamed from: X2 */
    private final int f87912X2;

    /* renamed from: Y2 */
    private final int f87913Y2;

    /* renamed from: Z2 */
    public ArrayList<String> f87914Z2;

    /* renamed from: a3 */
    private int f87915a3;

    /* renamed from: b3 */
    private int f87916b3;

    /* renamed from: c3 */
    private int f87917c3;

    /* renamed from: d3 */
    private int f87918d3;

    /* renamed from: e3 */
    private boolean f87919e3;

    /* renamed from: f3 */
    private boolean f87920f3;

    /* renamed from: g3 */
    private int f87921g3;

    /* renamed from: h3 */
    private final int f87922h3;

    /* renamed from: i3 */
    private long f87923i3;

    /* renamed from: j3 */
    private long f87924j3;

    /* renamed from: k3 */
    private long f87925k3;

    /* renamed from: l3 */
    private BitmapDrawable f87926l3;

    /* renamed from: m3 */
    private Rect f87927m3;

    /* renamed from: n3 */
    private Rect f87928n3;

    /* renamed from: o3 */
    private final int f87929o3;

    /* renamed from: p3 */
    private int f87930p3;

    /* renamed from: q3 */
    private boolean f87931q3;

    /* renamed from: r3 */
    private int f87932r3;

    /* renamed from: s */
    private final int f87933s;

    /* renamed from: s3 */
    private final AdapterView.OnItemLongClickListener f87934s3;

    /* renamed from: t3 */
    private final AbsListView.OnScrollListener f87935t3;

    public DynamicListView(Context context) {
        super(context);
        this.f87933s = 15;
        this.f87912X2 = 150;
        this.f87913Y2 = 15;
        this.f87915a3 = -1;
        this.f87916b3 = -1;
        this.f87917c3 = -1;
        this.f87918d3 = 0;
        this.f87919e3 = false;
        this.f87920f3 = false;
        this.f87921g3 = 0;
        this.f87922h3 = -1;
        this.f87923i3 = -1L;
        this.f87924j3 = -1L;
        this.f87925k3 = -1L;
        this.f87929o3 = -1;
        this.f87930p3 = -1;
        this.f87931q3 = false;
        this.f87932r3 = 0;
        this.f87934s3 = new AdapterView.OnItemLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.1
            @Override // android.widget.AdapterView.OnItemLongClickListener
            public boolean onItemLongClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                DynamicListView.this.f87918d3 = 0;
                DynamicListView dynamicListView = DynamicListView.this;
                int iPointToPosition = dynamicListView.pointToPosition(dynamicListView.f87917c3, DynamicListView.this.f87916b3);
                View childAt = DynamicListView.this.getChildAt(iPointToPosition - DynamicListView.this.getFirstVisiblePosition());
                DynamicListView dynamicListView2 = DynamicListView.this;
                dynamicListView2.f87924j3 = dynamicListView2.getAdapter().getItemId(iPointToPosition);
                DynamicListView dynamicListView3 = DynamicListView.this;
                dynamicListView3.f87926l3 = dynamicListView3.m72126t(childAt);
                childAt.setVisibility(4);
                DynamicListView.this.f87919e3 = true;
                DynamicListView dynamicListView4 = DynamicListView.this;
                dynamicListView4.m72106F(dynamicListView4.f87924j3);
                return true;
            }
        };
        this.f87935t3 = new AbsListView.OnScrollListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.6

            /* renamed from: a */
            private int f87945a = -1;

            /* renamed from: b */
            private int f87946b = -1;

            /* renamed from: c */
            private int f87947c;

            /* renamed from: d */
            private int f87948d;

            /* renamed from: e */
            private int f87949e;

            /* renamed from: c */
            private void m72137c() {
                if (this.f87948d <= 0 || this.f87949e != 0) {
                    return;
                }
                if (DynamicListView.this.f87919e3 && DynamicListView.this.f87920f3) {
                    DynamicListView.this.m72130z();
                } else if (DynamicListView.this.f87931q3) {
                    DynamicListView.this.m72105E();
                }
            }

            /* renamed from: a */
            public void m72138a() {
                if (this.f87947c == this.f87945a || !DynamicListView.this.f87919e3 || DynamicListView.this.f87924j3 == -1) {
                    return;
                }
                DynamicListView dynamicListView = DynamicListView.this;
                dynamicListView.m72106F(dynamicListView.f87924j3);
                DynamicListView.this.m72129y();
            }

            /* renamed from: b */
            public void m72139b() {
                if (this.f87947c + this.f87948d == this.f87945a + this.f87946b || !DynamicListView.this.f87919e3 || DynamicListView.this.f87924j3 == -1) {
                    return;
                }
                DynamicListView dynamicListView = DynamicListView.this;
                dynamicListView.m72106F(dynamicListView.f87924j3);
                DynamicListView.this.m72129y();
            }

            @Override // android.widget.AbsListView.OnScrollListener
            public void onScroll(AbsListView absListView, int i2, int i3, int i4) {
                this.f87947c = i2;
                this.f87948d = i3;
                int i5 = this.f87945a;
                if (i5 != -1) {
                    i2 = i5;
                }
                this.f87945a = i2;
                int i6 = this.f87946b;
                if (i6 != -1) {
                    i3 = i6;
                }
                this.f87946b = i3;
                m72138a();
                m72139b();
                this.f87945a = this.f87947c;
                this.f87946b = this.f87948d;
            }

            @Override // android.widget.AbsListView.OnScrollListener
            public void onScrollStateChanged(AbsListView absListView, int i2) {
                this.f87949e = i2;
                DynamicListView.this.f87932r3 = i2;
                m72137c();
            }
        };
        m72132B(context);
    }

    /* renamed from: C */
    private void m72103C(ArrayList arrayList, int i2, int i3) {
        Object obj = arrayList.get(i2);
        arrayList.set(i2, arrayList.get(i3));
        arrayList.set(i3, obj);
    }

    /* renamed from: D */
    private void m72104D() {
        View viewM72134x = m72134x(this.f87924j3);
        if (this.f87919e3) {
            this.f87923i3 = -1L;
            this.f87924j3 = -1L;
            this.f87925k3 = -1L;
            viewM72134x.setVisibility(0);
            this.f87926l3 = null;
            invalidate();
        }
        this.f87919e3 = false;
        this.f87920f3 = false;
        this.f87930p3 = -1;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: E */
    public void m72105E() {
        final View viewM72134x = m72134x(this.f87924j3);
        if (!this.f87919e3 && !this.f87931q3) {
            m72104D();
            return;
        }
        this.f87919e3 = false;
        this.f87931q3 = false;
        this.f87920f3 = false;
        this.f87930p3 = -1;
        if (this.f87932r3 != 0) {
            this.f87931q3 = true;
            return;
        }
        this.f87927m3.offsetTo(this.f87928n3.left, viewM72134x.getTop());
        ObjectAnimator objectAnimatorOfObject = ObjectAnimator.ofObject(this.f87926l3, "bounds", f87911u3, this.f87927m3);
        objectAnimatorOfObject.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.3
            @Override // android.animation.ValueAnimator.AnimatorUpdateListener
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                DynamicListView.this.invalidate();
            }
        });
        objectAnimatorOfObject.addListener(new AnimatorListenerAdapter() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.4
            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationEnd(Animator animator) {
                DynamicListView.this.f87923i3 = -1L;
                DynamicListView.this.f87924j3 = -1L;
                DynamicListView.this.f87925k3 = -1L;
                viewM72134x.setVisibility(0);
                DynamicListView.this.f87926l3 = null;
                DynamicListView.this.setEnabled(true);
                DynamicListView.this.invalidate();
            }

            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationStart(Animator animator) {
                DynamicListView.this.setEnabled(false);
            }
        });
        objectAnimatorOfObject.start();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: F */
    public void m72106F(long j2) {
        int iM72133w = m72133w(j2);
        StableArrayAdapter stableArrayAdapter = (StableArrayAdapter) getAdapter();
        this.f87923i3 = stableArrayAdapter.getItemId(iM72133w - 1);
        this.f87925k3 = stableArrayAdapter.getItemId(iM72133w + 1);
    }

    /* renamed from: b */
    static /* synthetic */ int m72108b(DynamicListView dynamicListView, int i2) {
        int i3 = dynamicListView.f87918d3 + i2;
        dynamicListView.f87918d3 = i3;
        return i3;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: t */
    public BitmapDrawable m72126t(View view) {
        int width = view.getWidth();
        int height = view.getHeight();
        int top = view.getTop();
        int left = view.getLeft();
        BitmapDrawable bitmapDrawable = new BitmapDrawable(getResources(), m72128v(view));
        this.f87928n3 = new Rect(left, top, width + left, height + top);
        Rect rect = new Rect(this.f87928n3);
        this.f87927m3 = rect;
        bitmapDrawable.setBounds(rect);
        return bitmapDrawable;
    }

    /* renamed from: u */
    private Bitmap m72127u(View view) {
        Bitmap bitmapCreateBitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        view.draw(new Canvas(bitmapCreateBitmap));
        return bitmapCreateBitmap;
    }

    /* renamed from: v */
    private Bitmap m72128v(View view) {
        Bitmap bitmapM72127u = m72127u(view);
        Canvas canvas = new Canvas(bitmapM72127u);
        Rect rect = new Rect(0, 0, bitmapM72127u.getWidth(), bitmapM72127u.getHeight());
        Paint paint = new Paint();
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(15.0f);
        paint.setColor(ViewCompat.f13527y);
        canvas.drawBitmap(bitmapM72127u, 0.0f, 0.0f, (Paint) null);
        canvas.drawRect(rect, paint);
        return bitmapM72127u;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: y */
    public void m72129y() {
        final int i2 = this.f87915a3 - this.f87916b3;
        int i3 = this.f87928n3.top + this.f87918d3 + i2;
        View viewM72134x = m72134x(this.f87925k3);
        View viewM72134x2 = m72134x(this.f87924j3);
        View viewM72134x3 = m72134x(this.f87923i3);
        boolean z = viewM72134x != null && i3 > viewM72134x.getTop();
        boolean z2 = viewM72134x3 != null && i3 < viewM72134x3.getTop();
        if (z || z2) {
            long j2 = z ? this.f87925k3 : this.f87923i3;
            if (!z) {
                viewM72134x = viewM72134x3;
            }
            int positionForView = getPositionForView(viewM72134x2);
            if (viewM72134x == null) {
                m72106F(this.f87924j3);
                return;
            }
            m72103C(this.f87914Z2, positionForView, getPositionForView(viewM72134x));
            ((BaseAdapter) getAdapter()).notifyDataSetChanged();
            this.f87916b3 = this.f87915a3;
            final int top = viewM72134x.getTop();
            viewM72134x2.setVisibility(0);
            viewM72134x.setVisibility(4);
            m72106F(this.f87924j3);
            final ViewTreeObserver viewTreeObserver = getViewTreeObserver();
            final long j3 = j2;
            viewTreeObserver.addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.2
                @Override // android.view.ViewTreeObserver.OnPreDrawListener
                public boolean onPreDraw() {
                    viewTreeObserver.removeOnPreDrawListener(this);
                    View viewM72134x4 = DynamicListView.this.m72134x(j3);
                    DynamicListView.m72108b(DynamicListView.this, i2);
                    viewM72134x4.setTranslationY(top - viewM72134x4.getTop());
                    ObjectAnimator objectAnimatorOfFloat = ObjectAnimator.ofFloat(viewM72134x4, (Property<View, Float>) View.TRANSLATION_Y, 0.0f);
                    objectAnimatorOfFloat.setDuration(150L);
                    objectAnimatorOfFloat.start();
                    return true;
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: z */
    public void m72130z() {
        this.f87920f3 = m72131A(this.f87927m3);
    }

    /* renamed from: A */
    public boolean m72131A(Rect rect) {
        int i2;
        int iComputeVerticalScrollOffset = computeVerticalScrollOffset();
        int height = getHeight();
        int iComputeVerticalScrollExtent = computeVerticalScrollExtent();
        int iComputeVerticalScrollRange = computeVerticalScrollRange();
        int i3 = rect.top;
        int iHeight = rect.height();
        if (i3 <= 0 && iComputeVerticalScrollOffset > 0) {
            i2 = -this.f87921g3;
        } else {
            if (i3 + iHeight < height || iComputeVerticalScrollOffset + iComputeVerticalScrollExtent >= iComputeVerticalScrollRange) {
                return false;
            }
            i2 = this.f87921g3;
        }
        smoothScrollBy(i2, 0);
        return true;
    }

    /* renamed from: B */
    public void m72132B(Context context) {
        setOnItemLongClickListener(this.f87934s3);
        setOnScrollListener(this.f87935t3);
        this.f87921g3 = (int) (15.0f / context.getResources().getDisplayMetrics().density);
    }

    @Override // android.widget.ListView, android.widget.AbsListView, android.view.ViewGroup, android.view.View
    protected void dispatchDraw(Canvas canvas) {
        super.dispatchDraw(canvas);
        BitmapDrawable bitmapDrawable = this.f87926l3;
        if (bitmapDrawable != null) {
            bitmapDrawable.draw(canvas);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:15:0x0028  */
    @Override // android.widget.AbsListView, android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean onTouchEvent(android.view.MotionEvent r5) {
        /*
            r4 = this;
            int r0 = r5.getAction()
            r0 = r0 & 255(0xff, float:3.57E-43)
            r1 = 0
            if (r0 == 0) goto L6a
            r2 = 1
            if (r0 == r2) goto L28
            r2 = 2
            if (r0 == r2) goto L30
            r1 = 3
            if (r0 == r1) goto L2c
            r1 = 6
            if (r0 == r1) goto L16
            goto L7e
        L16:
            int r0 = r5.getAction()
            r1 = 65280(0xff00, float:9.1477E-41)
            r0 = r0 & r1
            int r0 = r0 >> 8
            int r0 = r5.getPointerId(r0)
            int r1 = r4.f87930p3
            if (r0 != r1) goto L7e
        L28:
            r4.m72105E()
            goto L7e
        L2c:
            r4.m72104D()
            goto L7e
        L30:
            int r0 = r4.f87930p3
            r2 = -1
            if (r0 != r2) goto L36
            goto L7e
        L36:
            int r0 = r5.findPointerIndex(r0)
            float r0 = r5.getY(r0)
            int r0 = (int) r0
            r4.f87915a3 = r0
            int r2 = r4.f87916b3
            int r0 = r0 - r2
            boolean r2 = r4.f87919e3
            if (r2 == 0) goto L7e
            android.graphics.Rect r5 = r4.f87927m3
            android.graphics.Rect r2 = r4.f87928n3
            int r3 = r2.left
            int r2 = r2.top
            int r2 = r2 + r0
            int r0 = r4.f87918d3
            int r2 = r2 + r0
            r5.offsetTo(r3, r2)
            android.graphics.drawable.BitmapDrawable r5 = r4.f87926l3
            android.graphics.Rect r0 = r4.f87927m3
            r5.setBounds(r0)
            r4.invalidate()
            r4.m72129y()
            r4.f87920f3 = r1
            r4.m72130z()
            return r1
        L6a:
            float r0 = r5.getX()
            int r0 = (int) r0
            r4.f87917c3 = r0
            float r0 = r5.getY()
            int r0 = (int) r0
            r4.f87916b3 = r0
            int r0 = r5.getPointerId(r1)
            r4.f87930p3 = r0
        L7e:
            boolean r5 = super.onTouchEvent(r5)
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.DynamicListView.onTouchEvent(android.view.MotionEvent):boolean");
    }

    public void setCheeseList(ArrayList<String> arrayList) {
        this.f87914Z2 = arrayList;
    }

    /* renamed from: w */
    public int m72133w(long j2) {
        View viewM72134x = m72134x(j2);
        if (viewM72134x == null) {
            return -1;
        }
        return getPositionForView(viewM72134x);
    }

    /* renamed from: x */
    public View m72134x(long j2) {
        int firstVisiblePosition = getFirstVisiblePosition();
        StableArrayAdapter stableArrayAdapter = (StableArrayAdapter) getAdapter();
        for (int i2 = 0; i2 < getChildCount(); i2++) {
            View childAt = getChildAt(i2);
            if (stableArrayAdapter.getItemId(firstVisiblePosition + i2) == j2) {
                return childAt;
            }
        }
        return null;
    }

    public DynamicListView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f87933s = 15;
        this.f87912X2 = 150;
        this.f87913Y2 = 15;
        this.f87915a3 = -1;
        this.f87916b3 = -1;
        this.f87917c3 = -1;
        this.f87918d3 = 0;
        this.f87919e3 = false;
        this.f87920f3 = false;
        this.f87921g3 = 0;
        this.f87922h3 = -1;
        this.f87923i3 = -1L;
        this.f87924j3 = -1L;
        this.f87925k3 = -1L;
        this.f87929o3 = -1;
        this.f87930p3 = -1;
        this.f87931q3 = false;
        this.f87932r3 = 0;
        this.f87934s3 = new AdapterView.OnItemLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.1
            @Override // android.widget.AdapterView.OnItemLongClickListener
            public boolean onItemLongClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                DynamicListView.this.f87918d3 = 0;
                DynamicListView dynamicListView = DynamicListView.this;
                int iPointToPosition = dynamicListView.pointToPosition(dynamicListView.f87917c3, DynamicListView.this.f87916b3);
                View childAt = DynamicListView.this.getChildAt(iPointToPosition - DynamicListView.this.getFirstVisiblePosition());
                DynamicListView dynamicListView2 = DynamicListView.this;
                dynamicListView2.f87924j3 = dynamicListView2.getAdapter().getItemId(iPointToPosition);
                DynamicListView dynamicListView3 = DynamicListView.this;
                dynamicListView3.f87926l3 = dynamicListView3.m72126t(childAt);
                childAt.setVisibility(4);
                DynamicListView.this.f87919e3 = true;
                DynamicListView dynamicListView4 = DynamicListView.this;
                dynamicListView4.m72106F(dynamicListView4.f87924j3);
                return true;
            }
        };
        this.f87935t3 = new AbsListView.OnScrollListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.6

            /* renamed from: a */
            private int f87945a = -1;

            /* renamed from: b */
            private int f87946b = -1;

            /* renamed from: c */
            private int f87947c;

            /* renamed from: d */
            private int f87948d;

            /* renamed from: e */
            private int f87949e;

            /* renamed from: c */
            private void m72137c() {
                if (this.f87948d <= 0 || this.f87949e != 0) {
                    return;
                }
                if (DynamicListView.this.f87919e3 && DynamicListView.this.f87920f3) {
                    DynamicListView.this.m72130z();
                } else if (DynamicListView.this.f87931q3) {
                    DynamicListView.this.m72105E();
                }
            }

            /* renamed from: a */
            public void m72138a() {
                if (this.f87947c == this.f87945a || !DynamicListView.this.f87919e3 || DynamicListView.this.f87924j3 == -1) {
                    return;
                }
                DynamicListView dynamicListView = DynamicListView.this;
                dynamicListView.m72106F(dynamicListView.f87924j3);
                DynamicListView.this.m72129y();
            }

            /* renamed from: b */
            public void m72139b() {
                if (this.f87947c + this.f87948d == this.f87945a + this.f87946b || !DynamicListView.this.f87919e3 || DynamicListView.this.f87924j3 == -1) {
                    return;
                }
                DynamicListView dynamicListView = DynamicListView.this;
                dynamicListView.m72106F(dynamicListView.f87924j3);
                DynamicListView.this.m72129y();
            }

            @Override // android.widget.AbsListView.OnScrollListener
            public void onScroll(AbsListView absListView, int i2, int i3, int i4) {
                this.f87947c = i2;
                this.f87948d = i3;
                int i5 = this.f87945a;
                if (i5 != -1) {
                    i2 = i5;
                }
                this.f87945a = i2;
                int i6 = this.f87946b;
                if (i6 != -1) {
                    i3 = i6;
                }
                this.f87946b = i3;
                m72138a();
                m72139b();
                this.f87945a = this.f87947c;
                this.f87946b = this.f87948d;
            }

            @Override // android.widget.AbsListView.OnScrollListener
            public void onScrollStateChanged(AbsListView absListView, int i2) {
                this.f87949e = i2;
                DynamicListView.this.f87932r3 = i2;
                m72137c();
            }
        };
        m72132B(context);
    }

    public DynamicListView(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        this.f87933s = 15;
        this.f87912X2 = 150;
        this.f87913Y2 = 15;
        this.f87915a3 = -1;
        this.f87916b3 = -1;
        this.f87917c3 = -1;
        this.f87918d3 = 0;
        this.f87919e3 = false;
        this.f87920f3 = false;
        this.f87921g3 = 0;
        this.f87922h3 = -1;
        this.f87923i3 = -1L;
        this.f87924j3 = -1L;
        this.f87925k3 = -1L;
        this.f87929o3 = -1;
        this.f87930p3 = -1;
        this.f87931q3 = false;
        this.f87932r3 = 0;
        this.f87934s3 = new AdapterView.OnItemLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.1
            @Override // android.widget.AdapterView.OnItemLongClickListener
            public boolean onItemLongClick(AdapterView<?> adapterView, View view, int i22, long j2) {
                DynamicListView.this.f87918d3 = 0;
                DynamicListView dynamicListView = DynamicListView.this;
                int iPointToPosition = dynamicListView.pointToPosition(dynamicListView.f87917c3, DynamicListView.this.f87916b3);
                View childAt = DynamicListView.this.getChildAt(iPointToPosition - DynamicListView.this.getFirstVisiblePosition());
                DynamicListView dynamicListView2 = DynamicListView.this;
                dynamicListView2.f87924j3 = dynamicListView2.getAdapter().getItemId(iPointToPosition);
                DynamicListView dynamicListView3 = DynamicListView.this;
                dynamicListView3.f87926l3 = dynamicListView3.m72126t(childAt);
                childAt.setVisibility(4);
                DynamicListView.this.f87919e3 = true;
                DynamicListView dynamicListView4 = DynamicListView.this;
                dynamicListView4.m72106F(dynamicListView4.f87924j3);
                return true;
            }
        };
        this.f87935t3 = new AbsListView.OnScrollListener() { // from class: net.imedicaldoctor.imd.Fragments.DynamicListView.6

            /* renamed from: a */
            private int f87945a = -1;

            /* renamed from: b */
            private int f87946b = -1;

            /* renamed from: c */
            private int f87947c;

            /* renamed from: d */
            private int f87948d;

            /* renamed from: e */
            private int f87949e;

            /* renamed from: c */
            private void m72137c() {
                if (this.f87948d <= 0 || this.f87949e != 0) {
                    return;
                }
                if (DynamicListView.this.f87919e3 && DynamicListView.this.f87920f3) {
                    DynamicListView.this.m72130z();
                } else if (DynamicListView.this.f87931q3) {
                    DynamicListView.this.m72105E();
                }
            }

            /* renamed from: a */
            public void m72138a() {
                if (this.f87947c == this.f87945a || !DynamicListView.this.f87919e3 || DynamicListView.this.f87924j3 == -1) {
                    return;
                }
                DynamicListView dynamicListView = DynamicListView.this;
                dynamicListView.m72106F(dynamicListView.f87924j3);
                DynamicListView.this.m72129y();
            }

            /* renamed from: b */
            public void m72139b() {
                if (this.f87947c + this.f87948d == this.f87945a + this.f87946b || !DynamicListView.this.f87919e3 || DynamicListView.this.f87924j3 == -1) {
                    return;
                }
                DynamicListView dynamicListView = DynamicListView.this;
                dynamicListView.m72106F(dynamicListView.f87924j3);
                DynamicListView.this.m72129y();
            }

            @Override // android.widget.AbsListView.OnScrollListener
            public void onScroll(AbsListView absListView, int i22, int i3, int i4) {
                this.f87947c = i22;
                this.f87948d = i3;
                int i5 = this.f87945a;
                if (i5 != -1) {
                    i22 = i5;
                }
                this.f87945a = i22;
                int i6 = this.f87946b;
                if (i6 != -1) {
                    i3 = i6;
                }
                this.f87946b = i3;
                m72138a();
                m72139b();
                this.f87945a = this.f87947c;
                this.f87946b = this.f87948d;
            }

            @Override // android.widget.AbsListView.OnScrollListener
            public void onScrollStateChanged(AbsListView absListView, int i22) {
                this.f87949e = i22;
                DynamicListView.this.f87932r3 = i22;
                m72137c();
            }
        };
        m72132B(context);
    }
}

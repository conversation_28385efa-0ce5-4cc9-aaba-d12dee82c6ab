package net.imedicaldoctor.imd.Fragments.DRE;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextCheckViewHolder;

/* loaded from: classes3.dex */
public class DRESelectDialog extends DialogFragment {
    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_new_section_viewer, (ViewGroup) null);
        RecyclerView recyclerView = (RecyclerView) viewInflate.findViewById(C5562R.id.recycler_view);
        ArrayList parcelableArrayList = m15387y().getParcelableArrayList("Items");
        String string = m15387y().getString("TitleProperty");
        final int i2 = m15387y().containsKey("Position") ? m15387y().getInt("Position") : 0;
        recyclerView.setAdapter(new ChaptersAdapter(m15366r(), parcelableArrayList, string, C5562R.layout.list_view_item_ripple_text_check) { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRESelectDialog.1
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, final int i3) {
                ImageView imageView;
                int i4;
                RippleTextCheckViewHolder rippleTextCheckViewHolder = (RippleTextCheckViewHolder) viewHolder;
                rippleTextCheckViewHolder.f101485I.setTypeface(ResourcesCompat.m7158j(DRESelectDialog.this.m15366r(), C5562R.font.iransans));
                if (i3 == i2) {
                    imageView = rippleTextCheckViewHolder.f101487K;
                    i4 = 0;
                } else {
                    imageView = rippleTextCheckViewHolder.f101487K;
                    i4 = 8;
                }
                imageView.setVisibility(i4);
                rippleTextCheckViewHolder.f101485I.setText(bundle2.getString(this.f101432f));
                rippleTextCheckViewHolder.f101486J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRESelectDialog.1.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        DRESelectDialog.this.mo15205N2();
                        DRESelectDialog.this.m72042g3(bundle2, i3);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                return new RippleTextCheckViewHolder(view);
            }
        });
        recyclerView.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        recyclerView.m27459p(new CustomItemDecoration(m15366r()));
        builder.setView(viewInflate);
        return builder.create();
    }

    /* renamed from: g3 */
    public void m72042g3(Bundle bundle, int i2) {
        if (m15351l0() instanceof DREMainActivityFragment) {
            ((DREMainActivityFragment) m15351l0()).m72036r3(m15387y().getString("Type"), bundle, i2);
        }
    }
}

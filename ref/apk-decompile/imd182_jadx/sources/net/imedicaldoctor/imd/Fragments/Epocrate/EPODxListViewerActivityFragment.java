package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.ViewHolders.RippleTextImageArrowViewHolder;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class EPODxListViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public JSONObject f88021X4;

    /* renamed from: Y4 */
    public String f88022Y4;

    /* renamed from: Z4 */
    public RecyclerView f88023Z4;

    /* renamed from: a5 */
    public JSONObject f88024a5;

    /* renamed from: b5 */
    public ArrayList<Bundle> f88025b5;

    /* renamed from: c5 */
    public String f88026c5;

    /* renamed from: d5 */
    public String f88027d5;

    public class EpocrateAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public ArrayList<Bundle> f88028d;

        /* renamed from: e */
        public String f88029e;

        /* renamed from: f */
        public int f88030f;

        public EpocrateAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return EPODxListViewerActivityFragment.this.m72173I4(i2).containsKey("Title") ? 1 : 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            if (viewHolder.m27811F() != 0) {
                if (viewHolder.m27811F() == 1) {
                    ((HeaderCellViewHolder) viewHolder).f88038I.setText(EPODxListViewerActivityFragment.this.m72173I4(i2).getString("Title"));
                    return;
                }
                return;
            }
            RippleTextImageArrowViewHolder rippleTextImageArrowViewHolder = (RippleTextImageArrowViewHolder) viewHolder;
            final Bundle bundle = EPODxListViewerActivityFragment.this.m72173I4(i2).getBundle("Item");
            if (bundle.containsKey(TypedValues.AttributesType.f5395M)) {
                rippleTextImageArrowViewHolder.f101508I.setText(bundle.getString("title"));
                rippleTextImageArrowViewHolder.f101509J.setVisibility(8);
                rippleTextImageArrowViewHolder.f101510K.setVisibility(0);
                rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxListViewerActivityFragment.EpocrateAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) throws JSONException {
                        try {
                            EPODxListViewerActivityFragment ePODxListViewerActivityFragment = EPODxListViewerActivityFragment.this;
                            JSONObject jSONObjectM71886r1 = ePODxListViewerActivityFragment.f89579Q4.m71886r1(ePODxListViewerActivityFragment.f88021X4.getJSONArray("views"), "id", bundle.getString(TypedValues.AttributesType.f5395M));
                            if (!jSONObjectM71886r1.getString("type").equals("web")) {
                                Bundle bundle2 = new Bundle();
                                bundle2.putBundle("DB", EPODxListViewerActivityFragment.this.f89566D4);
                                bundle2.putString("ViewId", bundle.getString(TypedValues.AttributesType.f5395M));
                                bundle2.putString("mDB", EPODxListViewerActivityFragment.this.f88026c5);
                                bundle2.putString("URL", EPODxListViewerActivityFragment.this.f89567E4);
                                EPODxListViewerActivityFragment.this.f89579Q4.m71798N(EPODxListViewerActivity.class, EPODxListViewerActivityFragment.class, bundle2);
                                return;
                            }
                            String string = jSONObjectM71886r1.getString(HTML.Tag.f74425y);
                            Bundle bundle3 = new Bundle();
                            bundle3.putString("Title", jSONObjectM71886r1.getString("title"));
                            bundle3.putString("Html", string);
                            bundle3.putString("DocAddress", EPODxListViewerActivityFragment.this.f89567E4);
                            bundle3.putString("mDB", EPODxListViewerActivityFragment.this.f88026c5);
                            bundle3.putParcelableArrayList("Images", EPODxListViewerActivityFragment.this.f88025b5);
                            EPODxListViewerActivityFragment ePODxListViewerActivityFragment2 = EPODxListViewerActivityFragment.this;
                            ePODxListViewerActivityFragment2.f89579Q4.m71775B1(ePODxListViewerActivityFragment2.f89566D4, "web-" + EPODxListViewerActivityFragment.this.f89567E4, null, null, bundle3);
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                            e2.printStackTrace();
                        }
                    }
                });
                return;
            }
            if (bundle.containsKey("targetHTML")) {
                rippleTextImageArrowViewHolder.f101508I.setText(bundle.getString("title"));
                rippleTextImageArrowViewHolder.f101509J.setVisibility(8);
                rippleTextImageArrowViewHolder.f101510K.setVisibility(0);
                rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxListViewerActivityFragment.EpocrateAdapter.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        String string = bundle.getString("targetHTML");
                        Bundle bundle2 = new Bundle();
                        bundle2.putString("Title", bundle.getString("title"));
                        bundle2.putString("Html", string);
                        bundle2.putString("DocAddress", EPODxListViewerActivityFragment.this.f89567E4);
                        bundle2.putParcelableArrayList("Images", EPODxListViewerActivityFragment.this.f88025b5);
                        EPODxListViewerActivityFragment ePODxListViewerActivityFragment = EPODxListViewerActivityFragment.this;
                        ePODxListViewerActivityFragment.f89579Q4.m71775B1(ePODxListViewerActivityFragment.f89566D4, "web-" + EPODxListViewerActivityFragment.this.f89567E4, null, null, bundle2);
                    }
                });
                return;
            }
            if (bundle.containsKey("targetURL")) {
                rippleTextImageArrowViewHolder.f101508I.setText(bundle.getString("title"));
                String string = bundle.getString("targetURL");
                String str = string.contains("rx/monograph/") ? "plus_rx.png" : string.contains("dx/monograph/") ? "plus_dx.png" : string.contains("lab/monograph/") ? "plus_lab.png" : string.contains("lab/list/panel/") ? "plus_panel.png" : "";
                rippleTextImageArrowViewHolder.f101509J.setVisibility(0);
                ImageView imageView = rippleTextImageArrowViewHolder.f101509J;
                EPODxListViewerActivityFragment ePODxListViewerActivityFragment = EPODxListViewerActivityFragment.this;
                imageView.setImageBitmap(ePODxListViewerActivityFragment.m72176L4(ePODxListViewerActivityFragment.m15366r(), str));
                rippleTextImageArrowViewHolder.f101510K.setVisibility(0);
                rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxListViewerActivityFragment.EpocrateAdapter.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        String string2 = bundle.getString("targetURL");
                        EPODxListViewerActivityFragment ePODxListViewerActivityFragment2 = EPODxListViewerActivityFragment.this;
                        ePODxListViewerActivityFragment2.f89579Q4.m71800N1(ePODxListViewerActivityFragment2.f89566D4, string2);
                    }
                });
                return;
            }
            if (!bundle.containsKey("flag")) {
                rippleTextImageArrowViewHolder.f101508I.setText("title");
                rippleTextImageArrowViewHolder.f101509J.setVisibility(8);
                rippleTextImageArrowViewHolder.f101510K.setVisibility(8);
                return;
            }
            rippleTextImageArrowViewHolder.f101508I.setText(bundle.getString("title"));
            rippleTextImageArrowViewHolder.f101509J.setVisibility(0);
            ImageView imageView2 = rippleTextImageArrowViewHolder.f101509J;
            EPODxListViewerActivityFragment ePODxListViewerActivityFragment2 = EPODxListViewerActivityFragment.this;
            imageView2.setImageBitmap(ePODxListViewerActivityFragment2.m72176L4(ePODxListViewerActivityFragment2.m15366r(), "orb" + bundle.getString("flag") + ".png"));
            rippleTextImageArrowViewHolder.f101510K.setVisibility(0);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new RippleTextImageArrowViewHolder(LayoutInflater.from(EPODxListViewerActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_image_arrow, viewGroup, false));
            }
            if (i2 == 1) {
                return new HeaderCellViewHolder(LayoutInflater.from(EPODxListViewerActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_dx_list_header, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return EPODxListViewerActivityFragment.this.m72178O4();
        }

        /* renamed from: d0 */
        public String m72179d0(String str) {
            return str;
        }

        /* renamed from: e0 */
        public void m72180e0(Bundle bundle, int i2) {
        }
    }

    public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f88038I;

        public HeaderCellViewHolder(View view) {
            super(view);
            this.f88038I = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    /* renamed from: N4 */
    private void m72172N4(String str) {
        ArrayList<Bundle> arrayList = this.f88025b5;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        arrayList2.addAll(this.f88025b5);
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (((Bundle) arrayList2.get(i3)).getString("id").startsWith(str)) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public Bundle m72173I4(int i2) throws JSONException {
        int i3 = 0;
        int i4 = 0;
        for (int i5 = 0; i5 < this.f88024a5.getJSONArray("sections").length(); i5++) {
            try {
                JSONObject jSONObject = this.f88024a5.getJSONArray("sections").getJSONObject(i5);
                if (i2 == i3) {
                    Bundle bundle = new Bundle();
                    String string = jSONObject.has("headerText") ? jSONObject.getString("headerText") : "";
                    if (jSONObject.has("headers")) {
                        string = jSONObject.getJSONArray("headers").getJSONObject(0).getString("title");
                    }
                    bundle.putString("Title", string);
                    bundle.putInt("Row", 0);
                    bundle.putInt("Section", i4);
                    bundle.putInt("Row2", 1);
                    bundle.putInt("Section2", i4 - 1);
                    return bundle;
                }
                int length = i3 + jSONObject.getJSONArray("cells").length();
                if (i2 <= length) {
                    int length2 = (i2 - (length - jSONObject.getJSONArray("cells").length())) - 1;
                    Bundle bundle2 = new Bundle();
                    bundle2.putBundle("Item", this.f89579Q4.m71784G(jSONObject.getJSONArray("cells").getJSONObject(length2)));
                    bundle2.putInt("Row", length2);
                    bundle2.putInt("Section", i4);
                    return bundle2;
                }
                i3 = length + 1;
                i4++;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
                return null;
            }
        }
        return null;
    }

    /* renamed from: J4 */
    public void m72174J4() {
        this.f88023Z4.setItemAnimator(new DefaultItemAnimator());
        this.f88023Z4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88023Z4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: K4 */
    public String m72175K4(JSONObject jSONObject) {
        try {
            return jSONObject.has(TypedValues.AttributesType.f5395M) ? jSONObject.getString(TypedValues.AttributesType.f5395M) : jSONObject.getString("targetHTML");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            return null;
        }
    }

    /* renamed from: L4 */
    public Bitmap m72176L4(Context context, String str) throws Throwable {
        InputStream inputStreamOpen;
        InputStream inputStream = null;
        try {
            inputStreamOpen = context.getAssets().open(str);
            try {
                Bitmap bitmapDecodeStream = BitmapFactory.decodeStream(inputStreamOpen);
                if (inputStreamOpen != null) {
                    try {
                        inputStreamOpen.close();
                    } catch (Exception unused) {
                    }
                }
                return bitmapDecodeStream;
            } catch (Exception unused2) {
                if (inputStreamOpen != null) {
                    try {
                        inputStreamOpen.close();
                    } catch (Exception unused3) {
                    }
                }
                return null;
            } catch (Throwable th) {
                th = th;
                inputStream = inputStreamOpen;
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (Exception unused4) {
                    }
                }
                throw th;
            }
        } catch (Exception unused5) {
            inputStreamOpen = null;
        } catch (Throwable th2) {
            th = th2;
        }
    }

    /* renamed from: M4 */
    public void m72177M4() {
        try {
            JSONObject jSONObjectM71886r1 = this.f89579Q4.m71886r1(this.f88021X4.getJSONArray("views"), "id", this.f88022Y4);
            if (jSONObjectM71886r1.getString("id").equals("root") && this.f88027d5.length() > 0) {
                jSONObjectM71886r1.getJSONArray("sections").put(new JSONObject("{\"cells\":[{\"type\":\"flex0\",\"title\":\"Citations\", \"targetHTML\":\"" + this.f88027d5.replace("\"", "\\\"") + "\"}]}"));
            }
            this.f88024a5 = jSONObjectM71886r1;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
        }
    }

    /* renamed from: O4 */
    public int m72178O4() {
        if (this.f88024a5 == null) {
            return 0;
        }
        int length = 0;
        for (int i2 = 0; i2 < this.f88024a5.getJSONArray("sections").length(); i2++) {
            try {
                length = length + this.f88024a5.getJSONArray("sections").getJSONObject(i2).getJSONArray("cells").length() + 1;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
                return 0;
            }
        }
        return length;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList;
        Bundle bundleM72839v3;
        if (this.f88025b5.size() <= 0 || (arrayList = this.f88025b5) == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f88025b5)) == null) {
            return null;
        }
        return bundleM72839v3.getString("ImagePath");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws JSONException, Resources.NotFoundException {
        String str = "";
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88023Z4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        try {
            String str2 = this.f89563A4;
            if (str2 == null || str2.length() == 0) {
                if (m15387y().containsKey("ViewId")) {
                    this.f88022Y4 = m15387y().getString("ViewId");
                }
                if (m15387y().containsKey("mDB")) {
                    this.f88026c5 = m15387y().getString("mDB");
                }
                if (this.f88026c5 == null) {
                    this.f88026c5 = "Dx";
                }
                iMDLogger.m73550f("Loading Document", this.f89567E4);
                String str3 = this.f89567E4.split("-")[1];
                ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from " + this.f88026c5 + "_monographs where id=" + str3);
                if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                    JSONObject jSONObject = new JSONObject(this.f89579Q4.m71773B(arrayListM71817V.get(0).getString("monograph"), str3, "127"));
                    this.f89568F4 = jSONObject.getString("title");
                    this.f88021X4 = jSONObject;
                    if (this.f88022Y4 == null) {
                        this.f88022Y4 = "root";
                    }
                    this.f88027d5 = "";
                    if (jSONObject.has("citations") && this.f88021X4.getJSONObject("citations").has("articles")) {
                        for (int i2 = 0; i2 < this.f88021X4.getJSONObject("citations").getJSONArray("articles").length(); i2++) {
                            JSONObject jSONObject2 = this.f88021X4.getJSONObject("citations").getJSONArray("articles").getJSONObject(i2);
                            str = str + "<div id=\"articleCitation" + jSONObject2.getString("id") + "\" style=\"margin:10px\"><b>" + jSONObject2.getString("id") + ": </b>" + jSONObject2.getString(HTML.Tag.f74425y) + "</div>";
                        }
                        this.f88027d5 = str;
                    }
                    m72177M4();
                    ArrayList<Bundle> arrayList = new ArrayList<>();
                    if (this.f88021X4.has("media")) {
                        for (int i3 = 0; i3 < this.f88021X4.getJSONArray("media").length(); i3++) {
                            JSONObject jSONObject3 = this.f88021X4.getJSONArray("media").getJSONObject(i3);
                            String string = jSONObject3.getString(Annotation.f68285k3);
                            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, string, "pictures");
                            if (new File(strM71754h1).exists()) {
                                String str4 = jSONObject3.getString(HTML.Tag.f74389g) + StringUtils.f103471LF + jSONObject3.getString("source");
                                Bundle bundle2 = new Bundle();
                                bundle2.putString("ImagePath", strM71754h1);
                                bundle2.putString("id", string);
                                bundle2.putString("Description", str4);
                                arrayList.add(bundle2);
                            }
                        }
                    }
                    this.f88025b5 = arrayList;
                }
                CompressHelper.m71767x2(m15366r(), "Document doesn't exist", 1);
                return this.f89565C4;
            }
            this.f88023Z4.setAdapter(new EpocrateAdapter());
            m72174J4();
            mo72642f3(C5562R.menu.epolistmenu);
            m15358o2(false);
            m72786G3();
        } catch (Exception e2) {
            m72779B4(e2);
        }
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_gallery) {
            m72172N4("soheilvb");
        }
        return super.mo15329e1(menuItem);
    }
}

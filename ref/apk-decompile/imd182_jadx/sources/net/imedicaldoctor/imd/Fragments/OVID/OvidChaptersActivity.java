package net.imedicaldoctor.imd.Fragments.OVID;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.tool.xml.html.HTML;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.MessageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleSearchContentViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class OvidChaptersActivity extends iMDActivity {

    public static class OvidChaptersFragment extends SearchHelperFragment {

        /* renamed from: C4 */
        private static String f88669C4;

        /* renamed from: A4 */
        private String f88670A4;

        /* renamed from: B4 */
        private OvidContentSearchAdapter f88671B4;

        public class OvidChaptersAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f88676d;

            /* renamed from: e */
            public ArrayList<Bundle> f88677e;

            /* renamed from: f */
            public String f88678f;

            public OvidChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
                this.f88676d = context;
                this.f88677e = arrayList;
                this.f88678f = str;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: C */
            public int mo26845C(int i2) {
                ArrayList<Bundle> arrayList = this.f88677e;
                if (arrayList == null) {
                    return 0;
                }
                Bundle bundle = arrayList.get(i2);
                if (bundle.getString("leaf").equals(IcyHeaders.f28171a3)) {
                    return 0;
                }
                return (bundle.getString("xpath").length() > 0 || bundle.getString(HTML.Tag.f74369V).length() > 0) ? 1 : 2;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                if (viewHolder.m27811F() == 0 || viewHolder.m27811F() == 2) {
                    RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                    final Bundle bundle = this.f88677e.get(i2);
                    rippleTextViewHolder.f101515I.setText(bundle.getString(this.f88678f));
                    rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidChaptersAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            OvidChaptersAdapter.this.mo72426e0(bundle, i2);
                        }
                    });
                    return;
                }
                if (viewHolder.m27811F() == 1) {
                    RippleInfoTextViewHolder rippleInfoTextViewHolder = (RippleInfoTextViewHolder) viewHolder;
                    final Bundle bundle2 = this.f88677e.get(i2);
                    rippleInfoTextViewHolder.f88706I.setText(bundle2.getString(this.f88678f));
                    rippleInfoTextViewHolder.f88708K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidChaptersAdapter.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            OvidChaptersAdapter.this.mo72426e0(bundle2, i2);
                        }
                    });
                    rippleInfoTextViewHolder.f88707J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidChaptersAdapter.3
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            OvidChaptersAdapter.this.mo72425d0(bundle2, i2);
                        }
                    });
                }
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                if (i2 == 0) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88676d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
                }
                if (i2 == 1) {
                    return OvidChaptersFragment.this.new RippleInfoTextViewHolder(LayoutInflater.from(this.f88676d).inflate(C5562R.layout.list_view_item_ripple_goto_arrow, viewGroup, false));
                }
                if (i2 == 2) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88676d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
                }
                return null;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                ArrayList<Bundle> arrayList = this.f88677e;
                if (arrayList == null) {
                    return 0;
                }
                return arrayList.size();
            }

            /* renamed from: d0 */
            public void mo72425d0(Bundle bundle, int i2) {
            }

            /* renamed from: e0 */
            public void mo72426e0(Bundle bundle, int i2) {
            }
        }

        public class OvidContentSearchAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f88689d;

            /* renamed from: e */
            public ArrayList<Bundle> f88690e;

            /* renamed from: f */
            public String f88691f;

            /* renamed from: g */
            public String f88692g;

            public OvidContentSearchAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2) {
                this.f88689d = context;
                this.f88690e = arrayList;
                this.f88691f = str;
                this.f88692g = str2;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: C */
            public int mo26845C(int i2) {
                ArrayList<Bundle> arrayList = this.f88690e;
                if (arrayList == null || arrayList.size() == 0) {
                    return 0;
                }
                if (!this.f88690e.get(i2).getString("type").equals("0")) {
                    return 3;
                }
                OvidChaptersFragment ovidChaptersFragment = OvidChaptersFragment.this;
                CompressHelper compressHelper = ovidChaptersFragment.f88791k4;
                Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(ovidChaptersFragment.f88788h4, "Select * from TOC where id=" + this.f88690e.get(i2).getString("contentId")));
                if (bundleM71907z.getString("leaf").equals(IcyHeaders.f28171a3)) {
                    return 0;
                }
                return (bundleM71907z.getString("xpath").length() > 0 || bundleM71907z.getString(HTML.Tag.f74369V).length() > 0) ? 1 : 2;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                MaterialRippleLayout materialRippleLayout;
                View.OnClickListener onClickListener;
                ArrayList<Bundle> arrayList = this.f88690e;
                if (arrayList == null || arrayList.size() == 0) {
                    return;
                }
                if (viewHolder.m27811F() == 3) {
                    RippleSearchContentViewHolder rippleSearchContentViewHolder = (RippleSearchContentViewHolder) viewHolder;
                    final Bundle bundle = this.f88690e.get(i2);
                    rippleSearchContentViewHolder.f101479I.setText(bundle.getString(this.f88691f));
                    if (this.f88692g == null) {
                        rippleSearchContentViewHolder.f101480J.setVisibility(8);
                    } else {
                        rippleSearchContentViewHolder.f101480J.setVisibility(0);
                        rippleSearchContentViewHolder.f101480J.setText(Html.fromHtml(bundle.getString(this.f88692g)));
                    }
                    materialRippleLayout = rippleSearchContentViewHolder.f101481K;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidContentSearchAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            OvidContentSearchAdapter.this.mo72428e0(bundle, i2);
                        }
                    };
                } else {
                    OvidChaptersFragment ovidChaptersFragment = OvidChaptersFragment.this;
                    CompressHelper compressHelper = ovidChaptersFragment.f88791k4;
                    final Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(ovidChaptersFragment.f88788h4, "Select * from TOC where id=" + this.f88690e.get(i2).getString("contentId")));
                    if (viewHolder.m27811F() != 0 && viewHolder.m27811F() != 2) {
                        if (viewHolder.m27811F() == 1) {
                            RippleInfoTextViewHolder rippleInfoTextViewHolder = (RippleInfoTextViewHolder) viewHolder;
                            rippleInfoTextViewHolder.f88706I.setText(bundleM71907z.getString("name"));
                            rippleInfoTextViewHolder.f88708K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidContentSearchAdapter.3
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    OvidContentSearchAdapter.this.mo72428e0(bundleM71907z, i2);
                                }
                            });
                            rippleInfoTextViewHolder.f88707J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidContentSearchAdapter.4
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    OvidContentSearchAdapter.this.mo72427d0(bundleM71907z, i2);
                                }
                            });
                            return;
                        }
                        return;
                    }
                    RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                    rippleTextViewHolder.f101515I.setText(bundleM71907z.getString("name"));
                    materialRippleLayout = rippleTextViewHolder.f101516J;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidContentSearchAdapter.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            OvidContentSearchAdapter.this.mo72428e0(bundleM71907z, i2);
                        }
                    };
                }
                materialRippleLayout.setOnClickListener(onClickListener);
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                ArrayList<Bundle> arrayList = this.f88690e;
                if (arrayList == null || arrayList.size() == 0) {
                    return new MessageViewHolder(this.f88689d, LayoutInflater.from(this.f88689d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
                }
                if (i2 == 0) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88689d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
                }
                if (i2 == 1) {
                    return OvidChaptersFragment.this.new RippleInfoTextViewHolder(LayoutInflater.from(this.f88689d).inflate(C5562R.layout.list_view_item_ripple_goto_arrow, viewGroup, false));
                }
                if (i2 == 2) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88689d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
                }
                if (i2 == 3) {
                    return new RippleSearchContentViewHolder(LayoutInflater.from(this.f88689d).inflate(C5562R.layout.list_view_item_search_content_ripple, viewGroup, false));
                }
                return null;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                ArrayList<Bundle> arrayList = this.f88690e;
                if (arrayList == null || arrayList.size() == 0) {
                    return 1;
                }
                return this.f88690e.size();
            }

            /* renamed from: d0 */
            public void mo72427d0(Bundle bundle, int i2) {
            }

            /* renamed from: e0 */
            public void mo72428e0(Bundle bundle, int i2) {
            }

            /* renamed from: f0 */
            public void m72429f0(ArrayList<Bundle> arrayList) {
                this.f88690e = arrayList;
                m27491G();
            }
        }

        public class RippleInfoTextViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f88706I;

            /* renamed from: J */
            public ImageView f88707J;

            /* renamed from: K */
            public MaterialRippleLayout f88708K;

            public RippleInfoTextViewHolder(View view) {
                super(view);
                this.f88706I = (TextView) view.findViewById(C5562R.id.text_view);
                this.f88708K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
                this.f88707J = (ImageView) view.findViewById(C5562R.id.info_button);
            }
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72462O2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            String string;
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72462O2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            if (m15387y() == null || !m15387y().containsKey("ParentId")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
                string = "0";
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
                string = m15387y().getString("ParentId");
            }
            this.f88670A4 = string;
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select id as _id,* from toc where parentId = " + this.f88670A4);
            this.f88792l4 = new OvidChaptersAdapter(m15366r(), this.f88794n4, "name") { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.2
                @Override // net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidChaptersAdapter
                /* renamed from: d0 */
                public void mo72425d0(Bundle bundle2, int i2) {
                    OvidChaptersFragment.this.m72468V2();
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("gotoSection", bundle2);
                    OvidChaptersFragment ovidChaptersFragment = OvidChaptersFragment.this;
                    ovidChaptersFragment.f88791k4.m71775B1(ovidChaptersFragment.f88788h4, bundle2.getString("bookId"), null, null, bundle3);
                }

                @Override // net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidChaptersAdapter
                /* renamed from: e0 */
                public void mo72426e0(Bundle bundle2, int i2) {
                    OvidChaptersFragment.this.m72468V2();
                    String string2 = bundle2.getString("leaf");
                    String string3 = bundle2.getString("bookId");
                    if (string2.equals(IcyHeaders.f28171a3)) {
                        Bundle bundle3 = new Bundle();
                        bundle3.putBundle("gotoSection", bundle2);
                        OvidChaptersFragment ovidChaptersFragment = OvidChaptersFragment.this;
                        ovidChaptersFragment.f88791k4.m71775B1(ovidChaptersFragment.f88788h4, string3, null, null, bundle3);
                        return;
                    }
                    Bundle bundle4 = new Bundle();
                    bundle4.putBundle("DB", OvidChaptersFragment.this.f88788h4);
                    bundle4.putString("ParentId", bundle2.getString("id"));
                    OvidChaptersFragment.this.f88791k4.m71798N(OvidChaptersActivity.class, OvidChaptersFragment.class, bundle4);
                }
            };
            this.f88671B4 = new OvidContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.3
                @Override // net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidContentSearchAdapter
                /* renamed from: d0 */
                public void mo72427d0(Bundle bundle2, int i2) {
                    OvidChaptersFragment.this.m72468V2();
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("gotoSection", bundle2);
                    OvidChaptersFragment ovidChaptersFragment = OvidChaptersFragment.this;
                    ovidChaptersFragment.f88791k4.m71775B1(ovidChaptersFragment.f88788h4, bundle2.getString("bookId"), null, null, bundle3);
                }

                @Override // net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity.OvidChaptersFragment.OvidContentSearchAdapter
                /* renamed from: e0 */
                public void mo72428e0(Bundle bundle2, int i2) {
                    Bundle bundleM71844e0;
                    OvidChaptersFragment.this.m72468V2();
                    if (!bundle2.containsKey("type")) {
                        String string2 = bundle2.getString("leaf");
                        String string3 = bundle2.getString("bookId");
                        if (string2.equals(IcyHeaders.f28171a3)) {
                            Bundle bundle3 = new Bundle();
                            bundle3.putBundle("gotoSection", bundle2);
                            OvidChaptersFragment ovidChaptersFragment = OvidChaptersFragment.this;
                            ovidChaptersFragment.f88791k4.m71775B1(ovidChaptersFragment.f88788h4, string3, null, null, bundle3);
                            return;
                        }
                        Bundle bundle4 = new Bundle();
                        bundle4.putBundle("DB", OvidChaptersFragment.this.f88788h4);
                        bundle4.putString("ParentId", bundle2.getString("id"));
                        OvidChaptersFragment.this.f88791k4.m71798N(OvidChaptersActivity.class, OvidChaptersFragment.class, bundle4);
                        return;
                    }
                    String string4 = bundle2.getString("type");
                    String string5 = bundle2.getString("contentId");
                    if (string4.equals("0")) {
                        OvidChaptersFragment ovidChaptersFragment2 = OvidChaptersFragment.this;
                        CompressHelper compressHelper = ovidChaptersFragment2.f88791k4;
                        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(ovidChaptersFragment2.f88788h4, "Select * from TOC where id=" + string5));
                        if (bundleM71890s1.getString("leaf").equals(IcyHeaders.f28171a3)) {
                            Bundle bundle5 = new Bundle();
                            bundle5.putBundle("gotoSection", bundleM71890s1);
                            OvidChaptersFragment ovidChaptersFragment3 = OvidChaptersFragment.this;
                            ovidChaptersFragment3.f88791k4.m71775B1(ovidChaptersFragment3.f88788h4, bundleM71890s1.getString("bookId"), null, null, bundle5);
                            return;
                        }
                        Bundle bundle6 = new Bundle();
                        bundle6.putBundle("DB", OvidChaptersFragment.this.f88788h4);
                        bundle6.putString("ParentId", bundleM71890s1.getString("id"));
                        OvidChaptersFragment.this.f88791k4.m71798N(OvidChaptersActivity.class, OvidChaptersFragment.class, bundle6);
                        return;
                    }
                    if (string4.equals(IcyHeaders.f28171a3)) {
                        OvidChaptersFragment ovidChaptersFragment4 = OvidChaptersFragment.this;
                        ovidChaptersFragment4.f88791k4.m71772A1(ovidChaptersFragment4.f88788h4, string5, null, null);
                        return;
                    }
                    if (string4.equals(ExifInterface.f16317Y4)) {
                        return;
                    }
                    if (string4.equals(ExifInterface.f16326Z4)) {
                        OvidChaptersFragment ovidChaptersFragment5 = OvidChaptersFragment.this;
                        bundleM71844e0 = ovidChaptersFragment5.f88791k4.m71844e0(ovidChaptersFragment5.f88788h4, "select * from images where imagename='" + string5 + "'");
                        if (bundleM71844e0 == null) {
                            return;
                        }
                    } else {
                        if (!string4.equals("4")) {
                            if (string4.equals("5")) {
                                OvidChaptersFragment ovidChaptersFragment6 = OvidChaptersFragment.this;
                                ovidChaptersFragment6.f88791k4.m71772A1(ovidChaptersFragment6.f88788h4, string5, ovidChaptersFragment6.m72466T2(bundle2.getString("subText")), null);
                                return;
                            }
                            return;
                        }
                        OvidChaptersFragment ovidChaptersFragment7 = OvidChaptersFragment.this;
                        bundleM71844e0 = ovidChaptersFragment7.f88791k4.m71844e0(ovidChaptersFragment7.f88788h4, "select * from tables where id=" + string5);
                        if (bundleM71844e0 == null) {
                            return;
                        }
                    }
                    String string6 = bundleM71844e0.getString("bookId");
                    String string7 = bundleM71844e0.getString("goto");
                    OvidChaptersFragment ovidChaptersFragment8 = OvidChaptersFragment.this;
                    ovidChaptersFragment8.f88791k4.m71772A1(ovidChaptersFragment8.f88788h4, string6, null, string7);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(false);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: X2 */
        public void mo71973X2() {
            this.f88671B4.m72429f0(this.f88795o4);
            this.f88803w4.setAdapter(this.f88671B4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id, Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new OvidChaptersFragment());
    }
}

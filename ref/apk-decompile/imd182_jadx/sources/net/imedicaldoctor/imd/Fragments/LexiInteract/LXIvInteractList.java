package net.imedicaldoctor.imd.Fragments.LexiInteract;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract;

/* loaded from: classes3.dex */
public class LXIvInteractList extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f88445F4;

    /* renamed from: G4 */
    private ArrayList<Bundle> f88446G4;

    /* renamed from: H4 */
    private String f88447H4;

    /* renamed from: I4 */
    private String f88448I4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f88445F4 = m15387y().getBundle("db");
        this.f88446G4 = m15387y().getParcelableArrayList("items");
        this.f88447H4 = m15387y().getString("titleProperty");
        this.f88448I4 = m15387y().getString("type");
        new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new ArrayAdapter<Bundle>(m15366r(), C5562R.layout.list_view_item_simple_text, C5562R.id.text, this.f88446G4) { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractList.1
            @Override // android.widget.ArrayAdapter, android.widget.Adapter
            public View getView(int i2, View view, ViewGroup viewGroup) {
                if (view == null) {
                    view = LayoutInflater.from(LXIvInteractList.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text, viewGroup, false);
                    view.setTag(view.findViewById(C5562R.id.text));
                }
                ((TextView) view.getTag()).setText(((Bundle) LXIvInteractList.this.f88446G4.get(i2)).getString(LXIvInteractList.this.f88447H4));
                return view;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractList.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                ((LXIvInteract.LXIvInteractFragment) LXIvInteractList.this.m15351l0()).m72342q3((Bundle) LXIvInteractList.this.f88446G4.get(i2), LXIvInteractList.this.f88448I4);
                LXIvInteractList.this.mo15203M2();
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

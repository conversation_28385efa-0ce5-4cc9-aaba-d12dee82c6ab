package net.imedicaldoctor.imd.Fragments.LexiInteract;

import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class LXInteractResult extends iMDActivity {

    public static class LXInteractResultFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private ArrayList<Bundle> f88400A4;

        /* renamed from: B4 */
        private ArrayList<Bundle> f88401B4;

        /* renamed from: C4 */
        public NotStickySectionAdapter f88402C4;

        public class HeaderViewHolder {

            /* renamed from: a */
            public final TextView f88408a;

            public HeaderViewHolder(View view) {
                this.f88408a = (TextView) view.findViewById(C5562R.id.header_text);
            }
        }

        public class InteractionViewHolder {

            /* renamed from: a */
            public final TextView f88410a;

            /* renamed from: b */
            public final ImageView f88411b;

            /* renamed from: c */
            public final TextView f88412c;

            public InteractionViewHolder(View view) {
                this.f88412c = (TextView) view.findViewById(C5562R.id.drug_two_text);
                this.f88410a = (TextView) view.findViewById(C5562R.id.drug_one_text);
                this.f88411b = (ImageView) view.findViewById(C5562R.id.image);
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            CompressHelper compressHelper;
            Bundle bundle2;
            String str;
            View view = this.f88797q4;
            if (view != null) {
                return view;
            }
            this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            m72469W2(bundle);
            m72465S2();
            this.f88801u4.setText("");
            this.f88798r4.setTitle("Interaction Results");
            SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            this.f88799s4 = searchView;
            if (Build.VERSION.SDK_INT >= 26) {
                searchView.setImportantForAutofill(8);
            }
            this.f88799s4.setVisibility(8);
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            appBarLayout.m35746D(false, false);
            appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractResult.LXInteractResultFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    relativeLayout.setVisibility(0);
                }
            }, 800L);
            this.f88401B4 = m15387y().containsKey("Drugs") ? m15387y().getParcelableArrayList("Drugs") : new ArrayList<>();
            if (bundle == null || !bundle.containsKey("mInteractionSections")) {
                new ArrayList();
                ArrayList arrayList = new ArrayList();
                ArrayList arrayList2 = new ArrayList();
                Iterator<Bundle> it2 = this.f88401B4.iterator();
                while (it2.hasNext()) {
                    Bundle next = it2.next();
                    arrayList2.add(next.getString("id"));
                    if (next.getString("brand_id").length() > 0) {
                        arrayList.add(next.getString("brand_id"));
                    }
                }
                String strJoin = TextUtils.join(",", arrayList2);
                String strJoin2 = arrayList.size() > 0 ? TextUtils.join(",", arrayList) : "";
                this.f88791k4.m71866m(this.f88788h4, "drop table if exists temp_categories;");
                this.f88791k4.m71866m(this.f88788h4, "create table temp_categories as select c.id as id, b.generic_id as generic_id, b.name as name, b.id as brand_id from category c,category_generic_xref cgx, brand b where cgx.generic_id = b.generic_id and b.id in (" + strJoin2 + ") and c.id = cgx.category_id union select c.id as id, cgx.generic_id as generic_id, g.name as name, cast(null as integer) as brand_id from category c, category_generic_xref cgx, generic g where cgx.generic_id in (" + strJoin + ") and c.id = cgx.category_id and g.id = cgx.generic_id");
                if (this.f88401B4.size() == 1) {
                    compressHelper = this.f88791k4;
                    bundle2 = this.f88788h4;
                    str = "select m.id as id, m.risk as risk, c1.name as text1, t.generic_id, t.brand_id, c2.name as text2, m.filter                              from temp_categories t         join monograph m on (m.object_id = t.id)         join category c1 on (c1.id = m.object_id)         join category c2 on (c2.id = m.precipitant_id)         where not exists (select mgx.monograph_id from monograph_generic_exception_xref mgx                           where mgx.generic_id = t.generic_id                           and mgx.category_id = t.id                           and mgx.monograph_id = m.id)         and not exists (select mbx.monograph_id from monograph_brand_exception_xref mbx                         where mbx.brand_id = t.brand_id                         and mbx.monograph_id = m.id)         union         select m.id, m.risk as risk, c1.name as text1, t.generic_id, t.brand_id, c2.name as text2, m.filter         from temp_categories t         join monograph m on (m.precipitant_id = t.id)         join category c1 on (c1.id = m.object_id)         join category c2 on (c2.id = m.precipitant_id)         where not exists (select mgx.monograph_id from monograph_generic_exception_xref mgx                           where mgx.generic_id = t.generic_id                           and mgx.category_id = t.id                            and mgx.monograph_id = m.id)          and not exists (select mbx.monograph_id from monograph_brand_exception_xref mbx                          where mbx.brand_id = t.brand_id                          and mbx.monograph_id = m.id)          order by risk desc, text1, text2";
                } else {
                    compressHelper = this.f88791k4;
                    bundle2 = this.f88788h4;
                    str = "select m.id, m.risk as risk, c1.name as text1, c1.generic_id, c1.brand_id, c2.name as text2, c2.generic_id, c2.brand_id, m.filter                            from temp_categories c1                            join temp_categories c2 on (c1.generic_id != c2.generic_id)                            join monograph m on (m.object_id = c1.id and m.precipitant_id = c2.id)                            and not exists (                                            select mgx.monograph_id from monograph_generic_exception_xref mgx                                            where (mgx.generic_id = c1.generic_id and mgx.category_id = c1.id or mgx .generic_id = c2.generic_id and mgx.category_id = c2.id)                                            and mgx.monograph_id = m.id )                            and not exists (                                             select mbx.monograph_id                                            from monograph_brand_exception_xref mbx                                            where mbx.brand_id = c1.brand_id or mbx.brand_id = c2.brand_id and mbx.monograph_id = m.id ) group by m.id                           order by m.risk desc, text1, text2";
                }
                this.f88400A4 = this.f88791k4.m71887r2(compressHelper.m71817V(bundle2, str), "risk");
            } else {
                this.f88400A4 = bundle.getParcelableArrayList("mInteractionSections");
                this.f88401B4 = bundle.getParcelableArrayList("mDrugs");
            }
            NotStickySectionAdapter notStickySectionAdapter = new NotStickySectionAdapter(m15366r(), this.f88400A4, "title", C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractResult.LXInteractResultFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                /* renamed from: f0 */
                public void mo72200f0(RecyclerView.ViewHolder viewHolder, final Bundle bundle3, int i2) {
                    RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                    String string = bundle3.getString("text1");
                    String string2 = bundle3.getString("text2");
                    rippleTextFullViewHolder.f101499I.setText(string);
                    rippleTextFullViewHolder.f101500J.setText(string2);
                    String string3 = bundle3.getString("risk");
                    rippleTextFullViewHolder.f101501K.setImageDrawable(LXInteractResultFragment.this.m15320b0().getDrawable(string3.equals("5") ? C5562R.drawable.xinteraction : string3.equals("4") ? C5562R.drawable.dinteraction : string3.equals(ExifInterface.f16326Z4) ? C5562R.drawable.cinteraction : string3.equals(ExifInterface.f16317Y4) ? C5562R.drawable.binteraction : string3.equals(IcyHeaders.f28171a3) ? C5562R.drawable.ainteraction : 0));
                    rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractResult.LXInteractResultFragment.2.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            LXInteractResultFragment lXInteractResultFragment = LXInteractResultFragment.this;
                            CompressHelper compressHelper2 = lXInteractResultFragment.f88791k4;
                            Bundle bundleM71890s1 = compressHelper2.m71890s1(compressHelper2.m71817V(lXInteractResultFragment.f88788h4, "SELECT                                                   o.name||' / '||p.name as title,                                                   m.risk                as risk,                                                   m.summary             as summary,                                                   s.severity            as severity,                                                   onset.onset           as onset,                                                   r.reliability         as reliability,                                                   m.management          as management,                                                   m.discussion          as discussion,                                                   m.footnotes           as footnotes,                                                    m.dependencies                                                   as dependencies                                                    FROM monograph m                                                    JOIN severity s ON s.id = m.severity_id                                                    JOIN category o ON o.id = m.object_id                                                   JOIN category p ON p.id = m.precipitant_id                                                    JOIN reliability r ON r.id = m.reliability_id                                                   LEFT JOIN onset ON onset.id = m.onset_id                                                   WHERE m.id = " + bundle3.getString("id")));
                            LXInteractResultFragment lXInteractResultFragment2 = LXInteractResultFragment.this;
                            ArrayList<Bundle> arrayListM71817V = lXInteractResultFragment2.f88791k4.m71817V(lXInteractResultFragment2.f88788h4, "select c.name, c.id, g.name, g.id, mgx.category_id from monograph m join category c on m.object_id = c.id join category_generic_xref cgx on cgx.category_id = c.id join generic g on g.id = cgx.generic_id and not g.combination left join monograph_generic_exception_xref mgx on mgx.monograph_id = m.id and mgx.category_id = c.id and mgx.generic_id = g.id  where m.id = " + bundle3.getString("id") + " union select c.name, c.id, g.name, g.id, mgx.category_id  from monograph m join category c on m.precipitant_id = c.id  join category_generic_xref cgx on cgx.category_id = c.id  join generic g on g.id = cgx.generic_id and not g.combination  left join monograph_generic_exception_xref mgx on mgx.monograph_id = m.id and mgx.category_id = c.id and mgx.generic_id = g.id  where m.id = " + bundle3.getString("id") + ";");
                            Bundle bundle4 = new Bundle();
                            bundle4.putBundle("monograph", bundleM71890s1);
                            bundle4.putParcelableArrayList("monographMembers", arrayListM71817V);
                            bundle4.putBundle("monographItem", bundle3);
                            bundle4.putInt("Mode", 1);
                            LXInteractResultFragment lXInteractResultFragment3 = LXInteractResultFragment.this;
                            lXInteractResultFragment3.f88791k4.m71775B1(lXInteractResultFragment3.f88788h4, bundle3.getString("id"), null, null, bundle4);
                        }
                    });
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                /* renamed from: i0 */
                public String mo72201i0(String str2) {
                    return str2.equals("5") ? "X: Avoid combination" : str2.equals("4") ? "D: Consider therapy modification" : str2.equals(ExifInterface.f16326Z4) ? "C: Monitor therapy" : str2.equals(ExifInterface.f16317Y4) ? "B: No action needed" : str2.equals(IcyHeaders.f28171a3) ? "A: No known interaction" : str2;
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                /* renamed from: k0 */
                public RecyclerView.ViewHolder mo72202k0(View view2) {
                    return new RippleTextFullViewHolder(view2);
                }
            };
            this.f88402C4 = notStickySectionAdapter;
            notStickySectionAdapter.f101470i = "No Interactions Found";
            this.f88803w4.setAdapter(notStickySectionAdapter);
            m72461N2();
            m15358o2(false);
            m72468V2();
            return this.f88797q4;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new LXInteractResultFragment());
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.content.Context;
import android.os.Bundle;
import android.widget.ArrayAdapter;
import java.util.HashMap;
import java.util.List;

/* loaded from: classes3.dex */
public class StableArrayAdapter extends ArrayAdapter<Bundle> {

    /* renamed from: X */
    HashMap<String, Integer> f88926X;

    /* renamed from: s */
    final int f88927s;

    public StableArrayAdapter(Context context, int i2, List<Bundle> list) {
        super(context, i2, list);
        this.f88927s = -1;
        this.f88926X = new HashMap<>();
        int i3 = 0;
        for (Bundle bundle : list) {
            this.f88926X.put("Section" + bundle.getString("title"), Integer.valueOf(i3));
            i3++;
            for (int i4 = 0; i4 < bundle.getParcelableArrayList("items").size(); i4++) {
                Bundle bundle2 = (Bundle) bundle.getParcelableArrayList("items").get(i4);
                this.f88926X.put("Database" + bundle2.getString("Name"), Integer.valueOf(i3));
                i3++;
            }
        }
    }

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public long getItemId(int i2) {
        StringBuilder sb;
        String str;
        if (i2 < 0 || i2 >= this.f88926X.size()) {
            return -1L;
        }
        Bundle bundle = (Bundle) getItem(i2);
        if (bundle.containsKey("Item")) {
            bundle = bundle.getBundle("Item");
            sb = new StringBuilder();
            sb.append("Database");
            str = "Name";
        } else {
            sb = new StringBuilder();
            sb.append("Section");
            str = "Title";
        }
        sb.append(bundle.getString(str));
        return this.f88926X.get(sb.toString()).intValue();
    }

    @Override // android.widget.BaseAdapter, android.widget.Adapter
    public boolean hasStableIds() {
        return true;
    }
}

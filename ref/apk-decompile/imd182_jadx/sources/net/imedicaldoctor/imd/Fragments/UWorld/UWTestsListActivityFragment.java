package net.imedicaldoctor.imd.Fragments.UWorld;

import android.R;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Objects;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class UWTestsListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f89409A4;

    /* renamed from: B4 */
    public String f89410B4;

    /* renamed from: net.imedicaldoctor.imd.Fragments.UWorld.UWTestsListActivityFragment$1 */
    class C51381 extends ChaptersAdapter {
        C51381(Context context, ArrayList arrayList, String str, int i2) {
            super(context, arrayList, str, i2);
        }

        @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
        /* renamed from: e0 */
        public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle, int i2) {
            String str;
            MaterialRippleLayout materialRippleLayout;
            View.OnClickListener onClickListener;
            TestScoreViewHolder testScoreViewHolder = (TestScoreViewHolder) viewHolder;
            String str2 = "Test #" + bundle.getString("id");
            String string = bundle.getString("createdDate");
            String string2 = bundle.getString("qIds");
            String[] strArrSplit = string.split("\\|");
            if (strArrSplit.length > 1) {
                str = strArrSplit[1];
                String str3 = strArrSplit[0];
            } else {
                str = "";
            }
            String strM72694m3 = UWTestsListActivityFragment.this.m72694m3(bundle.getString("createdDate"));
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(string2, ",");
            Objects.requireNonNull(strArrSplitByWholeSeparator);
            String str4 = strArrSplitByWholeSeparator.length + " Qs, " + bundle.getString("mode");
            if (!bundle.getString("mode").equals("Reading") && !str.isEmpty()) {
                str4 = str4 + ", " + str.replace(":", "':") + "\"";
            }
            testScoreViewHolder.f89421I.setText(str2);
            testScoreViewHolder.f89422J.setText(str4 + "\nCreated: " + strM72694m3);
            testScoreViewHolder.f89423K.setText(CompressHelper.m71739O1(bundle.getString("subject") + " | " + bundle.getString("system"), 300));
            if (bundle.getString("done").equals(IcyHeaders.f28171a3)) {
                testScoreViewHolder.f89425M.setImageDrawable(UWTestsListActivityFragment.this.m15320b0().getDrawable(C5562R.drawable.circle_green));
                testScoreViewHolder.f89426N.setText("Score");
                testScoreViewHolder.f89424L.setVisibility(0);
                testScoreViewHolder.f89424L.setText(bundle.getString("score") + CSS.Value.f74136n0);
                materialRippleLayout = testScoreViewHolder.f89427O;
                onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestsListActivityFragment.1.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        UWTestsListActivityFragment uWTestsListActivityFragment = UWTestsListActivityFragment.this;
                        uWTestsListActivityFragment.f88791k4.m71772A1(uWTestsListActivityFragment.f88788h4, "testresult-" + bundle.getString("id"), null, null);
                    }
                };
            } else {
                testScoreViewHolder.f89425M.setImageDrawable(UWTestsListActivityFragment.this.m15320b0().getDrawable(C5562R.drawable.circle_blue));
                testScoreViewHolder.f89426N.setText("Resume");
                testScoreViewHolder.f89424L.setVisibility(8);
                materialRippleLayout = testScoreViewHolder.f89427O;
                onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestsListActivityFragment.1.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        UWTestsListActivityFragment uWTestsListActivityFragment = UWTestsListActivityFragment.this;
                        uWTestsListActivityFragment.f88791k4.m71772A1(uWTestsListActivityFragment.f88788h4, "test-" + bundle.getString("id"), null, null);
                    }
                };
            }
            materialRippleLayout.setOnClickListener(onClickListener);
            testScoreViewHolder.f89428P.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestsListActivityFragment.1.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    UWTestsListActivityFragment.this.m72692k3("QIDs", bundle.getString("qIds"));
                }
            });
            testScoreViewHolder.f89429Q.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestsListActivityFragment.1.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    new AlertDialog.Builder(UWTestsListActivityFragment.this.m15366r()).setTitle("Delete Test").mo1102l("Are you sure you want to delete this test?").setPositiveButton(R.string.yes, new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestsListActivityFragment.1.4.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i3) {
                            AnonymousClass4 anonymousClass4 = AnonymousClass4.this;
                            UWTestsListActivityFragment.this.m72693l3(bundle.getString("id"));
                        }
                    }).setNegativeButton(R.string.no, null).mo1095e(R.drawable.ic_dialog_alert).m1090I();
                }
            });
        }

        @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
        /* renamed from: h0 */
        public RecyclerView.ViewHolder mo71986h0(View view) {
            return UWTestsListActivityFragment.this.new TestScoreViewHolder(view);
        }
    }

    public class TestScoreViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f89421I;

        /* renamed from: J */
        public TextView f89422J;

        /* renamed from: K */
        public TextView f89423K;

        /* renamed from: L */
        public TextView f89424L;

        /* renamed from: M */
        public ImageView f89425M;

        /* renamed from: N */
        public TextView f89426N;

        /* renamed from: O */
        public MaterialRippleLayout f89427O;

        /* renamed from: P */
        public Button f89428P;

        /* renamed from: Q */
        public Button f89429Q;

        public TestScoreViewHolder(View view) {
            super(view);
            this.f89421I = (TextView) view.findViewById(C5562R.id.text_date);
            this.f89422J = (TextView) view.findViewById(C5562R.id.text_info1);
            this.f89423K = (TextView) view.findViewById(C5562R.id.text_info2);
            this.f89427O = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f89424L = (TextView) view.findViewById(C5562R.id.text_score);
            this.f89425M = (ImageView) view.findViewById(C5562R.id.image_view);
            this.f89426N = (TextView) view.findViewById(C5562R.id.text_resume);
            this.f89428P = (Button) view.findViewById(C5562R.id.button_copy_qids);
            this.f89429Q = (Button) view.findViewById(C5562R.id.button_delete);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: k3 */
    public void m72692k3(String str, String str2) {
        ((ClipboardManager) m15366r().getSystemService("clipboard")).setPrimaryClip(ClipData.newPlainText(str, str2));
        Toast.makeText(m15366r(), "Copied to clipboard", 0).show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: l3 */
    public void m72693l3(String str) {
        this.f88791k4.m71866m(this.f88788h4, "DELETE FROM tests WHERE id = " + str);
        this.f88791k4.m71866m(this.f88788h4, "DELETE FROM logs WHERE testId = " + str);
        ArrayList<Bundle> arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "Select * from tests order by id desc");
        this.f88794n4 = arrayListM71817V;
        ((ChaptersAdapter) this.f88792l4).m73465g0(arrayListM71817V);
        this.f88792l4.m27491G();
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        m72458J2();
        this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select * from tests order by id desc");
        C51381 c51381 = new C51381(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_uworld_test_button);
        this.f88792l4 = c51381;
        c51381.f101434h = "No Test Available";
        this.f88803w4.setAdapter(c51381);
        m72461N2();
        m15358o2(false);
        this.f88799s4.setVisibility(8);
        this.f88798r4.setTitle("Previous Tests");
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f89409A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f89409A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "";
    }

    /* renamed from: m3 */
    public String m72694m3(String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ");
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm").format(simpleDateFormat.parse(str));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return str;
        }
    }
}

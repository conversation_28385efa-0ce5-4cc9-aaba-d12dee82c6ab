package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.html.HTML;
import fi.iki.elonen.NanoHTTPD;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Utils.iMDWebView;

/* loaded from: classes3.dex */
public class EPOHTMLViewerFragment extends ViewerHelperFragment {
    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_webview, viewGroup, false);
        this.f89565C4 = viewInflate;
        this.f89569G4 = (iMDWebView) viewInflate.findViewById(C5562R.id.webView);
        this.f89579Q4 = new CompressHelper(m15366r());
        this.f89566D4 = m15387y().getBundle("DB");
        String string = m15387y().getString("Type");
        String string2 = m15387y().getString("URL");
        if (!string.equals(HTML.Tag.f74425y)) {
            if (string.equals("url")) {
                this.f89569G4.loadUrl(string2.replace("epourl-", ""));
            }
            return this.f89565C4;
        }
        String strReplace = string2.replace("epohtml-", "");
        try {
            String strM72817d4 = m72817d4(m15366r(), "EPOHeader.css");
            String strM72817d42 = m72817d4(m15366r(), "EPOFooter.css");
            this.f89569G4.loadData((strM72817d4.replace("[size]", "200").replace("[include]", "") + strReplace + strM72817d42).replace("..", "."), NanoHTTPD.f77082p, null);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
        }
        m72836s4();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        if (!this.f89579Q4.m71800N1(this.f89566D4, str) && str2.equals("http")) {
            this.f89579Q4.m71772A1(this.f89566D4, "epourl-" + str, null, null);
        }
        return true;
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.R;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;

/* loaded from: classes3.dex */
public class DividerItemDecoration extends RecyclerView.ItemDecoration {

    /* renamed from: c */
    private static final int[] f87905c = {R.attr.listDivider};

    /* renamed from: d */
    public static final int f87906d = 0;

    /* renamed from: e */
    public static final int f87907e = 1;

    /* renamed from: a */
    private final Drawable f87908a;

    /* renamed from: b */
    private int f87909b;

    public DividerItemDecoration(Context context, int i2) {
        TypedArray typedArrayObtainStyledAttributes = context.obtainStyledAttributes(f87905c);
        this.f87908a = typedArrayObtainStyledAttributes.getDrawable(0);
        typedArrayObtainStyledAttributes.recycle();
        m72101n(i2);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.ItemDecoration
    /* renamed from: f */
    public void mo27544f(Rect rect, int i2, RecyclerView recyclerView) {
        if (this.f87909b == 1) {
            rect.set(0, 0, 0, this.f87908a.getIntrinsicHeight());
        } else {
            rect.set(0, 0, this.f87908a.getIntrinsicWidth(), 0);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.ItemDecoration
    /* renamed from: h */
    public void mo27545h(Canvas canvas, RecyclerView recyclerView) {
        if (this.f87909b == 1) {
            m72100m(canvas, recyclerView);
        } else {
            m72099l(canvas, recyclerView);
        }
    }

    /* renamed from: l */
    public void m72099l(Canvas canvas, RecyclerView recyclerView) {
        int paddingTop = recyclerView.getPaddingTop();
        int height = recyclerView.getHeight() - recyclerView.getPaddingBottom();
        int childCount = recyclerView.getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = recyclerView.getChildAt(i2);
            int right = childAt.getRight() + ((ViewGroup.MarginLayoutParams) ((RecyclerView.LayoutParams) childAt.getLayoutParams())).rightMargin;
            this.f87908a.setBounds(right, paddingTop, this.f87908a.getIntrinsicHeight() + right, height);
            this.f87908a.draw(canvas);
        }
    }

    /* renamed from: m */
    public void m72100m(Canvas canvas, RecyclerView recyclerView) {
        int paddingLeft = recyclerView.getPaddingLeft();
        int width = recyclerView.getWidth() - recyclerView.getPaddingRight();
        int childCount = recyclerView.getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = recyclerView.getChildAt(i2);
            int bottom = childAt.getBottom() + ((ViewGroup.MarginLayoutParams) ((RecyclerView.LayoutParams) childAt.getLayoutParams())).bottomMargin;
            this.f87908a.setBounds(paddingLeft, bottom, width, this.f87908a.getIntrinsicHeight() + bottom);
            this.f87908a.draw(canvas);
        }
    }

    /* renamed from: n */
    public void m72101n(int i2) {
        if (i2 != 0 && i2 != 1) {
            throw new IllegalArgumentException("invalid orientation");
        }
        this.f87909b = i2;
    }
}

package net.imedicaldoctor.imd.Fragments.Uptodate;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.html.HTML;
import fi.iki.elonen.NanoHTTPD;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class UTDMenuFragment extends DialogFragment {
    /* renamed from: g3 */
    private static String m72725g3(Context context, String str) throws Exception {
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(context.getAssets().open(str), StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        while (true) {
            String line = bufferedReader.readLine();
            if (line == null) {
                bufferedReader.close();
                return sb.toString();
            }
            sb.append(line + StringUtils.f103471LF);
        }
    }

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_utdmenu, (ViewGroup) null);
        WebView webView = (WebView) viewInflate.findViewById(C5562R.id.webview);
        try {
            String string = m15387y().getString(HTML.Tag.f74425y);
            String strM72725g3 = m72725g3(m15366r(), "UTDHeader.css");
            String strM72725g32 = m72725g3(m15366r(), "UTDFooter.css");
            String str = strM72725g3.replace("[size]", "200").replace("[Title]", m15387y().getString("title")) + string + strM72725g32;
            webView.setWebViewClient(new WebViewClient() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDMenuFragment.1
                @Override // android.webkit.WebViewClient
                public boolean shouldOverrideUrlLoading(WebView webView2, String str2) {
                    iMDLogger.m73554j("MenuFragment", str2);
                    ((UTDViewerActivity.UTDViewerFragment) UTDMenuFragment.this.m15351l0()).m72746T4(str2);
                    UTDMenuFragment.this.mo15203M2();
                    return true;
                }
            });
            webView.getSettings().setAllowFileAccess(true);
            webView.getSettings().setJavaScriptEnabled(true);
            webView.loadDataWithBaseURL("file:///android_asset/", str, NanoHTTPD.f77082p, "utf-8", null);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in MenuFragment", e2.toString());
        }
        builder.setView(viewInflate);
        return builder.create();
    }
}

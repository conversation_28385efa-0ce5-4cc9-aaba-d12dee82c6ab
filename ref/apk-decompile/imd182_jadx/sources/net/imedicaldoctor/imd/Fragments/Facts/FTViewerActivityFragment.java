package net.imedicaldoctor.imd.Fragments.Facts;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class FTViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88252X4;

    /* renamed from: Y4 */
    public ArrayList<String> f88253Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88254Z4;

    /* renamed from: a5 */
    public boolean f88255a5;

    /* renamed from: K4 */
    private void m72253K4(String str) {
        ArrayList<String> arrayList = this.f88253Y4;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f88253Y4.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", next);
            bundle.putString("Description", "");
            bundle.putString("id", next);
            if (new File(next).length() > 5000) {
                arrayList2.add(bundle);
            }
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public void m72254I4() {
        Iterator<Bundle> it2 = this.f88254Z4.iterator();
        while (it2.hasNext()) {
            String string = it2.next().getString("fId");
            if (!string.equals("prodlist_DIV") && this.f89566D4.getString("Name").equals("facts.db")) {
                this.f89569G4.m73433g("showSingleSection('" + string + "');");
            }
        }
    }

    /* renamed from: J4 */
    public String m72255J4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return m72840w3(this.f88253Y4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        this.f88255a5 = true;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Facts.FTViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String str = FTViewerActivityFragment.this.f89563A4;
                    if (str == null || str.length() == 0) {
                        iMDLogger.m73550f("Loading Document", FTViewerActivityFragment.this.f89567E4);
                        FTViewerActivityFragment fTViewerActivityFragment = FTViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71817V = fTViewerActivityFragment.f89579Q4.m71817V(fTViewerActivityFragment.f89566D4, "Select * from docs where docName='" + FTViewerActivityFragment.this.f89567E4 + "'");
                        if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                            FTViewerActivityFragment.this.f88252X4 = arrayListM71817V.get(0);
                            FTViewerActivityFragment fTViewerActivityFragment2 = FTViewerActivityFragment.this;
                            fTViewerActivityFragment2.f88254Z4 = fTViewerActivityFragment2.f89579Q4.m71817V(fTViewerActivityFragment2.f89566D4, "select * from fields where docId='" + FTViewerActivityFragment.this.f89567E4 + "'");
                            FTViewerActivityFragment fTViewerActivityFragment3 = FTViewerActivityFragment.this;
                            fTViewerActivityFragment3.f89568F4 = fTViewerActivityFragment3.f88252X4.getString("title");
                            FTViewerActivityFragment fTViewerActivityFragment4 = FTViewerActivityFragment.this;
                            String strM71773B = fTViewerActivityFragment4.f89579Q4.m71773B(fTViewerActivityFragment4.f88252X4.getString(Annotation.f68283i3), FTViewerActivityFragment.this.f89567E4, "127");
                            FTViewerActivityFragment fTViewerActivityFragment5 = FTViewerActivityFragment.this;
                            String strM71773B2 = fTViewerActivityFragment5.f89579Q4.m71773B(fTViewerActivityFragment5.f88252X4.getString("java"), FTViewerActivityFragment.this.f89567E4, "127");
                            FTViewerActivityFragment fTViewerActivityFragment6 = FTViewerActivityFragment.this;
                            String strM72817d4 = fTViewerActivityFragment6.m72817d4(fTViewerActivityFragment6.m15366r(), "FTHeader.css");
                            FTViewerActivityFragment fTViewerActivityFragment7 = FTViewerActivityFragment.this;
                            String strM72817d42 = fTViewerActivityFragment7.m72817d4(fTViewerActivityFragment7.m15366r(), "FTFooter.css");
                            String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", FTViewerActivityFragment.this.f89568F4).replace("[include]", "").replace("[javascript]", strM71773B2);
                            FTViewerActivityFragment.this.f89563A4 = strReplace + strM71773B + strM72817d42;
                        }
                        FTViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                        return;
                    }
                    FTViewerActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    FTViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Facts.FTViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = FTViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    FTViewerActivityFragment fTViewerActivityFragment = FTViewerActivityFragment.this;
                    fTViewerActivityFragment.m72780C4(fTViewerActivityFragment.f89595p4);
                    return;
                }
                String str2 = FTViewerActivityFragment.this.f89566D4.getString("Path") + "/base";
                FTViewerActivityFragment fTViewerActivityFragment2 = FTViewerActivityFragment.this;
                fTViewerActivityFragment2.m72795O3(fTViewerActivityFragment2.f89563A4, str2);
                FTViewerActivityFragment.this.m72836s4();
                FTViewerActivityFragment.this.m72831p4();
                FTViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                FTViewerActivityFragment.this.m15358o2(false);
                FTViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = strM71753g1.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72255J4(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = strM71753g1 + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f88253Y4 = arrayList;
            mo71972o4();
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        if (this.f88255a5) {
            this.f88255a5 = false;
            m72254I4();
            this.f89569G4.m73433g("ConvertAllImages();");
            this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
            this.f89569G4.m73433g("onBodyLoad();");
            super.mo71956Z3(webView, str);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            m72253K4("asdfafdsaf");
            return true;
        }
        if (itemId == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f88254Z4);
            bundle.putString("TitleProperty", "title");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        String str4;
        if (str2.equals("image")) {
            m72253K4(str3);
            return true;
        }
        if (str2.equals(Annotation.f68285k3) || (str2.equals("http") & str3.contains("localhost:"))) {
            String str5 = "//" + CompressHelper.m71753g1(this.f89566D4, "base") + "/";
            if (str3.contains("#")) {
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, "#");
                String str6 = strArrSplitByWholeSeparator[0];
                String str7 = strArrSplitByWholeSeparator[1];
                String strReplace = str6.replace(str5, "").replace(".html", "");
                if (this.f88252X4.getString("docName").equalsIgnoreCase(strReplace)) {
                    this.f89569G4.m73433g("window.location.href = \"#" + str7 + "\"");
                    return true;
                }
                if (strReplace.length() == 0) {
                    this.f89569G4.m73433g("window.location.href = \"#" + str7 + "\"");
                    return true;
                }
                if (strReplace.endsWith("/")) {
                    this.f89569G4.m73433g("window.location.href = \"#" + str7 + "\"");
                    return true;
                }
                str4 = str7;
                str3 = strReplace;
            } else {
                str4 = "";
            }
            String strReplace2 = str3.replace(str5, "");
            if (strReplace2.length() == 0) {
                return true;
            }
            str3 = strReplace2.replace(".html", "").toLowerCase();
            if (str3.contains("/monodisp.aspx?")) {
                String strM71751f = CompressHelper.m71751f(str3, "monoid=", "&");
                if (strM71751f == null || strM71751f.length() == 0) {
                    strM71751f = this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(str3, "&monoid="));
                }
                CompressHelper compressHelper = this.f89579Q4;
                if (compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from docs where docName='" + strM71751f + "'")) == null) {
                    CompressHelper.m71767x2(m15366r(), "Sorry, not in this book", 1);
                } else {
                    this.f89579Q4.m71772A1(this.f89566D4, strM71751f, null, str4);
                }
            }
        }
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

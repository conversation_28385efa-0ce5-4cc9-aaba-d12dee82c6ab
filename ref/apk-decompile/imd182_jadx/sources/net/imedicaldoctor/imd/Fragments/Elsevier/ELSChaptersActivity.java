package net.imedicaldoctor.imd.Fragments.Elsevier;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class ELSChaptersActivity extends iMDActivity {

    public static class ELSChaptersFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private static String f87999A4;

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72462O2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72462O2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            ((RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout)).setVisibility(0);
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select id as _id,* from chapters");
            this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "name") { // from class: net.imedicaldoctor.imd.Fragments.Elsevier.ELSChaptersActivity.ELSChaptersFragment.1
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: f0 */
                public void mo71975f0(Bundle bundle2, int i2) {
                    ELSChaptersFragment.this.m72468V2();
                    ELSChaptersFragment eLSChaptersFragment = ELSChaptersFragment.this;
                    eLSChaptersFragment.f88791k4.m71772A1(eLSChaptersFragment.f88788h4, bundle2.getString("docId"), null, null);
                }
            };
            this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.Elsevier.ELSChaptersActivity.ELSChaptersFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
                /* renamed from: e0 */
                public void mo71953e0(Bundle bundle2, int i2) {
                    Bundle bundleM71844e0;
                    String str;
                    ELSChaptersFragment.this.m72468V2();
                    String string = bundle2.getString("type");
                    String string2 = bundle2.getString("contentId");
                    if (string.equals(IcyHeaders.f28171a3)) {
                        ELSChaptersFragment eLSChaptersFragment = ELSChaptersFragment.this;
                        eLSChaptersFragment.f88791k4.m71772A1(eLSChaptersFragment.f88788h4, string2, null, null);
                        return;
                    }
                    if (string.equals(ExifInterface.f16326Z4)) {
                        ELSChaptersFragment eLSChaptersFragment2 = ELSChaptersFragment.this;
                        bundleM71844e0 = eLSChaptersFragment2.f88791k4.m71844e0(eLSChaptersFragment2.f88788h4, "select * from images where id='" + string2 + "'");
                        if (bundleM71844e0 == null) {
                            return;
                        } else {
                            str = "docId";
                        }
                    } else {
                        if (!string.equals("4")) {
                            if (string.equals("5")) {
                                ELSChaptersFragment eLSChaptersFragment3 = ELSChaptersFragment.this;
                                eLSChaptersFragment3.f88791k4.m71772A1(eLSChaptersFragment3.f88788h4, string2, eLSChaptersFragment3.m72466T2(bundle2.getString("subText")), null);
                                return;
                            }
                            return;
                        }
                        ELSChaptersFragment eLSChaptersFragment4 = ELSChaptersFragment.this;
                        bundleM71844e0 = eLSChaptersFragment4.f88791k4.m71844e0(eLSChaptersFragment4.f88788h4, "select * from tables where id=" + string2);
                        if (bundleM71844e0 == null) {
                            return;
                        } else {
                            str = "sectionId";
                        }
                    }
                    String string3 = bundleM71844e0.getString(str);
                    String string4 = bundleM71844e0.getString("goto");
                    ELSChaptersFragment eLSChaptersFragment5 = ELSChaptersFragment.this;
                    eLSChaptersFragment5.f88791k4.m71772A1(eLSChaptersFragment5.f88788h4, string3, null, string4);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(true);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id, Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new ELSChaptersFragment());
    }
}

package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.app.Dialog;
import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity;

/* loaded from: classes3.dex */
public class VDDxFindingsDialog extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f89846F4;

    /* renamed from: G4 */
    private String f89847G4;

    /* renamed from: H4 */
    private Bundle f89848H4;

    /* renamed from: I4 */
    private ArrayList<String> f89849I4;

    /* renamed from: J4 */
    private ArrayList<String> f89850J4;

    /* renamed from: K4 */
    private String f89851K4;

    /* renamed from: L4 */
    private Bundle f89852L4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        String str;
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f89846F4 = m15387y().getBundle("db");
        this.f89852L4 = m15387y().getBundle("parent");
        this.f89848H4 = m15387y().getBundle("allFindings");
        this.f89849I4 = m15387y().getStringArrayList("selectedFindings");
        this.f89850J4 = m15387y().getStringArrayList("disabledItems");
        this.f89851K4 = m15387y().getString("moduleId");
        this.f89847G4 = !this.f89852L4.containsKey("id") ? "0" : this.f89852L4.getString("id");
        if (this.f89852L4.containsKey("leaf") && this.f89852L4.getString("leaf").equals(IcyHeaders.f28171a3)) {
            str = ("Select -1 as _id, '" + this.f89852L4.getString("id") + "' as id, '" + this.f89852L4.getString("shortName") + "' as shortName, '" + this.f89852L4.getString("longName") + "' as longName, '' as children, '1' as leaf, '' as modules UNION ") + "SELECT id as _id, id, shortName, longName, children, leaf, ',' || supportedModules || ',' as modules FROM Findings where parentId = " + this.f89847G4 + " and modules like '%," + this.f89851K4 + ",%' order by shortName asc";
        } else {
            str = "SELECT id as _id,id, shortName, longName, children, leaf, ',' || supportedModules || ',' as modules FROM Findings where parentId = " + this.f89847G4 + " and modules like '%," + this.f89851K4 + ",%' order by shortName asc";
        }
        final CompressHelper compressHelper = new CompressHelper(m15366r());
        final CursorAdapter cursorAdapter = new CursorAdapter(m15366r(), compressHelper.m71850h(compressHelper.m71817V(this.f89846F4, str)), 0) { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxFindingsDialog.1
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                TextView textView = (TextView) view.getTag();
                Bundle bundleM71868m2 = compressHelper.m71868m2(cursor);
                if (bundleM71868m2.getString("children").length() == 0) {
                    ImageView imageView = (ImageView) view.findViewById(C5562R.id.check_icon);
                    String string = bundleM71868m2.getString("id");
                    if (VDDxFindingsDialog.this.f89849I4.contains(string)) {
                        imageView.setVisibility(0);
                    } else {
                        imageView.setVisibility(4);
                    }
                    if (VDDxFindingsDialog.this.f89850J4.contains(string)) {
                        textView.setTextColor(-7829368);
                        imageView.setVisibility(0);
                    }
                }
                textView.setText(cursor.getString(cursor.getColumnIndex("shortName")));
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getItemViewType(int i2) {
                Cursor cursor = (Cursor) getItem(i2);
                return cursor.getString(cursor.getColumnIndex("children")).length() > 0 ? 0 : 1;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getViewTypeCount() {
                return 2;
            }

            @Override // android.widget.BaseAdapter, android.widget.ListAdapter
            public boolean isEnabled(int i2) {
                if (compressHelper.m71868m2((Cursor) getItem(i2)).getString("children").length() != 0) {
                    return true;
                }
                return !VDDxFindingsDialog.this.f89850J4.contains(r3.getString("id"));
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate2 = LayoutInflater.from(context).inflate(cursor.getString(cursor.getColumnIndex("children")).length() > 0 ? C5562R.layout.list_view_item_simple_text_arrow : C5562R.layout.list_view_item_simple_text_check, viewGroup, false);
                viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                return viewInflate2;
            }
        };
        listView.setAdapter((ListAdapter) cursorAdapter);
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxFindingsDialog.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                if (cursorMo10512c.moveToPosition(i2)) {
                    Bundle bundleM71868m2 = compressHelper.m71868m2(cursorMo10512c);
                    if (bundleM71868m2.getString("children").length() == 0) {
                        String string = bundleM71868m2.getString("id");
                        if (VDDxFindingsDialog.this.f89849I4.contains(string)) {
                            VDDxFindingsDialog.this.f89849I4.remove(string);
                        } else {
                            VDDxFindingsDialog.this.f89849I4.add(string);
                        }
                        ((VDDxBuilderActivity.VDDXBuilderFragment) VDDxFindingsDialog.this.m15351l0()).m72885J4();
                        cursorAdapter.notifyDataSetChanged();
                        return;
                    }
                    VDDxFindingsDialog vDDxFindingsDialog = new VDDxFindingsDialog();
                    Bundle bundle2 = new Bundle();
                    bundle2.putBundle("db", VDDxFindingsDialog.this.f89846F4);
                    bundle2.putBundle("allFindings", VDDxFindingsDialog.this.f89848H4);
                    bundle2.putStringArrayList("selectedFindings", VDDxFindingsDialog.this.f89849I4);
                    bundle2.putBundle("parent", bundleM71868m2);
                    bundle2.putString("moduleId", VDDxFindingsDialog.this.f89851K4);
                    bundle2.putStringArrayList("disabledItems", VDDxFindingsDialog.this.f89850J4);
                    vDDxFindingsDialog.m15342i2(bundle2);
                    vDDxFindingsDialog.mo15218Z2(true);
                    vDDxFindingsDialog.m15245A2(VDDxFindingsDialog.this.m15351l0(), 0);
                    vDDxFindingsDialog.mo15222e3(VDDxFindingsDialog.this.m15283M(), "VDDialogFragment");
                    VDDxFindingsDialog.this.mo15203M2();
                }
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

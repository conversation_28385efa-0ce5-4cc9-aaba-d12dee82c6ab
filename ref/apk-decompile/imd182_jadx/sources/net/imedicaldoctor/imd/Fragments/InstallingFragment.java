package net.imedicaldoctor.imd.Fragments;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.Process;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.observers.DisposableObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.iMDLogger;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.FileHeader;
import net.lingala.zip4j.p026io.inputstream.ZipInputStream;
import okio.BufferedSink;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class InstallingFragment extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f88261F4;

    /* renamed from: G4 */
    private String f88262G4;

    /* renamed from: H4 */
    private TextView f88263H4;

    /* renamed from: I4 */
    private TextView f88264I4;

    /* renamed from: J4 */
    private ProgressBar f88265J4;

    /* renamed from: K4 */
    private TextView f88266K4;

    /* renamed from: L4 */
    private ImageView f88267L4;

    /* renamed from: M4 */
    private View f88268M4;

    /* renamed from: N4 */
    private Timer f88269N4;

    /* renamed from: O4 */
    private int f88270O4;

    /* renamed from: P4 */
    private Activity f88271P4;

    /* renamed from: Q4 */
    private boolean f88272Q4;

    /* renamed from: R4 */
    private Button f88273R4;

    /* renamed from: S4 */
    private Button f88274S4;

    /* renamed from: T4 */
    private String f88275T4;

    /* renamed from: U4 */
    public Handler f88276U4 = new Handler() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.5
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            InstallingFragment.this.f88270O4++;
            if (InstallingFragment.this.f88270O4 == 7) {
                InstallingFragment.this.f88270O4 = 1;
            }
            String str = "";
            for (int i2 = 0; i2 < InstallingFragment.this.f88270O4; i2++) {
                str = str + ".";
            }
            InstallingFragment.this.f88266K4.setText(str);
        }
    };

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: g3 */
    public void m72265g3() {
        this.f88272Q4 = true;
        while (this.f88272Q4) {
        }
    }

    /* renamed from: i3 */
    static /* synthetic */ String m72267i3(InstallingFragment installingFragment, String str) {
        installingFragment.f88275T4 = str;
        return str;
    }

    /* renamed from: k3 */
    static /* synthetic */ boolean m72269k3(InstallingFragment installingFragment, boolean z) {
        installingFragment.f88272Q4 = z;
        return z;
    }

    /* renamed from: l3 */
    static /* synthetic */ void m72270l3(InstallingFragment installingFragment) {
        installingFragment.m72265g3();
    }

    /* renamed from: m3 */
    static /* synthetic */ void m72271m3(InstallingFragment installingFragment, File file) {
        installingFragment.m72279y3(file);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: y3 */
    public void m72279y3(File file) {
        try {
            if (!file.isDirectory()) {
                if (file.canRead() && file.canWrite()) {
                    return;
                }
                file.setReadable(true, false);
                file.setWritable(true, false);
                return;
            }
            if (!file.canRead() || !file.canWrite() || !file.canExecute()) {
                file.setReadable(true, false);
                file.setWritable(true, false);
                file.setExecutable(true, false);
            }
            File[] fileArrListFiles = file.listFiles();
            if (fileArrListFiles != null) {
                for (File file2 : fileArrListFiles) {
                    m72279y3(file2);
                }
            }
        } catch (Exception unused) {
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: M0 */
    public void mo15284M0(Activity activity) {
        super.mo15284M0(activity);
        this.f88271P4 = activity;
    }

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_installing, (ViewGroup) null);
        this.f88268M4 = viewInflate;
        this.f88263H4 = (TextView) viewInflate.findViewById(C5562R.id.installing_label);
        this.f88264I4 = (TextView) this.f88268M4.findViewById(C5562R.id.database_name);
        this.f88266K4 = (TextView) this.f88268M4.findViewById(C5562R.id.progress_label);
        this.f88265J4 = (ProgressBar) this.f88268M4.findViewById(C5562R.id.progress_bar);
        this.f88267L4 = (ImageView) this.f88268M4.findViewById(C5562R.id.stethoscope);
        this.f88273R4 = (Button) this.f88268M4.findViewById(C5562R.id.cancel_button);
        this.f88274S4 = (Button) this.f88268M4.findViewById(C5562R.id.close_button);
        Typeface typefaceCreateFromAsset = Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf");
        this.f88263H4.setTypeface(typefaceCreateFromAsset);
        this.f88264I4.setTypeface(typefaceCreateFromAsset);
        this.f88265J4.setProgress(0);
        m15366r().setFinishOnTouchOutside(false);
        final CompressHelper compressHelper = new CompressHelper(m15366r());
        final VBHelper vBHelper = new VBHelper(m15366r());
        Observable observableM59775s4 = Observable.m59451w1(new ObservableOnSubscribe<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.1
            /*  JADX ERROR: JadxRuntimeException in pass: BlockProcessor
                jadx.core.utils.exceptions.JadxRuntimeException: Unreachable block: B:316:0x09d1
                	at jadx.core.dex.visitors.blocks.BlockProcessor.checkForUnreachableBlocks(BlockProcessor.java:131)
                	at jadx.core.dex.visitors.blocks.BlockProcessor.processBlocksTree(BlockProcessor.java:57)
                	at jadx.core.dex.visitors.blocks.BlockProcessor.visit(BlockProcessor.java:49)
                */
            /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
            /* JADX WARN: Unreachable blocks removed: 2, instructions: 3 */
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@io.reactivex.rxjava3.annotations.NonNull io.reactivex.rxjava3.core.ObservableEmitter<android.os.Bundle> r47) throws java.lang.Throwable {
                /*
                    Method dump skipped, instructions count: 2763
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.InstallingFragment.C48601.mo59827a(io.reactivex.rxjava3.core.ObservableEmitter):void");
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e());
        final DisposableObserver<Bundle> disposableObserver = new DisposableObserver<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.2
            @Override // io.reactivex.rxjava3.core.Observer
            /* renamed from: c, reason: merged with bridge method [inline-methods] */
            public void onNext(@NonNull Bundle bundle2) {
                if (bundle2.containsKey("progress")) {
                    String string = bundle2.getString("labelText");
                    String string2 = bundle2.getString("progress");
                    InstallingFragment.this.f88264I4.setText(string + " ( " + string2 + "% )");
                    return;
                }
                String string3 = bundle2.getString("error");
                int i2 = bundle2.getInt("current");
                int i3 = bundle2.getInt("total");
                String string4 = bundle2.getString("dbName");
                InstallingFragment.this.f88263H4.setText(bundle2.getString("labelText"));
                InstallingFragment.this.f88264I4.setText(string4);
                InstallingFragment.this.f88265J4.setMax(i3);
                InstallingFragment.this.f88265J4.setProgress(i2 + 1);
                if (string3 == null || string3.length() == 0 || !InstallingFragment.this.f88272Q4) {
                    return;
                }
                new AlertDialog.Builder(InstallingFragment.this.m15366r(), C5562R.style.alertDialogTheme).setTitle("Error Occured in " + string4).mo1102l(string3).mo1115y("Ok . Continue", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.2.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i4) {
                        InstallingFragment.this.f88272Q4 = false;
                    }
                }).mo1106p("Close App", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.2.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i4) {
                        Process.killProcess(Process.myPid());
                    }
                }).m1090I();
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onComplete() {
                InstallingFragment.this.f88269N4.cancel();
                LocalBroadcastManager.m16410b(InstallingFragment.this.m72281v3()).m16413d(new Intent("reload"));
                InstallingFragment.this.mo15205N2();
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onError(@NonNull Throwable th) {
                iMDLogger.m73550f("InstallingFragment", "Error occured on installing : " + th.getMessage());
                th.printStackTrace();
                new AlertDialog.Builder(InstallingFragment.this.m15366r(), C5562R.style.alertDialogTheme).setTitle("Error Occured in Extract").mo1102l(th.getLocalizedMessage()).mo1115y("Ok . Continue", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.2.3
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                        InstallingFragment.this.mo15205N2();
                        LocalBroadcastManager.m16410b(InstallingFragment.this.m72281v3()).m16413d(new Intent("reload"));
                    }
                }).m1090I();
            }
        };
        observableM59775s4.mo59651a(disposableObserver);
        this.f88273R4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                disposableObserver.mo58425m();
                new AlertDialog.Builder(InstallingFragment.this.m15366r(), C5562R.style.alertDialogTheme).setTitle("What do you want to do ?").mo1102l(InstallingFragment.this.f88275T4).mo1115y("Delete This File", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.3.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                        ViewOnClickListenerC48623 viewOnClickListenerC48623 = ViewOnClickListenerC48623.this;
                        compressHelper.m71857j(InstallingFragment.this.f88275T4);
                        Process.killProcess(Process.myPid());
                    }
                }).mo1106p("Just Close App", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.3.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                        Process.killProcess(Process.myPid());
                    }
                }).m1090I();
            }
        });
        this.f88274S4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                InstallingFragment.this.mo15205N2();
            }
        });
        m72284z3();
        builder.setView(viewInflate);
        return builder.create();
    }

    /* renamed from: u3 */
    public byte[] m72280u3(ZipFile zipFile, String str, Bundle bundle) throws IOException {
        String str2;
        FileHeader next;
        try {
            Iterator<FileHeader> it2 = zipFile.m73605y().iterator();
            while (true) {
                if (!it2.hasNext()) {
                    str2 = null;
                    next = null;
                    break;
                }
                next = it2.next();
                String strM73863k = next.m73863k();
                if (strM73863k.endsWith("/" + str)) {
                    str2 = StringUtils.splitByWholeSeparator(strM73863k, "/")[0];
                    break;
                }
            }
            if (str2 == null) {
                Log.e("findFileInZip", "Can't find " + str);
                return null;
            }
            if (bundle != null) {
                bundle.putString("Folder", str2);
            }
            iMDLogger.m73554j("findFileInZip", "folder name is " + str2);
            ZipInputStream zipInputStreamM73606z = zipFile.m73606z(next);
            byte[] bArrMo75462b0 = Okio.m75769e(Okio.m75785u(zipInputStreamM73606z)).mo75462b0();
            zipInputStreamM73606z.close();
            return bArrMo75462b0;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            iMDLogger.m73550f("findFileInZip", e2.getMessage() + " - " + e2);
            return null;
        }
    }

    /* renamed from: v3 */
    public Activity m72281v3() {
        Activity activity = this.f88271P4;
        return activity == null ? m15366r() : activity;
    }

    /* renamed from: w3 */
    public Bundle m72282w3(String str, String str2, int i2, int i3, String str3) {
        Bundle bundle = new Bundle();
        bundle.putString("labelText", str);
        bundle.putString("dbName", str2);
        bundle.putInt("current", i2);
        bundle.putInt("total", i3);
        bundle.putString("error", str3);
        return bundle;
    }

    /* renamed from: x3 */
    public void m72283x3(Bundle bundle) throws IOException {
        ArrayList arrayList = new ArrayList();
        arrayList.add("visualdx.png");
        arrayList.add("uptodate.png");
        arrayList.add("irandarou.png");
        String string = bundle.getString("IconName");
        String str = bundle.getString("Path") + "/" + string;
        if (arrayList.contains(string)) {
            if (new File(str).exists()) {
                new CompressHelper(m15366r()).m71857j(str);
            }
            try {
                InputStream inputStreamOpen = m15366r().getAssets().open(string);
                BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(str)));
                try {
                    bufferedSinkM75768d.mo75508y1(Okio.m75769e(Okio.m75785u(inputStreamOpen)));
                    bufferedSinkM75768d.close();
                    inputStreamOpen.close();
                } finally {
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("replaceIcons", "Error in replacing icons " + str + " : " + e2);
                e2.printStackTrace();
            }
        }
    }

    /* renamed from: z3 */
    public void m72284z3() {
        Timer timer = new Timer();
        this.f88269N4 = timer;
        timer.scheduleAtFixedRate(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.InstallingFragment.6
            @Override // java.util.TimerTask, java.lang.Runnable
            public void run() {
                InstallingFragment.this.f88276U4.obtainMessage(1).sendToTarget();
            }
        }, 0L, 1000L);
    }
}

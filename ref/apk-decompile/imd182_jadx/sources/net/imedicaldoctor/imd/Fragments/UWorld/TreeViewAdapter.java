package net.imedicaldoctor.imd.Fragments.UWorld;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Set;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class TreeViewAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    /* renamed from: d */
    private Context f89176d;

    /* renamed from: e */
    private ArrayList<TreeItem> f89177e;

    /* renamed from: f */
    private Set<Integer> f89178f;

    static class ChildViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        TextView f89179I;

        /* renamed from: J */
        CheckBox f89180J;

        ChildViewHolder(View view) {
            super(view);
            this.f89179I = (TextView) view.findViewById(C5562R.id.title);
            this.f89180J = (CheckBox) view.findViewById(C5562R.id.checkbox);
        }
    }

    static class GroupViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        TextView f89181I;

        /* renamed from: J */
        CheckBox f89182J;

        /* renamed from: K */
        ImageView f89183K;

        GroupViewHolder(View view) {
            super(view);
            this.f89181I = (TextView) view.findViewById(C5562R.id.title);
            this.f89182J = (CheckBox) view.findViewById(C5562R.id.checkbox);
            this.f89183K = (ImageView) view.findViewById(C5562R.id.expand_collapse_icon);
        }
    }

    public TreeViewAdapter(Context context, ArrayList<TreeItem> arrayList, Set<Integer> set) {
        this.f89176d = context;
        this.f89177e = arrayList;
        this.f89178f = set;
        m72558j0();
    }

    /* renamed from: i0 */
    private TreeItem m72557i0(TreeItem treeItem) {
        Iterator<TreeItem> it2 = this.f89177e.iterator();
        while (it2.hasNext()) {
            TreeItem next = it2.next();
            if (next.f89169b && next.f89175h.contains(treeItem)) {
                return next;
            }
        }
        return null;
    }

    /* renamed from: j0 */
    private void m72558j0() {
        if (this.f89177e.isEmpty() || !this.f89177e.get(0).f89168a.startsWith("All")) {
            return;
        }
        TreeItem treeItem = this.f89177e.get(0);
        if (this.f89178f.isEmpty()) {
            treeItem.f89171d = true;
            this.f89178f.add(Integer.valueOf(treeItem.f89174g));
        } else if (this.f89178f.size() == 1 && this.f89178f.contains(Integer.valueOf(treeItem.f89174g))) {
            treeItem.f89171d = true;
        } else {
            treeItem.f89171d = false;
            this.f89178f.remove(Integer.valueOf(treeItem.f89174g));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: k0 */
    public /* synthetic */ void m72559k0(TreeItem treeItem, View view) {
        m72566r0(treeItem);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: l0 */
    public /* synthetic */ void m72560l0(TreeItem treeItem, View view) {
        if (treeItem.f89175h.isEmpty()) {
            m72564p0(treeItem);
        } else {
            m72565q0(treeItem);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: m0 */
    public /* synthetic */ void m72561m0(TreeItem treeItem, View view) {
        m72565q0(treeItem);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: n0 */
    public /* synthetic */ void m72562n0(TreeItem treeItem, View view) {
        m72564p0(treeItem);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: o0 */
    public /* synthetic */ void m72563o0(TreeItem treeItem, View view) {
        m72564p0(treeItem);
    }

    /* JADX WARN: Removed duplicated region for block: B:27:0x0099  */
    /* renamed from: p0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void m72564p0(net.imedicaldoctor.imd.Fragments.UWorld.TreeItem r5) {
        /*
            r4 = this;
            int r0 = r5.f89174g
            java.lang.String r1 = "All"
            r2 = 0
            if (r0 != 0) goto L58
            java.lang.String r0 = r5.f89168a
            boolean r0 = r0.startsWith(r1)
            if (r0 == 0) goto L58
            boolean r0 = r5.f89171d
            r0 = r0 ^ 1
            r5.f89171d = r0
            if (r0 == 0) goto L99
            java.util.Set<java.lang.Integer> r0 = r4.f89178f
            r0.clear()
            java.util.Set<java.lang.Integer> r0 = r4.f89178f
            int r1 = r5.f89174g
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)
            r0.add(r1)
            java.util.ArrayList<net.imedicaldoctor.imd.Fragments.UWorld.TreeItem> r0 = r4.f89177e
            java.util.Iterator r0 = r0.iterator()
        L2d:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto La4
            java.lang.Object r1 = r0.next()
            net.imedicaldoctor.imd.Fragments.UWorld.TreeItem r1 = (net.imedicaldoctor.imd.Fragments.UWorld.TreeItem) r1
            int r3 = r1.f89174g
            if (r3 == 0) goto L3f
            r1.f89171d = r2
        L3f:
            boolean r3 = r1.f89169b
            if (r3 == 0) goto L2d
            java.util.List<net.imedicaldoctor.imd.Fragments.UWorld.TreeItem> r1 = r1.f89175h
            java.util.Iterator r1 = r1.iterator()
        L49:
            boolean r3 = r1.hasNext()
            if (r3 == 0) goto L2d
            java.lang.Object r3 = r1.next()
            net.imedicaldoctor.imd.Fragments.UWorld.TreeItem r3 = (net.imedicaldoctor.imd.Fragments.UWorld.TreeItem) r3
            r3.f89171d = r2
            goto L49
        L58:
            boolean r0 = r5.f89171d
            r0 = r0 ^ 1
            r5.f89171d = r0
            if (r0 == 0) goto L99
            java.util.Set<java.lang.Integer> r0 = r4.f89178f
            int r3 = r5.f89174g
            java.lang.Integer r3 = java.lang.Integer.valueOf(r3)
            r0.add(r3)
            java.util.ArrayList<net.imedicaldoctor.imd.Fragments.UWorld.TreeItem> r0 = r4.f89177e
            boolean r0 = r0.isEmpty()
            if (r0 != 0) goto La4
            java.util.ArrayList<net.imedicaldoctor.imd.Fragments.UWorld.TreeItem> r0 = r4.f89177e
            java.lang.Object r0 = r0.get(r2)
            net.imedicaldoctor.imd.Fragments.UWorld.TreeItem r0 = (net.imedicaldoctor.imd.Fragments.UWorld.TreeItem) r0
            java.lang.String r0 = r0.f89168a
            boolean r0 = r0.startsWith(r1)
            if (r0 == 0) goto La4
            java.util.ArrayList<net.imedicaldoctor.imd.Fragments.UWorld.TreeItem> r0 = r4.f89177e
            java.lang.Object r0 = r0.get(r2)
            net.imedicaldoctor.imd.Fragments.UWorld.TreeItem r0 = (net.imedicaldoctor.imd.Fragments.UWorld.TreeItem) r0
            r0.f89171d = r2
            java.util.Set<java.lang.Integer> r1 = r4.f89178f
            int r0 = r0.f89174g
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)
            r1.remove(r0)
            goto La4
        L99:
            java.util.Set<java.lang.Integer> r0 = r4.f89178f
            int r1 = r5.f89174g
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)
            r0.remove(r1)
        La4:
            r4.m72569u0(r5)
            r4.m27491G()
            r4.m72558j0()
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.UWorld.TreeViewAdapter.m72564p0(net.imedicaldoctor.imd.Fragments.UWorld.TreeItem):void");
    }

    /* renamed from: q0 */
    private void m72565q0(TreeItem treeItem) {
        boolean z = !treeItem.f89173f;
        treeItem.f89173f = z;
        if (z) {
            ArrayList<TreeItem> arrayList = this.f89177e;
            arrayList.addAll(arrayList.indexOf(treeItem) + 1, treeItem.f89175h);
        } else {
            this.f89177e.removeAll(treeItem.f89175h);
        }
        m27491G();
    }

    /* renamed from: r0 */
    private void m72566r0(TreeItem treeItem) {
        boolean z = !treeItem.f89171d;
        if (treeItem.f89175h.isEmpty()) {
            m72564p0(treeItem);
            return;
        }
        treeItem.f89171d = z;
        treeItem.f89172e = false;
        for (TreeItem treeItem2 : treeItem.f89175h) {
            treeItem2.f89171d = z;
            Set<Integer> set = this.f89178f;
            Integer numValueOf = Integer.valueOf(treeItem2.f89174g);
            if (z) {
                set.add(numValueOf);
            } else {
                set.remove(numValueOf);
            }
        }
        m27491G();
        m72558j0();
    }

    /* renamed from: s0 */
    private void m72567s0(CheckBox checkBox, TreeItem treeItem) {
        Context context;
        int i2;
        if (treeItem.f89172e) {
            context = this.f89176d;
            i2 = C5562R.drawable.checkbox_partial;
        } else if (treeItem.f89171d) {
            context = this.f89176d;
            i2 = C5562R.drawable.checkbox_checked;
        } else {
            context = this.f89176d;
            i2 = C5562R.drawable.checkbox_unchecked;
        }
        checkBox.setButtonDrawable(ContextCompat.m6692l(context, i2));
    }

    /* renamed from: t0 */
    private void m72568t0(TreeItem treeItem) {
        if (treeItem == null || treeItem.f89175h.size() <= 0) {
            return;
        }
        Iterator<TreeItem> it2 = treeItem.f89175h.iterator();
        boolean z = true;
        boolean z2 = true;
        while (it2.hasNext()) {
            if (it2.next().f89171d) {
                z2 = false;
            } else {
                z = false;
            }
        }
        if (z) {
            treeItem.f89171d = true;
        } else {
            treeItem.f89171d = false;
            if (!z2) {
                treeItem.f89172e = true;
                return;
            }
        }
        treeItem.f89172e = false;
    }

    /* renamed from: u0 */
    private void m72569u0(TreeItem treeItem) {
        TreeItem treeItemM72557i0 = m72557i0(treeItem);
        if (treeItemM72557i0 != null) {
            Iterator<TreeItem> it2 = treeItemM72557i0.f89175h.iterator();
            boolean z = true;
            boolean z2 = true;
            while (it2.hasNext()) {
                if (it2.next().f89171d) {
                    z2 = false;
                } else {
                    z = false;
                }
            }
            if (z) {
                treeItemM72557i0.f89171d = true;
            } else {
                treeItemM72557i0.f89171d = false;
                if (!z2) {
                    treeItemM72557i0.f89172e = true;
                    return;
                }
            }
            treeItemM72557i0.f89172e = false;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: C */
    public int mo26845C(int i2) {
        return this.f89177e.get(i2).f89170c ? 1 : 0;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        View view;
        View.OnClickListener onClickListener;
        final TreeItem treeItem = this.f89177e.get(i2);
        if (treeItem.f89170c) {
            ChildViewHolder childViewHolder = (ChildViewHolder) viewHolder;
            childViewHolder.f89179I.setText(treeItem.f89168a);
            m72567s0(childViewHolder.f89180J, treeItem);
            childViewHolder.f89180J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.f
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    this.f89462s.m72562n0(treeItem, view2);
                }
            });
            view = childViewHolder.f33076a;
            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.g
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    this.f89464s.m72563o0(treeItem, view2);
                }
            };
        } else {
            m72568t0(treeItem);
            GroupViewHolder groupViewHolder = (GroupViewHolder) viewHolder;
            groupViewHolder.f89181I.setText(treeItem.f89168a);
            m72567s0(groupViewHolder.f89182J, treeItem);
            groupViewHolder.f89182J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.c
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    this.f89456s.m72559k0(treeItem, view2);
                }
            });
            groupViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.d
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    this.f89458s.m72560l0(treeItem, view2);
                }
            });
            groupViewHolder.f89183K.setImageResource(treeItem.f89173f ? C5562R.drawable.ic_collapse : C5562R.drawable.ic_expand);
            groupViewHolder.f89183K.setVisibility(treeItem.f89169b ? 0 : 8);
            view = groupViewHolder.f89183K;
            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.e
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    this.f89460s.m72561m0(treeItem, view2);
                }
            };
        }
        view.setOnClickListener(onClickListener);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        return i2 == 0 ? new GroupViewHolder(LayoutInflater.from(this.f89176d).inflate(C5562R.layout.treeview_group_item, viewGroup, false)) : new ChildViewHolder(LayoutInflater.from(this.f89176d).inflate(C5562R.layout.treeview_child_item, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        return this.f89177e.size();
    }
}

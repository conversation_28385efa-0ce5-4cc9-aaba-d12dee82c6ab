package net.imedicaldoctor.imd.Fragments.UWorld;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.database.SQLException;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.Html;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.exifinterface.media.ExifInterface;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.xml.xmp.DublinCoreProperties;
import com.itextpdf.tool.xml.html.HTML;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.requery.android.database.sqlite.SQLiteDatabase;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Fragments.UWorld.BackupCodesDialog;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.HeaderCellViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleSearchContentViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class UWMainActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public ArrayList<String> f89184A4;

    /* renamed from: B4 */
    public ArrayList<Bundle> f89185B4;

    /* renamed from: C4 */
    public ArrayList<Bundle> f89186C4;

    /* renamed from: D4 */
    public ArrayList<Bundle> f89187D4;

    /* renamed from: E4 */
    public ArrayList<Bundle> f89188E4;

    /* renamed from: F4 */
    public ArrayList<Bundle> f89189F4;

    /* renamed from: G4 */
    public ArrayList<Bundle> f89190G4;

    /* renamed from: H4 */
    public ArrayList<Bundle> f89191H4;

    /* renamed from: J4 */
    public String f89193J4;

    /* renamed from: O4 */
    public ArrayList<Integer> f89198O4;

    /* renamed from: P4 */
    public ArrayList<Integer> f89199P4;

    /* renamed from: Q4 */
    public ArrayList<Integer> f89200Q4;

    /* renamed from: f5 */
    private ArrayList<String> f89215f5;

    /* renamed from: g5 */
    public String f89216g5;

    /* renamed from: h5 */
    private Boolean f89217h5;

    /* renamed from: i5 */
    private Boolean f89218i5;

    /* renamed from: j5 */
    private Boolean f89219j5;

    /* renamed from: k5 */
    private EditText f89220k5;

    /* renamed from: I4 */
    public int f89192I4 = 0;

    /* renamed from: K4 */
    public int f89194K4 = 40;

    /* renamed from: L4 */
    public int f89195L4 = 0;

    /* renamed from: M4 */
    public int f89196M4 = 0;

    /* renamed from: N4 */
    public int f89197N4 = 0;

    /* renamed from: R4 */
    private final String f89201R4 = "Questions";

    /* renamed from: S4 */
    private final String f89202S4 = "Create A Test";

    /* renamed from: T4 */
    private final String f89203T4 = "Previous Tests";

    /* renamed from: U4 */
    private final String f89204U4 = "Settings";

    /* renamed from: V4 */
    private final String f89205V4 = "subject";

    /* renamed from: W4 */
    private final String f89206W4 = "system";

    /* renamed from: X4 */
    private final String f89207X4 = "numberquestion";

    /* renamed from: Y4 */
    private final String f89208Y4 = "testMode";

    /* renamed from: Z4 */
    private final String f89209Z4 = "filter";

    /* renamed from: a5 */
    private final String f89210a5 = "hardness";

    /* renamed from: b5 */
    private final String f89211b5 = "sort";

    /* renamed from: c5 */
    private final String f89212c5 = "uwfilters.dat";

    /* renamed from: d5 */
    private final String f89213d5 = "unusedsince.dat";

    /* renamed from: e5 */
    private final String f89214e5 = "backupstate.dat";

    /* renamed from: l5 */
    private String f89221l5 = "";

    public class AccountTextViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f89243I;

        /* renamed from: J */
        private final MaterialRippleLayout f89244J;

        public AccountTextViewHolder(View view) {
            super(view);
            this.f89243I = (TextView) view.findViewById(C5562R.id.text);
            this.f89244J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class NumberInputFilter implements InputFilter {
        public NumberInputFilter() {
        }

        @Override // android.text.InputFilter
        public CharSequence filter(CharSequence charSequence, int i2, int i3, Spanned spanned, int i4, int i5) {
            StringBuilder sb = new StringBuilder();
            while (i2 < i3) {
                if (String.valueOf(charSequence.charAt(i2)).matches("[0-9,]*")) {
                    sb.append(charSequence.charAt(i2));
                }
                i2++;
            }
            return sb.toString();
        }
    }

    public class QIDsViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final EditText f89247I;

        public QIDsViewHolder(View view) {
            super(view);
            this.f89247I = (EditText) view.findViewById(C5562R.id.qids_edit_text);
        }
    }

    public class UWAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        private final int f89249d = 0;

        /* renamed from: e */
        private final int f89250e = 4;

        /* renamed from: f */
        private final int f89251f = 1;

        /* renamed from: g */
        private final int f89252g = 2;

        /* renamed from: h */
        private final int f89253h = 3;

        /* renamed from: i */
        private final int f89254i = 5;

        /* renamed from: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment$UWAdapter$17 */
        class ViewOnClickListenerC511317 implements View.OnClickListener {
            ViewOnClickListenerC511317() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                if (UWMainActivityFragment.this.f89193J4.length() > 0) {
                    return;
                }
                new AlertDialog.Builder(UWMainActivityFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("This will backup your test history, favorites and highlights to the iMD Server and will give you a identifier to restore it later.").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.17.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) throws SQLException {
                        final ProgressDialog progressDialogShow = ProgressDialog.show(UWMainActivityFragment.this.m15366r(), "Backing up", "Please wait...", true);
                        UWMainActivityFragment.this.m72602i3(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.17.2.1
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(String str) throws Throwable {
                                progressDialogShow.dismiss();
                                UWMainActivityFragment.this.f89193J4 = "Backup identifier : " + str;
                                UWMainActivityFragment.this.m72576M3();
                                UWMainActivityFragment.this.m72601V3(11);
                            }
                        }, new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.17.2.2
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(String str) throws Throwable {
                                progressDialogShow.dismiss();
                                CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), str, 1);
                            }
                        });
                    }
                }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.17.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                    }
                }).m1090I();
            }
        }

        /* renamed from: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment$UWAdapter$19 */
        class ViewOnClickListenerC511519 implements View.OnClickListener {

            /* renamed from: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment$UWAdapter$19$1, reason: invalid class name */
            class AnonymousClass1 implements Consumer<String> {

                /* renamed from: s */
                final /* synthetic */ ProgressDialog f89281s;

                AnonymousClass1(ProgressDialog progressDialog) {
                    this.f89281s = progressDialog;
                }

                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(String str) throws Throwable {
                    String strM71738M0 = CompressHelper.m71738M0(str);
                    if (strM71738M0.length() == 0) {
                        this.f89281s.dismiss();
                        CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "No backup codes found", 1);
                        return;
                    }
                    String[] strArrSplitByWholeSeparatorPreserveAllTokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(strM71738M0, "|||");
                    if (strArrSplitByWholeSeparatorPreserveAllTokens == null || strArrSplitByWholeSeparatorPreserveAllTokens.length == 0) {
                        this.f89281s.dismiss();
                        CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "No backup codes found", 1);
                        return;
                    }
                    ArrayList arrayList = new ArrayList();
                    for (String str2 : strArrSplitByWholeSeparatorPreserveAllTokens) {
                        String[] strArrSplitByWholeSeparatorPreserveAllTokens2 = StringUtils.splitByWholeSeparatorPreserveAllTokens(str2, ",,,");
                        Bundle bundle = new Bundle();
                        bundle.putString(DublinCoreProperties.f73850d, strArrSplitByWholeSeparatorPreserveAllTokens2[0]);
                        bundle.putString(HTML.Tag.f74390g0, strArrSplitByWholeSeparatorPreserveAllTokens2[1]);
                        bundle.putString("title", strArrSplitByWholeSeparatorPreserveAllTokens2[2]);
                        arrayList.add(bundle);
                    }
                    this.f89281s.dismiss();
                    BackupCodesDialog.m72542a(UWMainActivityFragment.this.m15366r(), arrayList, new BackupCodesDialog.BackupCodeSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.19.1.1
                        @Override // net.imedicaldoctor.imd.Fragments.UWorld.BackupCodesDialog.BackupCodeSelectedListener
                        /* renamed from: a */
                        public void mo72543a(final String str3) {
                            new AlertDialog.Builder(UWMainActivityFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you sure you want to restore code '" + str3 + "'. This will delete test history, favorites and highlights of this database from this device").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.19.1.1.2
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface, int i2) {
                                    String str4 = str3;
                                    if (str4.length() == 0) {
                                        CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "You must enter a backup identifier", 1);
                                    } else {
                                        UWMainActivityFragment.this.m72604k3(str4);
                                    }
                                }
                            }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.19.1.1.1
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface, int i2) {
                                }
                            }).m1090I();
                        }
                    });
                }
            }

            ViewOnClickListenerC511519() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                final ProgressDialog progressDialogShow = ProgressDialog.show(UWMainActivityFragment.this.m15366r(), "Loading Backup Codes", "Please wait...", true);
                UWMainActivityFragment.this.f88791k4.m71874o0("LoadBackupCodesZip|||||" + UWMainActivityFragment.this.f88791k4.m71905y1()).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new AnonymousClass1(progressDialogShow), new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.19.2
                    @Override // io.reactivex.rxjava3.functions.Consumer
                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                    public void accept(Throwable th) throws Throwable {
                        progressDialogShow.dismiss();
                        CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "Error in contacting server", 1);
                    }
                });
            }
        }

        public UWAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
            Bundle bundleM72603j3 = uWMainActivityFragment.m72603j3(i2, uWMainActivityFragment.f89184A4);
            if (!bundleM72603j3.getString("Type").equals("Header") && bundleM72603j3.getString("Type").equals("Item")) {
                String string = bundleM72603j3.getString("Section");
                int i3 = bundleM72603j3.getInt("Index");
                if (string.equals("Questions")) {
                    return 1;
                }
                if (string.equals("Create A Test")) {
                    if (i3 == 9) {
                        return 4;
                    }
                    if (i3 == 8) {
                        return 3;
                    }
                    return i3 == 7 ? 5 : 2;
                }
                if (string.equals("Previous Tests")) {
                    return 1;
                }
                if (string.equals("Settings")) {
                    return 3;
                }
            }
            return 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            MaterialRippleLayout materialRippleLayout;
            View.OnClickListener viewOnClickListenerC511519;
            TextView textView;
            TextView textView2;
            String str;
            UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
            Bundle bundleM72603j3 = uWMainActivityFragment.m72603j3(i2, uWMainActivityFragment.f89184A4);
            if (bundleM72603j3.getString("Type").equals("Header")) {
                ((HeaderCellViewHolder) viewHolder).f101460I.setText(bundleM72603j3.getString("Text"));
            }
            if (bundleM72603j3.getString("Type").equals("Item")) {
                String string = bundleM72603j3.getString("Section");
                int i3 = bundleM72603j3.getInt("Index");
                if (string.equals("Questions")) {
                    RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                    if (i3 == 0) {
                        rippleTextViewHolder.f101515I.setText("Browse Questions");
                        materialRippleLayout = rippleTextViewHolder.f101516J;
                        viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                UWMainActivityFragment uWMainActivityFragment2 = UWMainActivityFragment.this;
                                uWMainActivityFragment2.f88791k4.m71798N(UWTocActivity.class, UWTocActivityFragment.class, uWMainActivityFragment2.m72612z3("0"));
                            }
                        };
                    } else {
                        if (i3 != 1) {
                            return;
                        }
                        rippleTextViewHolder.f101515I.setText("Favorite Questions");
                        materialRippleLayout = rippleTextViewHolder.f101516J;
                        viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                UWMainActivityFragment uWMainActivityFragment2 = UWMainActivityFragment.this;
                                uWMainActivityFragment2.f88791k4.m71798N(UWTocActivity.class, UWTocActivityFragment.class, uWMainActivityFragment2.m72612z3(ExifInterface.f16326Z4));
                            }
                        };
                    }
                } else {
                    if (string.equals("Create A Test")) {
                        if (i3 == 9) {
                            textView2 = ((HeaderCellViewHolder) viewHolder).f101460I;
                            str = UWMainActivityFragment.this.f89192I4 + " Questions Found";
                        } else if (i3 == 8) {
                            AccountTextViewHolder accountTextViewHolder = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder.f89243I.setTextColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            if (UWMainActivityFragment.this.f89192I4 > 0) {
                                accountTextViewHolder.f89244J.setBackgroundColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.green_dark));
                                accountTextViewHolder.f89243I.setText(UWMainActivityFragment.this.f89216g5);
                                materialRippleLayout = accountTextViewHolder.f89244J;
                                viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.3
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view) throws IOException {
                                        UWMainActivityFragment.this.m72608v3();
                                    }
                                };
                            } else {
                                accountTextViewHolder.f89244J.setBackgroundColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.material_grey_700));
                                textView2 = accountTextViewHolder.f89243I;
                                str = "No Question Available";
                            }
                        } else {
                            if (i3 == 7) {
                                QIDsViewHolder qIDsViewHolder = (QIDsViewHolder) viewHolder;
                                UWMainActivityFragment.this.f89220k5 = qIDsViewHolder.f89247I;
                                qIDsViewHolder.f89247I.setFilters(new InputFilter[]{UWMainActivityFragment.this.new NumberInputFilter()});
                                qIDsViewHolder.f89247I.addTextChangedListener(new TextWatcher() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.4
                                    @Override // android.text.TextWatcher
                                    public void afterTextChanged(Editable editable) {
                                        UWMainActivityFragment.this.m72605s3();
                                        UWMainActivityFragment.this.f88792l4.m27492H(13);
                                        UWMainActivityFragment.this.f88792l4.m27492H(12);
                                    }

                                    @Override // android.text.TextWatcher
                                    public void beforeTextChanged(CharSequence charSequence, int i4, int i5, int i6) {
                                    }

                                    @Override // android.text.TextWatcher
                                    public void onTextChanged(CharSequence charSequence, int i4, int i5, int i6) {
                                        UWMainActivityFragment.this.f89221l5 = charSequence.toString();
                                    }
                                });
                                return;
                            }
                            RippleTextViewHolder rippleTextViewHolder2 = (RippleTextViewHolder) viewHolder;
                            rippleTextViewHolder2.f33076a.getLayoutParams().height = -2;
                            rippleTextViewHolder2.f33076a.setVisibility(0);
                            if (i3 == 0) {
                                TextView textView3 = rippleTextViewHolder2.f101515I;
                                UWMainActivityFragment uWMainActivityFragment2 = UWMainActivityFragment.this;
                                textView3.setText(uWMainActivityFragment2.f89187D4.get(uWMainActivityFragment2.f89194K4).getString("title"));
                                materialRippleLayout = rippleTextViewHolder2.f101516J;
                                viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.5
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view) {
                                        UWMainActivityFragment uWMainActivityFragment3 = UWMainActivityFragment.this;
                                        uWMainActivityFragment3.m72598S3("numberquestion", uWMainActivityFragment3.f89187D4, "title", uWMainActivityFragment3.f89194K4);
                                    }
                                };
                            } else if (i3 == 1) {
                                TextView textView4 = rippleTextViewHolder2.f101515I;
                                StringBuilder sb = new StringBuilder();
                                sb.append("Test Mode : ");
                                UWMainActivityFragment uWMainActivityFragment3 = UWMainActivityFragment.this;
                                sb.append(uWMainActivityFragment3.f89188E4.get(uWMainActivityFragment3.f89195L4).getString("title"));
                                textView4.setText(sb.toString());
                                materialRippleLayout = rippleTextViewHolder2.f101516J;
                                viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.6
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view) {
                                        UWMainActivityFragment uWMainActivityFragment4 = UWMainActivityFragment.this;
                                        uWMainActivityFragment4.m72598S3("testMode", uWMainActivityFragment4.f89188E4, "title", uWMainActivityFragment4.f89195L4);
                                    }
                                };
                            } else {
                                if (i3 == 2) {
                                    if (UWMainActivityFragment.this.f89218i5.booleanValue()) {
                                        TextView textView5 = rippleTextViewHolder2.f101515I;
                                        UWMainActivityFragment uWMainActivityFragment4 = UWMainActivityFragment.this;
                                        textView5.setText(CompressHelper.m71739O1(uWMainActivityFragment4.m72595L3(uWMainActivityFragment4.f89185B4, uWMainActivityFragment4.f89198O4, "name"), 300));
                                        materialRippleLayout = rippleTextViewHolder2.f101516J;
                                        viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.7
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view) {
                                                UWMainActivityFragment uWMainActivityFragment5 = UWMainActivityFragment.this;
                                                uWMainActivityFragment5.m72597R3("subject", uWMainActivityFragment5.f89185B4, "name", uWMainActivityFragment5.f89198O4);
                                            }
                                        };
                                    }
                                    rippleTextViewHolder2.f33076a.getLayoutParams().height = 0;
                                    rippleTextViewHolder2.f33076a.setVisibility(8);
                                    rippleTextViewHolder2.f33076a.requestLayout();
                                    return;
                                }
                                if (i3 == 3) {
                                    if (UWMainActivityFragment.this.f89219j5.booleanValue()) {
                                        TextView textView6 = rippleTextViewHolder2.f101515I;
                                        UWMainActivityFragment uWMainActivityFragment5 = UWMainActivityFragment.this;
                                        textView6.setText(CompressHelper.m71739O1(uWMainActivityFragment5.m72595L3(uWMainActivityFragment5.f89186C4, uWMainActivityFragment5.f89199P4, "name"), 300));
                                        materialRippleLayout = rippleTextViewHolder2.f101516J;
                                        viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.8
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view) {
                                                UWMainActivityFragment uWMainActivityFragment6 = UWMainActivityFragment.this;
                                                uWMainActivityFragment6.m72597R3("system", uWMainActivityFragment6.f89186C4, "name", uWMainActivityFragment6.f89199P4);
                                            }
                                        };
                                    }
                                    rippleTextViewHolder2.f33076a.getLayoutParams().height = 0;
                                    rippleTextViewHolder2.f33076a.setVisibility(8);
                                    rippleTextViewHolder2.f33076a.requestLayout();
                                    return;
                                }
                                if (i3 == 4) {
                                    TextView textView7 = rippleTextViewHolder2.f101515I;
                                    StringBuilder sb2 = new StringBuilder();
                                    sb2.append("Filter : ");
                                    UWMainActivityFragment uWMainActivityFragment6 = UWMainActivityFragment.this;
                                    sb2.append(uWMainActivityFragment6.m72595L3(uWMainActivityFragment6.f89189F4, uWMainActivityFragment6.f89200Q4, "title"));
                                    textView7.setText(sb2.toString());
                                    materialRippleLayout = rippleTextViewHolder2.f101516J;
                                    viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.9
                                        @Override // android.view.View.OnClickListener
                                        public void onClick(View view) {
                                            UWMainActivityFragment uWMainActivityFragment7 = UWMainActivityFragment.this;
                                            uWMainActivityFragment7.m72597R3("filter", uWMainActivityFragment7.f89189F4, "title", uWMainActivityFragment7.f89200Q4);
                                        }
                                    };
                                } else {
                                    if (i3 == 5) {
                                        if (UWMainActivityFragment.this.f89217h5.booleanValue()) {
                                            TextView textView8 = rippleTextViewHolder2.f101515I;
                                            StringBuilder sb3 = new StringBuilder();
                                            sb3.append("Difficulty: ");
                                            UWMainActivityFragment uWMainActivityFragment7 = UWMainActivityFragment.this;
                                            sb3.append(uWMainActivityFragment7.f89190G4.get(uWMainActivityFragment7.f89196M4).getString("title"));
                                            textView8.setText(sb3.toString());
                                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                                            viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.10
                                                @Override // android.view.View.OnClickListener
                                                public void onClick(View view) {
                                                    UWMainActivityFragment uWMainActivityFragment8 = UWMainActivityFragment.this;
                                                    uWMainActivityFragment8.m72598S3("hardness", uWMainActivityFragment8.f89190G4, "title", uWMainActivityFragment8.f89196M4);
                                                }
                                            };
                                        }
                                        rippleTextViewHolder2.f33076a.getLayoutParams().height = 0;
                                        rippleTextViewHolder2.f33076a.setVisibility(8);
                                        rippleTextViewHolder2.f33076a.requestLayout();
                                        return;
                                    }
                                    if (i3 != 6) {
                                        return;
                                    }
                                    TextView textView9 = rippleTextViewHolder2.f101515I;
                                    StringBuilder sb4 = new StringBuilder();
                                    sb4.append("Sort By: ");
                                    UWMainActivityFragment uWMainActivityFragment8 = UWMainActivityFragment.this;
                                    sb4.append(uWMainActivityFragment8.f89191H4.get(uWMainActivityFragment8.f89197N4).getString("title"));
                                    textView9.setText(sb4.toString());
                                    materialRippleLayout = rippleTextViewHolder2.f101516J;
                                    viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.11
                                        @Override // android.view.View.OnClickListener
                                        public void onClick(View view) {
                                            UWMainActivityFragment uWMainActivityFragment9 = UWMainActivityFragment.this;
                                            uWMainActivityFragment9.m72598S3("sort", uWMainActivityFragment9.f89191H4, "title", uWMainActivityFragment9.f89197N4);
                                        }
                                    };
                                }
                            }
                        }
                        textView2.setText(str);
                        return;
                    }
                    if (string.equals("Previous Tests")) {
                        RippleTextViewHolder rippleTextViewHolder3 = (RippleTextViewHolder) viewHolder;
                        if (i3 == 0) {
                            rippleTextViewHolder3.f101515I.setText("Last Test");
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.12
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    CompressHelper compressHelper;
                                    Bundle bundle;
                                    StringBuilder sb5;
                                    String str2;
                                    UWMainActivityFragment uWMainActivityFragment9 = UWMainActivityFragment.this;
                                    CompressHelper compressHelper2 = uWMainActivityFragment9.f88791k4;
                                    Bundle bundleM71890s1 = compressHelper2.m71890s1(compressHelper2.m71817V(uWMainActivityFragment9.f88788h4, "Select * from tests order by id desc limit 1"));
                                    if (bundleM71890s1 == null) {
                                        CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "You haven't created any test yet", 0);
                                        return;
                                    }
                                    if (bundleM71890s1.getString("done").equals(IcyHeaders.f28171a3)) {
                                        UWMainActivityFragment uWMainActivityFragment10 = UWMainActivityFragment.this;
                                        compressHelper = uWMainActivityFragment10.f88791k4;
                                        bundle = uWMainActivityFragment10.f88788h4;
                                        sb5 = new StringBuilder();
                                        str2 = "testresult-";
                                    } else {
                                        UWMainActivityFragment uWMainActivityFragment11 = UWMainActivityFragment.this;
                                        compressHelper = uWMainActivityFragment11.f88791k4;
                                        bundle = uWMainActivityFragment11.f88788h4;
                                        sb5 = new StringBuilder();
                                        str2 = "test-";
                                    }
                                    sb5.append(str2);
                                    sb5.append(bundleM71890s1.getString("id"));
                                    compressHelper.m71772A1(bundle, sb5.toString(), null, null);
                                }
                            };
                        } else if (i3 == 1) {
                            rippleTextViewHolder3.f101515I.setText("Previous Tests");
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.13
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    UWMainActivityFragment uWMainActivityFragment9 = UWMainActivityFragment.this;
                                    uWMainActivityFragment9.f88791k4.m71798N(UWTestsListActivity.class, UWTestsListActivityFragment.class, uWMainActivityFragment9.m72612z3("0"));
                                }
                            };
                        } else {
                            if (i3 != 2) {
                                return;
                            }
                            rippleTextViewHolder3.f101515I.setText("Your Progress");
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.14
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    UWMainActivityFragment uWMainActivityFragment9 = UWMainActivityFragment.this;
                                    uWMainActivityFragment9.f88791k4.m71772A1(uWMainActivityFragment9.f88788h4, "progress", null, null);
                                }
                            };
                        }
                    } else {
                        if (!string.equals("Settings")) {
                            return;
                        }
                        if (i3 == 0) {
                            AccountTextViewHolder accountTextViewHolder2 = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder2.f89243I.setTextColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            accountTextViewHolder2.f89243I.setText("Reset History");
                            accountTextViewHolder2.f89244J.setBackgroundColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.red));
                            materialRippleLayout = accountTextViewHolder2.f89244J;
                            viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.15
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    new AlertDialog.Builder(UWMainActivityFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("This will delete all tests and history").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.15.2
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface, int i4) {
                                            UWMainActivityFragment uWMainActivityFragment9 = UWMainActivityFragment.this;
                                            uWMainActivityFragment9.f88791k4.m71866m(uWMainActivityFragment9.f88788h4, "delete from logs");
                                            UWMainActivityFragment uWMainActivityFragment10 = UWMainActivityFragment.this;
                                            uWMainActivityFragment10.f88791k4.m71866m(uWMainActivityFragment10.f88788h4, "delete from tests");
                                            UWMainActivityFragment uWMainActivityFragment11 = UWMainActivityFragment.this;
                                            uWMainActivityFragment11.f88791k4.m71866m(uWMainActivityFragment11.f88788h4, "delete from flags");
                                            UWMainActivityFragment.this.m72605s3();
                                            UWMainActivityFragment.this.m72601V3(11);
                                        }
                                    }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.15.1
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface, int i4) {
                                        }
                                    }).m1090I();
                                }
                            };
                        } else if (i3 == 1) {
                            AccountTextViewHolder accountTextViewHolder3 = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder3.f89243I.setTextColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            accountTextViewHolder3.f89243I.setText("Delete Favorites & Highlights");
                            accountTextViewHolder3.f89244J.setBackgroundColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.material_orange_800));
                            materialRippleLayout = accountTextViewHolder3.f89244J;
                            viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.16
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    new AlertDialog.Builder(UWMainActivityFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("This will delete all favorites and highlights of this database.").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.16.2
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface, int i4) throws SQLException {
                                            CompressHelper compressHelper = UWMainActivityFragment.this.f88791k4;
                                            compressHelper.m71881q(compressHelper.m71823X0(), "delete from favorites where dbName='" + UWMainActivityFragment.this.f88788h4.getString("Name").replace("'", "''") + "'");
                                            UWMainActivityFragment uWMainActivityFragment9 = UWMainActivityFragment.this;
                                            uWMainActivityFragment9.f88791k4.m71881q(uWMainActivityFragment9.m72591E3(), "delete from highlight where dbName='" + UWMainActivityFragment.this.f88788h4.getString("Name").replace("'", "''") + "'");
                                            CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "Favorites & Highlights Deleted.", 1);
                                        }
                                    }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.16.1
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface, int i4) {
                                        }
                                    }).m1090I();
                                }
                            };
                        } else if (i3 == 2) {
                            AccountTextViewHolder accountTextViewHolder4 = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder4.f89243I.setTextColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            accountTextViewHolder4.f89244J.setBackgroundColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.green));
                            String str2 = "Backup Data";
                            accountTextViewHolder4.f89243I.setText("Backup Data");
                            if (UWMainActivityFragment.this.f89193J4.length() == 0) {
                                textView = accountTextViewHolder4.f89243I;
                            } else {
                                textView = accountTextViewHolder4.f89243I;
                                str2 = UWMainActivityFragment.this.f89193J4;
                            }
                            textView.setText(str2);
                            materialRippleLayout = accountTextViewHolder4.f89244J;
                            viewOnClickListenerC511519 = new ViewOnClickListenerC511317();
                        } else if (i3 == 3) {
                            AccountTextViewHolder accountTextViewHolder5 = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder5.f89243I.setTextColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            accountTextViewHolder5.f89243I.setText("Restore With Code");
                            accountTextViewHolder5.f89244J.setBackgroundColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.dark_blue));
                            materialRippleLayout = accountTextViewHolder5.f89244J;
                            viewOnClickListenerC511519 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.18
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    final EditText editText = new EditText(UWMainActivityFragment.this.m15366r());
                                    editText.setTextColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.black));
                                    new AlertDialog.Builder(UWMainActivityFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Enter Backup Identifier. This will delete test history, favorites and highlights of this database from this device").setView(editText).mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.18.2
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface, int i4) {
                                            String string2 = editText.getText().toString();
                                            if (string2.length() == 0) {
                                                CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "You must enter a backup identifier", 1);
                                            } else {
                                                UWMainActivityFragment.this.m72604k3(string2);
                                            }
                                        }
                                    }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.UWAdapter.18.1
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface, int i4) {
                                        }
                                    }).m1090I();
                                }
                            };
                        } else {
                            if (i3 != 4) {
                                return;
                            }
                            AccountTextViewHolder accountTextViewHolder6 = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder6.f89243I.setTextColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            accountTextViewHolder6.f89243I.setText("List of Backups");
                            accountTextViewHolder6.f89244J.setBackgroundColor(UWMainActivityFragment.this.m15320b0().getColor(C5562R.color.material_deep_purple_900));
                            materialRippleLayout = accountTextViewHolder6.f89244J;
                            viewOnClickListenerC511519 = new ViewOnClickListenerC511519();
                        }
                    }
                }
                materialRippleLayout.setOnClickListener(viewOnClickListenerC511519);
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new HeaderCellViewHolder(LayoutInflater.from(UWMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 1) {
                return new RippleTextViewHolder(LayoutInflater.from(UWMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
            }
            if (i2 == 2) {
                return new RippleTextViewHolder(LayoutInflater.from(UWMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_list, viewGroup, false));
            }
            if (i2 == 3) {
                return UWMainActivityFragment.this.new AccountTextViewHolder(LayoutInflater.from(UWMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_account_text, viewGroup, false));
            }
            if (i2 == 4) {
                return new HeaderCellViewHolder(LayoutInflater.from(UWMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_footer, viewGroup, false));
            }
            if (i2 != 5) {
                return null;
            }
            return UWMainActivityFragment.this.new QIDsViewHolder(LayoutInflater.from(UWMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_qids, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
            return uWMainActivityFragment.m72599T3(uWMainActivityFragment.f89184A4);
        }

        /* renamed from: d0 */
        public void m72622d0(int i2) {
            for (int i3 = 0; i3 < mo26171b(); i3++) {
                if (i3 != i2) {
                    m27492H(i3);
                }
            }
        }
    }

    /* renamed from: F3 */
    private String m72573F3() throws IOException {
        try {
            File file = new File(CompressHelper.m71762u1(this.f88788h4, "backupstate.dat"));
            if (!file.exists()) {
                return "";
            }
            FileInputStream fileInputStream = new FileInputStream(file);
            ObjectInputStream objectInputStream = new ObjectInputStream(fileInputStream);
            String str = (String) objectInputStream.readObject();
            objectInputStream.close();
            fileInputStream.close();
            return str;
        } catch (Exception e2) {
            e2.printStackTrace();
            return "";
        }
    }

    /* renamed from: G3 */
    private void m72574G3() throws IOException {
        try {
            File file = new File(CompressHelper.m71762u1(this.f88788h4, "uwfilters.dat"));
            if (file.exists()) {
                FileInputStream fileInputStream = new FileInputStream(file);
                ObjectInputStream objectInputStream = new ObjectInputStream(fileInputStream);
                this.f89196M4 = objectInputStream.readInt();
                this.f89195L4 = objectInputStream.readInt();
                this.f89194K4 = objectInputStream.readInt();
                this.f89198O4 = (ArrayList) objectInputStream.readObject();
                this.f89199P4 = (ArrayList) objectInputStream.readObject();
                this.f89200Q4 = (ArrayList) objectInputStream.readObject();
                this.f89197N4 = objectInputStream.readInt();
                objectInputStream.close();
                fileInputStream.close();
            }
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* renamed from: H3 */
    private void m72575H3() throws IOException {
        try {
            File file = new File(CompressHelper.m71762u1(this.f88788h4, "unusedsince.dat"));
            if (file.exists()) {
                FileInputStream fileInputStream = new FileInputStream(file);
                ObjectInputStream objectInputStream = new ObjectInputStream(fileInputStream);
                this.f89215f5 = (ArrayList) objectInputStream.readObject();
                objectInputStream.close();
                fileInputStream.close();
            }
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: M3 */
    public void m72576M3() throws IOException {
        try {
            File file = new File(CompressHelper.m71762u1(this.f88788h4, "backupstate.dat"));
            String str = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + "|" + this.f88791k4.m71817V(this.f88788h4, "select count(*) c from logs").get(0).getString("c");
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            ObjectOutputStream objectOutputStream = new ObjectOutputStream(fileOutputStream);
            objectOutputStream.writeObject(str);
            objectOutputStream.close();
            fileOutputStream.close();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* renamed from: N3 */
    private void m72577N3() throws IOException {
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(new File(CompressHelper.m71762u1(this.f88788h4, "uwfilters.dat")));
            ObjectOutputStream objectOutputStream = new ObjectOutputStream(fileOutputStream);
            objectOutputStream.writeInt(this.f89196M4);
            objectOutputStream.writeInt(this.f89195L4);
            objectOutputStream.writeInt(this.f89194K4);
            objectOutputStream.writeObject(this.f89198O4);
            objectOutputStream.writeObject(this.f89199P4);
            objectOutputStream.writeObject(this.f89200Q4);
            objectOutputStream.writeInt(this.f89197N4);
            objectOutputStream.close();
            fileOutputStream.close();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* renamed from: O3 */
    private void m72578O3() throws IOException {
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(new File(CompressHelper.m71762u1(this.f88788h4, "unusedsince.dat")));
            ObjectOutputStream objectOutputStream = new ObjectOutputStream(fileOutputStream);
            objectOutputStream.writeObject(this.f89215f5);
            objectOutputStream.close();
            fileOutputStream.close();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* renamed from: Q3 */
    private boolean m72579Q3() {
        if (!m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("qbankbackup", true)) {
            return false;
        }
        String strM72573F3 = m72573F3();
        if (strM72573F3.length() == 0) {
            return true;
        }
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strM72573F3, "|");
        if (!new SimpleDateFormat("yyyy-MM-dd").format(new Date()).equals(strArrSplitByWholeSeparator[0])) {
            if (!this.f88791k4.m71817V(this.f88788h4, "select count(*) c from logs").get(0).getString("c").equals(strArrSplitByWholeSeparator[1])) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: r3 */
    private void m72586r3() {
        if (m72579Q3()) {
            this.f88791k4.m71807R0(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.1
                @Override // java.lang.Runnable
                public void run() throws SQLException {
                    UWMainActivityFragment.this.m72602i3(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.1.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(String str) throws Throwable {
                            UWMainActivityFragment.this.m72576M3();
                        }
                    }, new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.1.2
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(String str) throws Throwable {
                        }
                    });
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.2
                @Override // java.lang.Runnable
                public void run() {
                }
            });
        }
    }

    /* renamed from: A3 */
    public Bundle m72587A3(String str) {
        Bundle bundle = new Bundle();
        bundle.putString("title", str);
        return bundle;
    }

    /* renamed from: B3 */
    public Bundle m72588B3(String str, String str2) {
        Bundle bundle = new Bundle();
        bundle.putString("title", str);
        bundle.putString("sql", str2);
        return bundle;
    }

    /* renamed from: C3 */
    public Bundle m72589C3(String str, String str2, String str3) {
        Bundle bundle = new Bundle();
        bundle.putString("title", str);
        bundle.putString("Min", str2);
        bundle.putString("Max", str3);
        return bundle;
    }

    /* renamed from: D3 */
    public String m72590D3() throws IOException {
        String string = this.f89190G4.get(this.f89196M4).getString("Min");
        String string2 = this.f89190G4.get(this.f89196M4).getString("Max");
        ArrayList arrayList = new ArrayList();
        ArrayList arrayList2 = new ArrayList();
        Iterator<Integer> it2 = this.f89198O4.iterator();
        while (it2.hasNext()) {
            String string3 = this.f89185B4.get(it2.next().intValue()).getString("id");
            if (!string3.equals("0")) {
                arrayList2.add("(subId = " + string3 + ")");
            }
        }
        if (arrayList2.size() > 0) {
            arrayList.add("(" + StringUtils.join(arrayList2, " OR ") + ")");
        }
        ArrayList arrayList3 = new ArrayList();
        Iterator<Integer> it3 = this.f89199P4.iterator();
        while (it3.hasNext()) {
            String string4 = this.f89186C4.get(it3.next().intValue()).getString("id");
            if (!string4.equals("0")) {
                arrayList3.add("(sysId = " + string4 + ")");
            }
        }
        if (arrayList3.size() > 0) {
            arrayList.add("(" + StringUtils.join(arrayList3, " OR ") + ")");
        }
        ArrayList arrayList4 = new ArrayList();
        Iterator<Integer> it4 = this.f89200Q4.iterator();
        while (it4.hasNext()) {
            String string5 = this.f89189F4.get(it4.next().intValue()).getString("sql");
            if (string5.equals("UnusedNow")) {
                String str = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                this.f89215f5.add(str);
                m72578O3();
                m72607u3();
                string5 = "not (id in (select distinct qid from logs where length(answerDate)>10 and answerDate > '" + str + "'))";
            }
            if (string5.length() > 0) {
                arrayList4.add("(" + string5 + ")");
            }
        }
        if (arrayList4.size() > 0) {
            arrayList.add("(" + StringUtils.join(arrayList4, " AND ") + ")");
        }
        arrayList.add("(corrTaken / pplTaken)*100 >= " + string);
        arrayList.add("(corrTaken / pplTaken)*100 <= " + string2);
        if (!this.f89221l5.isEmpty()) {
            arrayList = new ArrayList();
            arrayList.add("(id in (" + m72610x3(this.f89221l5) + "))");
        }
        String strJoin = StringUtils.join(arrayList, " AND ");
        Log.d("UW", "Final Query : " + strJoin);
        return strJoin;
    }

    /* renamed from: E3 */
    public String m72591E3() throws SQLException {
        String str = this.f88791k4.m71797M1() + "/highlights.db";
        if (!new File(str).exists()) {
            SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("create virtual table highlight using fts4 (dbName, dbTitle, dbAddress, dbDate, dbDocName, type, text, note, save)");
        }
        return str;
    }

    /* renamed from: I3 */
    public int m72592I3(String str) {
        if (str.equals("Questions")) {
            return 2;
        }
        if (str.equals("Create A Test")) {
            return 10;
        }
        if (str.equals("Previous Tests")) {
            return 3;
        }
        return str.equals("Settings") ? 5 : 0;
    }

    /* renamed from: J3 */
    public void m72593J3(String str, ArrayList<Integer> arrayList) {
        if (str.equals("filter")) {
            this.f89200Q4 = arrayList;
        } else if (!str.equals("hardness") && !str.equals("testMode") && !str.equals("numberquestion")) {
            if (str.equals("system")) {
                this.f89199P4 = arrayList;
            } else if (str.equals("subject")) {
                this.f89198O4 = arrayList;
            }
        }
        m72605s3();
        m72601V3(11);
    }

    /* renamed from: K3 */
    public void m72594K3(String str, Bundle bundle, int i2) {
        if (!str.equals("filter")) {
            if (str.equals("hardness")) {
                this.f89196M4 = i2;
            } else if (str.equals("testMode")) {
                this.f89195L4 = i2;
            } else if (str.equals("numberquestion")) {
                this.f89194K4 = i2;
            } else if (!str.equals("system") && !str.equals("subject") && str.equals("sort")) {
                this.f89197N4 = i2;
            }
        }
        m72605s3();
        m72601V3(11);
    }

    /* renamed from: L3 */
    public String m72595L3(ArrayList<Bundle> arrayList, ArrayList<Integer> arrayList2, String str) {
        ArrayList arrayList3 = new ArrayList();
        Iterator<Integer> it2 = arrayList2.iterator();
        while (it2.hasNext()) {
            arrayList3.add(arrayList.get(it2.next().intValue()).getString(str));
        }
        return StringUtils.join(arrayList3, " | ");
    }

    /* renamed from: P3 */
    public void m72596P3() {
        iMDLogger.m73548d("sendFavorite", "Sending FavoriteChanged message");
        Intent intent = new Intent("net.imedicaldoctor.imd.favorite");
        intent.putExtra("Test", "Random data for test");
        LocalBroadcastManager.m16410b(m15366r()).m16413d(intent);
    }

    /* renamed from: R3 */
    public void m72597R3(String str, ArrayList<Bundle> arrayList, String str2, ArrayList<Integer> arrayList2) {
        CheckDialog checkDialog = new CheckDialog();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList("Items", arrayList);
        bundle.putString("TitleProperty", str2);
        bundle.putIntegerArrayList("Positions", arrayList2);
        bundle.putString("Type", str);
        checkDialog.m15245A2(this, 0);
        checkDialog.m15342i2(bundle);
        checkDialog.mo15218Z2(true);
        checkDialog.mo15222e3(m15283M(), "asdfasdfasdf");
    }

    /* renamed from: S3 */
    public void m72598S3(String str, ArrayList<Bundle> arrayList, String str2, int i2) {
        SelectDialog selectDialog = new SelectDialog();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList("Items", arrayList);
        bundle.putString("TitleProperty", str2);
        bundle.putInt("Position", i2);
        bundle.putString("Type", str);
        selectDialog.m15245A2(this, 0);
        selectDialog.m15342i2(bundle);
        selectDialog.mo15218Z2(true);
        selectDialog.mo15222e3(m15283M(), "asdfasdfasdf");
    }

    /* renamed from: T3 */
    public int m72599T3(ArrayList<String> arrayList) {
        int iM72592I3 = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            iM72592I3 = iM72592I3 + m72592I3(it2.next()) + 1;
        }
        return iM72592I3;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException, IOException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        this.f89216g5 = "Let's Go";
        this.f89193J4 = "";
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        m72462O2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f89198O4 = new ArrayList<>();
        this.f89199P4 = new ArrayList<>();
        this.f89200Q4 = new ArrayList<>();
        this.f89198O4.add(0);
        this.f89199P4.add(0);
        this.f89200Q4.add(0);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(true, false);
        relativeLayout.setVisibility(0);
        try {
            this.f89217h5 = Boolean.valueOf(!this.f88791k4.m71817V(this.f88788h4, "select count(distinct  corrTaken) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3));
            this.f89218i5 = Boolean.valueOf(!this.f88791k4.m71817V(this.f88788h4, "select count(distinct  subId) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3));
            this.f89219j5 = Boolean.valueOf(!this.f88791k4.m71817V(this.f88788h4, "select count(distinct  sysId) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3));
            this.f89185B4 = this.f88791k4.m71817V(this.f88788h4, "select 0 as id,'All Subjects' as name , sum(count) as count from subjects union select id, name,count from subjects");
            this.f89186C4 = this.f88791k4.m71817V(this.f88788h4, "select 0 as id, 'All Systems' as name , sum(count) as count from systems union select id, name,count from systems");
            this.f89187D4 = new ArrayList<>();
            for (int i2 = 0; i2 < 1001; i2++) {
                this.f89187D4.add(m72587A3(i2 + " Questions"));
            }
            m72575H3();
            if (this.f89215f5 == null) {
                this.f89215f5 = new ArrayList<>();
            }
            m72607u3();
            ArrayList<Bundle> arrayList = new ArrayList<>();
            this.f89188E4 = arrayList;
            arrayList.add(m72587A3("Reading"));
            this.f89188E4.add(m72587A3("Testing (Timed)"));
            ArrayList<Bundle> arrayList2 = new ArrayList<>();
            this.f89190G4 = arrayList2;
            arrayList2.add(m72589C3("All", "0", "100"));
            this.f89190G4.add(m72589C3("Easy", "75", "100"));
            this.f89190G4.add(m72589C3("Fair", "50", "75"));
            this.f89190G4.add(m72589C3("Hard", "25", "50"));
            this.f89190G4.add(m72589C3("Extreme !", "0", "25"));
            ArrayList<Bundle> arrayList3 = new ArrayList<>();
            this.f89191H4 = arrayList3;
            arrayList3.add(m72587A3("Random"));
            this.f89191H4.add(m72587A3("QID"));
            m15358o2(false);
            ArrayList<String> arrayList4 = new ArrayList<>();
            this.f89184A4 = arrayList4;
            arrayList4.add("Questions");
            this.f89184A4.add("Create A Test");
            this.f89184A4.add("Previous Tests");
            this.f89184A4.add("Settings");
            m72574G3();
            if (this.f89198O4 == null) {
                ArrayList<Integer> arrayList5 = new ArrayList<>();
                this.f89198O4 = arrayList5;
                arrayList5.add(0);
            }
            if (this.f89199P4 == null) {
                ArrayList<Integer> arrayList6 = new ArrayList<>();
                this.f89199P4 = arrayList6;
                arrayList6.add(0);
            }
            if (this.f89200Q4 == null) {
                ArrayList<Integer> arrayList7 = new ArrayList<>();
                this.f89200Q4 = arrayList7;
                arrayList7.add(0);
            }
            this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.4
                @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
                /* renamed from: d0 */
                public void mo72039d0(RecyclerView.ViewHolder viewHolder, int i3, Bundle bundle2) {
                    String string;
                    StringBuilder sb;
                    String str;
                    RippleSearchContentViewHolder rippleSearchContentViewHolder = (RippleSearchContentViewHolder) viewHolder;
                    rippleSearchContentViewHolder.f101479I.setText(bundle2.getString("text"));
                    final String string2 = bundle2.getString("type");
                    final String string3 = bundle2.getString("contentId");
                    if (string2.equals("0")) {
                        rippleSearchContentViewHolder.f101480J.setVisibility(8);
                        rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.4.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
                                uWMainActivityFragment.f88791k4.m71772A1(uWMainActivityFragment.f88788h4, "question-" + string3, null, null);
                            }
                        });
                        return;
                    }
                    if (string2.equals(IcyHeaders.f28171a3)) {
                        sb = new StringBuilder();
                        sb.append("<font color=\"red\">");
                        str = "Question";
                    } else if (string2.equals(ExifInterface.f16317Y4)) {
                        sb = new StringBuilder();
                        sb.append("<font color=\"red\">");
                        str = "Explanation";
                    } else {
                        if (!string2.equals(ExifInterface.f16326Z4)) {
                            string = "";
                            final String str2 = string + StringUtils.SPACE + bundle2.getString("subText");
                            rippleSearchContentViewHolder.f101480J.setText(Html.fromHtml(str2));
                            rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.4.2
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    if (string2.equals(ExifInterface.f16317Y4)) {
                                        UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
                                        uWMainActivityFragment.f88791k4.m71772A1(uWMainActivityFragment.f88788h4, "answer-" + string3, UWMainActivityFragment.this.m72466T2(str2), null);
                                        return;
                                    }
                                    UWMainActivityFragment uWMainActivityFragment2 = UWMainActivityFragment.this;
                                    uWMainActivityFragment2.f88791k4.m71772A1(uWMainActivityFragment2.f88788h4, "question-" + string3, UWMainActivityFragment.this.m72466T2(str2), null);
                                }
                            });
                        }
                        sb = new StringBuilder();
                        sb.append("<font color=\"red\">");
                        str = "Answer";
                    }
                    sb.append(str);
                    sb.append("</font>");
                    string = sb.toString();
                    final String str22 = string + StringUtils.SPACE + bundle2.getString("subText");
                    rippleSearchContentViewHolder.f101480J.setText(Html.fromHtml(str22));
                    rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.4.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            if (string2.equals(ExifInterface.f16317Y4)) {
                                UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
                                uWMainActivityFragment.f88791k4.m71772A1(uWMainActivityFragment.f88788h4, "answer-" + string3, UWMainActivityFragment.this.m72466T2(str22), null);
                                return;
                            }
                            UWMainActivityFragment uWMainActivityFragment2 = UWMainActivityFragment.this;
                            uWMainActivityFragment2.f88791k4.m71772A1(uWMainActivityFragment2.f88788h4, "question-" + string3, UWMainActivityFragment.this.m72466T2(str22), null);
                        }
                    });
                }
            };
            m72605s3();
            UWAdapter uWAdapter = new UWAdapter();
            this.f88792l4 = uWAdapter;
            this.f88803w4.setAdapter(uWAdapter);
            m72461N2();
            m72611y3();
            m72586r3();
        } catch (Exception unused) {
            this.f88791k4.m71901w2("Main database can't be found. you must delete and redownload the database.", new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.3
                @Override // java.lang.Runnable
                public void run() {
                    UWMainActivityFragment.this.f88791k4.m71830Z1(true);
                    UWMainActivityFragment.this.f88791k4.m71830Z1(false);
                }
            });
        }
        return this.f88797q4;
    }

    /* renamed from: U3 */
    public ArrayList<String> m72600U3(ArrayList<String> arrayList, int i2) {
        ArrayList<String> arrayList2 = new ArrayList<>();
        Iterator<String> it2 = arrayList.iterator();
        int length = 0;
        while (it2.hasNext()) {
            String next = it2.next();
            length += next.split(",").length;
            if (length > i2) {
                break;
            }
            arrayList2.add(next);
        }
        return arrayList2;
    }

    /* renamed from: V3 */
    public void m72601V3(int i2) {
        ((UWAdapter) this.f88792l4).m72622d0(i2);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88793m4.m73469f0(this.f88795o4);
        this.f88803w4.setAdapter(this.f88793m4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: g1 */
    public void mo15335g1() throws IOException {
        super.mo15335g1();
        m72577N3();
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    /* renamed from: i3 */
    public void m72602i3(final Consumer<String> consumer, final Consumer<String> consumer2) throws SQLException {
        String strM71889s0 = this.f88791k4.m71889s0(CompressHelper.m71752f1(this.f88788h4), "select qid, selectedAnswer, corrAnswer, answerDate, time, testId from logs", "qid,selectedAnswer,corrAnswer,answerDate,time,testId", null);
        String strM71889s02 = this.f88791k4.m71889s0(CompressHelper.m71752f1(this.f88788h4), "select id,qIds, createdDate, qIndex, done, mode, right, wrong, subject, system, hard from tests", "id,qIds,createdDate,qIndex,done,mode,right,wrong,subject,system,hard", null);
        CompressHelper compressHelper = this.f88791k4;
        String strM71889s03 = compressHelper.m71889s0(compressHelper.m71823X0(), "select dbName,dbTitle,dbAddress,dbDate,dbDocName from favorites where dbName='" + this.f88788h4.getString("Name").replace("'", "''") + "'", "dbName,dbTitle,dbAddress,dbDate,dbDocName", null);
        Bundle bundle = new Bundle();
        bundle.putString("text", "");
        try {
            this.f88791k4.m71874o0("SaveToFileZip|||||" + CompressHelper.m71732G0(strM71889s0 + "###" + strM71889s02 + "###" + strM71889s03 + "###" + this.f88791k4.m71889s0(m72591E3(), "select dbName,dbTitle,dbAddress,dbDate,dbDocName,type,text,note,save from highlight where dbName = '" + this.f88788h4.getString("Name").replace("'", "''") + "'", "dbName,dbTitle,dbAddress,dbDate,dbDocName,type,text,note,save", bundle)) + "|||||" + this.f88791k4.m71905y1() + "|||||" + this.f88788h4.getString("Title")).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.6
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(String str) throws Throwable {
                    consumer.accept(str);
                }
            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.7
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Throwable th) throws Throwable {
                    consumer2.accept("Error in contacting server");
                }
            });
        } catch (Exception e2) {
            CompressHelper.m71767x2(m15366r(), "Error in compressing data. " + e2.getMessage(), 1);
        }
    }

    /* renamed from: j3 */
    public Bundle m72603j3(int i2, ArrayList<String> arrayList) {
        Iterator<String> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            String next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Text", next);
                bundle.putString("Type", "Header");
                return bundle;
            }
            int iM72592I3 = i3 + m72592I3(next);
            if (i2 <= iM72592I3) {
                int iM72592I32 = (i2 - (iM72592I3 - m72592I3(next))) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putString("Section", next);
                bundle2.putInt("Index", iM72592I32);
                bundle2.putString("Type", "Item");
                return bundle2;
            }
            i3 = iM72592I3 + 1;
        }
        return null;
    }

    /* renamed from: k3 */
    public void m72604k3(String str) {
        final ProgressDialog progressDialogShow = ProgressDialog.show(m15366r(), "Restoring", "Please wait...", true);
        this.f88791k4.m71874o0("LoadFromFileZip|||||" + str).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.8
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str2) throws Throwable {
                CompressHelper compressHelper;
                String str3;
                String strM71752f1;
                String str4;
                String strM71738M0 = CompressHelper.m71738M0(str2);
                if (strM71738M0.length() == 0) {
                    progressDialogShow.dismiss();
                    CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "Identifier not found", 1);
                    return;
                }
                String[] strArrSplitByWholeSeparatorPreserveAllTokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(strM71738M0, "###");
                if (UWMainActivityFragment.this.f88791k4.m71892t(strArrSplitByWholeSeparatorPreserveAllTokens[0]) != 6) {
                    progressDialogShow.dismiss();
                    CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "This backup code is not created here.", 1);
                    return;
                }
                UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
                uWMainActivityFragment.f88791k4.m71866m(uWMainActivityFragment.f88788h4, "Delete from logs");
                UWMainActivityFragment uWMainActivityFragment2 = UWMainActivityFragment.this;
                uWMainActivityFragment2.f88791k4.m71866m(uWMainActivityFragment2.f88788h4, "Delete from tests");
                CompressHelper compressHelper2 = UWMainActivityFragment.this.f88791k4;
                compressHelper2.m71881q(compressHelper2.m71823X0(), "delete from favorites where dbName='" + UWMainActivityFragment.this.f88788h4.getString("Name").replace("'", "''") + "'");
                UWMainActivityFragment uWMainActivityFragment3 = UWMainActivityFragment.this;
                uWMainActivityFragment3.f88791k4.m71881q(uWMainActivityFragment3.m72591E3(), "delete from highlight where dbName='" + UWMainActivityFragment.this.f88788h4.getString("Name").replace("'", "''") + "'");
                UWMainActivityFragment uWMainActivityFragment4 = UWMainActivityFragment.this;
                uWMainActivityFragment4.f88791k4.m71867m0(strArrSplitByWholeSeparatorPreserveAllTokens[0], CompressHelper.m71752f1(uWMainActivityFragment4.f88788h4), "logs", "qid,selectedAnswer,corrAnswer,answerDate,time,testId", null);
                int iM71892t = UWMainActivityFragment.this.f88791k4.m71892t(strArrSplitByWholeSeparatorPreserveAllTokens[1]);
                if (iM71892t != 11) {
                    if (iM71892t == 10) {
                        UWMainActivityFragment uWMainActivityFragment5 = UWMainActivityFragment.this;
                        compressHelper = uWMainActivityFragment5.f88791k4;
                        str3 = strArrSplitByWholeSeparatorPreserveAllTokens[1];
                        strM71752f1 = CompressHelper.m71752f1(uWMainActivityFragment5.f88788h4);
                        str4 = "qIds,createdDate,qIndex,done,mode,right,wrong,subject,system,hard";
                    }
                    Bundle bundle = new Bundle();
                    bundle.putString("dbName", UWMainActivityFragment.this.f88788h4.getString("Name"));
                    bundle.putString("dbTitle", UWMainActivityFragment.this.f88788h4.getString("Title"));
                    CompressHelper compressHelper3 = UWMainActivityFragment.this.f88791k4;
                    compressHelper3.m71867m0(strArrSplitByWholeSeparatorPreserveAllTokens[2], compressHelper3.m71823X0(), "favorites", "dbName,dbTitle,dbAddress,dbDate,dbDocName", bundle);
                    UWMainActivityFragment uWMainActivityFragment6 = UWMainActivityFragment.this;
                    uWMainActivityFragment6.f88791k4.m71867m0(strArrSplitByWholeSeparatorPreserveAllTokens[3], uWMainActivityFragment6.m72591E3(), "highlight", "dbName,dbTitle,dbAddress,dbDate,dbDocName,type,text,note,save", bundle);
                    UWMainActivityFragment.this.m72611y3();
                    progressDialogShow.dismiss();
                    CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "Restore was successful", 1);
                    UWMainActivityFragment.this.m72605s3();
                    UWMainActivityFragment.this.m72601V3(11);
                    UWMainActivityFragment.this.m72596P3();
                }
                UWMainActivityFragment uWMainActivityFragment7 = UWMainActivityFragment.this;
                compressHelper = uWMainActivityFragment7.f88791k4;
                str3 = strArrSplitByWholeSeparatorPreserveAllTokens[1];
                strM71752f1 = CompressHelper.m71752f1(uWMainActivityFragment7.f88788h4);
                str4 = "id,qIds,createdDate,qIndex,done,mode,right,wrong,subject,system,hard";
                compressHelper.m71867m0(str3, strM71752f1, "tests", str4, null);
                Bundle bundle2 = new Bundle();
                bundle2.putString("dbName", UWMainActivityFragment.this.f88788h4.getString("Name"));
                bundle2.putString("dbTitle", UWMainActivityFragment.this.f88788h4.getString("Title"));
                CompressHelper compressHelper32 = UWMainActivityFragment.this.f88791k4;
                compressHelper32.m71867m0(strArrSplitByWholeSeparatorPreserveAllTokens[2], compressHelper32.m71823X0(), "favorites", "dbName,dbTitle,dbAddress,dbDate,dbDocName", bundle2);
                UWMainActivityFragment uWMainActivityFragment62 = UWMainActivityFragment.this;
                uWMainActivityFragment62.f88791k4.m71867m0(strArrSplitByWholeSeparatorPreserveAllTokens[3], uWMainActivityFragment62.m72591E3(), "highlight", "dbName,dbTitle,dbAddress,dbDate,dbDocName,type,text,note,save", bundle2);
                UWMainActivityFragment.this.m72611y3();
                progressDialogShow.dismiss();
                CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "Restore was successful", 1);
                UWMainActivityFragment.this.m72605s3();
                UWMainActivityFragment.this.m72601V3(11);
                UWMainActivityFragment.this.m72596P3();
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.9
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                progressDialogShow.dismiss();
                CompressHelper.m71767x2(UWMainActivityFragment.this.m15366r(), "Error in contacting server", 1);
            }
        });
    }

    /* renamed from: s3 */
    public void m72605s3() {
        CompressHelper compressHelper = this.f88791k4;
        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f88788h4, "select count(*) as c from questions where " + m72590D3()));
        this.f89192I4 = bundleM71890s1 == null ? 0 : Integer.valueOf(bundleM71890s1.getString("c")).intValue();
    }

    /* renamed from: t3 */
    public String m72606t3(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ").format(date);
    }

    /* renamed from: u3 */
    public void m72607u3() {
        ArrayList<Bundle> arrayList = new ArrayList<>();
        this.f89189F4 = arrayList;
        arrayList.add(m72588B3("All Questions", ""));
        this.f89189F4.add(m72588B3("Unused", "not (id in (select distinct qid from logs))"));
        this.f89189F4.add(m72588B3("Incorrect", "id in (select qid from (select qid,max(rowid),selectedanswer<>corrAnswer as res from logs group by qid) where res=1) "));
        this.f89189F4.add(m72588B3("Correct", "id in (select qid from (select qid,max(rowid),selectedanswer=corrAnswer as res from logs group by qid) where res=1) "));
        this.f89189F4.add(m72588B3("Favorites", "id in (" + this.f88791k4.m71858j0("select group_concat(\"'\" || dbAddress || \"'\") as s from favorites where dbName = '" + this.f88788h4.getString("Name") + "' and dbAddress NOT LIKE '%html-%'").get(0).getString("s").replace("question-", "").replace("answer-", "") + ")"));
        Iterator<String> it2 = this.f89215f5.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            this.f89189F4.add(m72588B3("Unused Since " + next, "not (id in (select distinct qid from logs where length(answerDate)>10 and answerDate > '" + next + "'))"));
        }
        this.f89189F4.add(m72588B3("Unused Since Now", "UnusedNow"));
    }

    /* renamed from: v3 */
    public void m72608v3() throws IOException {
        String strM72610x3;
        CompressHelper compressHelper;
        Bundle bundle;
        StringBuilder sb;
        String str;
        Iterator<Bundle> it2;
        if (this.f89216g5.equals("Let's Go")) {
            m72577N3();
            if (this.f89192I4 == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no questions matching your criteria", 0);
                return;
            }
            this.f89216g5 = "Generating";
            this.f88792l4.m27492H(12);
            String str2 = StringUtils.splitByWholeSeparator(this.f89187D4.get(this.f89194K4).getString("title"), StringUtils.SPACE)[0];
            String strM72606t3 = m72606t3(new Date());
            String strM72595L3 = m72595L3(this.f89185B4, this.f89198O4, "name");
            String strM72595L32 = m72595L3(this.f89186C4, this.f89199P4, "name");
            String string = this.f89190G4.get(this.f89196M4).getString("title");
            if (this.f89221l5.isEmpty()) {
                String strM72590D3 = m72590D3();
                if (this.f89197N4 == 0) {
                    compressHelper = this.f88791k4;
                    bundle = this.f88788h4;
                    sb = new StringBuilder();
                    sb.append("Select id, parentqid as parentQId from questions where ");
                    sb.append(strM72590D3);
                    str = " order by random() limit ";
                } else {
                    compressHelper = this.f88791k4;
                    bundle = this.f88788h4;
                    sb = new StringBuilder();
                    sb.append("Select id, parentqid as parentQId from questions where ");
                    sb.append(strM72590D3);
                    str = " order by id limit ";
                }
                sb.append(str);
                sb.append(str2);
                ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(bundle, sb.toString());
                ArrayList<String> arrayList = new ArrayList<>();
                ArrayList arrayList2 = new ArrayList();
                Iterator<Bundle> it3 = arrayListM71817V.iterator();
                int iIntValue = 0;
                while (it3.hasNext()) {
                    Bundle next = it3.next();
                    String string2 = next.getString("parentQId");
                    if (string2.equals("0")) {
                        it2 = it3;
                        arrayList.add(next.getString("id"));
                        iIntValue++;
                    } else if (arrayList2.contains(string2)) {
                        it2 = it3;
                    } else {
                        CompressHelper compressHelper2 = this.f88791k4;
                        Bundle bundle2 = this.f88788h4;
                        StringBuilder sb2 = new StringBuilder();
                        it2 = it3;
                        sb2.append("SELECT count(*) as c, group_concat(id) as g FROM (SELECT id FROM questions WHERE parentqid =");
                        sb2.append(string2);
                        sb2.append(" ORDER BY CASE WHEN id =");
                        sb2.append(string2);
                        sb2.append(" THEN 0 ELSE 1 END, id);");
                        Bundle bundle3 = compressHelper2.m71817V(bundle2, sb2.toString()).get(0);
                        iIntValue += Integer.valueOf(bundle3.getString("c")).intValue();
                        arrayList.add(bundle3.getString("g"));
                        arrayList2.add(string2);
                    }
                    it3 = it2;
                }
                if (iIntValue > Integer.valueOf(str2).intValue()) {
                    int iIntValue2 = iIntValue - Integer.valueOf(str2).intValue();
                    ArrayList arrayList3 = new ArrayList();
                    Iterator<String> it4 = arrayList.iterator();
                    while (it4.hasNext()) {
                        String next2 = it4.next();
                        if (next2 != null && !next2.contains(",")) {
                            arrayList3.add(next2);
                        }
                    }
                    Collections.shuffle(arrayList3);
                    for (int i2 = 0; i2 < iIntValue2 && !arrayList3.isEmpty(); i2++) {
                        arrayList.remove(arrayList3.get(0));
                        arrayList3.remove(0);
                    }
                }
                if (this.f89197N4 == 0) {
                    Collections.shuffle(arrayList);
                }
                if (iIntValue > Integer.valueOf(str2).intValue()) {
                    arrayList = m72600U3(arrayList, Integer.valueOf(str2).intValue());
                }
                strM72610x3 = C5150h.m72696a(",", arrayList);
            } else {
                strM72610x3 = m72610x3(this.f89221l5);
            }
            this.f88791k4.m71866m(this.f88788h4, "Insert into Tests (id, qIds, createdDate, qIndex, done, mode, right, wrong, subject, system, hard) values (null, '" + strM72610x3 + "', '" + strM72606t3 + "', 0, 0, '" + this.f89188E4.get(this.f89195L4).getString("title") + "', 0, 0, '" + m72609w3(strM72595L3) + "', '" + m72609w3(strM72595L32) + "', '" + m72609w3(string) + "')");
            CompressHelper compressHelper3 = this.f88791k4;
            String string3 = compressHelper3.m71890s1(compressHelper3.m71817V(this.f88788h4, "SELECT id FROM Tests ORDER BY id DESC LIMIT 1")).getString("id");
            CompressHelper compressHelper4 = this.f88791k4;
            Bundle bundle4 = this.f88788h4;
            StringBuilder sb3 = new StringBuilder();
            sb3.append("test-");
            sb3.append(string3);
            compressHelper4.m71772A1(bundle4, sb3.toString(), null, null);
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment.5
                @Override // java.lang.Runnable
                public void run() {
                    UWMainActivityFragment uWMainActivityFragment = UWMainActivityFragment.this;
                    uWMainActivityFragment.f89216g5 = "Let's Go";
                    uWMainActivityFragment.f88792l4.m27492H(12);
                }
            }, 1000L);
        }
    }

    /* renamed from: w3 */
    public String m72609w3(String str) {
        return str.replace("'", "''");
    }

    /* renamed from: x3 */
    public String m72610x3(String str) {
        return str.replace(StringUtils.SPACE, "").replace(".", ",").replace("-", ",");
    }

    /* renamed from: y3 */
    public void m72611y3() {
        CompressHelper compressHelper = this.f88791k4;
        compressHelper.m71881q(compressHelper.m71823X0(), "delete from favorites where dbName='" + this.f88788h4.getString("Name").replace("'", "''") + "' AND dbAddress like 'html-%'");
        ArrayList<Bundle> arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "select id,qIds from tests where score is null");
        if (arrayListM71817V == null) {
            return;
        }
        Iterator<Bundle> it2 = arrayListM71817V.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String string = next.getString("id");
            String string2 = next.getString("qIds");
            CompressHelper compressHelper2 = this.f88791k4;
            int iDoubleValue = (int) ((Double.valueOf(compressHelper2.m71890s1(compressHelper2.m71817V(this.f88788h4, "select count(*) as c from logs where testId='" + string + "' and selectedAnswer=corrAnswer")).getString("c")).doubleValue() / StringUtils.splitByWholeSeparatorPreserveAllTokens(string2, ",").length) * 100.0d);
            this.f88791k4.m71866m(this.f88788h4, "Update tests set score='" + iDoubleValue + "' where id=" + string);
        }
    }

    /* renamed from: z3 */
    public Bundle m72612z3(String str) {
        Bundle bundle = new Bundle();
        bundle.putBundle("DB", this.f88788h4);
        bundle.putString("ParentId", str);
        return bundle;
    }
}

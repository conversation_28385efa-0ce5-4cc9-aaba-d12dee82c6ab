package net.imedicaldoctor.imd.Fragments.AccessMedicine;

import android.app.Dialog;
import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.AccessMedicine.AMViewerActivity;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class AMSectionsViewer extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f87496F4;

    /* renamed from: G4 */
    private String f87497G4;

    /* renamed from: H4 */
    private String f87498H4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f87496F4 = m15387y().getBundle("db");
        this.f87497G4 = m15387y().getString("docId");
        this.f87498H4 = m15387y().getString("parentId");
        CompressHelper compressHelper = new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new CursorAdapter(m15366r(), compressHelper.m71850h(compressHelper.m71817V(this.f87496F4, "Select rowid as _id, * from sections where sectionId = " + this.f87497G4)), 0) { // from class: net.imedicaldoctor.imd.Fragments.AccessMedicine.AMSectionsViewer.1
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("name")));
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getItemViewType(int i2) {
                return 0;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getViewTypeCount() {
                return 1;
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate2 = LayoutInflater.from(AMSectionsViewer.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text, viewGroup, false);
                viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                return viewInflate2;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.AccessMedicine.AMSectionsViewer.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                if (cursorMo10512c.moveToPosition(i2)) {
                    String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("id"));
                    iMDLogger.m73554j("AMSectionsViewer", "Goto : " + string);
                    ((AMViewerActivity.AMViewerFragment) AMSectionsViewer.this.m15351l0()).mo71967C3(string);
                    AMSectionsViewer.this.mo15203M2();
                }
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

package net.imedicaldoctor.imd.Fragments.Skyscape;

import android.content.Intent;
import android.content.res.Resources;
import android.database.SQLException;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.html.HTML;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Data.UnzipCompleted;
import net.imedicaldoctor.imd.Decompress;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import okio.BufferedSink;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class SSViewerActivity extends ViewerHelperActivity {

    public static class SSViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private int f88919X4;

        /* renamed from: Y4 */
        public ArrayList<Bundle> f88920Y4;

        /* renamed from: Z4 */
        public ArrayList<Bundle> f88921Z4;

        /* renamed from: L4 */
        private void m72491L4(String str) {
            ArrayList<Bundle> arrayList = this.f88921Z4;
            if (arrayList == null || arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
                return;
            }
            ArrayList arrayList2 = new ArrayList();
            arrayList2.addAll(this.f88921Z4);
            int i2 = 0;
            for (int i3 = 0; i3 < arrayList2.size(); i3++) {
                if (((Bundle) arrayList2.get(i3)).getString("id").contains(str)) {
                    i2 = i3;
                }
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList2);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: C3 */
        public void mo71967C3(String str) {
            this.f89569G4.m73433g("document.getElementsByName(\"section__" + str + "\")[0].scrollIntoView(true);");
        }

        /* renamed from: I4 */
        public void m72492I4() {
            try {
                m72493K4("ssimages");
                m72493K4("ssscripts");
                m72493K4("ssstyles");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("SSViewer", "Error in getting ss resources : " + e2.getLocalizedMessage());
            }
        }

        /* renamed from: K4 */
        public void m72493K4(String str) throws IOException {
            String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
            if (!new File(strM71753g1).exists()) {
                new File(strM71753g1).mkdirs();
            }
            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, str, "base");
            if (!new File(strM71754h1).exists()) {
                new File(strM71754h1).mkdirs();
            }
            String[] list = new String[0];
            try {
                list = m15366r().getAssets().list(str);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("SSViewer copyfolder", "Can't list files");
            }
            for (String str2 : list) {
                File file = new File(strM71754h1 + '/' + str2);
                if (!file.exists()) {
                    try {
                        InputStream inputStreamOpen = m15366r().getAssets().open(str + "/" + str2);
                        try {
                            FileOutputStream fileOutputStream = new FileOutputStream(file);
                            try {
                                BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75780p(fileOutputStream));
                                try {
                                    bufferedSinkM75768d.mo75508y1(Okio.m75785u(inputStreamOpen));
                                    bufferedSinkM75768d.flush();
                                    bufferedSinkM75768d.close();
                                    fileOutputStream.close();
                                    if (inputStreamOpen != null) {
                                        inputStreamOpen.close();
                                    }
                                } catch (Throwable th) {
                                    if (bufferedSinkM75768d != null) {
                                        try {
                                            bufferedSinkM75768d.close();
                                        } catch (Throwable th2) {
                                            th.addSuppressed(th2);
                                        }
                                    }
                                    throw th;
                                }
                            } finally {
                            }
                        } finally {
                        }
                    } catch (Exception unused) {
                        iMDLogger.m73550f("CopyJavascript", "Error in Copying " + str2 + " to " + strM71753g1 + "/" + str2);
                    }
                }
            }
        }

        /* renamed from: M4 */
        public void m72494M4() {
            SSSectionsViewer sSSectionsViewer = new SSSectionsViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("items", this.f88920Y4);
            bundle.putString("titleProperty", HTML.Tag.f74369V);
            bundle.putString("sectionProperty", "sectionId");
            sSSectionsViewer.m15342i2(bundle);
            sSSectionsViewer.mo15218Z2(true);
            sSSectionsViewer.m15245A2(this, 0);
            sSSectionsViewer.mo15222e3(m15283M(), "SSSectionViewer");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            Bundle bundleM72839v3;
            ArrayList<Bundle> arrayList = this.f88921Z4;
            if (arrayList == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f88921Z4)) == null) {
                return null;
            }
            return bundleM72839v3.getString("ImagePath");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_amviewer, menu);
            m72833q4(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (m15387y().containsKey("Mode")) {
                this.f88919X4 = m15387y().getInt("Mode");
            } else {
                this.f88919X4 = 0;
            }
            if (bundle != null) {
                this.f88920Y4 = bundle.getParcelableArrayList("mSections");
                this.f88919X4 = bundle.getInt("Mode");
                this.f88921Z4 = bundle.getParcelableArrayList("mImages");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            iMDLogger.m73554j("AMViewer", "Loading AM Document with mDocAddress = " + this.f89567E4);
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSViewerActivity.SSViewerFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        CompressHelper compressHelper = new CompressHelper(SSViewerFragment.this.m15366r());
                        String str = SSViewerFragment.this.f89563A4;
                        if (str != null && str.length() != 0) {
                            return;
                        }
                        if (SSViewerFragment.this.f88919X4 == 0) {
                            ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(SSViewerFragment.this.f89566D4, "select * from document where id = " + SSViewerFragment.this.f89567E4);
                            if (arrayListM71817V.size() == 0) {
                                SSViewerFragment.this.f89595p4 = "Document doesn't exist";
                                return;
                            }
                            Bundle bundle2 = arrayListM71817V.get(0);
                            String strM71773B = compressHelper.m71773B(bundle2.getString("doc"), bundle2.getString("id"), "127");
                            SSViewerFragment.this.f89568F4 = bundle2.getString("title");
                            String strReplace = strM71773B.replace("<img data-original=\"s_audio_intext.jpeg\" src=\"spinnerLarge.gif\" alt=\"Image not available.\" class=\"contentFigures\" />", "<img src=\"s_audio_intext.jpeg\">").replace("<body>", "<body >");
                            String strM71751f = CompressHelper.m71751f(strReplace, "<body", ">");
                            if (strM71751f == null) {
                                strM71751f = "";
                            }
                            String strReplace2 = strReplace.replace("<body" + strM71751f + ">", "<body onload=\\\"onBodyLoad();\\\" style=\\\"-webkit-text-size-adjust:200%;\" " + strM71751f + "> <script src=\"log4javascript.js\" ></script><script src=\"core.js\" ></script><script src=\"dom.js\" ></script><script src=\"domrange.js\" ></script><script src=\"wrappedrange.js\" ></script><script src=\"wrappedselection.js\" ></script><script src=\"rangy-cssclassapplier.js\" ></script><script src=\"rangy-highlighter.js\" ></script><script src=\"hightlight.js\" ></script><script src=\"find.js\" ></script>").replace("../../../", "ss");
                            SSViewerFragment sSViewerFragment = SSViewerFragment.this;
                            sSViewerFragment.f88920Y4 = compressHelper.m71817V(sSViewerFragment.f89566D4, "Select * from documentSections where docId=" + SSViewerFragment.this.f89567E4);
                            SSViewerFragment.this.m72826m3();
                            SSViewerFragment.this.f89563A4 = strReplace2;
                        }
                        if (!compressHelper.m71903x1()) {
                            SSViewerFragment.this.m72827m4("Chapter");
                        }
                        SSViewerFragment.this.m72492I4();
                    } catch (Exception e2) {
                        e2.printStackTrace();
                        SSViewerFragment.this.f89595p4 = e2.getLocalizedMessage();
                    }
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSViewerActivity.SSViewerFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    String str = SSViewerFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        SSViewerFragment sSViewerFragment = SSViewerFragment.this;
                        sSViewerFragment.m72780C4(sSViewerFragment.f89595p4);
                        return;
                    }
                    String strM71753g1 = CompressHelper.m71753g1(SSViewerFragment.this.f89566D4, "base");
                    SSViewerFragment sSViewerFragment2 = SSViewerFragment.this;
                    sSViewerFragment2.m72795O3(sSViewerFragment2.f89563A4, strM71753g1);
                    if (SSViewerFragment.this.f89568F4.contains("- Calculator")) {
                        SSViewerFragment.this.m72837t4();
                    } else {
                        SSViewerFragment.this.m72836s4();
                    }
                    SSViewerFragment.this.m72831p4();
                    SSViewerFragment.this.mo72642f3(C5562R.menu.menu_amviewer);
                    SSViewerFragment.this.m15358o2(false);
                    SSViewerFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: W3 */
        public boolean mo71969W3(ConsoleMessage consoleMessage) throws NumberFormatException, SQLException {
            super.mo71969W3(consoleMessage);
            iMDLogger.m73550f("SSViewer - console", consoleMessage.message());
            new CompressHelper(m15366r());
            String[] strArrSplit = consoleMessage.message().split(",,,,,");
            if (strArrSplit.length == 2 && strArrSplit[0].equals("SSImage")) {
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
                if (strArrSplitByWholeSeparator.length > 0) {
                    ArrayList<Bundle> arrayList = new ArrayList<>();
                    for (String str : strArrSplitByWholeSeparator) {
                        if (!str.contains("ssimages")) {
                            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str, "/");
                            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, strArrSplitByWholeSeparator2[1] + "_" + strArrSplitByWholeSeparator2[2], "Images");
                            if (new File(strM71754h1).length() > ExoPlayer.f21773a1) {
                                Bundle bundle = new Bundle();
                                bundle.putString("ImagePath", strM71754h1);
                                bundle.putString("Description", null);
                                bundle.putString("id", strM71754h1);
                                arrayList.add(bundle);
                            }
                        }
                    }
                    this.f88921Z4 = arrayList;
                    arrayList.size();
                    mo71972o4();
                }
                for (String str2 : strArrSplitByWholeSeparator) {
                    if (!str2.contains("ssimages")) {
                        String[] strArrSplitByWholeSeparator3 = StringUtils.splitByWholeSeparator(str2, "/");
                        this.f89569G4.m73433g("updateImageSource(\"" + str2 + "\", \"" + (strArrSplitByWholeSeparator3[0] + "/Images/" + strArrSplitByWholeSeparator3[1] + "_" + strArrSplitByWholeSeparator3[2]) + "\")");
                    }
                }
            }
            return true;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: Z3 */
        public void mo71956Z3(WebView webView, String str) {
            super.mo71956Z3(webView, str);
            this.f89569G4.m73433g("updatePlayImage();");
            this.f89569G4.m73433g("console.log('SSImage,,,,,' +getImageList());");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_gallery) {
                m72491L4("soheilvb");
            }
            if (itemId == C5562R.id.action_menu) {
                m72494M4();
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
            try {
                iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
                CompressHelper compressHelper = new CompressHelper(m15366r());
                if (str2.equals("skyscape")) {
                    if (str3.contains("artinart:KAUD:url=")) {
                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, "/");
                        final String str4 = strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1];
                        String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, str4, "base");
                        if (new File(strM71754h1).exists()) {
                            m72813b4(strM71754h1);
                            return true;
                        }
                        Decompress.m71940f(CompressHelper.m71753g1(this.f89566D4, "Sounds.zip"), "Sounds/" + str4, new UnzipCompleted() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSViewerActivity.SSViewerFragment.3
                            @Override // net.imedicaldoctor.imd.Data.UnzipCompleted
                            /* renamed from: a */
                            public void mo71929a(String str5) {
                            }

                            @Override // net.imedicaldoctor.imd.Data.UnzipCompleted
                            /* renamed from: b */
                            public void mo71930b(byte[] bArr) {
                                String strM71754h12 = CompressHelper.m71754h1(SSViewerFragment.this.f89566D4, str4, "base");
                                File file = new File(strM71754h12);
                                if (file.exists()) {
                                    return;
                                }
                                try {
                                    BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
                                    try {
                                        bufferedSinkM75768d.write(bArr);
                                        bufferedSinkM75768d.flush();
                                        SSViewerFragment.this.m72813b4(strM71754h12);
                                        file.deleteOnExit();
                                        bufferedSinkM75768d.close();
                                    } finally {
                                    }
                                } catch (IOException e2) {
                                    FirebaseCrashlytics.m48010d().m48016g(e2);
                                    iMDLogger.m73550f("GetResource Error", "Unable to write byte array to " + file.getAbsolutePath() + " :" + e2.getLocalizedMessage());
                                }
                            }
                        });
                        return true;
                    }
                    if (str3.contains("artinart:KVID:url=")) {
                        String strM71754h12 = CompressHelper.m71754h1(this.f89566D4, compressHelper.m71893t1(StringUtils.splitByWholeSeparator(str3, "=")), "Videos");
                        Bundle bundle = new Bundle();
                        bundle.putString("isVideo", IcyHeaders.f28171a3);
                        bundle.putString("ImagePath", strM71754h12);
                        bundle.putString("Description", null);
                        bundle.putString("id", strM71754h12);
                        this.f88921Z4.add(bundle);
                        m72491L4(strM71754h12);
                        return true;
                    }
                    if (str3.equals("#section_list")) {
                        m72494M4();
                    } else {
                        if (!str3.startsWith("#")) {
                            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str3, "/");
                            String[] strArrSplitByWholeSeparator3 = StringUtils.splitByWholeSeparator(strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 2] + "/" + strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1], "#");
                            new Bundle();
                            Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from document where name = '" + strArrSplitByWholeSeparator3[0] + "'"));
                            if (bundleM71890s1 == null) {
                                CompressHelper.m71767x2(m15366r(), "Document is not available", 1);
                                return true;
                            }
                            compressHelper.m71772A1(this.f89566D4, bundleM71890s1.getString("id"), null, strArrSplitByWholeSeparator3.length > 1 ? strArrSplitByWholeSeparator3[1] : null);
                            return true;
                        }
                        this.f89569G4.m73433g("document.getElementsByName(\"" + str3.substring(1) + "\")[0].scrollIntoView(true);");
                        this.f89569G4.m73433g("document.body.scrollTop = window.pageYOffset - 50;");
                    }
                } else if (str2.equals("myskycape")) {
                    String[] strArrSplit = StringUtils.split(str3, "=");
                    if (!strArrSplit[0].equals("?url") || strArrSplit[1].contains("../")) {
                        return true;
                    }
                    String[] strArrSplitByWholeSeparator4 = StringUtils.splitByWholeSeparator(StringUtils.splitByWholeSeparator(strArrSplit[1], "&")[0], "/");
                    String[] strArrSplitByWholeSeparator5 = StringUtils.splitByWholeSeparator(strArrSplitByWholeSeparator4[strArrSplitByWholeSeparator4.length - 2] + "/" + strArrSplitByWholeSeparator4[strArrSplitByWholeSeparator4.length - 1], "#");
                    new Bundle();
                    Bundle bundleM71890s12 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from document where name = '" + strArrSplitByWholeSeparator5[0] + "'"));
                    if (bundleM71890s12 == null) {
                        CompressHelper.m71767x2(m15366r(), "Document is not available", 1);
                        return true;
                    }
                    compressHelper.m71772A1(this.f89566D4, bundleM71890s12.getString("id"), null, strArrSplitByWholeSeparator5.length > 1 ? strArrSplitByWholeSeparator5[1] : null);
                    return true;
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new SSViewerFragment(), bundle);
    }
}

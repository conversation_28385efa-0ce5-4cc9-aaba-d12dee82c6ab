package net.imedicaldoctor.imd.Fragments;

import android.view.View;
import androidx.recyclerview.widget.RecyclerView;
import com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemViewHolder;
import com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder;

/* loaded from: classes3.dex */
public abstract class AbstractAdvViewHolder extends RecyclerView.ViewHolder implements SwipeableItemViewHolder, DraggableItemViewHolder {

    /* renamed from: I */
    private int f87475I;

    /* renamed from: J */
    private int f87476J;

    /* renamed from: K */
    private int f87477K;

    /* renamed from: L */
    private float f87478L;

    /* renamed from: M */
    private float f87479M;

    /* renamed from: N */
    private float f87480N;

    /* renamed from: O */
    private int f87481O;

    public AbstractAdvViewHolder(View view) {
        super(view);
        this.f87476J = 0;
        this.f87477K = 0;
        this.f87479M = -3.4028235E38f;
        this.f87480N = Float.MAX_VALUE;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: a */
    public int mo50822a() {
        return this.f87476J;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: b */
    public float mo50823b() {
        return this.f87480N;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: c */
    public void mo50824c(int i2) {
        this.f87475I = i2;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemViewHolder
    /* renamed from: d */
    public int mo50469d() {
        return this.f87481O;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: f */
    public int mo50826f() {
        return this.f87477K;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: g */
    public void mo50827g(float f2) {
        this.f87478L = f2;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: h */
    public void mo50828h(int i2) {
        this.f87476J = i2;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemViewHolder
    /* renamed from: i */
    public void mo50470i(int i2) {
        this.f87481O = i2;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: j */
    public void mo50829j(float f2) {
        this.f87479M = f2;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: k */
    public float mo50830k() {
        return this.f87478L;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: l */
    public void mo50831l(float f2) {
        this.f87480N = f2;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: n */
    public float mo50832n() {
        return this.f87479M;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: o */
    public void mo50833o(int i2) {
        this.f87477K = i2;
    }

    @Override // com.h6ah4i.android.widget.advrecyclerview.swipeable.SwipeableItemViewHolder
    /* renamed from: p */
    public int mo50834p() {
        return this.f87475I;
    }
}

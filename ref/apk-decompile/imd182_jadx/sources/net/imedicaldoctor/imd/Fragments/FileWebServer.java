package net.imedicaldoctor.imd.Fragments;

import android.content.Context;
import android.net.wifi.WifiManager;
import android.text.format.Formatter;
import android.util.Log;
import androidx.media3.common.MimeTypes;
import com.google.common.net.HttpHeaders;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import fi.iki.elonen.NanoHTTPD;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.apache.commons.httpclient.cookie.Cookie2;
import org.apache.commons.httpclient.methods.multipart.FilePart;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class FileWebServer extends NanoHTTPD {

    /* renamed from: t */
    private final Context f88258t;

    /* renamed from: u */
    private final File f88259u;

    /* renamed from: v */
    private boolean f88260v;

    public FileWebServer(Context context, int i2, File file) {
        super(i2);
        this.f88260v = false;
        this.f88258t = context;
        this.f88259u = file;
    }

    /* renamed from: R */
    private String m72256R() {
        return Formatter.formatIpAddress(((WifiManager) this.f88258t.getSystemService("wifi")).getConnectionInfo().getIpAddress());
    }

    /* renamed from: S */
    private String m72257S(String str) {
        return str.endsWith(".html") ? NanoHTTPD.f77082p : str.endsWith(".css") ? HTML.Attribute.Value.f74318a : str.endsWith(".js") ? "application/javascript" : str.endsWith(".png") ? MimeTypes.f19867R0 : (str.endsWith(".jpg") || str.endsWith(".jpeg")) ? MimeTypes.f19865Q0 : FilePart.DEFAULT_CONTENT_TYPE;
    }

    /* renamed from: T */
    private NanoHTTPD.Response m72258T(String str) {
        NanoHTTPD.Response.Status status;
        String str2;
        File file = new File(this.f88259u, str);
        if (file.exists() && file.delete()) {
            status = NanoHTTPD.Response.Status.OK;
            str2 = "{\"status\":\"File deleted successfully\"}";
        } else {
            status = NanoHTTPD.Response.Status.NOT_FOUND;
            str2 = "{\"error\":\"File not found or deletion failed\"}";
        }
        return NanoHTTPD.m58293D(status, "application/json", str2);
    }

    /* renamed from: U */
    private NanoHTTPD.Response m72259U(NanoHTTPD.IHTTPSession iHTTPSession, String str) throws IOException {
        try {
            Map<String, String> mapMo58369j = iHTTPSession.mo58369j();
            iHTTPSession.mo58370k(mapMo58369j);
            String str2 = mapMo58369j.get(Annotation.f68285k3);
            if (str2 == null) {
                return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.BAD_REQUEST, "application/json", "{\"error\":\"No file uploaded\"}");
            }
            String str3 = iHTTPSession.mo58366g().get(Annotation.f68285k3).get(0);
            Log.d("UploadFileName", "Filename extracted: " + str3);
            File file = new File(str2);
            File file2 = new File(this.f88259u, str);
            if (!file2.exists() && !file2.mkdirs()) {
                return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.INTERNAL_ERROR, "application/json", "{\"error\":\"Failed to create directory\"}");
            }
            File file3 = new File(file2, str3);
            FileInputStream fileInputStream = new FileInputStream(file);
            try {
                FileOutputStream fileOutputStream = new FileOutputStream(file3);
                try {
                    byte[] bArr = new byte[1024];
                    while (true) {
                        int i2 = fileInputStream.read(bArr);
                        if (i2 == -1) {
                            fileOutputStream.close();
                            fileInputStream.close();
                            return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.OK, "application/json", "{\"status\":\"File uploaded successfully\"}");
                        }
                        fileOutputStream.write(bArr, 0, i2);
                    }
                } finally {
                }
            } catch (Throwable th) {
                try {
                    fileInputStream.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
                throw th;
            }
        } catch (NanoHTTPD.ResponseException | IOException unused) {
            return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.INTERNAL_ERROR, "application/json", "{\"error\":\"File upload failed\"}");
        }
    }

    /* renamed from: V */
    private NanoHTTPD.Response m72260V(String str) {
        File file = new File(this.f88259u, str);
        return file.isDirectory() ? m72262Y(file) : file.exists() ? m72263Z(file) : NanoHTTPD.m58293D(NanoHTTPD.Response.Status.NOT_FOUND, "text/plain", "File Not Found");
    }

    /* renamed from: X */
    private NanoHTTPD.Response m72261X(String str) throws IOException {
        try {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(this.f88258t.getAssets().open(str), StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            while (true) {
                String line = bufferedReader.readLine();
                if (line == null) {
                    bufferedReader.close();
                    return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.OK, m72257S(str), sb.toString());
                }
                sb.append(line);
                sb.append(StringUtils.f103471LF);
            }
        } catch (IOException unused) {
            return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.NOT_FOUND, "text/plain", "Asset Not Found");
        }
    }

    /* renamed from: Y */
    private NanoHTTPD.Response m72262Y(File file) throws JSONException {
        NanoHTTPD.Response.Status status;
        String string;
        File[] fileArrListFiles = file.listFiles();
        if (fileArrListFiles == null) {
            status = NanoHTTPD.Response.Status.INTERNAL_ERROR;
            string = "{\"error\":\"Failed to list files\"}";
        } else {
            JSONArray jSONArray = new JSONArray();
            for (File file2 : fileArrListFiles) {
                try {
                    JSONObject jSONObject = new JSONObject();
                    jSONObject.put("name", file2.getName());
                    jSONObject.put(Cookie2.PATH, file2.getPath().replace(this.f88259u.getPath() + "/", ""));
                    jSONObject.put("isDirectory", file2.isDirectory());
                    jSONObject.put("size", file2.length());
                    jSONArray.put(jSONObject);
                } catch (JSONException e2) {
                    e2.printStackTrace();
                    status = NanoHTTPD.Response.Status.INTERNAL_ERROR;
                    string = "{\"error\":\"Failed to create file object\"}";
                }
            }
            status = NanoHTTPD.Response.Status.OK;
            string = jSONArray.toString();
        }
        return NanoHTTPD.m58293D(status, "application/json", string);
    }

    /* renamed from: Z */
    private NanoHTTPD.Response m72263Z(File file) {
        try {
            NanoHTTPD.Response responseM58291B = NanoHTTPD.m58291B(NanoHTTPD.Response.Status.OK, m72257S(file.getName()), new FileInputStream(file));
            responseM58291B.m58384c(HttpHeaders.f62909a0, "attachment; filename=\"" + file.getName() + "\"");
            return responseM58291B;
        } catch (IOException unused) {
            return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.INTERNAL_ERROR, "text/plain", "Internal Server Error");
        }
    }

    @Override // fi.iki.elonen.NanoHTTPD
    /* renamed from: G */
    public NanoHTTPD.Response mo58313G(NanoHTTPD.IHTTPSession iHTTPSession) {
        String strMo58365c = iHTTPSession.mo58365c();
        NanoHTTPD.Method methodMo58371l = iHTTPSession.mo58371l();
        if (strMo58365c.equals("/")) {
            return m72261X("index.html");
        }
        if (strMo58365c.startsWith("/files/")) {
            String strReplaceFirst = strMo58365c.replaceFirst("/files/", "");
            if (methodMo58371l == NanoHTTPD.Method.GET) {
                return m72260V(strReplaceFirst);
            }
            if (methodMo58371l == NanoHTTPD.Method.POST) {
                return m72259U(iHTTPSession, strReplaceFirst);
            }
            if (methodMo58371l == NanoHTTPD.Method.DELETE) {
                return m72258T(strReplaceFirst);
            }
        }
        return NanoHTTPD.m58293D(NanoHTTPD.Response.Status.NOT_FOUND, "text/plain", "Not Found");
    }

    @Override // fi.iki.elonen.NanoHTTPD
    /* renamed from: L */
    public void mo58318L() throws IOException {
        super.mo58318L();
        this.f88260v = true;
    }

    @Override // fi.iki.elonen.NanoHTTPD
    /* renamed from: O */
    public void mo58321O() throws InterruptedException {
        super.mo58321O();
        this.f88260v = false;
    }

    /* renamed from: W */
    public boolean m72264W() {
        return this.f88260v;
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class EpoDXWebViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88233X4;

    /* renamed from: Y4 */
    public JSONObject f88234Y4;

    /* renamed from: Z4 */
    public String f88235Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f88236a5;

    /* renamed from: I4 */
    private void m72251I4(String str) {
        ArrayList<Bundle> arrayList = this.f88236a5;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        arrayList2.addAll(this.f88236a5);
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (((Bundle) arrayList2.get(i3)).getString("id").startsWith(str)) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList;
        Bundle bundleM72839v3;
        if (this.f88236a5.size() <= 0 || (arrayList = this.f88236a5) == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f88236a5)) == null) {
            return null;
        }
        return bundleM72839v3.getString("ImagePath");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EpoDXWebViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String str = EpoDXWebViewerActivityFragment.this.f89563A4;
                    if (str != null && str.length() != 0) {
                        return;
                    }
                    EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment = EpoDXWebViewerActivityFragment.this;
                    epoDXWebViewerActivityFragment.f89567E4 = epoDXWebViewerActivityFragment.m15387y().getString("DocAddress");
                    iMDLogger.m73550f("Loading Document", EpoDXWebViewerActivityFragment.this.f89567E4);
                    String str2 = EpoDXWebViewerActivityFragment.this.f89567E4.split("-")[1];
                    if (EpoDXWebViewerActivityFragment.this.m15387y().containsKey("mDB")) {
                        EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment2 = EpoDXWebViewerActivityFragment.this;
                        epoDXWebViewerActivityFragment2.f88235Z4 = epoDXWebViewerActivityFragment2.m15387y().getString("mDB");
                    }
                    EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment3 = EpoDXWebViewerActivityFragment.this;
                    if (epoDXWebViewerActivityFragment3.f88235Z4 == null) {
                        epoDXWebViewerActivityFragment3.f88235Z4 = "Dx";
                    }
                    ArrayList<Bundle> arrayListM71817V = epoDXWebViewerActivityFragment3.f89579Q4.m71817V(epoDXWebViewerActivityFragment3.f89566D4, "Select * from " + EpoDXWebViewerActivityFragment.this.f88235Z4 + "_monographs where id=" + str2);
                    if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                        JSONObject jSONObject = new JSONObject(EpoDXWebViewerActivityFragment.this.f89579Q4.m71773B(arrayListM71817V.get(0).getString("monograph"), str2, "127"));
                        EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment4 = EpoDXWebViewerActivityFragment.this;
                        epoDXWebViewerActivityFragment4.f88234Y4 = jSONObject;
                        epoDXWebViewerActivityFragment4.f89568F4 = epoDXWebViewerActivityFragment4.m15387y().getString("Title");
                        EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment5 = EpoDXWebViewerActivityFragment.this;
                        epoDXWebViewerActivityFragment5.f88236a5 = epoDXWebViewerActivityFragment5.m15387y().getParcelableArrayList("Images");
                        EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment6 = EpoDXWebViewerActivityFragment.this;
                        String strM72817d4 = epoDXWebViewerActivityFragment6.m72817d4(epoDXWebViewerActivityFragment6.m15366r(), "EPOHeader.css");
                        EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment7 = EpoDXWebViewerActivityFragment.this;
                        String strM72817d42 = epoDXWebViewerActivityFragment7.m72817d4(epoDXWebViewerActivityFragment7.m15366r(), "EPOFooter.css");
                        String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", EpoDXWebViewerActivityFragment.this.f89568F4).replace("[include]", "");
                        String strReplace2 = EpoDXWebViewerActivityFragment.this.m15387y().getString("Html").replace("..", ".");
                        EpoDXWebViewerActivityFragment.this.f89563A4 = strReplace + strReplace2 + strM72817d42;
                        return;
                    }
                    EpoDXWebViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                } catch (Exception e2) {
                    e2.printStackTrace();
                    EpoDXWebViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EpoDXWebViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = EpoDXWebViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment = EpoDXWebViewerActivityFragment.this;
                    epoDXWebViewerActivityFragment.m72780C4(epoDXWebViewerActivityFragment.f89595p4);
                    return;
                }
                EpoDXWebViewerActivityFragment epoDXWebViewerActivityFragment2 = EpoDXWebViewerActivityFragment.this;
                epoDXWebViewerActivityFragment2.m72795O3(epoDXWebViewerActivityFragment2.f89563A4, null);
                EpoDXWebViewerActivityFragment.this.m72836s4();
                EpoDXWebViewerActivityFragment.this.m72831p4();
                EpoDXWebViewerActivityFragment.this.mo72642f3(C5562R.menu.epolistmenu);
                EpoDXWebViewerActivityFragment.this.m15358o2(false);
                EpoDXWebViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_gallery) {
            m72251I4("soheilvb");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws JSONException {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        if (this.f89579Q4.m71800N1(this.f89566D4, str)) {
            return true;
        }
        if (str3.contains("//current?view=")) {
            try {
                JSONObject jSONObjectM71886r1 = this.f89579Q4.m71886r1(this.f88234Y4.getJSONArray("views"), "id", this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(str3, "//current?view=")));
                if (jSONObjectM71886r1 != null && jSONObjectM71886r1.getString("type").equals("image")) {
                    m72251I4(this.f88234Y4.getJSONArray("media").getJSONObject(Integer.valueOf(jSONObjectM71886r1.getJSONArray("image_refs").getString(0)).intValue()).getString(Annotation.f68285k3));
                }
                return true;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
                return true;
            }
        }
        if (!str3.contains("//current?article=")) {
            if (!str2.equals("http")) {
                return false;
            }
            this.f89579Q4.m71772A1(this.f89566D4, "epourl-" + str, null, null);
            return true;
        }
        try {
            String str4 = "<div style=\"margin:15px\">" + this.f88234Y4.getJSONObject("citations").getJSONArray("articles").getJSONObject(Integer.valueOf(this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(str3, "//current?article="))).intValue() - 1).getString(HTML.Tag.f74425y) + "</div>";
            this.f89579Q4.m71772A1(this.f89566D4, "epohtml-" + str4, null, null);
        } catch (Exception e3) {
            FirebaseCrashlytics.m48010d().m48016g(e3);
            e3.printStackTrace();
        }
        return true;
    }
}

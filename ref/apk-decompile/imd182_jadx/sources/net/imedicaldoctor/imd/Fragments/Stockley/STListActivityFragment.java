package net.imedicaldoctor.imd.Fragments.Stockley;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextGotoViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class STListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f89046A4;

    /* renamed from: B4 */
    public String f89047B4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            this.f89047B4 = "0";
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STListActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            this.f89047B4 = m15387y().getString("ParentId");
        }
        this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select * from toc where parentId=" + this.f89047B4);
        this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_goto_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STListActivityFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, final int i2) {
                RippleTextGotoViewHolder rippleTextGotoViewHolder = (RippleTextGotoViewHolder) viewHolder;
                rippleTextGotoViewHolder.f101504I.setText(bundle2.getString("title"));
                rippleTextGotoViewHolder.f101507L.setVisibility(8);
                rippleTextGotoViewHolder.f101506K.setVisibility(8);
                if (!bundle2.getString("leaf").equals(IcyHeaders.f28171a3)) {
                    if (bundle2.getString("docName").length() > 0) {
                        rippleTextGotoViewHolder.f101507L.setVisibility(0);
                        rippleTextGotoViewHolder.f101506K.setVisibility(0);
                        rippleTextGotoViewHolder.f101506K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STListActivityFragment.2.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                STListActivityFragment sTListActivityFragment = STListActivityFragment.this;
                                sTListActivityFragment.f88791k4.m71772A1(sTListActivityFragment.f88788h4, bundle2.getString("docName"), null, null);
                            }
                        });
                    } else {
                        rippleTextGotoViewHolder.f101507L.setVisibility(0);
                    }
                }
                rippleTextGotoViewHolder.f101505J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STListActivityFragment.2.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        STListActivityFragment.this.m72515i3(bundle2, i2);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                return new RippleTextGotoViewHolder(view);
            }
        };
        this.f89046A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STListActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                TextView textView;
                int i3;
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                if (bundle2.getString(Annotation.f68283i3).length() == 0) {
                    textView = rippleTextFullViewHolder.f101500J;
                    i3 = 8;
                } else {
                    textView = rippleTextFullViewHolder.f101500J;
                    i3 = 0;
                }
                textView.setVisibility(i3);
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STListActivityFragment.3.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        STListActivityFragment.this.m72468V2();
                        STListActivityFragment sTListActivityFragment = STListActivityFragment.this;
                        sTListActivityFragment.f88791k4.m71772A1(sTListActivityFragment.f88788h4, bundle2.getString("contentId"), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                STListActivityFragment.this.m72468V2();
                STListActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f89046A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f89046A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match '(text:" + str + "*) AND (type:1)' order by type asc");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    /* renamed from: i3 */
    public void m72515i3(Bundle bundle, int i2) {
        m72468V2();
        if (bundle.getString("leaf").equals(IcyHeaders.f28171a3)) {
            this.f88791k4.m71772A1(this.f88788h4, bundle.getString("docName"), null, null);
            return;
        }
        Bundle bundle2 = new Bundle();
        bundle2.putBundle("DB", this.f88788h4);
        bundle2.putString("ParentId", bundle.getString("id"));
        this.f88791k4.m71798N(STListActivity.class, STListActivityFragment.class, bundle2);
    }
}

package net.imedicaldoctor.imd.Fragments.UWorld;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;
import com.itextpdf.text.xml.xmp.DublinCoreProperties;
import com.itextpdf.tool.xml.html.HTML;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class BackupCodesAdapter extends ArrayAdapter<Bundle> {

    /* renamed from: X */
    private final ArrayList<Bundle> f89150X;

    /* renamed from: s */
    private final Context f89151s;

    public BackupCodesAdapter(Context context, ArrayList<Bundle> arrayList) {
        super(context, C5562R.layout.list_item_backup_code, arrayList);
        this.f89151s = context;
        this.f89150X = arrayList;
    }

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public View getView(int i2, View view, ViewGroup viewGroup) {
        View viewInflate = ((LayoutInflater) this.f89151s.getSystemService("layout_inflater")).inflate(C5562R.layout.list_item_backup_code, viewGroup, false);
        TextView textView = (TextView) viewInflate.findViewById(C5562R.id.textViewCode);
        TextView textView2 = (TextView) viewInflate.findViewById(C5562R.id.textViewDate);
        TextView textView3 = (TextView) viewInflate.findViewById(C5562R.id.textViewTitle);
        Bundle bundle = this.f89150X.get(i2);
        String string = bundle.getString(HTML.Tag.f74390g0);
        String string2 = bundle.getString(DublinCoreProperties.f73850d);
        String string3 = bundle.getString("title");
        textView.setText(string);
        textView2.setText(string2);
        textView3.setText(string3);
        return viewInflate;
    }
}

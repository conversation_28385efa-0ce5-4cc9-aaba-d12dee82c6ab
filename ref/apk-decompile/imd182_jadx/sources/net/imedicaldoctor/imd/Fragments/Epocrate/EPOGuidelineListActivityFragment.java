package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class EPOGuidelineListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88047A4;

    /* renamed from: B4 */
    public String f88048B4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String string;
        ChaptersAdapter chaptersAdapter;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            string = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOGuidelineListActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            string = m15387y().getString("ParentId");
        }
        this.f88048B4 = string;
        if (this.f88048B4 == null) {
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "SELECT * FROM gl_cats");
            chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOGuidelineListActivityFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: f0 */
                public void mo71975f0(Bundle bundle2, int i2) {
                    EPOGuidelineListActivityFragment.this.m72468V2();
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("DB", EPOGuidelineListActivityFragment.this.f88788h4);
                    bundle3.putString("ParentId", bundle2.getString("id"));
                    EPOGuidelineListActivityFragment.this.f88791k4.m71798N(EPOGuidelineListActivity.class, EPOGuidelineListActivityFragment.class, bundle3);
                }
            };
        } else {
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "select * from gl_cats_topics inner join gl_topics on gl_cats_topics.topicId = gl_topics.id where catId=" + this.f88048B4);
            chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOGuidelineListActivityFragment.3
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: e0 */
                public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                    RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                    rippleTextFullViewHolder.f101499I.setText(bundle2.getString("title"));
                    rippleTextFullViewHolder.f101500J.setText(bundle2.getString("subtitle"));
                    rippleTextFullViewHolder.f101501K.setVisibility(8);
                    rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOGuidelineListActivityFragment.3.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            EPOGuidelineListActivityFragment ePOGuidelineListActivityFragment = EPOGuidelineListActivityFragment.this;
                            ePOGuidelineListActivityFragment.f88791k4.m71772A1(ePOGuidelineListActivityFragment.f88788h4, "guideline-" + bundle2.getString("id"), null, null);
                        }
                    });
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: h0 */
                public RecyclerView.ViewHolder mo71986h0(View view) {
                    return new RippleTextFullViewHolder(view);
                }
            };
        }
        this.f88792l4 = chaptersAdapter;
        this.f88047A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOGuidelineListActivityFragment.4
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: g0 */
            public void mo71976g0(Bundle bundle2, int i2) {
                EPOGuidelineListActivityFragment.this.m72468V2();
                EPOGuidelineListActivityFragment ePOGuidelineListActivityFragment = EPOGuidelineListActivityFragment.this;
                ePOGuidelineListActivityFragment.f88791k4.m71772A1(ePOGuidelineListActivityFragment.f88788h4, "guideline-" + bundle2.getString("contentId"), null, null);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                EPOGuidelineListActivityFragment.this.m72468V2();
                EPOGuidelineListActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88047A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88047A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match 'text:" + str + "* AND typeText:Guideline AND type:1'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: c3 */
    public void mo72171c3() {
        this.f88800t4.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.guidelines_icon));
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "Guidelines";
    }
}

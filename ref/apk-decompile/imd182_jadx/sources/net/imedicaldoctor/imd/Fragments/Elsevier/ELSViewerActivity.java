package net.imedicaldoctor.imd.Fragments.Elsevier;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class ELSViewerActivity extends ViewerHelperActivity {

    public static class ELSViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private String f88009X4;

        /* renamed from: Y4 */
        private MenuItem f88010Y4;

        /* renamed from: Z4 */
        public ArrayList<Bundle> f88011Z4;

        /* renamed from: a5 */
        public ArrayList<Bundle> f88012a5;

        /* JADX INFO: Access modifiers changed from: private */
        /* JADX WARN: Removed duplicated region for block: B:53:0x0186  */
        /* JADX WARN: Removed duplicated region for block: B:98:0x016d A[EXC_TOP_SPLITTER, SYNTHETIC] */
        /* renamed from: I4 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public void m72164I4() {
            /*
                Method dump skipped, instructions count: 707
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Elsevier.ELSViewerActivity.ELSViewerFragment.m72164I4():void");
        }

        /* renamed from: M4 */
        public static void m72167M4(StringBuilder sb, String str, String str2) {
            while (true) {
                int iLastIndexOf = sb.lastIndexOf(str);
                if (iLastIndexOf == -1) {
                    return;
                } else {
                    sb.replace(iLastIndexOf, str.length() + iLastIndexOf, str2);
                }
            }
        }

        /* renamed from: N4 */
        private void m72168N4(String str) {
            Intent intent;
            ArrayList<Bundle> arrayList = this.f88011Z4;
            if (arrayList == null || arrayList.size() == 0) {
                ArrayList arrayList2 = new ArrayList();
                Bundle bundle = new Bundle();
                bundle.putString("id", str);
                bundle.putString("ImagePath", CompressHelper.m71754h1(this.f89566D4, str + ".jpg", "base"));
                bundle.putString("Description", "");
                arrayList2.add(bundle);
                intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
                intent.putExtra("Images", arrayList2);
                intent.putExtra("Start", 0);
            } else {
                int i2 = 0;
                for (int i3 = 0; i3 < this.f88011Z4.size(); i3++) {
                    if (this.f88011Z4.get(i3).getString("id").equals(str)) {
                        i2 = i3;
                    }
                }
                Log.e("Images count", "Images count" + this.f88011Z4.size());
                intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
                intent.putExtra("Images", this.f88011Z4);
                intent.putExtra("Start", i2);
            }
            mo15256D2(intent);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: D3 */
        public boolean mo72169D3() {
            ArrayList<Bundle> arrayList = this.f88011Z4;
            return (arrayList == null || arrayList.size() == 0) ? false : true;
        }

        /* renamed from: L4 */
        public void m72170L4(String str, Boolean bool) {
            StringBuilder sb;
            String str2;
            if (bool.booleanValue()) {
                sb = new StringBuilder();
                sb.append(str);
                str2 = " - Before";
            } else {
                sb = new StringBuilder();
                sb.append(str);
                str2 = " - After";
            }
            sb.append(str2);
            Log.e("TimeProfiler", sb.toString());
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            Bundle bundleM72839v3;
            ArrayList<Bundle> arrayList = this.f88011Z4;
            if (arrayList == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f88011Z4)) == null) {
                return null;
            }
            return bundleM72839v3.getString("ImagePath");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            m72170L4("OnCreateView", Boolean.TRUE);
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f88009X4 = bundle.getString("mResources");
                this.f88011Z4 = bundle.getParcelableArrayList("mImages");
                this.f88012a5 = bundle.getParcelableArrayList("mOtherImages");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Elsevier.ELSViewerActivity.ELSViewerFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    String strM72844y3;
                    try {
                        CompressHelper compressHelper = new CompressHelper(ELSViewerFragment.this.m15366r());
                        String str = ELSViewerFragment.this.f89563A4;
                        if (str == null || str.length() == 0) {
                            ELSViewerFragment eLSViewerFragment = ELSViewerFragment.this;
                            Boolean bool = Boolean.TRUE;
                            eLSViewerFragment.m72170L4("QueryDB", bool);
                            Bundle bundleM71844e0 = compressHelper.m71844e0(ELSViewerFragment.this.f89566D4, "Select id as _id,* from Docs where id=" + ELSViewerFragment.this.f89567E4);
                            if (bundleM71844e0 == null) {
                                ELSViewerFragment.this.f89595p4 = "Document doesn't exist";
                                return;
                            }
                            ELSViewerFragment eLSViewerFragment2 = ELSViewerFragment.this;
                            Boolean bool2 = Boolean.FALSE;
                            eLSViewerFragment2.m72170L4("QueryDB", bool2);
                            String string = bundleM71844e0.getString("mainContent");
                            ELSViewerFragment.this.f88009X4 = bundleM71844e0.getString("resources");
                            ELSViewerFragment.this.f89568F4 = bundleM71844e0.getString("name");
                            if (ELSViewerFragment.this.m72782E3(bundleM71844e0.getString("_id")).booleanValue()) {
                                strM72844y3 = ELSViewerFragment.this.m72844y3(bundleM71844e0.getString("_id"));
                            } else {
                                String str2 = new String(compressHelper.m71897v(string, bundleM71844e0.getString("_id"), "127"));
                                ELSViewerFragment eLSViewerFragment3 = ELSViewerFragment.this;
                                String strM72817d4 = eLSViewerFragment3.m72817d4(eLSViewerFragment3.m15366r(), "ELSHeaderNew.css");
                                ELSViewerFragment eLSViewerFragment4 = ELSViewerFragment.this;
                                String strM72817d42 = eLSViewerFragment4.m72817d4(eLSViewerFragment4.m15366r(), "ELSFooterNew.css");
                                String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", ELSViewerFragment.this.f89568F4);
                                String strReplace2 = str2.replace("<div class=\"table\"", "<div class=\"table\" style=\"width:90%%;overflow: scroll;\"").replace("<div once-if=\"item.tables.length &gt; 0\"", "<div once-if=\"item.tables.length &gt; 0\" style=\"width:90%%;overflow: scroll;\"").replace("<div once-if=\"bi.tables.length &gt; 0\"", "<div once-if=\"bi.tables.length &gt; 0\" style=\"width:90%%;overflow: scroll;\"").replace("<div once-if=\"item.math\"", "<div once-if=\"item.math\" style=\"width:90%%;overflow: scroll;\"").replace("ng-click=\"loadPartial('openPane')\"", "onclick=\"window.location='image://'+this.getElementsByTagName('img')[0].getAttribute('eid');\"").replace("javascript:app.eidLink", "eid://").replace("<div class=\"inline-image figure\"", "<div class=\"inline-image figure\" style=\"width:90%%;overflow: scroll;\"").replace("\u200dng-click=\"loadPartial('openPane')\"", "onclick=\"window.location='image://'+this.getElementsByTagName('img')[0].getAttribute('eid');\"").replace("javascript:app.eidLink", "eid://").replace("#!", "chapter:/").replace("ng-click=\"loadPartial('openPane')\"", "onclick=\"window.location='image://'+this.getElementsByTagName('img')[0].getAttribute('eid');\"").replace("javascript:app.eidLink", "eid://").replace("See additional content on Expert Consult", "");
                                int iIndexOf = strReplace2.indexOf("c-ckc-bibliography");
                                if (iIndexOf > -1) {
                                    strReplace2 = strReplace2.substring(0, iIndexOf) + strReplace2.substring(iIndexOf).replace("ng-repeat=\"item in XocsCtrl.sections\"", "ng-repeat=\"item in XocsCtrl.sections\" style=\"display:none\"");
                                }
                                strM72844y3 = strReplace + strReplace2 + strM72817d42;
                                ELSViewerFragment.this.m72812b3(bundleM71844e0.getString("_id"), strM72844y3);
                            }
                            ELSViewerFragment.this.m72826m3();
                            ELSViewerFragment eLSViewerFragment5 = ELSViewerFragment.this;
                            eLSViewerFragment5.f89563A4 = strM72844y3;
                            eLSViewerFragment5.m72170L4("LoadResource", bool);
                            ELSViewerFragment.this.m72164I4();
                            ELSViewerFragment.this.m72170L4("LoadResource", bool2);
                        }
                        String strM71753g1 = CompressHelper.m71753g1(ELSViewerFragment.this.f89566D4, "base");
                        if (iMD.m73534a()) {
                            File file = new File(strM71753g1 + "/test.html");
                            if (file.exists()) {
                                file.delete();
                            }
                            CompressHelper.m71729E2(file, ELSViewerFragment.this.f89563A4);
                        }
                        ELSViewerFragment.this.m72820h3(strM71753g1);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        ELSViewerFragment.this.f89595p4 = e2.getLocalizedMessage();
                    }
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Elsevier.ELSViewerActivity.ELSViewerFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    String str = ELSViewerFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        ELSViewerFragment eLSViewerFragment = ELSViewerFragment.this;
                        eLSViewerFragment.m72780C4(eLSViewerFragment.f89595p4);
                        return;
                    }
                    ELSViewerFragment.this.m72836s4();
                    String strM71753g1 = CompressHelper.m71753g1(ELSViewerFragment.this.f89566D4, "base");
                    ELSViewerFragment eLSViewerFragment2 = ELSViewerFragment.this;
                    eLSViewerFragment2.m72795O3(eLSViewerFragment2.f89563A4, strM71753g1);
                    ELSViewerFragment.this.m72831p4();
                    ELSViewerFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                    ELSViewerFragment.this.m15358o2(false);
                    ELSViewerFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: Z3 */
        public void mo71956Z3(WebView webView, String str) {
            super.mo71956Z3(webView, str);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_gallery) {
                m72168N4("soheilvb");
            }
            if (itemId == C5562R.id.action_menu) {
                ELSSectionsViewer eLSSectionsViewer = new ELSSectionsViewer();
                Bundle bundle = new Bundle();
                bundle.putBundle("db", this.f89566D4);
                bundle.putString("docId", this.f89567E4);
                bundle.putString("parentId", "0");
                eLSSectionsViewer.m15342i2(bundle);
                eLSSectionsViewer.mo15218Z2(true);
                eLSSectionsViewer.m15245A2(this, 0);
                eLSSectionsViewer.mo15222e3(m15305V(), "ELSSectionsViewer");
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            menu.findItem(C5562R.id.action_gallery).setVisible(mo72169D3());
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
            String str4;
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            if (str2.equals("image")) {
                m72168N4(str3.substring(2));
                return true;
            }
            if (!str2.equals("chapter")) {
                if (!str2.equals(Annotation.f68285k3) && (!str2.equals("http") || !str3.contains("localhost:"))) {
                    return true;
                }
                String[] strArrSplit = str3.split("/");
                mo71967C3(strArrSplit[strArrSplit.length - 1]);
                return true;
            }
            CompressHelper compressHelper = new CompressHelper(m15366r());
            String[] strArrSplit2 = str3.split("/");
            String str5 = strArrSplit2[strArrSplit2.length - 1];
            if (str5.contains("?scroll")) {
                str4 = StringUtils.splitByWholeSeparator(str5, "scrollTo=#")[1];
                str5 = StringUtils.splitByWholeSeparator(str5, "?")[0];
            } else {
                str4 = null;
            }
            Bundle bundleM71844e0 = compressHelper.m71844e0(this.f89566D4, "Select id from docs where eid='" + str5 + "'");
            if (bundleM71844e0 == null) {
                CompressHelper.m71767x2(m15366r(), "No Document Found", 1);
                return true;
            }
            compressHelper.m71772A1(this.f89566D4, bundleM71844e0.getString("id"), null, str4);
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new ELSViewerFragment(), bundle);
    }
}

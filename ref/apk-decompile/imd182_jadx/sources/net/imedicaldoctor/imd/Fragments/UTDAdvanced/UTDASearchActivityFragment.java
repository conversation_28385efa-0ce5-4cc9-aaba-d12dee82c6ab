package net.imedicaldoctor.imd.Fragments.UTDAdvanced;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersDecoration;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersSectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class UTDASearchActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    private StickyRecyclerHeadersDecoration f89134A4;

    /* renamed from: B4 */
    public SpellSearchAdapter f89135B4;

    /* renamed from: C4 */
    public String f89136C4;

    /* renamed from: D4 */
    public TabLayout f89137D4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list_tab, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f89137D4 = (TabLayout) this.f88797q4.findViewById(C5562R.id.tabs);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(true, false);
        relativeLayout.setVisibility(0);
        this.f89136C4 = null;
        this.f88794n4 = this.f88791k4.m71819W(this.f88788h4, "SELECT * FROM toc order by section asc", "pathways.db");
        this.f89137D4.setVisibility(0);
        String[] strArr = {"Pathways", "Lab Interpretation"};
        for (int i2 = 0; i2 < 2; i2++) {
            TabLayout.Tab tabM40228I = this.f89137D4.m40228I();
            tabM40228I.m40276D(strArr[i2]);
            this.f89137D4.m40248i(tabM40228I);
        }
        this.f89137D4.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDASearchActivityFragment.1
            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: a */
            public void mo40255a(TabLayout.Tab tab) {
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: b */
            public void mo40256b(TabLayout.Tab tab) {
                UTDASearchActivityFragment uTDASearchActivityFragment;
                CompressHelper compressHelper;
                Bundle bundle2;
                String str;
                String str2;
                if (tab.m40284k() == 0) {
                    uTDASearchActivityFragment = UTDASearchActivityFragment.this;
                    compressHelper = uTDASearchActivityFragment.f88791k4;
                    bundle2 = uTDASearchActivityFragment.f88788h4;
                    str = "SELECT * FROM TOC order by section asc";
                    str2 = "pathways.db";
                } else {
                    uTDASearchActivityFragment = UTDASearchActivityFragment.this;
                    compressHelper = uTDASearchActivityFragment.f88791k4;
                    bundle2 = uTDASearchActivityFragment.f88788h4;
                    str = "SELECT * FROM TOC";
                    str2 = "lab.db";
                }
                uTDASearchActivityFragment.f88794n4 = compressHelper.m71819W(bundle2, str, str2);
                if (UTDASearchActivityFragment.this.f88799s4.getQuery().toString().length() > 0) {
                    UTDASearchActivityFragment uTDASearchActivityFragment2 = UTDASearchActivityFragment.this;
                    uTDASearchActivityFragment2.f88795o4 = uTDASearchActivityFragment2.mo71950a3(uTDASearchActivityFragment2.f88799s4.getQuery().toString());
                    UTDASearchActivityFragment.this.mo71973X2();
                } else {
                    UTDASearchActivityFragment uTDASearchActivityFragment3 = UTDASearchActivityFragment.this;
                    ((ChaptersSectionAdapter) uTDASearchActivityFragment3.f88792l4).m73468g0(uTDASearchActivityFragment3.f88794n4);
                    UTDASearchActivityFragment uTDASearchActivityFragment4 = UTDASearchActivityFragment.this;
                    uTDASearchActivityFragment4.f88803w4.setAdapter(uTDASearchActivityFragment4.f88792l4);
                }
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: c */
            public void mo40257c(TabLayout.Tab tab) {
            }
        });
        this.f88792l4 = new ChaptersSectionAdapter(m15366r(), this.f88794n4, "title", HTML.Tag.f74369V) { // from class: net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDASearchActivityFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersSectionAdapter
            /* renamed from: f0 */
            public void mo72538f0(Bundle bundle2, int i3) {
                CompressHelper compressHelper;
                Bundle bundle3;
                StringBuilder sb;
                String str;
                UTDASearchActivityFragment.this.m72468V2();
                if (bundle2.getString("leaf").equals(IcyHeaders.f28171a3)) {
                    if (UTDASearchActivityFragment.this.f89137D4.getSelectedTabPosition() == 0) {
                        compressHelper = new CompressHelper(UTDASearchActivityFragment.this.m15366r());
                        bundle3 = UTDASearchActivityFragment.this.f88788h4;
                        sb = new StringBuilder();
                        str = "pathway-";
                    } else {
                        compressHelper = new CompressHelper(UTDASearchActivityFragment.this.m15366r());
                        bundle3 = UTDASearchActivityFragment.this.f88788h4;
                        sb = new StringBuilder();
                        str = "lab-";
                    }
                    sb.append(str);
                    sb.append(bundle2.getString("id"));
                    compressHelper.m71772A1(bundle3, sb.toString(), null, null);
                }
            }
        };
        this.f89135B4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDASearchActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i3) {
                TextView textView;
                int i4;
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                if (bundle2.getString(Annotation.f68283i3).equals(bundle2.getString("text")) || bundle2.getString(Annotation.f68283i3).length() == 0) {
                    textView = rippleTextFullViewHolder.f101500J;
                    i4 = 8;
                } else {
                    textView = rippleTextFullViewHolder.f101500J;
                    i4 = 0;
                }
                textView.setVisibility(i4);
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDASearchActivityFragment.3.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CompressHelper compressHelper;
                        Bundle bundle3;
                        StringBuilder sb;
                        String str;
                        UTDASearchActivityFragment.this.m72468V2();
                        if (bundle2.getString("typeText").equals("Pathway")) {
                            UTDASearchActivityFragment uTDASearchActivityFragment = UTDASearchActivityFragment.this;
                            compressHelper = uTDASearchActivityFragment.f88791k4;
                            bundle3 = uTDASearchActivityFragment.f88788h4;
                            sb = new StringBuilder();
                            str = "pathway-";
                        } else {
                            UTDASearchActivityFragment uTDASearchActivityFragment2 = UTDASearchActivityFragment.this;
                            compressHelper = uTDASearchActivityFragment2.f88791k4;
                            bundle3 = uTDASearchActivityFragment2.f88788h4;
                            sb = new StringBuilder();
                            str = "lab-";
                        }
                        sb.append(str);
                        sb.append(bundle2.getString("contentId"));
                        compressHelper.m71772A1(bundle3, sb.toString(), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                UTDASearchActivityFragment.this.m72468V2();
                UTDASearchActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        StickyRecyclerHeadersDecoration stickyRecyclerHeadersDecoration = new StickyRecyclerHeadersDecoration((StickyRecyclerHeadersAdapter) this.f88792l4);
        this.f89134A4 = stickyRecyclerHeadersDecoration;
        this.f88803w4.m27459p(stickyRecyclerHeadersDecoration);
        this.f88803w4.setLayoutManager(new LinearLayoutManager(m15366r()));
        this.f88803w4.setItemAnimator(new DefaultItemAnimator());
        this.f88803w4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88792l4.m27502Z(new RecyclerView.AdapterDataObserver() { // from class: net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDASearchActivityFragment.4
            @Override // androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
            /* renamed from: a */
            public void mo27287a() {
                UTDASearchActivityFragment.this.f89134A4.m58207n();
            }
        });
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f89135B4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f89135B4);
        this.f89134A4.m58207n();
        this.f88803w4.m27380A1(this.f89134A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: Z2 */
    public void mo72211Z2() {
        this.f88803w4.m27459p(this.f89134A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        CompressHelper compressHelper;
        Bundle bundle;
        StringBuilder sb;
        String str2;
        if (this.f89137D4.getSelectedTabPosition() == 0) {
            compressHelper = this.f88791k4;
            bundle = this.f88788h4;
            sb = new StringBuilder();
            sb.append("Select * from search where search match '(text:");
            sb.append(str);
            sb.append("* OR content:");
            sb.append(str);
            str2 = "*) AND type:1 AND typeText:Pathway'";
        } else {
            compressHelper = this.f88791k4;
            bundle = this.f88788h4;
            sb = new StringBuilder();
            sb.append("Select * from search where search match '(text:");
            sb.append(str);
            sb.append("* OR content:");
            sb.append(str);
            str2 = "*) AND type:1 AND typeText:Lab'";
        }
        sb.append(str2);
        return compressHelper.m71819W(bundle, sb.toString(), "search.db");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71819W(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'", "search.db");
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.android.material.tabs.TabLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersDecoration;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.StickySectionAdapter;

/* loaded from: classes3.dex */
public class EPOInteractSingleActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f88101X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88102Y4;

    /* renamed from: Z4 */
    public TabLayout f88103Z4;

    /* renamed from: a5 */
    public String f88104a5;

    /* renamed from: b5 */
    public String f88105b5;

    /* renamed from: c5 */
    public String f88106c5;

    /* renamed from: d5 */
    public String f88107d5;

    /* renamed from: e5 */
    private StickyRecyclerHeadersDecoration f88108e5;

    /* renamed from: f5 */
    public OverviewAdapter f88109f5;

    /* renamed from: g5 */
    public StickySectionAdapter f88110g5;

    /* renamed from: h5 */
    public StickySectionAdapter f88111h5;

    public class OverviewAdapter extends RecyclerView.Adapter {
        public OverviewAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            ((TextViewHolder) viewHolder).f88116I.setText(Html.fromHtml(EPOInteractSingleActivityFragment.this.f88107d5));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            View viewInflate = LayoutInflater.from(EPOInteractSingleActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_text, viewGroup, false);
            EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment = EPOInteractSingleActivityFragment.this;
            return ePOInteractSingleActivityFragment.new TextViewHolder(ePOInteractSingleActivityFragment.m15366r(), viewInflate);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return 1;
        }
    }

    public class TextViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f88116I;

        public TextViewHolder(Context context, View view) {
            super(view);
            this.f88116I = (TextView) view.findViewById(C5562R.id.text);
            this.f88116I.setTypeface(Typeface.createFromAsset(context.getAssets(), "fonts/HelveticaNeue-Light.otf"));
        }
    }

    /* renamed from: K4 */
    public void m72205K4() {
        this.f88101X4.setItemAnimator(new DefaultItemAnimator());
        this.f88101X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88101X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String string;
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_epointeract_single, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88101X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        this.f88106c5 = "RX.sqlite";
        this.f88103Z4 = (TabLayout) this.f89565C4.findViewById(C5562R.id.tabs);
        String[] strArr = {"Overview", "By Category", "By Drug"};
        for (int i2 = 0; i2 < 3; i2++) {
            TabLayout.Tab tabM40228I = this.f88103Z4.m40228I();
            tabM40228I.m40276D(strArr[i2]);
            this.f88103Z4.m40248i(tabM40228I);
        }
        this.f88103Z4.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractSingleActivityFragment.1
            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: a */
            public void mo40255a(TabLayout.Tab tab) {
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: b */
            public void mo40256b(TabLayout.Tab tab) {
                RecyclerView recyclerView;
                RecyclerView.Adapter adapter;
                int selectedTabPosition = EPOInteractSingleActivityFragment.this.f88103Z4.getSelectedTabPosition();
                if (selectedTabPosition == 0) {
                    if (EPOInteractSingleActivityFragment.this.f88108e5 != null) {
                        EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment = EPOInteractSingleActivityFragment.this;
                        ePOInteractSingleActivityFragment.f88101X4.m27380A1(ePOInteractSingleActivityFragment.f88108e5);
                        EPOInteractSingleActivityFragment.this.f88108e5.m58207n();
                        EPOInteractSingleActivityFragment.this.f88108e5 = null;
                    }
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment2 = EPOInteractSingleActivityFragment.this;
                    recyclerView = ePOInteractSingleActivityFragment2.f88101X4;
                    adapter = ePOInteractSingleActivityFragment2.f88109f5;
                } else if (selectedTabPosition == 1) {
                    if (EPOInteractSingleActivityFragment.this.f88108e5 != null) {
                        EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment3 = EPOInteractSingleActivityFragment.this;
                        ePOInteractSingleActivityFragment3.f88101X4.m27380A1(ePOInteractSingleActivityFragment3.f88108e5);
                        EPOInteractSingleActivityFragment.this.f88108e5.m58207n();
                        EPOInteractSingleActivityFragment.this.f88108e5 = null;
                    }
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment4 = EPOInteractSingleActivityFragment.this;
                    ePOInteractSingleActivityFragment4.f88108e5 = new StickyRecyclerHeadersDecoration(ePOInteractSingleActivityFragment4.f88110g5);
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment5 = EPOInteractSingleActivityFragment.this;
                    ePOInteractSingleActivityFragment5.f88101X4.m27459p(ePOInteractSingleActivityFragment5.f88108e5);
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment6 = EPOInteractSingleActivityFragment.this;
                    recyclerView = ePOInteractSingleActivityFragment6.f88101X4;
                    adapter = ePOInteractSingleActivityFragment6.f88110g5;
                } else {
                    if (selectedTabPosition != 2) {
                        return;
                    }
                    if (EPOInteractSingleActivityFragment.this.f88108e5 != null) {
                        EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment7 = EPOInteractSingleActivityFragment.this;
                        ePOInteractSingleActivityFragment7.f88101X4.m27380A1(ePOInteractSingleActivityFragment7.f88108e5);
                        EPOInteractSingleActivityFragment.this.f88108e5.m58207n();
                        EPOInteractSingleActivityFragment.this.f88108e5 = null;
                    }
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment8 = EPOInteractSingleActivityFragment.this;
                    ePOInteractSingleActivityFragment8.f88108e5 = new StickyRecyclerHeadersDecoration(ePOInteractSingleActivityFragment8.f88111h5);
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment9 = EPOInteractSingleActivityFragment.this;
                    ePOInteractSingleActivityFragment9.f88101X4.m27459p(ePOInteractSingleActivityFragment9.f88108e5);
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment10 = EPOInteractSingleActivityFragment.this;
                    recyclerView = ePOInteractSingleActivityFragment10.f88101X4;
                    adapter = ePOInteractSingleActivityFragment10.f88111h5;
                }
                recyclerView.setAdapter(adapter);
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: c */
            public void mo40257c(TabLayout.Tab tab) {
            }
        });
        String str = this.f89567E4.split("-")[1];
        this.f88104a5 = str;
        ArrayList<Bundle> arrayListM71822X = this.f89579Q4.m71822X(this.f89566D4, "Select * from drug where ID=" + str, this.f88106c5, true);
        if (arrayListM71822X == null || arrayListM71822X.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "Sorry, Drug can't be found", 1);
        } else {
            Bundle bundle2 = arrayListM71822X.get(0);
            this.f88102Y4 = this.f89579Q4.m71822X(this.f89566D4, "Select * from pill_pictures where drug_id=" + str, this.f88106c5, true);
            String str2 = "NAME";
            if (bundle2.getString("GENERIC_ID").length() == 0 || bundle2.getString("GENERIC_ID").equals("0")) {
                string = bundle2.getString("ID");
                bundle2.getString("NAME");
            } else {
                string = bundle2.getString("GENERIC_ID");
                try {
                    CompressHelper compressHelper = this.f89579Q4;
                    compressHelper.m71890s1(compressHelper.m71822X(this.f89566D4, "SELECT  DRUG.ID ,  DRUG.CLINICAL_ID ,  DRUG.GENERIC_ID ,  DRUG.NAME ,  DRUG.DRUG_TYPE ,  DRUG.ACTIVE ,  DRUG.ADULT_DSG_ID ,  DRUG.PEDS_DSG_ID ,  DRUG.MFR_STRING_ID ,  DRUG.BBW_ID   FROM DRUG   WHERE  ID =  " + str, this.f88106c5, true)).getString("NAME");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                }
            }
            this.f89568F4 = bundle2.getString("NAME");
            this.f88105b5 = string;
            CompressHelper compressHelper2 = this.f89579Q4;
            Bundle bundleM71890s1 = compressHelper2.m71890s1(compressHelper2.m71819W(this.f89566D4, "SELECT  DDI_GROUP.ID ,  DDI_GROUP.NAME ,  DDI_GROUP.PHARMACOLOGIC_CLASS_ID                      FROM DDI_GROUP                     JOIN DRUG_TO_DDI_GROUP ON DDI_GROUP.ID = DRUG_TO_DDI_GROUP.GROUP_ID                     WHERE DRUG_TO_DDI_GROUP.DRUG_ID=" + string, this.f88106c5));
            String string2 = bundleM71890s1.getString("ID");
            String string3 = bundleM71890s1.getString("NAME");
            ArrayList<Bundle> arrayListM71822X2 = this.f89579Q4.m71822X(this.f89566D4, "SELECT  CHARACTERISTIC.ID ,  CHARACTERISTIC.NAME                             FROM INGREDIENT_CHARACTERISTIC                            JOIN CHARACTERISTIC ON INGREDIENT_CHARACTERISTIC.CHARACTERISTIC_ID = CHARACTERISTIC.ID                            WHERE INGREDIENT_CHARACTERISTIC.INGREDIENT_ID = " + string2 + "                            ORDER BY INGREDIENT_CHARACTERISTIC.DISPLAY_ORDER", this.f88106c5, true);
            String str3 = "<div><font color=\"#009900\"><b>" + string3 + "</b></font></div>Interaction Characteristics : <br/>";
            Iterator<Bundle> it2 = arrayListM71822X2.iterator();
            while (it2.hasNext()) {
                str3 = str3 + "<br/>   • " + it2.next().getString("NAME");
            }
            this.f88107d5 = str3;
            if (arrayListM71822X2.size() == 0) {
                this.f88107d5 = "<b>No Information<b>";
            }
            this.f88109f5 = new OverviewAdapter();
            this.f88110g5 = new StickySectionAdapter(m15366r(), this.f89579Q4.m71887r2(this.f89579Q4.m71822X(this.f89566D4, "SELECT                     DDI.ID AS DDI_ID,                    DRUG_TO_DDI_GROUP.GROUP_ID AS GROUP_0_ID,                    CASE WHEN DDI.GROUP_0_ID = DRUG_TO_DDI_GROUP.GROUP_ID THEN DDI.GROUP_1_ID ELSE DDI.GROUP_0_ID END AS GROUP_1_ID,                    DDI_GROUP.NAME AS GROUP_1_NAME,                    DDI.CATEGORY_ID,                    DDI.ACTION_STRING_ID,                    DDI.EFFECT_STRING_ID,                    DDI.MECHANISM_STRING_ID,                    (select name from ddi_category where ddi_category.id=ddi.category_id) as CATEGORY,                     (select name from ddi_group where ddi_group.id=DRUG_TO_DDI_GROUP.GROUP_ID) as GROUP_0_NAME,                     (select string from general_string where general_string.id=ddi.action_string_id) as ACTION_STRING,                     (select string from general_string where general_string.id=ddi.effect_string_id) as EFFECT_STRING,                     (select string from general_string where general_string.id=ddi.mechanism_string_id) as MECHANISM_STRING                     FROM DRUG                     JOIN DRUG_TO_DDI_GROUP ON DRUG_TO_DDI_GROUP.DRUG_ID = DRUG.ID                     JOIN DDI ON DDI.GROUP_0_ID = DRUG_TO_DDI_GROUP.GROUP_ID OR DDI.GROUP_1_ID = DRUG_TO_DDI_GROUP.GROUP_ID                     JOIN DDI_GROUP ON DDI_GROUP.ID = CASE WHEN DDI.GROUP_0_ID = DRUG_TO_DDI_GROUP.GROUP_ID THEN DDI.GROUP_1_ID ELSE DDI.GROUP_0_ID END                    WHERE DRUG.ID = " + string + "                    ORDER BY CATEGORY_ID, GROUP_1_NAME, GROUP_0_NAME", this.f88106c5, true), "CATEGORY_ID"), "GROUP_1_NAME") { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractSingleActivityFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.StickySectionAdapter
                /* renamed from: h0 */
                public void mo72190h0(Bundle bundle3, int i3) {
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment = EPOInteractSingleActivityFragment.this;
                    ePOInteractSingleActivityFragment.f89579Q4.m71772A1(ePOInteractSingleActivityFragment.f89566D4, "interactview-" + bundle3.getString("DDI_ID"), null, null);
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.StickySectionAdapter
                /* renamed from: j0 */
                public String mo72206j0(String str4) {
                    return str4.equals("5") ? "Caution Advised" : str4.equals("4") ? "Therapeutic Advantage" : str4.equals(ExifInterface.f16326Z4) ? "Monitor / Modify Tx" : str4.equals(ExifInterface.f16317Y4) ? "Avoid / Use Alternative" : str4.equals(IcyHeaders.f28171a3) ? "Contraindicated" : str4;
                }
            };
            this.f88111h5 = new StickySectionAdapter(m15366r(), this.f89579Q4.m71891s2(this.f89579Q4.m71822X(this.f89566D4, "SELECT DISTINCT                            DRUG.ID,                           DRUG.CLINICAL_ID,                           DRUG.GENERIC_ID,                           DRUG.NAME,                           DRUG.DRUG_TYPE,                           DRUG.ACTIVE,                           DRUG.ADULT_DSG_ID,                           DRUG.PEDS_DSG_ID,                           DRUG.MFR_STRING_ID,                           DRUG.BBW_ID                           FROM DRUG_TO_INTERACTING_DRUG                           JOIN DRUG ON (                            DRUG.ID = CASE WHEN DRUG_TO_INTERACTING_DRUG.DRUG_0_ID = " + string + " THEN DRUG_TO_INTERACTING_DRUG.DRUG_1_ID ELSE DRUG_TO_INTERACTING_DRUG.DRUG_0_ID END  or                            DRUG.GENERIC_ID = CASE WHEN DRUG_TO_INTERACTING_DRUG.DRUG_0_ID = " + string + " THEN DRUG_TO_INTERACTING_DRUG.DRUG_1_ID ELSE DRUG_TO_INTERACTING_DRUG.DRUG_0_ID END                            )                            WHERE                            DRUG_TO_INTERACTING_DRUG.DRUG_1_ID = " + string + " OR                           DRUG_TO_INTERACTING_DRUG.DRUG_0_ID = " + string + "                           ORDER BY DRUG.NAME", this.f88106c5, true), "NAME"), str2) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractSingleActivityFragment.3
                @Override // net.imedicaldoctor.imd.ViewHolders.StickySectionAdapter
                /* renamed from: h0 */
                public void mo72190h0(Bundle bundle3, int i3) {
                    String string4 = bundle3.getString("ID");
                    if (bundle3.getString("GENERIC_ID").length() > 0 && !bundle3.getString("GENERIC_ID").equals("0")) {
                        string4 = bundle3.getString("GENERIC_ID");
                    }
                    EPOInteractSingleActivityFragment ePOInteractSingleActivityFragment = EPOInteractSingleActivityFragment.this;
                    ePOInteractSingleActivityFragment.f89579Q4.m71772A1(ePOInteractSingleActivityFragment.f89566D4, "interactview-" + EPOInteractSingleActivityFragment.this.f88105b5 + "-" + string4, null, null);
                }
            };
            this.f88101X4.setAdapter(this.f88109f5);
            m72205K4();
            mo72642f3(C5562R.menu.favorite);
            m15358o2(false);
            m72786G3();
        }
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3;
        ArrayList<Bundle> arrayList = this.f88102Y4;
        if (arrayList == null || arrayList.size() == 0 || (bundleM72839v3 = m72839v3(this.f88102Y4)) == null) {
            return;
        }
        Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
    }
}

package net.imedicaldoctor.imd.Fragments.Lexi;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.net.HttpHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Lexi.LXItems;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class LXViewer extends ViewerHelperActivity {

    public static class LXViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        public ArrayList<String> f88347X4;

        /* renamed from: Y4 */
        public Bundle f88348Y4;

        /* renamed from: Z4 */
        public String f88349Z4;

        /* renamed from: a5 */
        public String f88350a5;

        /* renamed from: b5 */
        public boolean f88351b5;

        /* renamed from: c5 */
        private String f88352c5;

        /* renamed from: d5 */
        private Bundle f88353d5;

        /* renamed from: e5 */
        private ArrayList<Bundle> f88354e5;

        /* renamed from: f5 */
        private MenuItem f88355f5;

        /* renamed from: g5 */
        private int f88356g5;

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: I4 */
        public void m72302I4() throws IOException {
            m72821j3("ivc_compatible.png");
            m72821j3("ivc_conflict.png");
            m72821j3("ivc_incompatible.png");
            m72821j3("ivc_no_info.png");
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: J4 */
        public String m72303J4(ArrayList<Bundle> arrayList, ArrayList<Bundle> arrayList2, String str) {
            StringBuilder sb;
            String str2;
            StringBuilder sb2;
            String str3;
            String str4 = "<div><div class=\"ivc_table_label\">" + str + "</div><table class=\"ivc_compatibility\"><tbody>";
            int i2 = 0;
            while (i2 < ((arrayList.size() - 1) / 6) + 1) {
                if (i2 != 0) {
                    str4 = str4 + "<tr><td class=\"ivc_table_spacer\" colspan=\"6\"></td></tr>";
                }
                String string = str4 + "<tr>";
                int i3 = i2 + 1;
                int size = i3 * 6;
                if (arrayList.size() < size) {
                    size = arrayList.size();
                }
                int i4 = i2 * 6;
                for (int i5 = i4; i5 < size; i5++) {
                    if (i5 % 2 == 1) {
                        sb2 = new StringBuilder();
                        sb2.append(string);
                        str3 = "<td class=\"ivc_compatibility_header ivc_compatibility_even_col\">";
                    } else {
                        sb2 = new StringBuilder();
                        sb2.append(string);
                        str3 = "<td class=\"ivc_compatibility_header ivc_compatibility_odd_col\">";
                    }
                    sb2.append(str3);
                    sb2.append(arrayList.get(i5).getString("name"));
                    sb2.append("</td>");
                    string = sb2.toString();
                }
                String string2 = (string + "</tr>") + "<tr>";
                while (i4 < size) {
                    Bundle bundleM71759q1 = CompressHelper.m71759q1(arrayList2, "name", arrayList.get(i4).getString("name"));
                    String str5 = "ivc_no_info.png";
                    if (bundleM71759q1 != null && !bundleM71759q1.getString(Annotation.f68283i3).equals("6")) {
                        str5 = bundleM71759q1.getString(Annotation.f68283i3).equals("5") ? "ivc_compatible.png" : bundleM71759q1.getString(Annotation.f68283i3).equals(ExifInterface.f16326Z4) ? "ivc_conflict.png" : bundleM71759q1.getString(Annotation.f68283i3).equals(IcyHeaders.f28171a3) ? "ivc_incompatible.png" : "";
                    }
                    if (i4 % 2 == 1) {
                        sb = new StringBuilder();
                        sb.append(string2);
                        str2 = "<td class=\"ivc_compatibility_content ivc_compatibility_even_col\"><img src=\"";
                    } else {
                        sb = new StringBuilder();
                        sb.append(string2);
                        str2 = "<td class=\"ivc_compatibility_content ivc_compatibility_even_odd\"><img src=\"";
                    }
                    sb.append(str2);
                    sb.append(str5);
                    sb.append("\" width=\"25px\" height=\"25px\"></td>");
                    string2 = sb.toString();
                    i4++;
                }
                str4 = string2 + "</tr>";
                i2 = i3;
            }
            return str4 + "</tbody></table></div>";
        }

        /* renamed from: K4 */
        private void m72304K4() {
        }

        /* renamed from: T4 */
        private String m72313T4() {
            Bundle bundle = this.f88353d5;
            if (bundle == null) {
                return "alaki";
            }
            if (!bundle.containsKey("globalid")) {
                return "adsf";
            }
            return CompressHelper.m71754h1(this.f89566D4, this.f88353d5.getString("globalid") + ".mp3", "sound");
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: U4 */
        public void m72314U4() {
            MenuItem menuItemFindItem;
            Menu menu = this.f89598s4;
            if (menu == null || (menuItemFindItem = menu.findItem(C5562R.id.action_sound)) == null) {
                return;
            }
            menuItemFindItem.setVisible(new File(m72313T4()).exists());
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: V4 */
        public String m72315V4(String str, String str2, String str3, String str4, String str5) {
            return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + str5 + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: W4 */
        public String m72316W4(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8) {
            return "<a name=\"f" + str8 + "\"><div id=\"h" + str8 + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + str8 + ");toggleHeaderExpanded(h" + str8 + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + str8 + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
        }

        /* renamed from: Y4 */
        private void m72317Y4(String str) {
            ArrayList<String> arrayList = this.f88347X4;
            if (arrayList == null || arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
                return;
            }
            ArrayList arrayList2 = new ArrayList();
            Iterator<String> it2 = this.f88347X4.iterator();
            while (it2.hasNext()) {
                String next = it2.next();
                Bundle bundle = new Bundle();
                bundle.putString("ImagePath", next);
                try {
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(next, "/");
                    String str2 = strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1];
                    bundle.putString("Description", this.f88348Y4.containsKey(str2) ? this.f88348Y4.getString(str2) : "");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
                bundle.putString("id", next);
                if (new File(next).length() > 5000) {
                    arrayList2.add(bundle);
                }
            }
            int i2 = 0;
            for (int i3 = 0; i3 < arrayList2.size(); i3++) {
                if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                    i2 = i3;
                }
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList2);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: C3 */
        public void mo71967C3(String str) {
            iMDLogger.m73554j("Viewer activity , Gotosection", str);
            this.f89569G4.m73433g("document.getElementById(\"" + str + "\").scrollIntoView(true);document.body.scrollTop = window.pageYOffset - 50;");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_lxviewer, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (m15387y().containsKey("Mode")) {
                this.f88356g5 = m15387y().getInt("Mode");
            } else {
                this.f88356g5 = 0;
            }
            if (bundle != null) {
                this.f88352c5 = bundle.getString("mResources");
                this.f88356g5 = bundle.getInt("Mode");
                this.f88353d5 = bundle.getBundle("mDocument");
                this.f88354e5 = bundle.getParcelableArrayList("mFields");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            iMDLogger.m73554j("LXViewer", "Loading Lexi Document with mDocAddress = " + this.f89567E4);
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Lexi.LXViewer.LXViewerFragment.1
                /* JADX WARN: Removed duplicated region for block: B:180:0x0943 A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                /* JADX WARN: Removed duplicated region for block: B:181:0x095d  */
                /* JADX WARN: Removed duplicated region for block: B:185:0x0968 A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                /* JADX WARN: Removed duplicated region for block: B:197:0x0a97 A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                /* JADX WARN: Removed duplicated region for block: B:229:0x0d3e A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                /* JADX WARN: Removed duplicated region for block: B:230:0x0d5a  */
                /* JADX WARN: Removed duplicated region for block: B:234:0x0d65 A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                /* JADX WARN: Removed duplicated region for block: B:247:0x0e75 A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                /* JADX WARN: Removed duplicated region for block: B:45:0x02cf A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                /* JADX WARN: Removed duplicated region for block: B:94:0x05d8 A[Catch: Exception -> 0x0020, TryCatch #1 {Exception -> 0x0020, blocks: (B:3:0x0004, B:5:0x0015, B:245:0x0e6a, B:247:0x0e75, B:248:0x0e7c, B:11:0x0024, B:14:0x0052, B:16:0x0079, B:18:0x0080, B:21:0x00f2, B:23:0x0106, B:25:0x0181, B:27:0x0195, B:28:0x01dc, B:30:0x01e2, B:42:0x02a8, B:43:0x02c9, B:45:0x02cf, B:47:0x02f5, B:48:0x031d, B:50:0x0379, B:41:0x0287, B:51:0x0396, B:53:0x03ae, B:54:0x0415, B:56:0x041b, B:59:0x0442, B:62:0x0450, B:64:0x0462, B:65:0x047c, B:74:0x04bc, B:66:0x0480, B:68:0x048a, B:71:0x0495, B:72:0x049a, B:73:0x04b5, B:75:0x04c4, B:78:0x04d0, B:80:0x04f8, B:82:0x0518, B:91:0x05be, B:94:0x05d8, B:96:0x0603, B:98:0x0609, B:100:0x0634, B:102:0x063a, B:104:0x0644, B:108:0x064f, B:112:0x0658, B:114:0x065c, B:115:0x0660, B:116:0x067d, B:148:0x07c2, B:150:0x07de, B:117:0x0683, B:119:0x068b, B:120:0x06a8, B:122:0x06b0, B:124:0x06f4, B:125:0x070f, B:126:0x0732, B:127:0x0738, B:129:0x0740, B:146:0x077b, B:133:0x074d, B:136:0x0758, B:139:0x0763, B:142:0x076e, B:147:0x079d, B:151:0x07ef, B:152:0x0813, B:90:0x059c, B:153:0x0817, B:156:0x082e, B:158:0x0862, B:161:0x0871, B:163:0x08b3, B:164:0x08b8, B:165:0x08c3, B:167:0x08c9, B:178:0x092c, B:180:0x0943, B:182:0x095e, B:183:0x0962, B:185:0x0968, B:187:0x0988, B:188:0x09bd, B:193:0x0a2d, B:189:0x09c2, B:191:0x09ce, B:192:0x0a09, B:194:0x0a47, B:195:0x0a91, B:197:0x0a97, B:200:0x0ae3, B:201:0x0aeb, B:203:0x0af1, B:204:0x0b2d, B:207:0x0b49, B:209:0x0b5f, B:210:0x0b6b, B:211:0x0bce, B:177:0x090c, B:212:0x0c38, B:214:0x0c52, B:215:0x0cca, B:217:0x0cd0, B:227:0x0d29, B:229:0x0d3e, B:231:0x0d5b, B:232:0x0d5f, B:234:0x0d65, B:236:0x0d85, B:237:0x0dba, B:242:0x0e2b, B:238:0x0dc0, B:240:0x0dcc, B:241:0x0e07, B:243:0x0e41, B:226:0x0d09), top: B:254:0x0004 }] */
                @Override // java.lang.Runnable
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public void run() {
                    /*
                        Method dump skipped, instructions count: 3776
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Lexi.LXViewer.LXViewerFragment.RunnableC48841.run():void");
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Lexi.LXViewer.LXViewerFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    String str = LXViewerFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        LXViewerFragment lXViewerFragment = LXViewerFragment.this;
                        lXViewerFragment.m72780C4(lXViewerFragment.f89595p4);
                        return;
                    }
                    LXViewerFragment.this.f89569G4.clearCache(true);
                    LXViewerFragment lXViewerFragment2 = LXViewerFragment.this;
                    lXViewerFragment2.m72795O3(lXViewerFragment2.f89563A4, lXViewerFragment2.f88349Z4);
                    LXViewerFragment.this.m72836s4();
                    LXViewerFragment.this.m72831p4();
                    LXViewerFragment.this.mo72642f3(C5562R.menu.menu_lxviewer);
                    LXViewerFragment.this.m15358o2(false);
                    LXViewerFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: W3 */
        public boolean mo71969W3(ConsoleMessage consoleMessage) {
            String[] strArrSplit = consoleMessage.message().split(",,,,,");
            if (strArrSplit[0].equals("images")) {
                if (strArrSplit.length < 2) {
                    return true;
                }
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
                ArrayList<String> arrayList = new ArrayList<>();
                for (String str : strArrSplitByWholeSeparator) {
                    String strReplace = this.f88349Z4.replace("file://", "");
                    if (strReplace.endsWith("/")) {
                        strReplace = strReplace.substring(0, strReplace.length() - 1);
                    }
                    String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str, "/");
                    for (String str2 : strArrSplitByWholeSeparator2) {
                        strReplace = str2.equals("..") ? m72318X4(strReplace) : strReplace + "/" + str2;
                    }
                    try {
                        if (this.f88351b5 && strArrSplitByWholeSeparator2.length > 0) {
                            String str3 = strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1];
                            CompressHelper compressHelper = this.f89579Q4;
                            Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(this.f89566D4, "Select * from images where imageName='" + str3 + "'"));
                            if (bundleM71907z != null) {
                                String string = bundleM71907z.getString("desc");
                                if (!this.f88348Y4.containsKey(str3)) {
                                    this.f88348Y4.putString(str3, string);
                                }
                            }
                        }
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                    }
                    File file = new File(strReplace);
                    file.length();
                    if (file.length() > ExoPlayer.f21773a1) {
                        arrayList.add(strReplace);
                    }
                    iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strReplace);
                }
                this.f88347X4 = arrayList;
                mo71972o4();
            }
            return super.mo71969W3(consoleMessage);
        }

        /* renamed from: X4 */
        public String m72318X4(String str) {
            ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
            arrayList.remove(arrayList.size() - 1);
            return StringUtils.join(arrayList, "/");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: Z3 */
        public void mo71956Z3(WebView webView, String str) {
            super.mo71956Z3(webView, str);
            this.f89569G4.m73433g("ConvertAllImages();");
            this.f89569G4.m73433g("fixAllImages2();");
            this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) throws IllegalStateException {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_sound && new File(m72313T4()).exists()) {
                m72813b4(m72313T4());
            }
            if (itemId == C5562R.id.action_menu) {
                LXSectionsViewer lXSectionsViewer = new LXSectionsViewer();
                Bundle bundle = new Bundle();
                bundle.putParcelableArrayList("fields", this.f88354e5);
                lXSectionsViewer.m15342i2(bundle);
                lXSectionsViewer.mo15218Z2(true);
                lXSectionsViewer.m15245A2(this, 0);
                lXSectionsViewer.mo15222e3(m15283M(), "LXSectionsViewer");
            }
            if (itemId == C5562R.id.action_gallery) {
                m72317Y4("soheilvb");
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            this.f88355f5 = menu.findItem(C5562R.id.action_sound);
            m72314U4();
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: v4 */
        public boolean mo71959v4() {
            if (this.f88356g5 != 0) {
                return false;
            }
            return super.mo71959v4();
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws UnsupportedEncodingException {
            String strSubstring;
            Bundle bundle;
            String strDecode;
            String str4;
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            CompressHelper compressHelper = new CompressHelper(m15366r());
            if (str2.equals("image")) {
                m72317Y4(str3);
                return true;
            }
            if (str2.equals("index")) {
                Bundle bundle2 = new Bundle();
                bundle2.putBundle("DB", this.f89566D4);
                Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from indexitem where id=" + str3));
                if (bundleM71890s1 == null) {
                    CompressHelper.m71767x2(m15366r(), "There is nothing there . sorry . please report <NAME_EMAIL>", 1);
                    return true;
                }
                bundle2.putString("ParentId", bundleM71890s1.getString("id"));
                bundle2.putInt("Mode", 2);
                compressHelper.m71798N(LXItems.class, LXItems.LXItemsFragment.class, bundle2);
            } else if (str2.equals("urn")) {
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, ":");
                if (strArrSplitByWholeSeparator[0].equals("lexicalc")) {
                    final String str5 = "Lexi-CALC";
                    ArrayList arrayList = new ArrayList(Collections2.m42365d(((iMD) m15366r().getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.Lexi.LXViewer.LXViewerFragment.3
                        @Override // com.google.common.base.Predicate
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public boolean apply(Bundle bundle3) {
                            return bundle3.getString("Title").equals(str5);
                        }
                    }));
                    if (arrayList.size() == 0) {
                        CompressHelper.m71767x2(m15366r(), "You Must Install Lexi-CALC", 1);
                        return true;
                    }
                    bundle = this.f89566D4;
                    strSubstring = strArrSplitByWholeSeparator[1];
                } else if (strArrSplitByWholeSeparator[0].equals("lims")) {
                    if (strArrSplitByWholeSeparator[1].charAt(0) == 's') {
                        compressHelper.m71772A1(this.f89566D4, strArrSplitByWholeSeparator[1].substring(1), null, null);
                        return true;
                    }
                    if (strArrSplitByWholeSeparator[1].charAt(0) == 'b') {
                        final String strSubstring2 = strArrSplitByWholeSeparator[1].substring(1);
                        strSubstring = strArrSplitByWholeSeparator[2].substring(1);
                        if (this.f89566D4.getString("Name").equals("429_martindale_f.sqlite") && strSubstring2.equals("429")) {
                            try {
                                strDecode = URLDecoder.decode(strSubstring, "UTF-8");
                            } catch (Exception unused) {
                                strDecode = strSubstring;
                            }
                            if (strDecode.contains("#")) {
                                str4 = StringUtils.splitByWholeSeparator(strDecode, "#")[1];
                                strDecode = StringUtils.splitByWholeSeparator(strDecode, "#")[0];
                            } else {
                                str4 = "";
                            }
                            if (this.f89567E4.equals(strDecode)) {
                                this.f89569G4.m73433g("document.getElementById('" + str4 + "').scrollIntoView(true);");
                                this.f89569G4.m73433g("element=document.getElementById('" + str4 + "');element.parentNode.removeChild(element);");
                            } else {
                                compressHelper.m71772A1(this.f89566D4, strSubstring, null, str4);
                            }
                            return true;
                        }
                        ArrayList arrayList2 = new ArrayList(Collections2.m42365d(((iMD) m15366r().getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.Lexi.LXViewer.LXViewerFragment.4
                            @Override // com.google.common.base.Predicate
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public boolean apply(Bundle bundle3) {
                                if (bundle3.containsKey("lexiID")) {
                                    return bundle3.getString("lexiID").equals(strSubstring2);
                                }
                                return false;
                            }
                        }));
                        if (arrayList2.size() == 0) {
                            CompressHelper.m71767x2(m15366r(), "You Must Install Database with id " + strSubstring2, 1);
                            return true;
                        }
                        bundle = (Bundle) arrayList2.get(0);
                    }
                }
                compressHelper.m71772A1(bundle, strSubstring, null, null);
            } else if (str2.equals("ivcpair")) {
                ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(this.f89566D4, "select vf.sequence as sequence, vf.label as label, f.content as content, vf.fieldtype_id as typeId from document d join chapter c on c.id = d.chapter_id join view v on v.id = c.view_id join viewfield vf on vf.view_id = v.id left join field f on f.document_id = d.id and vf.fieldtype_id = f.fieldtype_id left join fieldtypesite s on vf.fieldtype_id = s.fieldtype_id left join ivsolution l on d.id = l.document_id and vf.fieldtype_id = l.fieldtype_id where d.id =" + str3 + " and (f.content is not null or l.solution_id is not null) and f.fieldtype_id != 38 and f.fieldtype_id != 42 union select vf.sequence, vf.label, iv.content, vf.fieldtype_id from document d join chapter c on c.id = d.chapter_id join view v on v.id = c.view_id join viewfield vf on vf.view_id = v.id left join ivfield iv on iv.document_id = d.id and vf.fieldtype_id = iv.fieldtype_id left join fieldtypesite s on vf.fieldtype_id = s.fieldtype_id left join ivsolution l on d.id = l.document_id and vf.fieldtype_id = l.fieldtype_id where d.id =" + str3 + " and (iv.content is not null or l.solution_id is not null) and iv.fieldtype_id != 38 and iv.fieldtype_id != 42");
                Bundle bundle3 = new Bundle();
                bundle3.putParcelableArrayList("ivMonograph", arrayListM71817V);
                bundle3.putParcelableArrayList("Solutions", m15387y().getParcelableArrayList("Solutions"));
                bundle3.putParcelableArrayList("Sites", m15387y().getParcelableArrayList("Sites"));
                bundle3.putInt("Mode", 3);
                if (m15387y().containsKey(HttpHeaders.f62930g)) {
                    bundle3.putString(HttpHeaders.f62930g, m15387y().getString(HttpHeaders.f62930g));
                }
                bundle3.putString("docId", str3);
                compressHelper.m71775B1(this.f89566D4, "Pair - " + str3, null, null, bundle3);
            }
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new LXViewerFragment(), bundle);
    }
}

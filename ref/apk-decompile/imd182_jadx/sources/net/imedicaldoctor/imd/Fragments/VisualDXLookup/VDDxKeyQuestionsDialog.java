package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity;

/* loaded from: classes3.dex */
public class VDDxKeyQuestionsDialog extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f89858F4;

    /* renamed from: G4 */
    private ArrayList<String> f89859G4;

    /* renamed from: H4 */
    private ArrayList<Bundle> f89860H4;

    /* renamed from: I4 */
    private Bundle f89861I4;

    /* renamed from: J4 */
    public ArrayList<String> f89862J4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        final ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f89858F4 = m15387y().getBundle("db");
        this.f89861I4 = m15387y().getBundle("allFindings");
        this.f89859G4 = m15387y().getStringArrayList("selectedKeyQuestions");
        this.f89860H4 = m15387y().getParcelableArrayList("allKeyQuestions");
        this.f89862J4 = new ArrayList<>();
        Iterator<Bundle> it2 = this.f89860H4.iterator();
        while (it2.hasNext()) {
            this.f89862J4.add(it2.next().getString("title"));
        }
        new CompressHelper(m15366r());
        BaseAdapter baseAdapter = new BaseAdapter() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxKeyQuestionsDialog.1
            @Override // android.widget.BaseAdapter, android.widget.ListAdapter
            public boolean areAllItemsEnabled() {
                return false;
            }

            @Override // android.widget.Adapter
            public int getCount() {
                VDDxKeyQuestionsDialog vDDxKeyQuestionsDialog = VDDxKeyQuestionsDialog.this;
                return vDDxKeyQuestionsDialog.m72924l3(vDDxKeyQuestionsDialog.f89862J4);
            }

            @Override // android.widget.Adapter
            public Object getItem(int i2) {
                VDDxKeyQuestionsDialog vDDxKeyQuestionsDialog = VDDxKeyQuestionsDialog.this;
                return vDDxKeyQuestionsDialog.m72922g3(i2, vDDxKeyQuestionsDialog.f89862J4);
            }

            @Override // android.widget.Adapter
            public long getItemId(int i2) {
                return 0L;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getItemViewType(int i2) {
                return ((Bundle) getItem(i2)).getString("Type").equals("Header") ? 0 : 1;
            }

            @Override // android.widget.Adapter
            public View getView(int i2, View view, ViewGroup viewGroup) {
                Bundle bundle2 = (Bundle) getItem(i2);
                if (bundle2.getString("Type").equals("Header")) {
                    if (view == null) {
                        view = LayoutInflater.from(VDDxKeyQuestionsDialog.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false);
                        view.setTag(view.findViewById(C5562R.id.header_text));
                    }
                    ((TextView) view.findViewById(C5562R.id.header_text)).setText(bundle2.getString("Text"));
                    return view;
                }
                if (view == null) {
                    view = LayoutInflater.from(VDDxKeyQuestionsDialog.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_check, viewGroup, false);
                }
                TextView textView = (TextView) view.findViewById(C5562R.id.text);
                ImageView imageView = (ImageView) view.findViewById(C5562R.id.check_icon);
                String string = bundle2.getString("Section");
                String string2 = ((Bundle) ((Bundle) VDDxKeyQuestionsDialog.this.f89860H4.get(VDDxKeyQuestionsDialog.this.f89862J4.indexOf(string))).getParcelableArrayList("findings").get(bundle2.getInt("Index"))).getString("findingId");
                textView.setText(VDDxKeyQuestionsDialog.this.f89861I4.getString(string2));
                if (VDDxKeyQuestionsDialog.this.f89859G4.contains(string2)) {
                    imageView.setVisibility(0);
                } else {
                    imageView.setVisibility(4);
                }
                return view;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getViewTypeCount() {
                return 2;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public boolean hasStableIds() {
                return false;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public boolean isEmpty() {
                return false;
            }

            @Override // android.widget.BaseAdapter, android.widget.ListAdapter
            public boolean isEnabled(int i2) {
                return ((Bundle) getItem(i2)).getString("Type").equals("Item");
            }
        };
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxKeyQuestionsDialog.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                Bundle bundle2 = (Bundle) adapterView.getAdapter().getItem(i2);
                if (bundle2.getString("Type").equals("Header")) {
                    return;
                }
                int i3 = bundle2.getInt("Index");
                Bundle bundle3 = (Bundle) VDDxKeyQuestionsDialog.this.f89860H4.get(VDDxKeyQuestionsDialog.this.f89862J4.indexOf(bundle2.getString("Section")));
                String string = ((Bundle) bundle3.getParcelableArrayList("findings").get(i3)).getString("findingId");
                if (VDDxKeyQuestionsDialog.this.f89859G4.contains(string)) {
                    VDDxKeyQuestionsDialog.this.f89859G4.remove(string);
                } else {
                    if (bundle3.getString("type").equals("radiogroup")) {
                        Iterator it3 = bundle3.getParcelableArrayList("findings").iterator();
                        while (it3.hasNext()) {
                            VDDxKeyQuestionsDialog.this.f89859G4.remove(((Bundle) it3.next()).getString("findingId"));
                        }
                    }
                    VDDxKeyQuestionsDialog.this.f89859G4.add(string);
                }
                ((VDDxBuilderActivity.VDDXBuilderFragment) VDDxKeyQuestionsDialog.this.m15351l0()).m72885J4();
                ((BaseAdapter) listView.getAdapter()).notifyDataSetChanged();
            }
        });
        listView.setAdapter((ListAdapter) baseAdapter);
        builder.setView(viewInflate);
        return builder.create();
    }

    /* renamed from: g3 */
    public Bundle m72922g3(int i2, ArrayList<String> arrayList) {
        Iterator<String> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            String next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Text", next);
                bundle.putString("Type", "Header");
                return bundle;
            }
            int iM72923k3 = i3 + m72923k3(next);
            if (i2 <= iM72923k3) {
                int iM72923k32 = (i2 - (iM72923k3 - m72923k3(next))) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putString("Section", next);
                bundle2.putInt("Index", iM72923k32);
                bundle2.putString("Type", "Item");
                return bundle2;
            }
            i3 = iM72923k3 + 1;
        }
        return null;
    }

    /* renamed from: k3 */
    public int m72923k3(String str) {
        return this.f89860H4.get(this.f89862J4.indexOf(str)).getStringArrayList("findings").size();
    }

    /* renamed from: l3 */
    public int m72924l3(ArrayList<String> arrayList) {
        int iM72923k3 = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            iM72923k3 = iM72923k3 + m72923k3(it2.next()) + 1;
        }
        return iM72923k3;
    }
}

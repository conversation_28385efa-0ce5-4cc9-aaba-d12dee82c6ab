package net.imedicaldoctor.imd.Fragments.TOL;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextGotoViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class PsychoListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f89100A4;

    /* renamed from: B4 */
    public String f89101B4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        m72462O2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        ((RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout)).setVisibility(0);
        ArrayList<Bundle> arrayList = new ArrayList<>();
        this.f88794n4 = arrayList;
        arrayList.add(m72522i3("Tower Of London Test", 1));
        this.f88794n4.add(m72522i3("IOWA Gambling Test", 10));
        this.f88794n4.add(m72522i3("Share Result", 100));
        ChaptersAdapter chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_goto_arrow) { // from class: net.imedicaldoctor.imd.Fragments.TOL.PsychoListActivityFragment.1
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, final int i2) {
                RippleTextGotoViewHolder rippleTextGotoViewHolder = (RippleTextGotoViewHolder) viewHolder;
                rippleTextGotoViewHolder.f101504I.setText(bundle2.getString("title"));
                rippleTextGotoViewHolder.f101507L.setVisibility(8);
                rippleTextGotoViewHolder.f101506K.setVisibility(8);
                rippleTextGotoViewHolder.f101505J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.PsychoListActivityFragment.1.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        PsychoListActivityFragment.this.m72523j3(bundle2, i2);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                return new RippleTextGotoViewHolder(view);
            }
        };
        this.f88792l4 = chaptersAdapter;
        this.f88803w4.setAdapter(chaptersAdapter);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "Psychological Tests";
    }

    /* renamed from: i3 */
    public Bundle m72522i3(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("title", str);
        bundle.putInt("id", i2);
        return bundle;
    }

    /* renamed from: j3 */
    public void m72523j3(Bundle bundle, int i2) {
        m72468V2();
        if (bundle.getInt("id") == 100) {
            CompressHelper compressHelper = this.f88791k4;
            compressHelper.m71896u2(compressHelper.m71806R(), "*/*");
            return;
        }
        this.f88791k4.m71772A1(this.f88788h4, bundle.getInt("id") + "", null, null);
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class settingsList extends DialogFragment {

    /* renamed from: F4 */
    private ArrayList<Bundle> f90856F4;

    /* renamed from: G4 */
    private String f90857G4;

    /* renamed from: H4 */
    private String f90858H4;

    /* renamed from: I4 */
    private String f90859I4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f90856F4 = m15387y().getParcelableArrayList("items");
        this.f90857G4 = m15387y().getString("titleProperty");
        this.f90858H4 = m15387y().getString("type");
        this.f90859I4 = m15387y().getString("selected");
        new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new ArrayAdapter<Bundle>(m15366r(), C5562R.layout.list_view_item_simple_text_check, C5562R.id.text, this.f90856F4) { // from class: net.imedicaldoctor.imd.Fragments.settingsList.1
            @Override // android.widget.ArrayAdapter, android.widget.Adapter
            public View getView(int i2, View view, ViewGroup viewGroup) {
                if (view == null) {
                    view = LayoutInflater.from(settingsList.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_check, viewGroup, false);
                    view.setTag(view.findViewById(C5562R.id.text));
                }
                TextView textView = (TextView) view.getTag();
                String string = ((Bundle) settingsList.this.f90856F4.get(i2)).getString(settingsList.this.f90857G4);
                textView.setText(string);
                if (string.equals(settingsList.this.f90859I4)) {
                    view.findViewById(C5562R.id.check_icon).setVisibility(0);
                } else {
                    view.findViewById(C5562R.id.check_icon).setVisibility(8);
                }
                return view;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.settingsList.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                ((accountFragment) settingsList.this.m15351l0()).m72995s3((Bundle) settingsList.this.f90856F4.get(i2), settingsList.this.f90858H4);
                settingsList.this.mo15203M2();
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

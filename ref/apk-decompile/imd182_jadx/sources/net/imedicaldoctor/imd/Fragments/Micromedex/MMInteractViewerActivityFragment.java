package net.imedicaldoctor.imd.Fragments.Micromedex;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.google.common.net.HttpHeaders;
import java.util.ArrayList;
import java.util.Arrays;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MMInteractViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88585X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88586Y4;

    /* renamed from: Z4 */
    public int f88587Z4;

    /* renamed from: I4 */
    public void m72390I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f88586Y4.add(bundle);
    }

    /* renamed from: J4 */
    public String m72391J4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88587Z4 + 1;
        this.f88587Z4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded3\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded3(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: K4 */
    public String m72392K4(String str, String str2, String str3, String str4) {
        int i2 = this.f88587Z4 + 1;
        this.f88587Z4 = i2;
        return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + String.valueOf(i2) + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
    }

    /* renamed from: L4 */
    public String m72393L4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88587Z4 + 1;
        this.f88587Z4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: M4 */
    public String m72394M4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88587Z4 + 1;
        this.f88587Z4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded2\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded2(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: N4 */
    public String m72395N4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String str = MMInteractViewerActivityFragment.this.f89563A4;
                    if (str == null || str.length() == 0) {
                        iMDLogger.m73550f("Loading Document", MMInteractViewerActivityFragment.this.f89567E4);
                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(MMInteractViewerActivityFragment.this.f89567E4.split("-")[1], ",,,,,");
                        String str2 = strArrSplitByWholeSeparator[0];
                        MMInteractViewerActivityFragment mMInteractViewerActivityFragment = MMInteractViewerActivityFragment.this;
                        mMInteractViewerActivityFragment.f89568F4 = strArrSplitByWholeSeparator[1];
                        mMInteractViewerActivityFragment.f89579Q4.m71866m(mMInteractViewerActivityFragment.f89566D4, "Update app_state set value=" + str2 + " where key='current_doc'");
                        MMInteractViewerActivityFragment mMInteractViewerActivityFragment2 = MMInteractViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71817V = mMInteractViewerActivityFragment2.f89579Q4.m71817V(mMInteractViewerActivityFragment2.f89566D4, "Select * from v_interactions_mono");
                        if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                            MMInteractViewerActivityFragment.this.f88586Y4 = new ArrayList<>();
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment3 = MMInteractViewerActivityFragment.this;
                            mMInteractViewerActivityFragment3.f88587Z4 = 0;
                            mMInteractViewerActivityFragment3.f88585X4 = arrayListM71817V.get(0);
                            StringBuilder sb = new StringBuilder();
                            sb.append("");
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment4 = MMInteractViewerActivityFragment.this;
                            sb.append(mMInteractViewerActivityFragment4.m72394M4("Evidence", "", "LTR", mMInteractViewerActivityFragment4.f88585X4.getString("evidence"), "", "margin-left:10px;margin-top:5px", ""));
                            String string = sb.toString();
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment5 = MMInteractViewerActivityFragment.this;
                            mMInteractViewerActivityFragment5.m72390I4("Evidence", mMInteractViewerActivityFragment5.f88587Z4);
                            StringBuilder sb2 = new StringBuilder();
                            sb2.append(string);
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment6 = MMInteractViewerActivityFragment.this;
                            sb2.append(mMInteractViewerActivityFragment6.m72394M4("Onset", "", "LTR", mMInteractViewerActivityFragment6.f88585X4.getString("onset"), "", "margin-left:10px;margin-top:5px", ""));
                            String string2 = sb2.toString();
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment7 = MMInteractViewerActivityFragment.this;
                            mMInteractViewerActivityFragment7.m72390I4("Onset", mMInteractViewerActivityFragment7.f88587Z4);
                            StringBuilder sb3 = new StringBuilder();
                            sb3.append(string2);
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment8 = MMInteractViewerActivityFragment.this;
                            sb3.append(mMInteractViewerActivityFragment8.m72394M4("Severity", "", "LTR", mMInteractViewerActivityFragment8.f88585X4.getString("severity"), "", "margin-left:10px;margin-top:5px", ""));
                            String string3 = sb3.toString();
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment9 = MMInteractViewerActivityFragment.this;
                            mMInteractViewerActivityFragment9.m72390I4("Severity", mMInteractViewerActivityFragment9.f88587Z4);
                            StringBuilder sb4 = new StringBuilder();
                            sb4.append(string3);
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment10 = MMInteractViewerActivityFragment.this;
                            sb4.append(mMInteractViewerActivityFragment10.m72394M4(HttpHeaders.f62930g, "", "LTR", mMInteractViewerActivityFragment10.f88585X4.getString("warning"), "", "margin-left:10px;margin-top:5px", ""));
                            String string4 = sb4.toString();
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment11 = MMInteractViewerActivityFragment.this;
                            mMInteractViewerActivityFragment11.m72390I4(HttpHeaders.f62930g, mMInteractViewerActivityFragment11.f88587Z4);
                            StringBuilder sb5 = new StringBuilder();
                            sb5.append(string4);
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment12 = MMInteractViewerActivityFragment.this;
                            sb5.append(mMInteractViewerActivityFragment12.m72394M4("Description", "", "LTR", mMInteractViewerActivityFragment12.f88585X4.getString("monograph"), "", "margin-left:10px;margin-top:5px", ""));
                            String string5 = sb5.toString();
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment13 = MMInteractViewerActivityFragment.this;
                            mMInteractViewerActivityFragment13.m72390I4("Description", mMInteractViewerActivityFragment13.f88587Z4);
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment14 = MMInteractViewerActivityFragment.this;
                            String strM72817d4 = mMInteractViewerActivityFragment14.m72817d4(mMInteractViewerActivityFragment14.m15366r(), "MMHeader.css");
                            MMInteractViewerActivityFragment mMInteractViewerActivityFragment15 = MMInteractViewerActivityFragment.this;
                            String strM72817d42 = mMInteractViewerActivityFragment15.m72817d4(mMInteractViewerActivityFragment15.m15366r(), "MMFooter.css");
                            String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", MMInteractViewerActivityFragment.this.f89568F4).replace("[include]", "");
                            MMInteractViewerActivityFragment.this.f89563A4 = strReplace + string5 + strM72817d42;
                        }
                        MMInteractViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                        return;
                    }
                    MMInteractViewerActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    MMInteractViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = MMInteractViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    MMInteractViewerActivityFragment mMInteractViewerActivityFragment = MMInteractViewerActivityFragment.this;
                    mMInteractViewerActivityFragment.m72780C4(mMInteractViewerActivityFragment.f89595p4);
                    return;
                }
                String strM71753g1 = CompressHelper.m71753g1(MMInteractViewerActivityFragment.this.f89566D4, "base");
                MMInteractViewerActivityFragment mMInteractViewerActivityFragment2 = MMInteractViewerActivityFragment.this;
                mMInteractViewerActivityFragment2.m72795O3(mMInteractViewerActivityFragment2.f89563A4, strM71753g1);
                MMInteractViewerActivityFragment.this.m72836s4();
                MMInteractViewerActivityFragment.this.m72831p4();
                MMInteractViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                MMInteractViewerActivityFragment.this.m15358o2(false);
                MMInteractViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f88586Y4);
            bundle.putString("TitleProperty", "label");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

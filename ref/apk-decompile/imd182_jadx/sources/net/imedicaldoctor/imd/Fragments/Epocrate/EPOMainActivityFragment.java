package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.SwitchCompat;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import java.util.HashMap;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class EPOMainActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public GridLayoutManager f88131A4;

    /* renamed from: B4 */
    public SpellSearchAdapter f88132B4;

    /* renamed from: C4 */
    private CustomItemDecoration f88133C4;

    public static class CardViewPlaceHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f88138I;

        /* renamed from: J */
        private final ImageView f88139J;

        /* renamed from: K */
        private final MaterialRippleLayout f88140K;

        public CardViewPlaceHolder(View view) {
            super(view);
            this.f88138I = (TextView) view.findViewById(C5562R.id.text_view);
            this.f88139J = (ImageView) view.findViewById(C5562R.id.image_view);
            this.f88140K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class CollectionAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        HashMap<String, Integer> f88141d = new HashMap<>();

        public CollectionAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            return i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return i2 < EPOMainActivityFragment.this.f88794n4.size() ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) throws Resources.NotFoundException {
            SwitchCompat switchCompat;
            CompoundButton.OnCheckedChangeListener onCheckedChangeListener;
            if (viewHolder.m27811F() == 0) {
                final Bundle bundle = EPOMainActivityFragment.this.f88794n4.get(i2);
                CardViewPlaceHolder cardViewPlaceHolder = (CardViewPlaceHolder) viewHolder;
                cardViewPlaceHolder.f88138I.setText(bundle.getString("Title"));
                cardViewPlaceHolder.f88139J.setImageDrawable(EPOMainActivityFragment.this.m15366r().getResources().getDrawable(bundle.getInt("Image")));
                cardViewPlaceHolder.f88140K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivityFragment.CollectionAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CompressHelper compressHelper;
                        Class<?> cls;
                        Class<?> cls2;
                        String string = bundle.getString("Type");
                        Bundle bundle2 = new Bundle();
                        bundle2.putBundle("DB", EPOMainActivityFragment.this.f88788h4);
                        if (string.equals("dx")) {
                            compressHelper = EPOMainActivityFragment.this.f88791k4;
                            cls = EPODxListActivity.class;
                            cls2 = EPODxListActivityFragment.class;
                        } else if (string.equals("lab")) {
                            compressHelper = EPOMainActivityFragment.this.f88791k4;
                            cls = EPOLabListActivity.class;
                            cls2 = EPOLabListActivityFragment.class;
                        } else if (string.equals("rx")) {
                            compressHelper = EPOMainActivityFragment.this.f88791k4;
                            cls = EPORxListActivity.class;
                            cls2 = EPORxListActivityFragment.class;
                        } else if (string.equals("interact")) {
                            compressHelper = EPOMainActivityFragment.this.f88791k4;
                            cls = EPOInteractActivity.class;
                            cls2 = EPOInteractActivityFragment.class;
                        } else if (string.equals("id")) {
                            compressHelper = EPOMainActivityFragment.this.f88791k4;
                            cls = EPOIDListActivity.class;
                            cls2 = EPOIDListActivityFragment.class;
                        } else {
                            if (string.equals("guideline")) {
                                EPOMainActivityFragment.this.f88791k4.m71803P("https://www.epocrates.com/e/guidelines/list/view?cid=ListGuidelines");
                                return;
                            }
                            if (string.equals("table")) {
                                compressHelper = EPOMainActivityFragment.this.f88791k4;
                                cls = EPOTableListActivity.class;
                                cls2 = EPOTableListActivityFragment.class;
                            } else {
                                if (!string.equals("pillid")) {
                                    return;
                                }
                                compressHelper = EPOMainActivityFragment.this.f88791k4;
                                cls = EPOPillActivity.class;
                                cls2 = EPOPillActivityFragment.class;
                            }
                        }
                        compressHelper.m71798N(cls, cls2, bundle2);
                    }
                });
                return;
            }
            if (viewHolder.m27811F() == 1) {
                SwitchPlaceHolder switchPlaceHolder = (SwitchPlaceHolder) viewHolder;
                if (i2 - EPOMainActivityFragment.this.f88794n4.size() == 0) {
                    switchPlaceHolder.f88147I.setText("Show Disease Monograph as List");
                    switchPlaceHolder.f88148J.setChecked(EPOMainActivityFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("DiseaseList", false));
                    switchCompat = switchPlaceHolder.f88148J;
                    onCheckedChangeListener = new CompoundButton.OnCheckedChangeListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivityFragment.CollectionAdapter.2
                        @Override // android.widget.CompoundButton.OnCheckedChangeListener
                        public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                            EPOMainActivityFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("DiseaseList", z).commit();
                        }
                    };
                } else {
                    switchPlaceHolder.f88147I.setText("Show Lab Monograph as List");
                    switchPlaceHolder.f88148J.setChecked(EPOMainActivityFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("LabList", true));
                    switchCompat = switchPlaceHolder.f88148J;
                    onCheckedChangeListener = new CompoundButton.OnCheckedChangeListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivityFragment.CollectionAdapter.3
                        @Override // android.widget.CompoundButton.OnCheckedChangeListener
                        public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                            EPOMainActivityFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("LabList", z).commit();
                        }
                    };
                }
                switchCompat.setOnCheckedChangeListener(onCheckedChangeListener);
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new CardViewPlaceHolder(LayoutInflater.from(EPOMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_card_view_epocrate, viewGroup, false));
            }
            if (i2 == 1) {
                return new SwitchPlaceHolder(LayoutInflater.from(EPOMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_switch, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return EPOMainActivityFragment.this.f88794n4.size() + 2;
        }
    }

    public static class SwitchPlaceHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f88147I;

        /* renamed from: J */
        private final SwitchCompat f88148J;

        public SwitchPlaceHolder(View view) {
            super(view);
            this.f88147I = (TextView) view.findViewById(C5562R.id.text_view);
            this.f88148J = (SwitchCompat) view.findViewById(C5562R.id.switch_view);
        }
    }

    /* renamed from: i3 */
    private Bundle m72210i3(String str, String str2, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("Title", str);
        bundle.putString("Type", str2);
        bundle.putInt("Image", i2);
        return bundle;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        ((RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout)).setVisibility(0);
        ArrayList<Bundle> arrayList = new ArrayList<>();
        this.f88794n4 = arrayList;
        arrayList.add(m72210i3("Diseases", "dx", C5562R.drawable.diseases_icon));
        this.f88794n4.add(m72210i3("Drugs", "rx", C5562R.drawable.drugs_icon));
        this.f88794n4.add(m72210i3("Labs", "lab", C5562R.drawable.labs_icon));
        this.f88794n4.add(m72210i3("Drug Interactions", "interact", C5562R.drawable.interaction_check_icon));
        this.f88794n4.add(m72210i3("Infectious Disease Treatment", "id", C5562R.drawable.id_tx_icon));
        this.f88794n4.add(m72210i3("Guidelines", "guideline", C5562R.drawable.guidelines_icon));
        this.f88794n4.add(m72210i3("Tables", "table", C5562R.drawable.tables_icon));
        this.f88794n4.add(m72210i3("Pill ID", "pillid", C5562R.drawable.pill_id_icon));
        CollectionAdapter collectionAdapter = new CollectionAdapter();
        this.f88792l4 = collectionAdapter;
        this.f88803w4.setAdapter(collectionAdapter);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(m15366r(), 2);
        this.f88131A4 = gridLayoutManager;
        gridLayoutManager.m27031R3(new GridLayoutManager.SpanSizeLookup() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivityFragment.1
            @Override // androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
            /* renamed from: f */
            public int mo27056f(int i2) {
                RecyclerView.Adapter adapter = EPOMainActivityFragment.this.f88803w4.getAdapter();
                EPOMainActivityFragment ePOMainActivityFragment = EPOMainActivityFragment.this;
                return (adapter != ePOMainActivityFragment.f88132B4 && i2 <= ePOMainActivityFragment.f88794n4.size() - 1) ? 1 : 2;
            }
        });
        this.f88132B4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivityFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                int i3 = 0;
                if (bundle2.getString(Annotation.f68283i3).length() == 0) {
                    rippleTextFullViewHolder.f101500J.setVisibility(8);
                } else {
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                }
                String string = bundle2.getString("type");
                String string2 = bundle2.getString("typeText");
                if (string2.equals("Dx")) {
                    i3 = C5562R.drawable.plus_dx;
                } else if (string2.equals("Rx")) {
                    i3 = string.equals("7") ? C5562R.drawable.plus_alt : string.equals("6") ? C5562R.drawable.plus_otc : C5562R.drawable.plus_rx;
                } else if (string2.equals("ID")) {
                    i3 = C5562R.drawable.plus_id;
                } else if (string2.equals("Lab")) {
                    i3 = C5562R.drawable.plus_lab;
                } else if (string2.equals("Guideline")) {
                    i3 = C5562R.drawable.plus_gl;
                } else if (string2.equals("Table")) {
                    i3 = C5562R.drawable.plus_table;
                }
                rippleTextFullViewHolder.f101501K.setImageDrawable(EPOMainActivityFragment.this.m15366r().getResources().getDrawable(i3));
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivityFragment.2.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CompressHelper compressHelper;
                        Bundle bundle3;
                        StringBuilder sb;
                        String str;
                        EPOMainActivityFragment.this.m72468V2();
                        String string3 = bundle2.getString("typeText");
                        String string4 = bundle2.getString("contentId");
                        if (string3.equals("Dx")) {
                            EPOMainActivityFragment ePOMainActivityFragment = EPOMainActivityFragment.this;
                            compressHelper = ePOMainActivityFragment.f88791k4;
                            bundle3 = ePOMainActivityFragment.f88788h4;
                            sb = new StringBuilder();
                            str = "dx-";
                        } else if (string3.equals("Rx")) {
                            EPOMainActivityFragment ePOMainActivityFragment2 = EPOMainActivityFragment.this;
                            compressHelper = ePOMainActivityFragment2.f88791k4;
                            bundle3 = ePOMainActivityFragment2.f88788h4;
                            sb = new StringBuilder();
                            str = "rx-";
                        } else if (string3.equals("ID")) {
                            EPOMainActivityFragment ePOMainActivityFragment3 = EPOMainActivityFragment.this;
                            compressHelper = ePOMainActivityFragment3.f88791k4;
                            bundle3 = ePOMainActivityFragment3.f88788h4;
                            sb = new StringBuilder();
                            str = "id-";
                        } else if (string3.equals("Lab")) {
                            EPOMainActivityFragment ePOMainActivityFragment4 = EPOMainActivityFragment.this;
                            compressHelper = ePOMainActivityFragment4.f88791k4;
                            bundle3 = ePOMainActivityFragment4.f88788h4;
                            sb = new StringBuilder();
                            str = "lab-";
                        } else if (string3.equals("Guideline")) {
                            EPOMainActivityFragment ePOMainActivityFragment5 = EPOMainActivityFragment.this;
                            compressHelper = ePOMainActivityFragment5.f88791k4;
                            bundle3 = ePOMainActivityFragment5.f88788h4;
                            sb = new StringBuilder();
                            str = "guideline-";
                        } else {
                            if (!string3.equals("Table")) {
                                return;
                            }
                            EPOMainActivityFragment ePOMainActivityFragment6 = EPOMainActivityFragment.this;
                            compressHelper = ePOMainActivityFragment6.f88791k4;
                            bundle3 = ePOMainActivityFragment6.f88788h4;
                            sb = new StringBuilder();
                            str = "table-";
                        }
                        sb.append(str);
                        sb.append(string4);
                        compressHelper.m71772A1(bundle3, sb.toString(), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                EPOMainActivityFragment.this.m72468V2();
                EPOMainActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                return new RippleTextFullViewHolder(view);
            }
        };
        this.f88133C4 = new CustomItemDecoration(m15366r());
        this.f88803w4.setLayoutManager(this.f88131A4);
        this.f88803w4.setItemAnimator(new DefaultItemAnimator());
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        try {
            this.f88803w4.m27380A1(this.f88133C4);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        this.f88803w4.m27459p(this.f88133C4);
        this.f88132B4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88132B4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: Z2 */
    public void mo72211Z2() {
        try {
            this.f88803w4.m27380A1(this.f88133C4);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match '(text:" + str + "* OR content:" + str + "*) NOT (type:5)'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }
}

package net.imedicaldoctor.imd.Fragments.Statdx;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class SDDocActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88967X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88968Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88969Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f88970a5;

    /* renamed from: b5 */
    public String f88971b5;

    /* renamed from: c5 */
    public String f88972c5;

    /* renamed from: I4 */
    private void m72503I4(String str) {
        if (this.f88969Z4.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
            return;
        }
        ArrayList arrayList = new ArrayList();
        arrayList.addAll(this.f88969Z4);
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            if (((Bundle) arrayList.get(i3)).getString("id").startsWith(str)) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList = this.f88968Y4;
        if (arrayList == null || arrayList.size() <= 0) {
            return null;
        }
        Bundle bundleM72839v3 = m72839v3(this.f88968Y4);
        String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, bundleM72839v3.getString("id") + ".jpg", "images-E");
        m72804T3(bundleM72839v3.getString("id"), "images-E");
        return strM71754h1;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDDocActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                String str;
                try {
                    String str2 = SDDocActivityFragment.this.f89563A4;
                    if (str2 == null || str2.length() == 0) {
                        SDDocActivityFragment.this.f88969Z4 = new ArrayList<>();
                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(SDDocActivityFragment.this.f89567E4, ",,,");
                        String str3 = "127";
                        if (strArrSplitByWholeSeparator.length == 3) {
                            SDDocActivityFragment sDDocActivityFragment = SDDocActivityFragment.this;
                            sDDocActivityFragment.f88972c5 = strArrSplitByWholeSeparator[2];
                            ArrayList<Bundle> arrayListM71817V = sDDocActivityFragment.f89579Q4.m71817V(sDDocActivityFragment.f89566D4, "Select id,title,description as content from cases where id='" + SDDocActivityFragment.this.f88972c5 + "'");
                            if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                                SDDocActivityFragment.this.f88967X4 = arrayListM71817V.get(0);
                                SDDocActivityFragment sDDocActivityFragment2 = SDDocActivityFragment.this;
                                sDDocActivityFragment2.f89568F4 = sDDocActivityFragment2.f88967X4.getString("title");
                                SDDocActivityFragment sDDocActivityFragment3 = SDDocActivityFragment.this;
                                sDDocActivityFragment3.f88968Y4 = sDDocActivityFragment3.f89579Q4.m71817V(sDDocActivityFragment3.f89566D4, "Select imageId as id, * from cases_images where caseId='" + SDDocActivityFragment.this.f88972c5 + "'");
                                SDDocActivityFragment sDDocActivityFragment4 = SDDocActivityFragment.this;
                                if (sDDocActivityFragment4.f88968Y4 == null) {
                                    sDDocActivityFragment4.f88968Y4 = new ArrayList<>();
                                }
                                if (SDDocActivityFragment.this.f88968Y4.size() > 0) {
                                    Iterator<Bundle> it2 = SDDocActivityFragment.this.f88968Y4.iterator();
                                    while (it2.hasNext()) {
                                        Bundle next = it2.next();
                                        Bundle bundle2 = new Bundle();
                                        bundle2.putString("ImagePath", CompressHelper.m71754h1(SDDocActivityFragment.this.f89566D4, next.getString("imageId") + ".jpg", "images-E"));
                                        bundle2.putString("id", next.getString("imageId"));
                                        bundle2.putString("Encrypted", IcyHeaders.f28171a3);
                                        bundle2.putBundle("db", SDDocActivityFragment.this.f89566D4);
                                        SDDocActivityFragment.this.f88969Z4.add(bundle2);
                                    }
                                }
                                SDDocActivityFragment.this.f88970a5 = new ArrayList<>();
                                Bundle bundle3 = new Bundle();
                                bundle3.putString("fieldTitle", "Description");
                                bundle3.putString("fieldId", "");
                                SDDocActivityFragment.this.f88970a5.add(bundle3);
                                str = str3;
                            }
                            SDDocActivityFragment.this.f89595p4 = "Document doesn't exist";
                            return;
                        }
                        SDDocActivityFragment sDDocActivityFragment5 = SDDocActivityFragment.this;
                        sDDocActivityFragment5.f88971b5 = strArrSplitByWholeSeparator[1];
                        ArrayList<Bundle> arrayListM71817V2 = sDDocActivityFragment5.f89579Q4.m71817V(sDDocActivityFragment5.f89566D4, "Select * from docs where id='" + SDDocActivityFragment.this.f88971b5 + "'");
                        if (arrayListM71817V2 != null && arrayListM71817V2.size() != 0) {
                            SDDocActivityFragment.this.f88967X4 = arrayListM71817V2.get(0);
                            SDDocActivityFragment.this.f89568F4 = SDDocActivityFragment.this.f88967X4.getString("title") + " - " + SDDocActivityFragment.this.f88967X4.getString("category");
                            SDDocActivityFragment sDDocActivityFragment6 = SDDocActivityFragment.this;
                            sDDocActivityFragment6.f88968Y4 = sDDocActivityFragment6.f89579Q4.m71817V(sDDocActivityFragment6.f89566D4, "Select * from images where docId='" + SDDocActivityFragment.this.f88971b5 + "'");
                            SDDocActivityFragment sDDocActivityFragment7 = SDDocActivityFragment.this;
                            if (sDDocActivityFragment7.f88968Y4 == null) {
                                sDDocActivityFragment7.f88968Y4 = new ArrayList<>();
                            }
                            if (SDDocActivityFragment.this.f88968Y4.size() > 0) {
                                Iterator<Bundle> it3 = SDDocActivityFragment.this.f88968Y4.iterator();
                                while (it3.hasNext()) {
                                    Bundle next2 = it3.next();
                                    Bundle bundle4 = new Bundle();
                                    bundle4.putString("ImagePath", CompressHelper.m71754h1(SDDocActivityFragment.this.f89566D4, next2.getString("id") + ".jpg", "images-E"));
                                    bundle4.putString("id", next2.getString("id"));
                                    bundle4.putString("Encrypted", IcyHeaders.f28171a3);
                                    String str4 = str3;
                                    bundle4.putString("DescriptionHTML2", SDDocActivityFragment.this.f89579Q4.m71773B(next2.getString(HTML.Tag.f74389g), next2.getString("id"), str4));
                                    bundle4.putBundle("db", SDDocActivityFragment.this.f89566D4);
                                    SDDocActivityFragment.this.f88969Z4.add(bundle4);
                                    str3 = str4;
                                }
                            }
                            str = str3;
                            SDDocActivityFragment sDDocActivityFragment8 = SDDocActivityFragment.this;
                            sDDocActivityFragment8.f88970a5 = sDDocActivityFragment8.f89579Q4.m71817V(sDDocActivityFragment8.f89566D4, "Select * from fields where topicId='" + SDDocActivityFragment.this.f88971b5 + "'");
                            SDDocActivityFragment sDDocActivityFragment9 = SDDocActivityFragment.this;
                            if (sDDocActivityFragment9.f88970a5 == null) {
                                sDDocActivityFragment9.f88970a5 = new ArrayList<>();
                            }
                        }
                        SDDocActivityFragment.this.f89595p4 = "Document doesn't exist";
                        return;
                        SDDocActivityFragment sDDocActivityFragment10 = SDDocActivityFragment.this;
                        String strM72817d4 = sDDocActivityFragment10.m72817d4(sDDocActivityFragment10.m15366r(), "ASHeader.css");
                        SDDocActivityFragment sDDocActivityFragment11 = SDDocActivityFragment.this;
                        String strM72817d42 = sDDocActivityFragment11.m72817d4(sDDocActivityFragment11.m15366r(), "ASFooter.css");
                        String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", SDDocActivityFragment.this.f89568F4).replace("[include]", "");
                        SDDocActivityFragment sDDocActivityFragment12 = SDDocActivityFragment.this;
                        String strM71773B = sDDocActivityFragment12.f89579Q4.m71773B(sDDocActivityFragment12.f88967X4.getString(Annotation.f68283i3), SDDocActivityFragment.this.f88967X4.getString("id"), str);
                        SDDocActivityFragment.this.f89563A4 = strReplace + strM71773B + strM72817d42;
                    }
                    SDDocActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    SDDocActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDDocActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = SDDocActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    SDDocActivityFragment sDDocActivityFragment = SDDocActivityFragment.this;
                    sDDocActivityFragment.m72780C4(sDDocActivityFragment.f89595p4);
                    return;
                }
                String strM71753g1 = CompressHelper.m71753g1(SDDocActivityFragment.this.f89566D4, "base");
                SDDocActivityFragment sDDocActivityFragment2 = SDDocActivityFragment.this;
                sDDocActivityFragment2.m72795O3(sDDocActivityFragment2.f89563A4, strM71753g1);
                SDDocActivityFragment.this.m72836s4();
                SDDocActivityFragment.this.m72831p4();
                SDDocActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                SDDocActivityFragment.this.m15358o2(false);
                SDDocActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            m72503I4("soheilvb");
        }
        if (itemId == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f88970a5);
            bundle.putString("TitleProperty", "fieldTitle");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        m72801S2().m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDDocActivityFragment.3
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                if (str != null) {
                    File file = new File(str);
                    if (file.exists()) {
                        try {
                            Glide.m30041G(SDDocActivityFragment.this.m15366r()).mo30123h(new CompressHelper(SDDocActivityFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127")).m30165B2(SDDocActivityFragment.this.f89575M4);
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                            iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                        }
                    }
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDDocActivityFragment.4
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
            }
        });
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

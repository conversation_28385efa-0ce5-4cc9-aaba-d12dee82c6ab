package net.imedicaldoctor.imd.Fragments;

import android.os.Build;
import android.os.Bundle;
import android.view.ActionMode;
import android.view.inputmethod.InputMethodManager;
import androidx.annotation.Nullable;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.iMDWebView;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class ViewerHelperActivity extends iMDActivity {

    /* renamed from: y3 */
    private ActionMode f89560y3 = null;

    /* renamed from: b1 */
    private void m72762b1() {
        try {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService("input_method");
            getCurrentFocus().clearFocus();
            inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity
    /* renamed from: Z0 */
    public int mo72763Z0() {
        int identifier = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (identifier > 0) {
            return getResources().getDimensionPixelSize(identifier);
        }
        return 0;
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public void onActionModeFinished(ActionMode actionMode) {
        if (actionMode == null) {
            actionMode = this.f89560y3;
        }
        if (Build.VERSION.SDK_INT <= 22) {
            if (!new File(new CompressHelper(this).m71797M1() + "/action.txt").exists()) {
                return;
            }
        }
        actionMode.getMenu().clear();
        iMDWebView imdwebview = (iMDWebView) findViewById(C5562R.id.webView);
        if (imdwebview != null) {
            imdwebview.m73433g("console.log('finisham,,,,,');");
        }
        super.onActionModeFinished(actionMode);
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x0040  */
    @Override // android.app.Activity, android.view.Window.Callback
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void onActionModeStarted(android.view.ActionMode r7) {
        /*
            r6 = this;
            java.lang.String r0 = "ACtionMode"
            java.lang.String r1 = "onActionModeStarted"
            net.imedicaldoctor.imd.iMDLogger.m73554j(r0, r1)
            r0 = **********(0x7f0a03b1, float:1.8345263E38)
            android.view.View r1 = r6.findViewById(r0)
            android.webkit.WebView r1 = (android.webkit.WebView) r1
            if (r1 != 0) goto L13
            return
        L13:
            r6.f89560y3 = r7
            int r2 = android.os.Build.VERSION.SDK_INT
            r3 = 22
            if (r2 > r3) goto L40
            java.io.File r3 = new java.io.File
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            net.imedicaldoctor.imd.Data.CompressHelper r5 = new net.imedicaldoctor.imd.Data.CompressHelper
            r5.<init>(r6)
            java.lang.String r5 = r5.m71797M1()
            r4.append(r5)
            java.lang.String r5 = "/action.txt"
            r4.append(r5)
            java.lang.String r4 = r4.toString()
            r3.<init>(r4)
            boolean r3 = r3.exists()
            if (r3 == 0) goto L8b
        L40:
            boolean r1 = r1.isFocused()
            if (r1 != 0) goto L47
            return
        L47:
            android.view.Menu r1 = r7.getMenu()
            r1.clear()
            r1 = 30
            if (r2 <= r1) goto L5a
            r1 = 100
            net.imedicaldoctor.imd.Fragments.C5283a.m72958a(r7, r1)
            r7.finish()
        L5a:
            android.view.View r0 = r6.findViewById(r0)
            net.imedicaldoctor.imd.Utils.iMDWebView r0 = (net.imedicaldoctor.imd.Utils.iMDWebView) r0
            if (r0 == 0) goto L8b
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r2 = "getRect("
            r1.append(r2)
            int r2 = r0.getWidth()
            r1.append(r2)
            java.lang.String r2 = ","
            r1.append(r2)
            int r2 = r0.getHeight()
            r1.append(r2)
            java.lang.String r2 = ")"
            r1.append(r2)
            java.lang.String r1 = r1.toString()
            r0.m73433g(r1)
        L8b:
            super.onActionModeStarted(r7)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.ViewerHelperActivity.onActionModeStarted(android.view.ActionMode):void");
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(@Nullable Bundle bundle) {
        super.onCreate(bundle);
        if (getSharedPreferences("default_preferences", 0).getBoolean("wakelock", true)) {
            getWindow().addFlags(128);
        }
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class EPORxListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88199A4;

    /* renamed from: B4 */
    public String f88200B4;

    /* renamed from: C4 */
    public Bundle f88201C4;

    /* renamed from: D4 */
    public boolean f88202D4;

    /* renamed from: E4 */
    public String f88203E4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String string;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f88203E4 = "RX.sqlite";
        this.f88201C4 = new Bundle();
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            string = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxListActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            string = m15387y().getString("ParentId");
        }
        this.f88200B4 = string;
        if (this.f88200B4 == null) {
            this.f88794n4 = this.f88791k4.m71819W(this.f88788h4, "SELECT  DRUG_CLASS.ID ,  DRUG_CLASS.NAME   FROM DRUG_CLASS_HIERARCHY   JOIN DRUG_CLASS ON  DRUG_CLASS_HIERARCHY.PARENT_ID =  ''  AND DRUG_CLASS.ID = DRUG_CLASS_HIERARCHY.CHILD_ID    ORDER BY  DRUG_CLASS.NAME COLLATE NOCASE", this.f88203E4);
        } else {
            ArrayList<Bundle> arrayListM71819W = this.f88791k4.m71819W(this.f88788h4, "SELECT  DRUG_CLASS.ID ,  DRUG_CLASS.NAME   FROM DRUG_CLASS_HIERARCHY   JOIN DRUG_CLASS ON  DRUG_CLASS_HIERARCHY.PARENT_ID =  " + this.f88200B4 + " AND DRUG_CLASS.ID = DRUG_CLASS_HIERARCHY.CHILD_ID    ORDER BY  DRUG_CLASS.NAME COLLATE NOCASE", this.f88203E4);
            this.f88794n4 = arrayListM71819W;
            if (arrayListM71819W == null || arrayListM71819W.size() == 0) {
                this.f88202D4 = true;
                this.f88794n4 = this.f88791k4.m71819W(this.f88788h4, "SELECT  DRUG.ID ,  DRUG.CLINICAL_ID ,  DRUG.GENERIC_ID ,  DRUG.NAME ,  DRUG.DRUG_TYPE ,  DRUG.ACTIVE ,  DRUG.ADULT_DSG_ID ,  DRUG.PEDS_DSG_ID ,  DRUG.MFR_STRING_ID ,  DRUG.BBW_ID   FROM DRUG_TO_DRUG_CLASS   JOIN DRUG ON  DRUG_TO_DRUG_CLASS.DRUG_CLASS_ID =  " + this.f88200B4 + "   AND DRUG.ID = DRUG_TO_DRUG_CLASS.DRUG_ID    WHERE ACTIVE = 1 ORDER BY  DRUG.NAME COLLATE NOCASE", this.f88203E4);
                CompressHelper compressHelper = this.f88791k4;
                String string2 = compressHelper.m71890s1(compressHelper.m71819W(this.f88788h4, "SELECT  group_concat(generic_ID) as a  FROM DRUG_TO_DRUG_CLASS   JOIN DRUG ON  DRUG_TO_DRUG_CLASS.DRUG_CLASS_ID =  " + this.f88200B4 + "   AND DRUG.ID = DRUG_TO_DRUG_CLASS.DRUG_ID    WHERE ACTIVE = 1 AND generic_ID<>''   ORDER BY  DRUG.NAME COLLATE NOCASE", this.f88203E4)).getString("a");
                ArrayList<Bundle> arrayList = this.f88794n4;
                if (arrayList == null || arrayList.size() == 0) {
                    this.f88794n4 = this.f88791k4.m71819W(this.f88788h4, "SELECT  DRUG.ID ,  DRUG.CLINICAL_ID ,  DRUG.GENERIC_ID ,  DRUG.NAME ,  DRUG.DRUG_TYPE ,  DRUG.ACTIVE ,  DRUG.ADULT_DSG_ID ,  DRUG.PEDS_DSG_ID ,  DRUG.MFR_STRING_ID ,  DRUG.BBW_ID   FROM DRUG  WHERE DRUG_TYPE=7 order by NAME asc", this.f88203E4);
                    CompressHelper compressHelper2 = this.f88791k4;
                    string2 = compressHelper2.m71890s1(compressHelper2.m71819W(this.f88788h4, "SELECT  group_concat(generic_ID) as a  FROM DRUG WHERE DRUG_TYPE=7 AND generic_id<>'' ORDER BY NAME COLLATE NOCASE", this.f88203E4)).getString("a");
                }
                ArrayList<Bundle> arrayListM71819W2 = this.f88791k4.m71819W(this.f88788h4, "SELECT  DRUG.ID ,  DRUG.CLINICAL_ID ,  DRUG.GENERIC_ID ,  DRUG.NAME ,  DRUG.DRUG_TYPE ,  DRUG.ACTIVE ,  DRUG.ADULT_DSG_ID ,  DRUG.PEDS_DSG_ID ,  DRUG.MFR_STRING_ID ,  DRUG.BBW_ID   FROM DRUG   WHERE  ID in (" + string2 + ")", this.f88203E4);
                if (arrayListM71819W2 != null) {
                    Iterator<Bundle> it2 = arrayListM71819W2.iterator();
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        String string3 = next.getString("ID");
                        String string4 = next.getString("NAME");
                        if (!this.f88201C4.containsKey(string3)) {
                            this.f88201C4.putString(string3, string4);
                        }
                    }
                }
            }
        }
        this.f88792l4 = this.f88202D4 ? new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxListActivityFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, final int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("NAME"));
                String string5 = bundle2.getString("GENERIC_ID");
                String string6 = bundle2.getString("ID");
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                String string7 = bundle2.getString("DRUG_TYPE");
                int i3 = string7.equals("7") ? C5562R.drawable.plus_alt : string7.equals("6") ? C5562R.drawable.plus_otc : C5562R.drawable.plus_rx;
                if (!string5.equals(string6) && EPORxListActivityFragment.this.f88201C4.containsKey(string5)) {
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    rippleTextFullViewHolder.f101500J.setText(EPORxListActivityFragment.this.f88201C4.getString(string5));
                } else {
                    rippleTextFullViewHolder.f101500J.setVisibility(8);
                }
                rippleTextFullViewHolder.f101501K.setImageDrawable(EPORxListActivityFragment.this.m15366r().getResources().getDrawable(i3));
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxListActivityFragment.2.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        EPORxListActivityFragment.this.m72231i3(bundle2, i2);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                return new RippleTextFullViewHolder(view);
            }
        } : new ChaptersAdapter(m15366r(), this.f88794n4, "NAME", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxListActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: f0 */
            public void mo71975f0(Bundle bundle2, int i2) {
                EPORxListActivityFragment.this.m72231i3(bundle2, i2);
            }
        };
        this.f88199A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxListActivityFragment.4
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                TextView textView;
                int i3;
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                if (bundle2.getString(Annotation.f68283i3).length() == 0) {
                    textView = rippleTextFullViewHolder.f101500J;
                    i3 = 8;
                } else {
                    textView = rippleTextFullViewHolder.f101500J;
                    i3 = 0;
                }
                textView.setVisibility(i3);
                String string5 = bundle2.getString("type");
                rippleTextFullViewHolder.f101501K.setImageDrawable(EPORxListActivityFragment.this.m15366r().getResources().getDrawable(string5.equals("7") ? C5562R.drawable.plus_alt : string5.equals("6") ? C5562R.drawable.plus_otc : C5562R.drawable.plus_rx));
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxListActivityFragment.4.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        EPORxListActivityFragment.this.m72468V2();
                        EPORxListActivityFragment ePORxListActivityFragment = EPORxListActivityFragment.this;
                        ePORxListActivityFragment.f88791k4.m71772A1(ePORxListActivityFragment.f88788h4, "rx-" + bundle2.getString("contentId"), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                EPORxListActivityFragment.this.m72468V2();
                EPORxListActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                return new RippleTextFullViewHolder(view);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88199A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88199A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match '(text:" + str + "* OR content:" + str + "*) AND typeText:RX NOT (type:5)'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: c3 */
    public void mo72171c3() {
        this.f88800t4.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.drugs_icon));
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "Drugs";
    }

    /* renamed from: i3 */
    public void m72231i3(Bundle bundle, int i2) {
        m72468V2();
        if (!this.f88202D4) {
            Bundle bundle2 = new Bundle();
            bundle2.putBundle("DB", this.f88788h4);
            bundle2.putString("ParentId", bundle.getString("ID"));
            this.f88791k4.m71798N(EPORxListActivity.class, EPORxListActivityFragment.class, bundle2);
            return;
        }
        this.f88791k4.m71772A1(this.f88788h4, "rx-" + bundle.getString("ID"), null, null);
    }
}

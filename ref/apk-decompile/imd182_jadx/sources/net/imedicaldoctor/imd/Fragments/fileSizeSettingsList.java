package net.imedicaldoctor.imd.Fragments;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class fileSizeSettingsList extends DialogFragment {

    /* renamed from: F4 */
    private ArrayList<Bundle> f90716F4;

    /* renamed from: G4 */
    private String f90717G4;

    /* renamed from: H4 */
    private String f90718H4;

    /* renamed from: I4 */
    private String f90719I4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme);
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f90716F4 = m15387y().getParcelableArrayList("items");
        this.f90717G4 = m15387y().getString("titleProperty");
        this.f90718H4 = m15387y().getString("type");
        this.f90719I4 = m15387y().getString("selected");
        new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new ArrayAdapter<Bundle>(m15366r(), C5562R.layout.list_view_item_text_subtitle_check, C5562R.id.text, this.f90716F4) { // from class: net.imedicaldoctor.imd.Fragments.fileSizeSettingsList.1
            @Override // android.widget.ArrayAdapter, android.widget.Adapter
            public View getView(int i2, View view, ViewGroup viewGroup) {
                if (view == null) {
                    view = LayoutInflater.from(fileSizeSettingsList.this.m15366r()).inflate(C5562R.layout.list_view_item_text_subtitle_check, viewGroup, false);
                    view.setTag(view.findViewById(C5562R.id.text));
                }
                TextView textView = (TextView) view.getTag();
                TextView textView2 = (TextView) view.findViewById(C5562R.id.subtext);
                String string = ((Bundle) fileSizeSettingsList.this.f90716F4.get(i2)).getString(fileSizeSettingsList.this.f90717G4);
                textView2.setText(((Bundle) fileSizeSettingsList.this.f90716F4.get(i2)).getString("Size"));
                textView.setText(string);
                if (((Bundle) fileSizeSettingsList.this.f90716F4.get(i2)).containsKey("Path")) {
                    string = ((Bundle) fileSizeSettingsList.this.f90716F4.get(i2)).getString("Path");
                }
                if (string.equals(fileSizeSettingsList.this.f90719I4)) {
                    view.findViewById(C5562R.id.check_icon).setVisibility(0);
                } else {
                    view.findViewById(C5562R.id.check_icon).setVisibility(8);
                }
                return view;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.fileSizeSettingsList.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                Bundle bundle2 = (Bundle) fileSizeSettingsList.this.f90716F4.get(i2);
                if (fileSizeSettingsList.this.m15351l0().getClass() == accountFragment.class) {
                    ((accountFragment) fileSizeSettingsList.this.m15351l0()).m72995s3(bundle2, fileSizeSettingsList.this.f90718H4);
                } else {
                    ((downloadFragment) fileSizeSettingsList.this.m15351l0()).m73225V3(bundle2, fileSizeSettingsList.this.f90718H4);
                }
                fileSizeSettingsList.this.mo15203M2();
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

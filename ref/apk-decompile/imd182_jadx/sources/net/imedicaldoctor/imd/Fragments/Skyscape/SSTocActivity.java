package net.imedicaldoctor.imd.Fragments.Skyscape;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.MessageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class SSTocActivity extends iMDActivity {

    public static class SSTocFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private String f88883A4;

        /* renamed from: B4 */
        private SSTOCSearchChaptersAdapter f88884B4;

        public class RippleInfoTextViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f88889I;

            /* renamed from: J */
            public ImageView f88890J;

            /* renamed from: K */
            public MaterialRippleLayout f88891K;

            public RippleInfoTextViewHolder(View view) {
                super(view);
                this.f88889I = (TextView) view.findViewById(C5562R.id.text_view);
                this.f88891K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
                this.f88890J = (ImageView) view.findViewById(C5562R.id.info_button);
            }
        }

        public class SSTOCChaptersAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f88893d;

            /* renamed from: e */
            public ArrayList<Bundle> f88894e;

            /* renamed from: f */
            public String f88895f;

            public SSTOCChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
                this.f88893d = context;
                this.f88894e = arrayList;
                this.f88895f = str;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: C */
            public int mo26845C(int i2) {
                Bundle bundle = this.f88894e.get(i2);
                if (bundle.getString("leaf").equals(IcyHeaders.f28171a3)) {
                    return 0;
                }
                return bundle.getString("docId").length() > 0 ? 1 : 2;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                if (viewHolder.m27811F() == 0 || viewHolder.m27811F() == 2) {
                    RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                    final Bundle bundle = this.f88894e.get(i2);
                    rippleTextViewHolder.f101515I.setText(bundle.getString(this.f88895f));
                    rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCChaptersAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SSTOCChaptersAdapter.this.mo72486e0(bundle, i2);
                        }
                    });
                    return;
                }
                if (viewHolder.m27811F() == 1) {
                    RippleInfoTextViewHolder rippleInfoTextViewHolder = (RippleInfoTextViewHolder) viewHolder;
                    final Bundle bundle2 = this.f88894e.get(i2);
                    rippleInfoTextViewHolder.f88889I.setText(bundle2.getString(this.f88895f));
                    rippleInfoTextViewHolder.f88891K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCChaptersAdapter.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SSTOCChaptersAdapter.this.mo72486e0(bundle2, i2);
                        }
                    });
                    rippleInfoTextViewHolder.f88890J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCChaptersAdapter.3
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SSTOCChaptersAdapter.this.mo72485d0(bundle2, i2);
                        }
                    });
                }
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                if (i2 == 0) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88893d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
                }
                if (i2 == 1) {
                    return SSTocFragment.this.new RippleInfoTextViewHolder(LayoutInflater.from(this.f88893d).inflate(C5562R.layout.list_view_item_ripple_goto_arrow, viewGroup, false));
                }
                if (i2 == 2) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88893d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
                }
                return null;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                return this.f88894e.size();
            }

            /* renamed from: d0 */
            public void mo72485d0(Bundle bundle, int i2) {
            }

            /* renamed from: e0 */
            public void mo72486e0(Bundle bundle, int i2) {
            }
        }

        public class SSTOCSearchChaptersAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f88906d;

            /* renamed from: e */
            public ArrayList<Bundle> f88907e;

            /* renamed from: f */
            public String f88908f;

            public SSTOCSearchChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
                this.f88906d = context;
                this.f88907e = arrayList;
                this.f88908f = str;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: C */
            public int mo26845C(int i2) {
                Bundle bundle = this.f88907e.get(i2);
                if (bundle.getString("leaf").equals(IcyHeaders.f28171a3)) {
                    return 0;
                }
                return bundle.getString("docId").length() > 0 ? 1 : 2;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                ArrayList<Bundle> arrayList = this.f88907e;
                if (arrayList == null || arrayList.size() == 0) {
                    return;
                }
                if (viewHolder.m27811F() == 0 || viewHolder.m27811F() == 2) {
                    RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                    final Bundle bundle = this.f88907e.get(i2);
                    rippleTextViewHolder.f101515I.setText(bundle.getString(this.f88908f));
                    rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCSearchChaptersAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SSTOCSearchChaptersAdapter.this.mo72488e0(bundle, i2);
                        }
                    });
                    return;
                }
                if (viewHolder.m27811F() == 1) {
                    RippleInfoTextViewHolder rippleInfoTextViewHolder = (RippleInfoTextViewHolder) viewHolder;
                    final Bundle bundle2 = this.f88907e.get(i2);
                    rippleInfoTextViewHolder.f88889I.setText(bundle2.getString(this.f88908f));
                    rippleInfoTextViewHolder.f88891K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCSearchChaptersAdapter.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SSTOCSearchChaptersAdapter.this.mo72488e0(bundle2, i2);
                        }
                    });
                    rippleInfoTextViewHolder.f88890J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCSearchChaptersAdapter.3
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SSTOCSearchChaptersAdapter.this.mo72487d0(bundle2, i2);
                        }
                    });
                }
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                ArrayList<Bundle> arrayList = this.f88907e;
                if (arrayList == null || arrayList.size() == 0) {
                    return new MessageViewHolder(this.f88906d, LayoutInflater.from(this.f88906d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
                }
                if (i2 == 0) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88906d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
                }
                if (i2 == 1) {
                    return SSTocFragment.this.new RippleInfoTextViewHolder(LayoutInflater.from(this.f88906d).inflate(C5562R.layout.list_view_item_ripple_goto_arrow, viewGroup, false));
                }
                if (i2 == 2) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f88906d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
                }
                return null;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                return this.f88907e.size();
            }

            /* renamed from: d0 */
            public void mo72487d0(Bundle bundle, int i2) {
            }

            /* renamed from: e0 */
            public void mo72488e0(Bundle bundle, int i2) {
            }

            /* renamed from: f0 */
            public void m72489f0(ArrayList<Bundle> arrayList) {
                this.f88907e = arrayList;
                m27491G();
            }
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72463P2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72463P2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            if (m15387y() == null || !m15387y().containsKey("ParentId")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
                this.f88883A4 = "0";
            } else {
                if (m15387y().getString("ParentId").equals("0")) {
                    appBarLayout.m35746D(true, false);
                    relativeLayout.setVisibility(0);
                } else {
                    appBarLayout.m35746D(false, false);
                    appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.1
                        @Override // java.lang.Runnable
                        public void run() {
                            relativeLayout.setVisibility(0);
                        }
                    }, 800L);
                }
                this.f88883A4 = m15387y().getString("ParentId");
            }
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select * from toc where parentId = " + this.f88883A4);
            String str = "name";
            this.f88792l4 = new SSTOCChaptersAdapter(m15366r(), this.f88794n4, str) { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.2
                @Override // net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCChaptersAdapter
                /* renamed from: d0 */
                public void mo72485d0(Bundle bundle2, int i2) {
                    SSTocFragment.this.m72468V2();
                    new CompressHelper(SSTocFragment.this.m15366r()).m71772A1(SSTocFragment.this.f88788h4, bundle2.getString("docId"), null, null);
                }

                @Override // net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCChaptersAdapter
                /* renamed from: e0 */
                public void mo72486e0(Bundle bundle2, int i2) {
                    SSTocFragment.this.m72468V2();
                    String string = bundle2.getString("leaf");
                    String string2 = bundle2.getString("docId");
                    if (string.equals(IcyHeaders.f28171a3)) {
                        new CompressHelper(SSTocFragment.this.m15366r()).m71772A1(SSTocFragment.this.f88788h4, string2, null, null);
                        return;
                    }
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("DB", SSTocFragment.this.f88788h4);
                    bundle3.putString("ParentId", bundle2.getString("id"));
                    new CompressHelper(SSTocFragment.this.m15366r()).m71798N(SSTocActivity.class, SSTocFragment.class, bundle3);
                }
            };
            this.f88884B4 = new SSTOCSearchChaptersAdapter(m15366r(), this.f88795o4, str) { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.3
                @Override // net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCSearchChaptersAdapter
                /* renamed from: d0 */
                public void mo72487d0(Bundle bundle2, int i2) {
                    SSTocFragment.this.m72468V2();
                    new CompressHelper(SSTocFragment.this.m15366r()).m71772A1(SSTocFragment.this.f88788h4, bundle2.getString("docId"), null, null);
                }

                @Override // net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity.SSTocFragment.SSTOCSearchChaptersAdapter
                /* renamed from: e0 */
                public void mo72488e0(Bundle bundle2, int i2) {
                    SSTocFragment.this.m72468V2();
                    String string = bundle2.getString("leaf");
                    String string2 = bundle2.getString("docId");
                    if (string.equals(IcyHeaders.f28171a3)) {
                        new CompressHelper(SSTocFragment.this.m15366r()).m71772A1(SSTocFragment.this.f88788h4, string2, null, null);
                        return;
                    }
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("DB", SSTocFragment.this.f88788h4);
                    bundle3.putString("ParentId", bundle2.getString("id"));
                    new CompressHelper(SSTocFragment.this.m15366r()).m71798N(SSTocActivity.class, SSTocFragment.class, bundle3);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(true);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: X2 */
        public void mo71973X2() {
            this.f88884B4.m72489f0(this.f88795o4);
            this.f88803w4.setAdapter(this.f88884B4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id, Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new SSTocFragment());
    }
}

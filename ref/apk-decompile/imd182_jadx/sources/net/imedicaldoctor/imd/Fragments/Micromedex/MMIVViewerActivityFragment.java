package net.imedicaldoctor.imd.Fragments.Micromedex;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.text.ttml.TtmlNode;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.httpclient.cookie.CookiePolicy;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MMIVViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88550X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88551Y4;

    /* renamed from: Z4 */
    public int f88552Z4;

    /* renamed from: I4 */
    public void m72379I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f88551Y4.add(bundle);
    }

    /* renamed from: J4 */
    public String m72380J4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88552Z4 + 1;
        this.f88552Z4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded3\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded3(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: K4 */
    public String m72381K4(String str, String str2, String str3, String str4) {
        int i2 = this.f88552Z4 + 1;
        this.f88552Z4 = i2;
        return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + String.valueOf(i2) + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
    }

    /* renamed from: L4 */
    public String m72382L4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88552Z4 + 1;
        this.f88552Z4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: M4 */
    public String m72383M4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88552Z4 + 1;
        this.f88552Z4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded2\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded2(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: N4 */
    public String m72384N4(String str) {
        return str.equals("C") ? "iv_compat_compatible" : str.equals("I") ? "iv_compat_incompatible" : str.equals("U") ? "iv_compat_uncertain" : str.equals("N") ? "iv_compat_nottested" : str.equals(ExifInterface.f16308X4) ? "iv_compat_cautionvariable" : "";
    }

    /* renamed from: O4 */
    public String m72385O4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        String str = this.f89563A4;
        if (str == null || str.length() == 0) {
            this.f88552Z4 = 0;
            this.f88551Y4 = new ArrayList<>();
            m72834r3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVViewerActivityFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    String str2;
                    String str3;
                    MMIVViewerActivityFragment mMIVViewerActivityFragment = MMIVViewerActivityFragment.this;
                    String str4 = "";
                    mMIVViewerActivityFragment.f89563A4 = "";
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(mMIVViewerActivityFragment.f89567E4.replace("doc-", ""), ",,,");
                    if (strArrSplitByWholeSeparator[0].equals("solution")) {
                        String str5 = strArrSplitByWholeSeparator[1];
                        String str6 = strArrSplitByWholeSeparator[2];
                        String str7 = strArrSplitByWholeSeparator[3];
                        String str8 = strArrSplitByWholeSeparator[4];
                        MMIVViewerActivityFragment mMIVViewerActivityFragment2 = MMIVViewerActivityFragment.this;
                        mMIVViewerActivityFragment2.f89579Q4.m71866m(mMIVViewerActivityFragment2.f89566D4, "Update app_state set value=" + str7 + ", title='" + str8 + "' where key='current_solution_id'");
                        MMIVViewerActivityFragment mMIVViewerActivityFragment3 = MMIVViewerActivityFragment.this;
                        StringBuilder sb = new StringBuilder();
                        sb.append(str6);
                        sb.append(" - ");
                        sb.append(str8);
                        mMIVViewerActivityFragment3.f89568F4 = sb.toString();
                        MMIVViewerActivityFragment mMIVViewerActivityFragment4 = MMIVViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71817V = mMIVViewerActivityFragment4.f89579Q4.m71817V(mMIVViewerActivityFragment4.f89566D4, "Select * from v_solution_intermediate");
                        if (arrayListM71817V == null) {
                            arrayListM71817V = new ArrayList<>();
                        }
                        Iterator<Bundle> it2 = arrayListM71817V.iterator();
                        while (it2.hasNext()) {
                            Bundle next = it2.next();
                            String str9 = next.getString("name") + " - " + next.getString("concentration");
                            if (next.getString("storage").length() > 0) {
                                str3 = "" + MMIVViewerActivityFragment.this.m72383M4("Storage", "", "LTR", next.getString("storage"), "", "margin-left:15px;", "");
                            } else {
                                str3 = "";
                            }
                            if (next.getString("study_period").length() > 0) {
                                str3 = str3 + MMIVViewerActivityFragment.this.m72383M4("Study Period", "", "LTR", next.getString("study_period"), "", "margin-left:15px;", "");
                            }
                            if (next.getString(TtmlNode.f29712W).length() > 0) {
                                str3 = str3 + MMIVViewerActivityFragment.this.m72383M4("Container", "", "LTR", next.getString(TtmlNode.f29712W), "", "margin-left:15px;", "");
                            }
                            if (next.getString(CookiePolicy.BROWSER_COMPATIBILITY).length() > 0) {
                                str3 = str3 + MMIVViewerActivityFragment.this.m72383M4("Physical Compatibility", "", "LTR", next.getString(CookiePolicy.BROWSER_COMPATIBILITY), "", "margin-left:15px;", "");
                            }
                            if (next.getString("stability").length() > 0) {
                                str3 = str3 + MMIVViewerActivityFragment.this.m72383M4("Chemical Stability", "", "LTR", next.getString("stability"), "", "margin-left:15px;", "");
                            }
                            String str10 = str3;
                            String str11 = "<div style=\"display: flex; align-items: center\"><img src=\"" + ("file:///android_asset/" + MMIVViewerActivityFragment.this.m72384N4(next.getString("result")) + ".png") + "\" style=\"margin:2px;width: 100%%;max-width:25px\"/>" + str9 + "</div>";
                            Iterator<Bundle> it3 = it2;
                            MMIVViewerActivityFragment.this.f89563A4 = MMIVViewerActivityFragment.this.f89563A4 + MMIVViewerActivityFragment.this.m72382L4(str11, "", "LTR", str10, "", "margin-left:10px;margin-top:5px", "");
                            MMIVViewerActivityFragment mMIVViewerActivityFragment5 = MMIVViewerActivityFragment.this;
                            mMIVViewerActivityFragment5.m72379I4(str9, mMIVViewerActivityFragment5.f88552Z4);
                            it2 = it3;
                        }
                        return;
                    }
                    String str12 = strArrSplitByWholeSeparator[1];
                    String str13 = strArrSplitByWholeSeparator[2];
                    String str14 = strArrSplitByWholeSeparator[3];
                    String str15 = strArrSplitByWholeSeparator[4];
                    MMIVViewerActivityFragment mMIVViewerActivityFragment6 = MMIVViewerActivityFragment.this;
                    String str16 = "stability";
                    mMIVViewerActivityFragment6.f89579Q4.m71866m(mMIVViewerActivityFragment6.f89566D4, "Update app_state set value=" + str12 + ", title='" + str13 + "' where key='current_agent_id'");
                    MMIVViewerActivityFragment mMIVViewerActivityFragment7 = MMIVViewerActivityFragment.this;
                    mMIVViewerActivityFragment7.f89579Q4.m71866m(mMIVViewerActivityFragment7.f89566D4, "Update app_state set value=" + str14 + ", title='" + str15 + "' where key='current_drug2_id'");
                    MMIVViewerActivityFragment mMIVViewerActivityFragment8 = MMIVViewerActivityFragment.this;
                    StringBuilder sb2 = new StringBuilder();
                    sb2.append(str13);
                    sb2.append(" - ");
                    sb2.append(str15);
                    mMIVViewerActivityFragment8.f89568F4 = sb2.toString();
                    MMIVViewerActivityFragment mMIVViewerActivityFragment9 = MMIVViewerActivityFragment.this;
                    ArrayList<Bundle> arrayListM71817V2 = mMIVViewerActivityFragment9.f89579Q4.m71817V(mMIVViewerActivityFragment9.f89566D4, "Select * from v_drug_drug_intermediate");
                    if (arrayListM71817V2 == null) {
                        arrayListM71817V2 = new ArrayList<>();
                    }
                    Iterator<Bundle> it4 = arrayListM71817V2.iterator();
                    while (it4.hasNext()) {
                        Bundle next2 = it4.next();
                        String str17 = next2.getString("drug1_title") + StringUtils.SPACE + next2.getString("drug1_concentration") + " : " + next2.getString("drug1_vehicle") + " <br/> " + next2.getString("drug2_title") + StringUtils.SPACE + next2.getString("drug2_concentration") + " :";
                        if (next2.getString("storage").length() > 0) {
                            str2 = str4 + MMIVViewerActivityFragment.this.m72383M4("Storage", "", "LTR", next2.getString("storage"), "", "margin-left:15px;", "");
                        } else {
                            str2 = str4;
                        }
                        if (next2.getString("study_period").length() > 0) {
                            str2 = str2 + MMIVViewerActivityFragment.this.m72383M4("Study Period", "", "LTR", next2.getString("study_period"), "", "margin-left:15px;", "");
                        }
                        if (next2.getString(TtmlNode.f29712W).length() > 0) {
                            str2 = str2 + MMIVViewerActivityFragment.this.m72383M4("Container", "", "LTR", next2.getString(TtmlNode.f29712W), "", "margin-left:15px;", "");
                        }
                        if (next2.getString(CookiePolicy.BROWSER_COMPATIBILITY).length() > 0) {
                            str2 = str2 + MMIVViewerActivityFragment.this.m72383M4("Physical Compatibility", "", "LTR", next2.getString(CookiePolicy.BROWSER_COMPATIBILITY), "", "margin-left:15px;", "");
                        }
                        String str18 = str16;
                        if (next2.getString(str18).length() > 0) {
                            str2 = str2 + MMIVViewerActivityFragment.this.m72383M4("Chemical Stability", "", "LTR", next2.getString(str18), "", "margin-left:15px;", "");
                        }
                        String str19 = str2;
                        String str20 = "<div style=\"display: flex; align-items: center\"><img src=\"" + ("file:///android_asset/" + MMIVViewerActivityFragment.this.m72384N4(next2.getString("result")) + ".png") + "\" style=\"margin:2px;width: 100%%;max-width:25px\"/>" + str17 + "</div>";
                        Iterator<Bundle> it5 = it4;
                        MMIVViewerActivityFragment.this.f89563A4 = MMIVViewerActivityFragment.this.f89563A4 + MMIVViewerActivityFragment.this.m72382L4(str20, "", "LTR", str19, "", "margin-left:10px;margin-top:5px", "");
                        MMIVViewerActivityFragment mMIVViewerActivityFragment10 = MMIVViewerActivityFragment.this;
                        mMIVViewerActivityFragment10.m72379I4(str17, mMIVViewerActivityFragment10.f88552Z4);
                        it4 = it5;
                        str4 = str4;
                        str16 = str18;
                    }
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVViewerActivityFragment.2
                @Override // java.lang.Runnable
                public void run() throws IOException {
                    MMIVViewerActivityFragment mMIVViewerActivityFragment = MMIVViewerActivityFragment.this;
                    String strM72817d4 = mMIVViewerActivityFragment.m72817d4(mMIVViewerActivityFragment.m15366r(), "MMHeader.css");
                    MMIVViewerActivityFragment mMIVViewerActivityFragment2 = MMIVViewerActivityFragment.this;
                    String strM72817d42 = mMIVViewerActivityFragment2.m72817d4(mMIVViewerActivityFragment2.m15366r(), "MMFooter.css");
                    String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", MMIVViewerActivityFragment.this.f89568F4).replace("[include]", "");
                    String strM71753g1 = CompressHelper.m71753g1(MMIVViewerActivityFragment.this.f89566D4, "base");
                    MMIVViewerActivityFragment.this.f89563A4 = strReplace + MMIVViewerActivityFragment.this.f89563A4 + strM72817d42;
                    MMIVViewerActivityFragment.this.m72826m3();
                    MMIVViewerActivityFragment mMIVViewerActivityFragment3 = MMIVViewerActivityFragment.this;
                    mMIVViewerActivityFragment3.m72795O3(mMIVViewerActivityFragment3.f89563A4, strM71753g1);
                    MMIVViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                }
            });
            m72836s4();
        }
        m72831p4();
        m15358o2(false);
        m72786G3();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f88551Y4);
            bundle.putString("TitleProperty", "label");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

package net.imedicaldoctor.imd.Fragments.VisualDDX;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.fragment.app.Fragment;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class VDDxResultActivity extends iMDActivity {

    public static class PlaceholderFragment extends Fragment {
        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            return layoutInflater.inflate(C5562R.layout.fragment_vddx_result, viewGroup, false);
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_vddx_result);
        if (bundle == null) {
            m15416k0().m15664u().m15818f(C5562R.id.container, new PlaceholderFragment()).mo15164r();
        }
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        return true;
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        menuItem.getItemId();
        return super.onOptionsItemSelected(menuItem);
    }
}

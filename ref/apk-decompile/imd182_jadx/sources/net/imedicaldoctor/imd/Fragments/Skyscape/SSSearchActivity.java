package net.imedicaldoctor.imd.Fragments.Skyscape;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.tool.xml.html.HTML;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Fragments.Skyscape.SSTocActivity;
import net.imedicaldoctor.imd.ViewHolders.MessageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleSearchContentViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.iMDActivity;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class SSSearchActivity extends iMDActivity {

    public static class SSSearchFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private int f88856A4;

        /* renamed from: B4 */
        private Bundle f88857B4;

        /* renamed from: C4 */
        private SkyscapeContentSearchAdapter f88858C4;

        /* renamed from: D4 */
        private String f88859D4;

        public class SkyscapeAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f88864d;

            /* renamed from: e */
            public ArrayList<Bundle> f88865e;

            public SkyscapeAdapter(Context context, ArrayList<Bundle> arrayList) {
                this.f88864d = context;
                this.f88865e = arrayList;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                final Bundle bundle = this.f88865e.get(i2);
                rippleTextViewHolder.f101515I.setText(this.f88865e.get(i2).getString(SSSearchFragment.this.f88856A4 == 0 ? "name" : SSSearchFragment.this.f88856A4 == 1 ? "Name" : SSSearchFragment.this.f88856A4 == 2 ? "title" : ""));
                rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity.SSSearchFragment.SkyscapeAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        SkyscapeAdapter.this.mo72479d0(bundle, i2);
                    }
                });
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                return new RippleTextViewHolder(LayoutInflater.from(this.f88864d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                ArrayList<Bundle> arrayList = this.f88865e;
                if (arrayList == null) {
                    return 0;
                }
                return arrayList.size();
            }

            /* renamed from: d0 */
            public void mo72479d0(Bundle bundle, int i2) {
            }
        }

        public class SkyscapeContentSearchAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f88870d;

            /* renamed from: e */
            public ArrayList<Bundle> f88871e;

            /* renamed from: f */
            public String f88872f;

            /* renamed from: g */
            public String f88873g;

            public SkyscapeContentSearchAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2) {
                this.f88870d = context;
                this.f88871e = arrayList;
                this.f88872f = str;
                this.f88873g = str2;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                String string;
                String string2;
                ArrayList<Bundle> arrayList = this.f88871e;
                if (arrayList == null || arrayList.size() == 0) {
                    return;
                }
                RippleSearchContentViewHolder rippleSearchContentViewHolder = (RippleSearchContentViewHolder) viewHolder;
                final Bundle bundle = this.f88871e.get(i2);
                if (SSSearchFragment.this.f88856A4 == 0) {
                    string = bundle.getString("Name");
                    string2 = bundle.getString("indexType");
                } else {
                    if (SSSearchFragment.this.f88856A4 == 1) {
                        string = bundle.getString("Name");
                    } else if (SSSearchFragment.this.f88856A4 == 2) {
                        string = bundle.getString("title");
                    } else {
                        string = "";
                        string2 = string;
                    }
                    string2 = "";
                }
                rippleSearchContentViewHolder.f101479I.setText(string);
                if (string2.length() > 0) {
                    rippleSearchContentViewHolder.f101480J.setVisibility(0);
                    rippleSearchContentViewHolder.f101480J.setText(string2);
                } else {
                    rippleSearchContentViewHolder.f101480J.setVisibility(8);
                }
                rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity.SSSearchFragment.SkyscapeContentSearchAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        SkyscapeContentSearchAdapter.this.mo72480d0(bundle, i2);
                    }
                });
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                ArrayList<Bundle> arrayList = this.f88871e;
                if (arrayList != null && arrayList.size() != 0) {
                    return new RippleSearchContentViewHolder(LayoutInflater.from(this.f88870d).inflate(C5562R.layout.list_view_item_search_content_ripple, viewGroup, false));
                }
                return new MessageViewHolder(this.f88870d, LayoutInflater.from(this.f88870d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                ArrayList<Bundle> arrayList = this.f88871e;
                if (arrayList == null || arrayList.size() == 0) {
                    return 1;
                }
                return this.f88871e.size();
            }

            /* renamed from: d0 */
            public void mo72480d0(Bundle bundle, int i2) {
            }

            /* renamed from: e0 */
            public void m72481e0(ArrayList<Bundle> arrayList) {
                this.f88871e = arrayList;
                m27491G();
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: k3 */
        public Bundle m72478k3(Bundle bundle) {
            String str;
            String str2;
            Bundle bundle2 = new Bundle();
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(bundle.getString("docId"), "|");
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(bundle.getString(HTML.Tag.f74369V), "|");
            for (int i2 = 0; i2 < strArrSplitByWholeSeparator.length; i2++) {
                if (strArrSplitByWholeSeparator2.length > i2) {
                    str = strArrSplitByWholeSeparator[i2];
                    str2 = strArrSplitByWholeSeparator2[i2];
                } else {
                    str = strArrSplitByWholeSeparator[i2];
                    str2 = "";
                }
                bundle2.putString(str, str2);
            }
            return bundle2;
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72462O2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            CompressHelper compressHelper;
            Bundle bundle2;
            String str;
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72462O2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            this.f88857B4 = (m15387y() == null || !m15387y().containsKey("SelectedItem")) ? null : m15387y().getBundle("SelectedItem");
            if (m15387y() == null || !m15387y().containsKey("Mode")) {
                this.f88856A4 = 0;
            } else {
                this.f88856A4 = m15387y().getInt("Mode");
            }
            ArrayList<Bundle> arrayList = new ArrayList<>();
            int i2 = this.f88856A4;
            if (i2 == 0) {
                arrayList = this.f88791k4.m71817V(this.f88788h4, "select * from indexType");
                ArrayList<Bundle> arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "select * from sqlite_master where name='TOC'");
                if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                    Bundle bundle3 = new Bundle();
                    bundle3.putString("id", "-1");
                    bundle3.putString("name", "Table Of Contents");
                    arrayList.add(bundle3);
                }
            } else {
                if (i2 == 1) {
                    compressHelper = this.f88791k4;
                    bundle2 = this.f88788h4;
                    str = "select * from indexes where indexTypeId=" + this.f88857B4.getString("id");
                } else if (i2 == 2) {
                    compressHelper = this.f88791k4;
                    bundle2 = this.f88788h4;
                    str = "select * from document where id in (" + this.f88857B4.getString("docId").replace("|", ",") + ")";
                }
                arrayList = compressHelper.m71817V(bundle2, str);
            }
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            if (this.f88856A4 == 0) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity.SSSearchFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            this.f88792l4 = new SkyscapeAdapter(m15366r(), arrayList) { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity.SSSearchFragment.2
                @Override // net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity.SSSearchFragment.SkyscapeAdapter
                /* renamed from: d0 */
                public void mo72479d0(Bundle bundle4, int i3) {
                    SSSearchFragment.this.m72468V2();
                    if (SSSearchFragment.this.f88856A4 == 0) {
                        if (bundle4.getString("id").equals("-1")) {
                            Bundle bundle5 = new Bundle();
                            bundle5.putBundle("DB", SSSearchFragment.this.f88788h4);
                            SSSearchFragment.this.f88791k4.m71798N(SSTocActivity.class, SSTocActivity.SSTocFragment.class, bundle5);
                            return;
                        } else {
                            Bundle bundle6 = new Bundle();
                            bundle6.putBundle("SelectedItem", bundle4);
                            bundle6.putBundle("DB", SSSearchFragment.this.f88788h4);
                            bundle6.putInt("Mode", 1);
                            SSSearchFragment.this.f88791k4.m71798N(SSSearchActivity.class, SSSearchFragment.class, bundle6);
                            return;
                        }
                    }
                    if (SSSearchFragment.this.f88856A4 != 1) {
                        if (SSSearchFragment.this.f88856A4 == 2) {
                            SSSearchFragment sSSearchFragment = SSSearchFragment.this;
                            sSSearchFragment.f88791k4.m71772A1(sSSearchFragment.f88788h4, bundle4.getString("id"), null, bundle4.getString("id"));
                            return;
                        }
                        return;
                    }
                    if (!bundle4.getString("docId").contains("|")) {
                        SSSearchFragment sSSearchFragment2 = SSSearchFragment.this;
                        sSSearchFragment2.f88791k4.m71772A1(sSSearchFragment2.f88788h4, bundle4.getString("docId"), null, bundle4.getString(HTML.Tag.f74369V));
                        return;
                    }
                    Bundle bundleM72478k3 = SSSearchFragment.this.m72478k3(bundle4);
                    Bundle bundle7 = new Bundle();
                    bundle7.putBundle("SelectedItem", bundle4);
                    bundle7.putBundle("DB", SSSearchFragment.this.f88788h4);
                    bundle7.putInt("Mode", 2);
                    bundle7.putBundle("GotoSections", bundleM72478k3);
                    SSSearchFragment.this.f88791k4.m71798N(SSSearchActivity.class, SSSearchFragment.class, bundle7);
                }
            };
            this.f88858C4 = new SkyscapeContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity.SSSearchFragment.3
                @Override // net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity.SSSearchFragment.SkyscapeContentSearchAdapter
                /* renamed from: d0 */
                public void mo72480d0(Bundle bundle4, int i3) {
                    SSSearchFragment.this.m72468V2();
                    if (SSSearchFragment.this.f88856A4 != 0 && SSSearchFragment.this.f88856A4 != 1) {
                        if (SSSearchFragment.this.f88856A4 == 2) {
                            SSSearchFragment sSSearchFragment = SSSearchFragment.this;
                            sSSearchFragment.f88791k4.m71772A1(sSSearchFragment.f88788h4, bundle4.getString("id"), null, bundle4.getString("id"));
                            return;
                        }
                        return;
                    }
                    if (!bundle4.getString("docId").contains("|")) {
                        SSSearchFragment sSSearchFragment2 = SSSearchFragment.this;
                        sSSearchFragment2.f88791k4.m71772A1(sSSearchFragment2.f88788h4, bundle4.getString("docId"), null, bundle4.getString(HTML.Tag.f74369V));
                        return;
                    }
                    Bundle bundle5 = new Bundle();
                    bundle5.putBundle("SelectedItem", bundle4);
                    bundle5.putBundle("DB", SSSearchFragment.this.f88788h4);
                    bundle5.putInt("Mode", 2);
                    bundle5.putBundle("GotoSections", SSSearchFragment.this.m72478k3(bundle4));
                    SSSearchFragment.this.f88791k4.m71798N(SSSearchActivity.class, SSSearchFragment.class, bundle5);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(true);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: X2 */
        public void mo71973X2() {
            this.f88858C4.m72481e0(this.f88795o4);
            this.f88803w4.setAdapter(this.f88858C4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            String string;
            StringBuilder sb;
            String str2;
            int i2 = this.f88856A4;
            if (i2 == 0) {
                sb = new StringBuilder();
                sb.append("Select rowid as _id,Id as docId, indexName as Name,indexType,section  from search where indexName match '");
                sb.append(str);
                str2 = "*'";
            } else if (i2 == 1) {
                sb = new StringBuilder();
                sb.append("Select rowid as _id,Id as docId, indexName as Name,indexType,section from search where search match 'indexName:");
                sb.append(str);
                sb.append("* AND indexType:");
                sb.append(this.f88857B4.getString("name"));
                str2 = "'";
            } else {
                if (i2 != 2) {
                    string = "";
                    return this.f88791k4.m71817V(this.f88788h4, string);
                }
                sb = new StringBuilder();
                sb.append("select * from document where id in (");
                sb.append(this.f88857B4.getString("docId").replace("|", ","));
                sb.append(") and lower(title) like '");
                sb.append(str.toLowerCase());
                str2 = "%'";
            }
            sb.append(str2);
            string = sb.toString();
            return this.f88791k4.m71817V(this.f88788h4, string);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new SSSearchFragment());
    }
}

package net.imedicaldoctor.imd.Fragments.VisualDDX;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextArrowViewHolder;
import net.imedicaldoctor.imd.iMDActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.json.JSONArray;

/* loaded from: classes3.dex */
public class VDDxScenarioActivity extends iMDActivity {

    public static class VDDxScenarioFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private ListAdapter f89790A4;

        /* renamed from: B4 */
        private int f89791B4;

        public class DatabaseHeaderViewHolder {

            /* renamed from: a */
            public final TextView f89800a;

            public DatabaseHeaderViewHolder(View view) {
                this.f89800a = (TextView) view.findViewById(C5562R.id.header_text);
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            mo71990Q2();
            this.f88799s4.setVisibility(8);
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            String strM71753g1 = CompressHelper.m71753g1(this.f88788h4, "focusAreaList.json");
            final CompressHelper compressHelper = new CompressHelper(m15366r());
            try {
                ArrayList<Bundle> arrayListM71782F = compressHelper.m71782F(new JSONArray(CompressHelper.m71750e2(new File(strM71753g1))));
                AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
                final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
                if (m15387y() == null || !m15387y().containsKey("Parent")) {
                    appBarLayout.m35746D(true, false);
                    relativeLayout.setVisibility(0);
                    this.f89791B4 = 0;
                } else {
                    this.f89791B4 = m15387y().getInt("Parent");
                    appBarLayout.m35746D(false, false);
                    appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxScenarioActivity.VDDxScenarioFragment.1
                        @Override // java.lang.Runnable
                        public void run() {
                            relativeLayout.setVisibility(0);
                        }
                    }, 800L);
                }
                this.f89791B4--;
                ArrayList arrayList = new ArrayList();
                int i2 = this.f89791B4;
                if (i2 == -1) {
                    Bundle bundle2 = new Bundle();
                    bundle2.putString("title", "Select A Clinical Scenario");
                    bundle2.putParcelableArrayList("items", arrayListM71782F);
                    arrayList.add(bundle2);
                } else {
                    int size = arrayListM71782F.get(i2).getParcelableArrayList("children").size();
                    for (int i3 = 0; i3 < size; i3++) {
                        Bundle bundle3 = new Bundle();
                        Bundle bundle4 = (Bundle) arrayListM71782F.get(this.f89791B4).getParcelableArrayList("children").get(i3);
                        bundle3.putString("title", bundle4.getString("name"));
                        bundle3.putParcelableArrayList("items", bundle4.getParcelableArrayList("children"));
                        arrayList.add(bundle3);
                    }
                }
                this.f88792l4 = new NotStickySectionAdapter(m15366r(), arrayList, "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxScenarioActivity.VDDxScenarioFragment.2
                    @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                    /* renamed from: f0 */
                    public void mo72200f0(RecyclerView.ViewHolder viewHolder, final Bundle bundle5, int i4) {
                        MaterialRippleLayout materialRippleLayout;
                        View.OnClickListener onClickListener;
                        RippleTextArrowViewHolder rippleTextArrowViewHolder = (RippleTextArrowViewHolder) viewHolder;
                        rippleTextArrowViewHolder.f101482I.setText(bundle5.getString("name"));
                        if (bundle5.getString("moduleId").equals("-1")) {
                            rippleTextArrowViewHolder.f101484K.setVisibility(0);
                            materialRippleLayout = rippleTextArrowViewHolder.f101483J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxScenarioActivity.VDDxScenarioFragment.2.1
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    Bundle bundle6 = new Bundle();
                                    bundle6.putInt("Parent", bundle5.getInt("Index") + 1);
                                    bundle6.putBundle("DB", VDDxScenarioFragment.this.f88788h4);
                                    compressHelper.m71798N(VDDxScenarioActivity.class, VDDxScenarioFragment.class, bundle6);
                                }
                            };
                        } else {
                            rippleTextArrowViewHolder.f101484K.setVisibility(8);
                            materialRippleLayout = rippleTextArrowViewHolder.f101483J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxScenarioActivity.VDDxScenarioFragment.2.2
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    Bundle bundle6 = new Bundle();
                                    bundle6.putBundle("moduleInfo", bundle5);
                                    C52552 c52552 = C52552.this;
                                    compressHelper.m71775B1(VDDxScenarioFragment.this.f88788h4, bundle5.getString("moduleId"), null, null, bundle6);
                                }
                            };
                        }
                        materialRippleLayout.setOnClickListener(onClickListener);
                    }

                    @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                    /* renamed from: k0 */
                    public RecyclerView.ViewHolder mo72202k0(View view) {
                        return new RippleTextArrowViewHolder(view);
                    }
                };
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("JSON error", "Error in Parsing " + strM71753g1);
            }
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(false);
            return viewInflate;
        }

        /* renamed from: i3 */
        public Bundle m72892i3(int i2, ArrayList<Bundle> arrayList) {
            Iterator<Bundle> it2 = arrayList.iterator();
            int i3 = 0;
            while (it2.hasNext()) {
                Bundle next = it2.next();
                if (i2 == i3) {
                    Bundle bundle = new Bundle();
                    bundle.putString("Title", next.getString("title"));
                    return bundle;
                }
                int size = i3 + next.getParcelableArrayList("items").size();
                if (i2 <= size) {
                    int size2 = (i2 - (size - next.getParcelableArrayList("items").size())) - 1;
                    Bundle bundle2 = new Bundle();
                    bundle2.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                    bundle2.putInt("Index", size2);
                    return bundle2;
                }
                i3 = size + 1;
            }
            return null;
        }

        /* renamed from: j3 */
        public int m72893j3(ArrayList<Bundle> arrayList) {
            int size = 0;
            if (arrayList == null) {
                return 0;
            }
            Iterator<Bundle> it2 = arrayList.iterator();
            while (it2.hasNext()) {
                size = size + it2.next().getParcelableArrayList("items").size() + 1;
            }
            return size;
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new VDDxScenarioFragment());
    }
}

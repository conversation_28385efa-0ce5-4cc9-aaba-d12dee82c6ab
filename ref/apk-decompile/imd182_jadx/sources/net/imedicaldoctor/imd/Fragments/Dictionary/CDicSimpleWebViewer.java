package net.imedicaldoctor.imd.Fragments.Dictionary;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class CDicSimpleWebViewer extends ViewerHelperActivity {

    public static class CDicSimpleWebViewerFragment extends ViewerHelperFragment {
        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_general_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (m15387y() == null) {
                return viewInflate;
            }
            this.f89569G4.loadUrl(this.f89567E4.split("-")[1]);
            m72836s4();
            m15358o2(false);
            m15366r().setTitle("");
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
            if (!str2.equals("ldoce")) {
                return false;
            }
            CompressHelper compressHelper = new CompressHelper(m15366r());
            String strSubstring = StringUtils.splitByWholeSeparator(str3, "?")[0].substring(2);
            Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71819W(this.f89566D4, "select * from LongMean where word='" + strSubstring + "'", "LongMean.db"));
            if (bundleM71907z == null) {
                return true;
            }
            compressHelper.m71772A1(this.f89566D4, "EE-5,,,,," + bundleM71907z.getString("id") + ",,,,," + bundleM71907z.getString("word"), null, null);
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new CDicSimpleWebViewerFragment());
    }
}

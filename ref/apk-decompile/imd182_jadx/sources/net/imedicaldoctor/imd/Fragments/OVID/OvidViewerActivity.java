package net.imedicaldoctor.imd.Fragments.OVID;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.html.HTML;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class OvidViewerActivity extends ViewerHelperActivity {

    public static class OvidViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private String f88718X4;

        /* renamed from: Y4 */
        private MenuItem f88719Y4;

        /* renamed from: Z4 */
        public ArrayList<Bundle> f88720Z4;

        /* renamed from: a5 */
        private Bundle f88721a5;

        /* renamed from: b5 */
        private boolean f88722b5;

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: I4 */
        public void m72431I4() {
            String str;
            String[] strArr;
            ArrayList arrayList;
            int i2;
            String str2;
            String str3;
            String str4;
            ArrayList<Bundle> arrayList2;
            String strM71754h1;
            CompressHelper compressHelper = new CompressHelper(m15366r());
            ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(this.f89566D4, "Select rowid as _id, * from images where bookid='" + this.f89567E4 + "'");
            ArrayList<Bundle> arrayList3 = new ArrayList<>();
            ArrayList arrayList4 = new ArrayList();
            if (arrayListM71817V == null) {
                arrayListM71817V = new ArrayList<>();
            }
            Iterator<Bundle> it2 = arrayListM71817V.iterator();
            while (true) {
                str = "ImagePath";
                String str5 = "base";
                if (!it2.hasNext()) {
                    break;
                }
                Bundle next = it2.next();
                String[] strArrSplit = StringUtils.split(next.getString("imagename"), "|");
                Iterator<Bundle> it3 = it2;
                int length = strArrSplit.length;
                ArrayList<Bundle> arrayList5 = arrayList3;
                int i3 = 0;
                while (i3 < length) {
                    int i4 = length;
                    String str6 = strArrSplit[i3];
                    String[] strArr2 = strArrSplit;
                    String strM71754h12 = CompressHelper.m71754h1(this.f89566D4, str6, str5);
                    int i5 = i3;
                    Bundle bundle = new Bundle();
                    File file = new File(strM71754h12);
                    if (file.exists()) {
                        ArrayList arrayList6 = arrayList4;
                        try {
                            strM71754h1 = CompressHelper.m71754h1(this.f89566D4, str6, str5);
                            str4 = str5;
                        } catch (Exception e2) {
                            e = e2;
                            str4 = str5;
                        }
                        try {
                            if (!new File(strM71754h1).exists()) {
                                CompressHelper.m71728D2(new File(strM71754h1), compressHelper.m71899w(CompressHelper.m71748d2(file), file.getName(), "127"));
                            }
                        } catch (Exception e3) {
                            e = e3;
                            FirebaseCrashlytics.m48010d().m48016g(e);
                            iMDLogger.m73550f("LoadResourcesOther", "Error in loading " + strM71754h12 + " . error " + e.getLocalizedMessage());
                            e.printStackTrace();
                            bundle.putString("ImagePath", strM71754h12);
                            bundle.putString("Description", next.getString("descriptionSimple"));
                            bundle.putString("id", str6);
                            arrayList4 = arrayList6;
                            arrayList4.add(str6);
                            arrayList2 = arrayList5;
                            arrayList2.add(bundle);
                            i3 = i5 + 1;
                            arrayList5 = arrayList2;
                            length = i4;
                            strArrSplit = strArr2;
                            str5 = str4;
                        }
                        bundle.putString("ImagePath", strM71754h12);
                        bundle.putString("Description", next.getString("descriptionSimple"));
                        bundle.putString("id", str6);
                        arrayList4 = arrayList6;
                        arrayList4.add(str6);
                        arrayList2 = arrayList5;
                        arrayList2.add(bundle);
                    } else {
                        str4 = str5;
                        arrayList2 = arrayList5;
                    }
                    i3 = i5 + 1;
                    arrayList5 = arrayList2;
                    length = i4;
                    strArrSplit = strArr2;
                    str5 = str4;
                }
                it2 = it3;
                arrayList3 = arrayList5;
            }
            String str7 = "base";
            this.f88720Z4 = arrayList3;
            if (this.f88718X4.length() == 0) {
                return;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(this.f88718X4, "|");
            int length2 = strArrSplitByWholeSeparator.length;
            int i6 = 0;
            while (i6 < length2) {
                String str8 = strArrSplitByWholeSeparator[i6];
                String strM71754h13 = CompressHelper.m71754h1(this.f89566D4, str8, "Resources-E");
                File file2 = new File(strM71754h13);
                if (arrayList4.contains(str8)) {
                    strArr = strArrSplitByWholeSeparator;
                    arrayList = arrayList4;
                    i2 = length2;
                    str2 = str;
                    str3 = str7;
                } else {
                    strArr = strArrSplitByWholeSeparator;
                    Bundle bundle2 = new Bundle();
                    arrayList = arrayList4;
                    i2 = length2;
                    str3 = str7;
                    String strM71754h14 = CompressHelper.m71754h1(this.f89566D4, str8, str3);
                    bundle2.putString(str, strM71754h14);
                    str2 = str;
                    bundle2.putString("Description", "");
                    bundle2.putString("id", str8);
                    File file3 = new File(strM71754h14);
                    if (!file3.exists() ? !(!file2.exists() || file2.length() <= 5000) : file3.length() > 5000) {
                        this.f88720Z4.add(bundle2);
                    }
                }
                if (file2.exists()) {
                    try {
                        String strM71754h15 = CompressHelper.m71754h1(this.f89566D4, str8, str3);
                        if (!new File(strM71754h15).exists()) {
                            CompressHelper.m71728D2(new File(strM71754h15), compressHelper.m71899w(CompressHelper.m71748d2(file2), file2.getName(), "127"));
                        }
                    } catch (Exception e4) {
                        FirebaseCrashlytics.m48010d().m48016g(e4);
                        iMDLogger.m73550f("LoadResourcesOther", "Error in loading " + strM71754h13 + " . error " + e4.getLocalizedMessage());
                        e4.printStackTrace();
                    }
                }
                i6++;
                str7 = str3;
                strArrSplitByWholeSeparator = strArr;
                length2 = i2;
                str = str2;
                arrayList4 = arrayList;
            }
            MenuItem menuItem = this.f88719Y4;
            if (menuItem != null) {
                menuItem.setVisible(this.f88720Z4.size() != 0);
            }
        }

        /* renamed from: N4 */
        private void m72435N4(String str) {
            if (str.contains("?")) {
                str = str.split("\\?")[0];
            }
            ArrayList<Bundle> arrayList = this.f88720Z4;
            if (arrayList == null || arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
                return;
            }
            int i2 = 0;
            for (int i3 = 0; i3 < this.f88720Z4.size(); i3++) {
                if (this.f88720Z4.get(i3).getString("id").equals(str)) {
                    i2 = i3;
                }
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", this.f88720Z4);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        /* renamed from: M4 */
        public void m72436M4(Bundle bundle) {
            CompressHelper compressHelper = new CompressHelper(m15366r());
            Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "Select * from Docs Where bookid = '" + this.f89567E4 + "'"));
            if (bundle.getString(HTML.Tag.f74369V).length() > 0) {
                this.f89569G4.m73433g("showSection('" + bundle.getString(HTML.Tag.f74369V) + "');");
                return;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(bundle.getString("xpath").replace(bundleM71890s1.getString("xpath"), ""), "/");
            this.f89569G4.m73433g("jump = document;");
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("[")) {
                    this.f89569G4.m73433g("" + ("jump = jump.getElementsByClassName('" + str.substring(0, str.indexOf("[")) + "')[" + String.valueOf(Integer.valueOf(CompressHelper.m71751f(str, "[", "]")).intValue() - 1) + "];"));
                }
            }
            this.f89569G4.m73433g("jump.scrollIntoView(true);");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            ArrayList<Bundle> arrayList;
            Bundle bundleM72839v3;
            if (this.f88720Z4.size() <= 0 || (arrayList = this.f88720Z4) == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f88720Z4)) == null) {
                return null;
            }
            return bundleM72839v3.getString("ImagePath");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.elsviewer, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f88718X4 = bundle.getString("mResources");
                this.f88720Z4 = bundle.getParcelableArrayList("mImages");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidViewerActivity.OvidViewerFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        CompressHelper compressHelper = new CompressHelper(OvidViewerFragment.this.m15366r());
                        String str = OvidViewerFragment.this.f89563A4;
                        if (str == null || str.length() == 0) {
                            if (OvidViewerFragment.this.m15387y() != null && OvidViewerFragment.this.m15387y().containsKey("gotoSection")) {
                                OvidViewerFragment ovidViewerFragment = OvidViewerFragment.this;
                                ovidViewerFragment.f88721a5 = ovidViewerFragment.m15387y().getBundle("gotoSection");
                            }
                            Bundle bundleM71844e0 = compressHelper.m71844e0(OvidViewerFragment.this.f89566D4, "Select * from Docs Where bookid = '" + OvidViewerFragment.this.f89567E4 + "'");
                            if (bundleM71844e0 == null) {
                                OvidViewerFragment.this.f89595p4 = "Document doesn't exist";
                                return;
                            }
                            String string = bundleM71844e0.getString("mainContent");
                            OvidViewerFragment.this.f88718X4 = bundleM71844e0.getString("resources");
                            OvidViewerFragment.this.f89568F4 = bundleM71844e0.getString("name");
                            String str2 = new String(compressHelper.m71897v(string, bundleM71844e0.getString("bookid"), "127"));
                            OvidViewerFragment ovidViewerFragment2 = OvidViewerFragment.this;
                            String strM72817d4 = ovidViewerFragment2.m72817d4(ovidViewerFragment2.m15366r(), "OVIDHeader.css");
                            OvidViewerFragment ovidViewerFragment3 = OvidViewerFragment.this;
                            String strM72817d42 = ovidViewerFragment3.m72817d4(ovidViewerFragment3.m15366r(), "OVIDFooter.css");
                            String strReplace = (strM72817d4.replace("[size]", "200").replace("[title]", OvidViewerFragment.this.f89568F4) + str2 + strM72817d42).replace("<div class=\"FG\"", "<div class=\"FG\" style=\"width:100%;overflow: scroll;\"").replace("<div class=\"TB\"", "<div class=\"TB\" style=\"width:100%;overflow: scroll;\"").replace("<div class=\"MATH\"", "<div class=\"MATH\" style=\"width:100%;overflow: scroll;\"");
                            OvidViewerFragment.this.m72826m3();
                            OvidViewerFragment ovidViewerFragment4 = OvidViewerFragment.this;
                            ovidViewerFragment4.f89563A4 = strReplace;
                            ovidViewerFragment4.m72431I4();
                        }
                        if (!compressHelper.m71903x1()) {
                            OvidViewerFragment.this.m72827m4("Chapter");
                        }
                        OvidViewerFragment.this.m72820h3(CompressHelper.m71753g1(OvidViewerFragment.this.f89566D4, "base"));
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        OvidViewerFragment.this.f89595p4 = e2.getLocalizedMessage();
                    }
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidViewerActivity.OvidViewerFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    String str = OvidViewerFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        OvidViewerFragment ovidViewerFragment = OvidViewerFragment.this;
                        ovidViewerFragment.m72780C4(ovidViewerFragment.f89595p4);
                        return;
                    }
                    String strM71753g1 = CompressHelper.m71753g1(OvidViewerFragment.this.f89566D4, "base");
                    OvidViewerFragment ovidViewerFragment2 = OvidViewerFragment.this;
                    ovidViewerFragment2.m72795O3(ovidViewerFragment2.f89563A4, strM71753g1);
                    OvidViewerFragment.this.m72836s4();
                    OvidViewerFragment.this.m72831p4();
                    OvidViewerFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                    OvidViewerFragment.this.m15358o2(false);
                    OvidViewerFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: Z3 */
        public void mo71956Z3(WebView webView, String str) {
            super.mo71956Z3(webView, str);
            Bundle bundle = this.f88721a5;
            if (bundle != null) {
                m72436M4(bundle);
                this.f88721a5 = null;
            }
            this.f89569G4.m73433g("fixAllImages2();");
            this.f89569G4.m73433g("ConvertAllImages();");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_gallery) {
                m72435N4("soheilvb");
            }
            if (itemId == C5562R.id.action_menu) {
                CompressHelper compressHelper = new CompressHelper(m15366r());
                Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "Select id from toc where bookid='" + this.f89567E4 + "' AND section='" + this.f89567E4 + "'"));
                OvidSectionsViewer ovidSectionsViewer = new OvidSectionsViewer();
                Bundle bundle = new Bundle();
                bundle.putBundle("db", this.f89566D4);
                bundle.putString("parentId", bundleM71890s1.getString("id"));
                ovidSectionsViewer.m15342i2(bundle);
                ovidSectionsViewer.mo15218Z2(true);
                ovidSectionsViewer.m15245A2(this, 0);
                ovidSectionsViewer.mo15222e3(m15283M(), "OvidSectionsViewer");
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            MenuItem menuItemFindItem = menu.findItem(C5562R.id.action_gallery);
            this.f88719Y4 = menuItemFindItem;
            ArrayList<Bundle> arrayList = this.f88720Z4;
            menuItemFindItem.setVisible((arrayList == null || arrayList.size() == 0) ? false : true);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            CompressHelper compressHelper = new CompressHelper(m15366r());
            if (str2.equals("image")) {
                try {
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, "/");
                    m72435N4(strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1]);
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
                return true;
            }
            if (str2.equals("chapter")) {
                String strSubstring = str3.substring(2);
                if (strSubstring.startsWith("PG")) {
                    ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(this.f89566D4, "Select * from pages where pagenum=" + strSubstring.substring(2));
                    if (arrayListM71817V.size() == 0) {
                        return true;
                    }
                    compressHelper.m71772A1(this.f89566D4, arrayListM71817V.get(0).getString("bookid"), null, strSubstring);
                } else {
                    compressHelper.m71772A1(this.f89566D4, strSubstring, null, strSubstring);
                }
            }
            String str4 = str3 + "&";
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str4, "/");
            if (!str4.contains("Book+Image") && strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1].charAt(0) == '#') {
                String strReplace = strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1].substring(1).replace("&", "");
                this.f89569G4.m73433g("showSection('" + strReplace + "')");
            }
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new OvidViewerFragment(), bundle);
    }
}

package net.imedicaldoctor.imd.Fragments.UWorld;

import android.os.Bundle;
import java.util.ArrayList;
import net.imedicaldoctor.imd.Fragments.Martindale.MDListActivity;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;

/* loaded from: classes3.dex */
public class UWTocActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public String f89431A4;

    /* renamed from: B4 */
    public String f89432B4 = "";

    /* renamed from: C4 */
    public ArrayList<String> f89433C4;

    /* renamed from: D4 */
    private Boolean f89434D4;

    /* renamed from: E4 */
    private Boolean f89435E4;

    /* renamed from: F4 */
    public String f89436F4;

    /* JADX WARN: Removed duplicated region for block: B:54:0x02a2  */
    /* JADX WARN: Removed duplicated region for block: B:56:0x02aa  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x02b6  */
    /* JADX WARN: Removed duplicated region for block: B:62:0x02c5 A[LOOP:0: B:60:0x02bf->B:62:0x02c5, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:65:0x02df  */
    /* JADX WARN: Removed duplicated region for block: B:70:0x02fd  */
    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public android.view.View mo15303U0(android.view.LayoutInflater r15, android.view.ViewGroup r16, android.os.Bundle r17) throws android.content.res.Resources.NotFoundException {
        /*
            Method dump skipped, instructions count: 827
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.UWorld.UWTocActivityFragment.mo15303U0(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88793m4.m73469f0(this.f88795o4);
        this.f88803w4.setAdapter(this.f88793m4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return this.f89432B4;
    }

    /* renamed from: i3 */
    public void m72695i3(Bundle bundle, int i2) {
        m72468V2();
        if (!bundle.getString("docId").equals("0")) {
            this.f88791k4.m71772A1(this.f88788h4, bundle.getString("docId"), null, null);
            return;
        }
        Bundle bundle2 = new Bundle();
        bundle2.putBundle("DB", this.f88788h4);
        bundle2.putString("ParentId", bundle.getString("id"));
        this.f88791k4.m71798N(MDListActivity.class, UWTocActivityFragment.class, bundle2);
    }
}

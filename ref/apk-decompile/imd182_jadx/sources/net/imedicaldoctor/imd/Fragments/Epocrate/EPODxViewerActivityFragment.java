package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.css.CSS;
import com.itextpdf.tool.xml.html.HTML;
import java.io.File;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Lexi.LXSectionsViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class EPODxViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public String f88039X4;

    /* renamed from: Y4 */
    public Bundle f88040Y4;

    /* renamed from: Z4 */
    public JSONObject f88041Z4;

    /* renamed from: a5 */
    public int f88042a5;

    /* renamed from: b5 */
    public ArrayList<Bundle> f88043b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f88044c5;

    /* renamed from: O4 */
    private void m72181O4(String str) {
        ArrayList<Bundle> arrayList = this.f88043b5;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        arrayList2.addAll(this.f88043b5);
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (((Bundle) arrayList2.get(i3)).getString("id").startsWith(str)) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: C3 */
    public void mo71967C3(String str) {
        iMDLogger.m73554j("Gotosection", str);
        this.f89569G4.m73433g("document.getElementById(\"" + str + "\").scrollIntoView(true);document.body.scrollTop = window.pageYOffset - 50;");
    }

    /* renamed from: I4 */
    public void m72182I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f88044c5.add(bundle);
    }

    /* renamed from: J4 */
    public String m72183J4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88042a5 + 1;
        this.f88042a5 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded3\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded3(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: K4 */
    public String m72184K4(String str, String str2, String str3, String str4) {
        int i2 = this.f88042a5 + 1;
        this.f88042a5 = i2;
        return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + String.valueOf(i2) + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
    }

    /* renamed from: L4 */
    public String m72185L4(JSONObject jSONObject, int i2) throws JSONException {
        JSONObject jSONObject2;
        String str;
        StringBuilder sb;
        String str2;
        String str3;
        String str4;
        int i3;
        int i4;
        String str5;
        String str6;
        String str7;
        JSONObject jSONObject3;
        String str8;
        String string;
        String str9 = "flag";
        String str10 = "cells";
        String str11 = "sections";
        String str12 = "title";
        try {
            if (jSONObject.has("targetHTML")) {
                return jSONObject.getString("targetHTML");
            }
            String str13 = "";
            if (!jSONObject.has(TypedValues.AttributesType.f5395M)) {
                return "";
            }
            JSONObject jSONObjectM71886r1 = this.f89579Q4.m71886r1(this.f88041Z4.getJSONArray("views"), "id", jSONObject.getString(TypedValues.AttributesType.f5395M));
            if (jSONObjectM71886r1.getString("type").equals("web")) {
                return jSONObjectM71886r1.getString(HTML.Tag.f74425y);
            }
            String string2 = "";
            int i5 = 0;
            while (i5 < jSONObjectM71886r1.getJSONArray(str11).length()) {
                JSONObject jSONObject4 = jSONObjectM71886r1.getJSONArray(str11).getJSONObject(i5);
                String str14 = str13;
                int i6 = 0;
                while (true) {
                    jSONObject2 = jSONObjectM71886r1;
                    str = string2;
                    if (i6 >= jSONObject4.getJSONArray(str10).length()) {
                        break;
                    }
                    JSONObject jSONObject5 = jSONObject4.getJSONArray(str10).getJSONObject(i6);
                    int i7 = i6;
                    String strM72185L4 = m72185L4(jSONObject5, i2 + 1);
                    if (jSONObject5.has(str12)) {
                        JSONObject jSONObject6 = jSONObject4;
                        String string3 = jSONObject5.getString(str12);
                        int i8 = i5;
                        String str15 = str13;
                        if (jSONObject5.has(str9)) {
                            str8 = str12;
                            StringBuilder sb2 = new StringBuilder();
                            str2 = str10;
                            sb2.append("orb");
                            sb2.append(jSONObject5.getString(str9));
                            sb2.append(".png");
                            string3 = "<div style=\"display: flex; align-items: center\"><img src=\"" + ("file:///android_asset/" + sb2.toString()) + "\" style=\"margin:2px;width: 100%%;max-width:25px\"/>" + string3 + "</div>";
                        } else {
                            str8 = str12;
                            str2 = str10;
                        }
                        if (strM72185L4.length() <= 0) {
                            i3 = i7;
                            str6 = str8;
                            str7 = str9;
                            jSONObject3 = jSONObject6;
                            str3 = str11;
                            str5 = str15;
                            str4 = str;
                            i4 = i8;
                            if (jSONObject5.has("targetURL")) {
                                String string4 = jSONObject5.getString("targetURL");
                                String str16 = "file:///android_asset/" + (string4.contains("rx/monograph/") ? "plus_rx.png" : string4.contains("dx/monograph/") ? "plus_dx.png" : string4.contains("lab/monograph/") ? "plus_lab.png" : string4.contains("lab/list/panel/") ? "plus_panel.png" : str5);
                                StringBuilder sb3 = new StringBuilder();
                                sb3.append(str14);
                                sb3.append(m72184K4("<div style=\"display: flex; align-items: center\"><img src=\"" + str16 + "\" style=\"margin:2px;width: 100%%;max-width:25px\"/><a href=\"" + jSONObject5.getString("targetURL") + "\">" + jSONObject5.getString(str6) + "</a></div>", str5, "margin-left: " + (i2 * 5) + CSS.Value.f74124h0, str5));
                                string = sb3.toString();
                            } else {
                                StringBuilder sb4 = new StringBuilder();
                                sb4.append(str14);
                                sb4.append(m72184K4(jSONObject5.getString(str6), str5, "margin-left: " + (i2 * 5) + CSS.Value.f74124h0, str5));
                                string = sb4.toString();
                            }
                        } else if (i2 < 2) {
                            StringBuilder sb5 = new StringBuilder();
                            sb5.append(str14);
                            i3 = i7;
                            str7 = str9;
                            jSONObject3 = jSONObject6;
                            str3 = str11;
                            i4 = i8;
                            str4 = str;
                            str5 = str15;
                            sb5.append(m72187N4(string3, "", "LTR", strM72185L4, "", "margin-left: " + (i2 * 5) + CSS.Value.f74124h0, ""));
                            string = sb5.toString();
                            str6 = str8;
                        } else {
                            i3 = i7;
                            str6 = str8;
                            str7 = str9;
                            jSONObject3 = jSONObject6;
                            str3 = str11;
                            str5 = str15;
                            str4 = str;
                            i4 = i8;
                            StringBuilder sb6 = new StringBuilder();
                            sb6.append(str14);
                            sb6.append(m72183J4(string3, "", "LTR", strM72185L4, "", "margin-left: " + (i2 * 5) + CSS.Value.f74124h0, ""));
                            string = sb6.toString();
                        }
                        str14 = string;
                    } else {
                        str2 = str10;
                        str3 = str11;
                        str4 = str;
                        i3 = i7;
                        i4 = i5;
                        str5 = str13;
                        str6 = str12;
                        str7 = str9;
                        jSONObject3 = jSONObject4;
                        str14 = str14 + strM72185L4;
                    }
                    i6 = i3 + 1;
                    jSONObjectM71886r1 = jSONObject2;
                    jSONObject4 = jSONObject3;
                    str12 = str6;
                    str13 = str5;
                    i5 = i4;
                    str9 = str7;
                    str11 = str3;
                    string2 = str4;
                    str10 = str2;
                }
                String str17 = str9;
                String str18 = str10;
                String str19 = str11;
                JSONObject jSONObject7 = jSONObject4;
                int i9 = i5;
                String str20 = str13;
                String str21 = str12;
                if (!jSONObject7.has("headerText")) {
                    sb = new StringBuilder();
                    sb.append(str);
                    sb.append(str14);
                } else if (i2 < 2) {
                    StringBuilder sb7 = new StringBuilder();
                    sb7.append(str);
                    sb = sb7;
                    sb.append(m72187N4(jSONObject7.getString("headerText"), "", "LTR", str14, "", "margin-left: " + (i2 * 5) + CSS.Value.f74124h0, ""));
                } else {
                    StringBuilder sb8 = new StringBuilder();
                    sb8.append(str);
                    sb = sb8;
                    sb.append(m72183J4(jSONObject7.getString("headerText"), "", "LTR", str14, "", "margin-left: " + (i2 * 5) + CSS.Value.f74124h0, ""));
                }
                string2 = sb.toString();
                i5 = i9 + 1;
                jSONObjectM71886r1 = jSONObject2;
                str12 = str21;
                str13 = str20;
                str9 = str17;
                str11 = str19;
                str10 = str18;
            }
            return string2;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            return null;
        }
    }

    /* renamed from: M4 */
    public String m72186M4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88042a5 + 1;
        this.f88042a5 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: N4 */
    public String m72187N4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88042a5 + 1;
        this.f88042a5 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded2\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded2(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList;
        Bundle bundleM72839v3;
        if (this.f88043b5.size() <= 0 || (arrayList = this.f88043b5) == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f88043b5)) == null) {
            return null;
        }
        return bundleM72839v3.getString("ImagePath");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() throws JSONException {
                String str;
                String str2 = "cells";
                String str3 = "sections";
                String str4 = "title";
                String str5 = "";
                try {
                    String str6 = EPODxViewerActivityFragment.this.f89563A4;
                    if (str6 != null && str6.length() != 0) {
                        return;
                    }
                    EPODxViewerActivityFragment ePODxViewerActivityFragment = EPODxViewerActivityFragment.this;
                    ePODxViewerActivityFragment.f88042a5 = 0;
                    ePODxViewerActivityFragment.f88044c5 = new ArrayList<>();
                    iMDLogger.m73550f("Loading Document", EPODxViewerActivityFragment.this.f89567E4);
                    String str7 = EPODxViewerActivityFragment.this.f89567E4.split("-")[1];
                    if (EPODxViewerActivityFragment.this.m15387y().containsKey("mDB")) {
                        EPODxViewerActivityFragment ePODxViewerActivityFragment2 = EPODxViewerActivityFragment.this;
                        ePODxViewerActivityFragment2.f88039X4 = ePODxViewerActivityFragment2.m15387y().getString("mDB");
                    }
                    EPODxViewerActivityFragment ePODxViewerActivityFragment3 = EPODxViewerActivityFragment.this;
                    if (ePODxViewerActivityFragment3.f88039X4 == null) {
                        ePODxViewerActivityFragment3.f88039X4 = "Dx";
                    }
                    ArrayList<Bundle> arrayListM71817V = ePODxViewerActivityFragment3.f89579Q4.m71817V(ePODxViewerActivityFragment3.f89566D4, "Select * from " + EPODxViewerActivityFragment.this.f88039X4 + "_monographs where id=" + str7);
                    if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                        EPODxViewerActivityFragment.this.f88040Y4 = arrayListM71817V.get(0);
                        EPODxViewerActivityFragment ePODxViewerActivityFragment4 = EPODxViewerActivityFragment.this;
                        EPODxViewerActivityFragment.this.f88041Z4 = new JSONObject(ePODxViewerActivityFragment4.f89579Q4.m71773B(ePODxViewerActivityFragment4.f88040Y4.getString("monograph"), str7, "127"));
                        EPODxViewerActivityFragment ePODxViewerActivityFragment5 = EPODxViewerActivityFragment.this;
                        ePODxViewerActivityFragment5.f89568F4 = ePODxViewerActivityFragment5.f88041Z4.getString("title");
                        EPODxViewerActivityFragment ePODxViewerActivityFragment6 = EPODxViewerActivityFragment.this;
                        JSONObject jSONObjectM71886r1 = ePODxViewerActivityFragment6.f89579Q4.m71886r1(ePODxViewerActivityFragment6.f88041Z4.getJSONArray("views"), "id", "root");
                        EPODxViewerActivityFragment ePODxViewerActivityFragment7 = EPODxViewerActivityFragment.this;
                        String strM72817d4 = ePODxViewerActivityFragment7.m72817d4(ePODxViewerActivityFragment7.m15366r(), "EPOHeader.css");
                        EPODxViewerActivityFragment ePODxViewerActivityFragment8 = EPODxViewerActivityFragment.this;
                        String strM72817d42 = ePODxViewerActivityFragment8.m72817d4(ePODxViewerActivityFragment8.m15366r(), "EPOFooter.css");
                        String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", EPODxViewerActivityFragment.this.f89568F4).replace("[include]", "");
                        String str8 = "";
                        int i2 = 0;
                        while (i2 < jSONObjectM71886r1.getJSONArray(str3).length()) {
                            JSONObject jSONObject = jSONObjectM71886r1.getJSONArray(str3).getJSONObject(i2);
                            int i3 = 0;
                            while (true) {
                                str = str3;
                                if (i3 < jSONObject.getJSONArray(str2).length()) {
                                    JSONObject jSONObject2 = jSONObject.getJSONArray(str2).getJSONObject(i3);
                                    String str9 = str2;
                                    String string = jSONObject2.getString(str4);
                                    str8 = str8 + EPODxViewerActivityFragment.this.m72186M4(string, "", "LTR", EPODxViewerActivityFragment.this.m72185L4(jSONObject2, 1).replace("<html>", "").replace("</html>", ""), "", "", "");
                                    EPODxViewerActivityFragment ePODxViewerActivityFragment9 = EPODxViewerActivityFragment.this;
                                    ePODxViewerActivityFragment9.m72182I4(string, ePODxViewerActivityFragment9.f88042a5);
                                    i3++;
                                    str2 = str9;
                                    str3 = str;
                                    str4 = str4;
                                    jSONObjectM71886r1 = jSONObjectM71886r1;
                                }
                            }
                            i2++;
                            str3 = str;
                            jSONObjectM71886r1 = jSONObjectM71886r1;
                        }
                        if (EPODxViewerActivityFragment.this.f88041Z4.has("citations") && EPODxViewerActivityFragment.this.f88041Z4.getJSONObject("citations").has("articles")) {
                            for (int i4 = 0; i4 < EPODxViewerActivityFragment.this.f88041Z4.getJSONObject("citations").getJSONArray("articles").length(); i4++) {
                                JSONObject jSONObject3 = EPODxViewerActivityFragment.this.f88041Z4.getJSONObject("citations").getJSONArray("articles").getJSONObject(i4);
                                str5 = str5 + "<div id=\"articleCitation" + jSONObject3.getString("id") + "\" style=\"margin:10px\"><b>" + jSONObject3.getString("id") + ": </b>" + jSONObject3.getString(HTML.Tag.f74425y) + "</div>";
                            }
                            if (str5.length() > 0) {
                                str8 = str8 + EPODxViewerActivityFragment.this.m72186M4("Citations", "", "LTR", str5, "", "", "");
                                EPODxViewerActivityFragment ePODxViewerActivityFragment10 = EPODxViewerActivityFragment.this;
                                ePODxViewerActivityFragment10.m72182I4("Citations", ePODxViewerActivityFragment10.f88042a5);
                            }
                        }
                        ArrayList<Bundle> arrayList = new ArrayList<>();
                        if (EPODxViewerActivityFragment.this.f88041Z4.has("media")) {
                            for (int i5 = 0; i5 < EPODxViewerActivityFragment.this.f88041Z4.getJSONArray("media").length(); i5++) {
                                JSONObject jSONObject4 = EPODxViewerActivityFragment.this.f88041Z4.getJSONArray("media").getJSONObject(i5);
                                String string2 = jSONObject4.getString(Annotation.f68285k3);
                                String strM71754h1 = CompressHelper.m71754h1(EPODxViewerActivityFragment.this.f89566D4, string2, "pictures");
                                if (new File(strM71754h1).exists()) {
                                    String str10 = jSONObject4.getString(HTML.Tag.f74389g) + StringUtils.f103471LF + jSONObject4.getString("source");
                                    Bundle bundle2 = new Bundle();
                                    bundle2.putString("ImagePath", strM71754h1);
                                    bundle2.putString("id", string2);
                                    bundle2.putString("Description", str10);
                                    arrayList.add(bundle2);
                                }
                            }
                        }
                        EPODxViewerActivityFragment.this.f88043b5 = arrayList;
                        String strReplace2 = str8.replace("..", ".");
                        EPODxViewerActivityFragment.this.f89563A4 = strReplace + strReplace2 + strM72817d42;
                        return;
                    }
                    EPODxViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                } catch (Exception e2) {
                    e2.printStackTrace();
                    EPODxViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = EPODxViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    EPODxViewerActivityFragment ePODxViewerActivityFragment = EPODxViewerActivityFragment.this;
                    ePODxViewerActivityFragment.m72780C4(ePODxViewerActivityFragment.f89595p4);
                    return;
                }
                String strM71752f1 = CompressHelper.m71752f1(EPODxViewerActivityFragment.this.f89566D4);
                EPODxViewerActivityFragment ePODxViewerActivityFragment2 = EPODxViewerActivityFragment.this;
                ePODxViewerActivityFragment2.m72795O3(ePODxViewerActivityFragment2.f89563A4, strM71752f1);
                EPODxViewerActivityFragment.this.m72836s4();
                EPODxViewerActivityFragment.this.m72831p4();
                EPODxViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                EPODxViewerActivityFragment.this.m15358o2(false);
                EPODxViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            m72181O4("soheilvb");
        }
        if (itemId == C5562R.id.action_menu) {
            LXSectionsViewer lXSectionsViewer = new LXSectionsViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("fields", this.f88044c5);
            lXSectionsViewer.m15342i2(bundle);
            lXSectionsViewer.mo15218Z2(true);
            lXSectionsViewer.m15245A2(this, 0);
            lXSectionsViewer.mo15222e3(m15283M(), "LXSectionsViewer");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws JSONException {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        if (this.f89579Q4.m71800N1(this.f89566D4, str)) {
            return true;
        }
        if (str3.contains("//current?view=")) {
            try {
                JSONObject jSONObjectM71886r1 = this.f89579Q4.m71886r1(this.f88041Z4.getJSONArray("views"), "id", this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(str3, "//current?view=")));
                if (jSONObjectM71886r1 != null && jSONObjectM71886r1.getString("type").equals("image")) {
                    m72181O4(this.f88041Z4.getJSONArray("media").getJSONObject(Integer.valueOf(jSONObjectM71886r1.getJSONArray("image_refs").getString(0)).intValue()).getString(Annotation.f68285k3));
                }
                return true;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
                return true;
            }
        }
        if (!str3.contains("//current?article=")) {
            if (!str2.equals("http")) {
                return false;
            }
            this.f89579Q4.m71772A1(this.f89566D4, "epourl-" + str, null, null);
            return true;
        }
        try {
            String str4 = "<div style=\"margin:15px\">" + this.f88041Z4.getJSONObject("citations").getJSONArray("articles").getJSONObject(Integer.valueOf(this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(str3, "//current?article="))).intValue() - 1).getString(HTML.Tag.f74425y) + "</div>";
            this.f89579Q4.m71772A1(this.f89566D4, "epohtml-" + str4, null, null);
        } catch (Exception e3) {
            FirebaseCrashlytics.m48010d().m48016g(e3);
            e3.printStackTrace();
        }
        return true;
    }
}

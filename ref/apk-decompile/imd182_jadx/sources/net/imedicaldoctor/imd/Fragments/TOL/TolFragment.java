package net.imedicaldoctor.imd.Fragments.TOL;

import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.exifinterface.media.ExifInterface;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class TolFragment extends Fragment {

    /* renamed from: e4 */
    public LinearLayout f89106e4;

    /* renamed from: f4 */
    public LinearLayout f89107f4;

    /* renamed from: g4 */
    public LinearLayout f89108g4;

    /* renamed from: h4 */
    public LinearLayout f89109h4;

    /* renamed from: i4 */
    public LinearLayout f89110i4;

    /* renamed from: j4 */
    public LinearLayout f89111j4;

    /* renamed from: k4 */
    public RelativeLayout f89112k4;

    /* renamed from: l4 */
    public TextView f89113l4;

    /* renamed from: m4 */
    public TextView f89114m4;

    /* renamed from: n4 */
    public ImageView f89115n4;

    /* renamed from: o4 */
    public TextView f89116o4;

    /* renamed from: p4 */
    public ArrayList<String> f89117p4;

    /* renamed from: q4 */
    public int f89118q4;

    /* renamed from: r4 */
    public String[] f89119r4;

    /* renamed from: s4 */
    public String[] f89120s4;

    /* renamed from: t4 */
    public long f89121t4;

    /* renamed from: u4 */
    public long f89122u4;

    /* renamed from: v4 */
    public long f89123v4;

    /* renamed from: P2 */
    public static TolFragment m72526P2() {
        return new TolFragment();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: Q2 */
    public void m72527Q2(int i2) throws IllegalStateException, IOException, IllegalArgumentException {
        this.f89123v4++;
        if (this.f89115n4.getVisibility() == 0) {
            this.f89120s4[i2] = this.f89120s4[i2] + this.f89115n4.getTag().toString();
            this.f89115n4.setVisibility(8);
        } else {
            if (this.f89120s4[i2].length() <= 0) {
                return;
            }
            String strSubstring = this.f89120s4[i2].substring(r0.length() - 1);
            String[] strArr = this.f89120s4;
            strArr[i2] = strArr[i2].substring(0, r2.length() - 1);
            this.f89115n4.setImageDrawable(m72531N2(strSubstring));
            this.f89115n4.setVisibility(0);
            this.f89115n4.setTag(strSubstring);
        }
        m72535U2();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: R2 */
    public void m72528R2() throws IllegalStateException, IOException, IllegalArgumentException {
        if (this.f89118q4 < this.f89117p4.size() - 1) {
            m72532O2();
            this.f89112k4.setVisibility(8);
        }
    }

    /* renamed from: L2 */
    public void m72529L2(LinearLayout linearLayout, String str) {
        ImageView imageView = new ImageView(m15366r());
        imageView.setImageDrawable(m72531N2(str));
        Math.round(TypedValue.applyDimension(1, 150.0f, m15320b0().getDisplayMetrics()));
        imageView.setLayoutParams(new LinearLayout.LayoutParams(-1, Math.round(TypedValue.applyDimension(1, 50.0f, m15320b0().getDisplayMetrics()))));
        linearLayout.addView(imageView, 0);
    }

    /* renamed from: M2 */
    public void m72530M2(LinearLayout linearLayout, String str) {
        linearLayout.removeAllViews();
        for (char c2 : str.toCharArray()) {
            m72529L2(linearLayout, String.valueOf(c2));
        }
    }

    /* renamed from: N2 */
    public Drawable m72531N2(String str) {
        FragmentActivity fragmentActivityM15366r;
        int i2;
        if (str.equals("R")) {
            fragmentActivityM15366r = m15366r();
            i2 = C5562R.drawable.tolred;
        } else if (str.equals("B")) {
            fragmentActivityM15366r = m15366r();
            i2 = C5562R.drawable.tolblue;
        } else if (str.equals("G")) {
            fragmentActivityM15366r = m15366r();
            i2 = C5562R.drawable.tolgreen;
        } else if (str.equals("Y")) {
            fragmentActivityM15366r = m15366r();
            i2 = C5562R.drawable.tolyellow;
        } else {
            if (!str.equals(ExifInterface.f16254R4)) {
                return null;
            }
            fragmentActivityM15366r = m15366r();
            i2 = C5562R.drawable.tolyashmi;
        }
        return fragmentActivityM15366r.getDrawable(i2);
    }

    /* renamed from: O2 */
    public void m72532O2() throws IllegalStateException, IOException, IllegalArgumentException {
        this.f89122u4 = System.currentTimeMillis();
        this.f89118q4++;
        this.f89116o4.setText("مرحله " + m72533S2(this.f89118q4 + 1) + " از " + m72533S2(this.f89117p4.size()));
        String[] strArrSplitPreserveAllTokens = StringUtils.splitPreserveAllTokens(this.f89117p4.get(this.f89118q4), "-");
        this.f89119r4 = new String[]{strArrSplitPreserveAllTokens[0], strArrSplitPreserveAllTokens[1], strArrSplitPreserveAllTokens[2]};
        this.f89120s4 = new String[]{strArrSplitPreserveAllTokens[3], strArrSplitPreserveAllTokens[4], strArrSplitPreserveAllTokens[5]};
        m72535U2();
    }

    /* renamed from: S2 */
    public String m72533S2(long j2) {
        return (j2 + "").replace(IcyHeaders.f28171a3, "۱").replace(ExifInterface.f16317Y4, "۲").replace(ExifInterface.f16326Z4, "۳").replace("4", "۴").replace("5", "۵").replace("6", "۶").replace("7", "۷").replace("8", "۸").replace("9", "۹").replace("0", "۰");
    }

    /* renamed from: T2 */
    public void m72534T2() {
        final String str = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        final CompressHelper compressHelper = new CompressHelper(m15366r());
        final EditText editText = new EditText(m15366r());
        editText.setTextColor(m15320b0().getColor(C5562R.color.black));
        new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("لطفا نام و نام خانوادگی را وارد کنید").setView(editText).mo1115y("ذخیره", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.TolFragment.7
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
                FragmentActivity fragmentActivityM15366r;
                String str2;
                String strM71833a1 = compressHelper.m71833a1(editText.getText().toString());
                if (strM71833a1.length() == 0) {
                    fragmentActivityM15366r = TolFragment.this.m15366r();
                    str2 = "لطفا یک نام وارد کنید";
                } else {
                    CompressHelper compressHelper2 = compressHelper;
                    compressHelper2.m71881q(compressHelper2.m71806R(), "Insert into tol values (null, '" + strM71833a1 + "', " + TolFragment.this.f89121t4 + ", " + TolFragment.this.f89123v4 + ", '" + str + "')");
                    fragmentActivityM15366r = TolFragment.this.m15366r();
                    str2 = "با موفقیت ذخیره شد";
                }
                CompressHelper.m71767x2(fragmentActivityM15366r, str2, 1);
            }
        }).mo1106p("بستن", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.TolFragment.6
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
            }
        }).m1090I();
    }

    @Override // androidx.fragment.app.Fragment
    @Nullable
    /* renamed from: U0 */
    public View mo15303U0(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        View viewInflate = layoutInflater.inflate(C5562R.layout.main_tol, viewGroup, false);
        m15366r().getWindow().setFlags(1024, 1024);
        this.f89106e4 = (LinearLayout) viewInflate.findViewById(C5562R.id.topleft);
        this.f89107f4 = (LinearLayout) viewInflate.findViewById(C5562R.id.topmiddle);
        this.f89108g4 = (LinearLayout) viewInflate.findViewById(C5562R.id.topright);
        this.f89109h4 = (LinearLayout) viewInflate.findViewById(C5562R.id.bottomleft);
        this.f89110i4 = (LinearLayout) viewInflate.findViewById(C5562R.id.bottomcenter);
        this.f89111j4 = (LinearLayout) viewInflate.findViewById(C5562R.id.bottomright);
        this.f89116o4 = (TextView) viewInflate.findViewById(C5562R.id.title_text);
        this.f89113l4 = (TextView) viewInflate.findViewById(C5562R.id.overlay_text_1);
        this.f89114m4 = (TextView) viewInflate.findViewById(C5562R.id.overlay_text_2);
        this.f89112k4 = (RelativeLayout) viewInflate.findViewById(C5562R.id.overlay_layout);
        this.f89115n4 = (ImageView) viewInflate.findViewById(C5562R.id.imagehand);
        this.f89117p4 = new ArrayList<>(Arrays.asList(StringUtils.splitPreserveAllTokens(new CompressHelper(m15366r()).m71846f2(CompressHelper.m71753g1(m15387y().getBundle("DB"), "tol.txt")), StringUtils.f103471LF)));
        this.f89118q4 = -1;
        this.f89121t4 = 0L;
        this.f89123v4 = 0L;
        this.f89113l4.setText("به بازی برج های لندن خوش آمدید");
        this.f89114m4.setText((("هدف از این بازی جابجایی مهره ها در سه ستون پایین است تا مشابه ستون های بالا شوند.\nبا کلیک بر روی هر ستون ، مهره را می توانید بردارید") + "\nو با کلیک بر روی ستون دیگر آن مهره را به آنجا منتقل کنید.") + "\n\nبرای شروع کلیک کنید");
        this.f89112k4.setVisibility(0);
        this.f89112k4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.TolFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                TolFragment.this.m72528R2();
            }
        });
        this.f89112k4.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.TolFragment.2
            @Override // android.view.View.OnLongClickListener
            public boolean onLongClick(View view) {
                TolFragment tolFragment = TolFragment.this;
                if (tolFragment.f89118q4 != tolFragment.f89117p4.size() - 1) {
                    return false;
                }
                TolFragment.this.m72534T2();
                return true;
            }
        });
        this.f89109h4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.TolFragment.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                TolFragment.this.m72527Q2(0);
            }
        });
        this.f89110i4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.TolFragment.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                TolFragment.this.m72527Q2(1);
            }
        });
        this.f89111j4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.TolFragment.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                TolFragment.this.m72527Q2(2);
            }
        });
        return viewInflate;
    }

    /* renamed from: U2 */
    public void m72535U2() throws IllegalStateException, IOException, IllegalArgumentException {
        TextView textView;
        String str;
        m72530M2(this.f89106e4, this.f89119r4[0]);
        m72530M2(this.f89107f4, this.f89119r4[1]);
        m72530M2(this.f89108g4, this.f89119r4[2]);
        m72530M2(this.f89109h4, this.f89120s4[0]);
        m72530M2(this.f89110i4, this.f89120s4[1]);
        m72530M2(this.f89111j4, this.f89120s4[2]);
        if (StringUtils.join(this.f89119r4, "-").equals(StringUtils.join(this.f89120s4, "-"))) {
            CompressHelper.m71744V1(m15366r(), "success.mp3");
            this.f89121t4 += System.currentTimeMillis() - this.f89122u4;
            this.f89112k4.setVisibility(0);
            if (this.f89118q4 < this.f89117p4.size() - 1) {
                this.f89113l4.setText("مرحله با موفقیت به پایان رسید.");
                textView = this.f89114m4;
                str = "برای ادامه کلیک کنید";
            } else {
                this.f89113l4.setText("آزمون به پایان رسید .\n با تشکر فراوان از شما");
                textView = this.f89114m4;
                str = "مجموع زمان : " + m72533S2(this.f89121t4 / 1000) + " ثانیه\nمجموع حرکات : " + m72533S2(this.f89123v4) + " تا";
            }
            textView.setText(str);
        }
    }

    /* renamed from: V2 */
    public void m72536V2(String str) {
        String[] strArrSplitPreserveAllTokens = StringUtils.splitPreserveAllTokens(str, "-");
        m72530M2(this.f89106e4, strArrSplitPreserveAllTokens[0]);
        m72530M2(this.f89107f4, strArrSplitPreserveAllTokens[1]);
        m72530M2(this.f89108g4, strArrSplitPreserveAllTokens[2]);
        m72530M2(this.f89109h4, strArrSplitPreserveAllTokens[3]);
        m72530M2(this.f89110i4, strArrSplitPreserveAllTokens[4]);
        m72530M2(this.f89111j4, strArrSplitPreserveAllTokens[5]);
    }
}

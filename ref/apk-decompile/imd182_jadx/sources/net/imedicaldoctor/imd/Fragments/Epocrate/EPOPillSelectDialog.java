package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.io.IOException;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class EPOPillSelectDialog extends DialogFragment {

    /* renamed from: F4 */
    private ArrayList<Bundle> f88186F4;

    /* renamed from: G4 */
    private String f88187G4;

    /* renamed from: H4 */
    private String f88188H4;

    /* renamed from: I4 */
    private RecyclerView f88189I4;

    public class ImageTextCheckViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f88190I;

        /* renamed from: J */
        public ImageView f88191J;

        /* renamed from: K */
        public ImageView f88192K;

        /* renamed from: L */
        public MaterialRippleLayout f88193L;

        public ImageTextCheckViewHolder(View view) {
            super(view);
            this.f88190I = (TextView) view.findViewById(C5562R.id.text_view);
            this.f88191J = (ImageView) view.findViewById(C5562R.id.image_view);
            this.f88192K = (ImageView) view.findViewById(C5562R.id.check_icon);
            this.f88193L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    private class SelectAdapter extends RecyclerView.Adapter {
        private SelectAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            ImageTextCheckViewHolder imageTextCheckViewHolder = (ImageTextCheckViewHolder) viewHolder;
            if (i2 == 0) {
                imageTextCheckViewHolder.f88191J.setVisibility(8);
                if (EPOPillSelectDialog.this.f88188H4.equals("-1")) {
                    imageTextCheckViewHolder.f88192K.setVisibility(0);
                } else {
                    imageTextCheckViewHolder.f88192K.setVisibility(8);
                }
                imageTextCheckViewHolder.f88190I.setText("Any " + EPOPillSelectDialog.this.f88187G4);
                imageTextCheckViewHolder.f88193L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillSelectDialog.SelectAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        ((EPOPillActivityFragment) EPOPillSelectDialog.this.m15351l0()).m72220l3(EPOPillSelectDialog.this.f88187G4, null);
                    }
                });
                return;
            }
            final Bundle bundle = (Bundle) EPOPillSelectDialog.this.f88186F4.get(i2 - 1);
            imageTextCheckViewHolder.f88191J.setVisibility(0);
            if (EPOPillSelectDialog.this.f88188H4.equals(bundle.getString("ID"))) {
                imageTextCheckViewHolder.f88192K.setVisibility(0);
            } else {
                imageTextCheckViewHolder.f88192K.setVisibility(8);
            }
            imageTextCheckViewHolder.f88190I.setText(bundle.getString("STRING_TEXT"));
            imageTextCheckViewHolder.f88191J.setImageBitmap(EPOPillSelectDialog.m72230j3(EPOPillSelectDialog.this.m15366r(), "pill" + bundle.getString("STRING_TEXT").replace("-", "").replace("_", "").replace(StringUtils.SPACE, "") + ".png"));
            imageTextCheckViewHolder.f88193L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillSelectDialog.SelectAdapter.2
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ((EPOPillActivityFragment) EPOPillSelectDialog.this.m15351l0()).m72220l3(EPOPillSelectDialog.this.f88187G4, bundle);
                    EPOPillSelectDialog.this.mo15205N2();
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            return EPOPillSelectDialog.this.new ImageTextCheckViewHolder(LayoutInflater.from(EPOPillSelectDialog.this.m15366r()).inflate(C5562R.layout.list_view_item_image_text_check, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return EPOPillSelectDialog.this.f88186F4.size() + 1;
        }
    }

    /* renamed from: j3 */
    public static Bitmap m72230j3(Context context, String str) {
        try {
            return BitmapFactory.decodeStream(context.getAssets().open(str));
        } catch (IOException unused) {
            return null;
        }
    }

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_new_section_viewer, (ViewGroup) null);
        this.f88189I4 = (RecyclerView) viewInflate.findViewById(C5562R.id.recycler_view);
        this.f88186F4 = m15387y().getParcelableArrayList("Items");
        this.f88187G4 = m15387y().getString("Category");
        this.f88188H4 = m15387y().getString("Selected");
        new CompressHelper(m15366r());
        this.f88189I4.setAdapter(new SelectAdapter());
        this.f88189I4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        builder.setView(viewInflate);
        return builder.create();
    }
}

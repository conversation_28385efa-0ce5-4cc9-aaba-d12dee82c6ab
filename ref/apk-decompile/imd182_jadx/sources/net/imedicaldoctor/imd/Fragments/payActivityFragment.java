package net.imedicaldoctor.imd.Fragments;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.media3.extractor.p003ts.TsExtractor;
import com.google.common.net.HttpHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import java.util.Map;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.iMDWebView;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.Views.ProgressBarCircularIndeterminate;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class payActivityFragment extends Fragment {

    /* renamed from: e4 */
    private View f90795e4;

    /* renamed from: f4 */
    private iMDWebView f90796f4;

    /* renamed from: g4 */
    private boolean f90797g4;

    /* renamed from: h4 */
    private boolean f90798h4;

    /* renamed from: i4 */
    private WebResourceRequest f90799i4;

    /* renamed from: j4 */
    private ProgressBarCircularIndeterminate f90800j4;

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: N2 */
    public void m73346N2() {
        this.f90800j4.setVisibility(8);
    }

    /* renamed from: P2 */
    private void m73347P2() {
        this.f90800j4.setBackgroundColor(Color.parseColor("#1e88e5"));
        this.f90800j4.setVisibility(0);
    }

    /* renamed from: O2 */
    public WebResourceResponse m73348O2(WebView webView, WebResourceRequest webResourceRequest) {
        Map<String, String> requestHeaders;
        String string = webResourceRequest.getUrl().toString();
        if (this.f90797g4) {
            return null;
        }
        try {
            requestHeaders = webResourceRequest.getRequestHeaders();
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Exeption", e2.getLocalizedMessage());
        }
        if (requestHeaders.get(HttpHeaders.f62916c) == null) {
            return null;
        }
        if (requestHeaders.get(HttpHeaders.f62916c).contains("x-www-form-urlencoded") && requestHeaders.containsKey(HttpHeaders.f62845F) && requestHeaders.get(HttpHeaders.f62845F).contains("shaparak")) {
            this.f90797g4 = true;
            this.f90798h4 = false;
            this.f90799i4 = webResourceRequest;
            m15366r().runOnUiThread(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.3
                @Override // java.lang.Runnable
                public void run() {
                    VBHelper vBHelper = new VBHelper(payActivityFragment.this.m15366r());
                    payActivityFragment.this.f90796f4.m73433g("" + vBHelper.m73448j(vBHelper.m73448j("2204F7542EDADE86DEC73251369885B8A241E7692D1BA9FC430D497DAD9FA92BCB875485C0C7C870F709A03B18FA69D195F6BB98332699C8497AD7FE77E43D8E9BC1E3BC9596D7130F58CC3FE3DCA77B89865653F7F54C54B9368A3DD07B5E478F9FFA72F3264AD6BEFA58190B571E3546E6C387AF72DA96CAB58446AAEF93931BC8195BD517535791DD314AC24F788D8B2CA46D641B0B9897231B8296D392B83DCC9F577080522A7E79BC7568B38A5558CDE2F315A7FBD1E2F06520F77AAF3E916655590623C0D447DB5E4E08992EC822DC267673EEEBAEE6B8ACD0A9BC2E7D3DB2A6A5B8834CF19AFA2E0172238EF9CFCEFADE51BC6687C0F6C8174D886C05EB08F3061A888EF9F89CC72A08CAF6C43C9324422B89F6DA736628BC8253698B096D1A38F0942F22C54CB0A39F3EEA02883A563F05EC778800CBA65EA4687F8AF45E927CB8893A27EE9D7BCDCD55E2E2466E7F833301219F5D24C0C369FE5E3ACD7067251C26AB51D7989DBD78DCE47291E136B032176B6FD77562218B98AF1E291AAFBB373826EBE57340BB07BBF1A8", "127"), "127"));
                    payActivityFragment.this.f90796f4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.3.1
                        @Override // java.lang.Runnable
                        public void run() {
                            payActivityFragment.this.f90798h4 = true;
                        }
                    }, 100L);
                }
            });
            while (!this.f90798h4) {
                iMDLogger.m73550f("Wait", "waiting ...");
            }
        }
        iMDLogger.m73550f("URL", string + ", Method : " + webResourceRequest.getRequestHeaders().toString());
        return null;
    }

    @Override // androidx.fragment.app.Fragment
    @Nullable
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View view = this.f90795e4;
        if (view != null) {
            return view;
        }
        this.f90795e4 = layoutInflater.inflate(C5562R.layout.fragment_pay, viewGroup, false);
        this.f90800j4 = (ProgressBarCircularIndeterminate) this.f90795e4.findViewById(C5562R.id.progress_bar);
        ActionBar actionBarM1122F0 = ((AppCompatActivity) m15366r()).m1122F0();
        actionBarM1122F0.mo937Y(true);
        this.f90796f4 = (iMDWebView) this.f90795e4.findViewById(C5562R.id.webView);
        CompressHelper compressHelper = new CompressHelper(m15366r());
        VBHelper vBHelper = new VBHelper(m15366r());
        this.f90796f4.getSettings().setAllowContentAccess(true);
        this.f90796f4.getSettings().setAllowFileAccess(true);
        this.f90796f4.getSettings().setDomStorageEnabled(true);
        this.f90796f4.getSettings().setJavaScriptEnabled(true);
        this.f90796f4.getSettings().setUseWideViewPort(true);
        this.f90796f4.setWebChromeClient(new WebChromeClient() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.1
            @Override // android.webkit.WebChromeClient
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                iMDLogger.m73550f("Console", consoleMessage.message());
                String strMessage = consoleMessage.message();
                if (strMessage.contains("a,,,")) {
                    payActivityFragment.this.f90798h4 = true;
                    String strM73452n = new VBHelper(payActivityFragment.this.m15366r()).m73452n(new VBHelper(payActivityFragment.this.m15366r()).m73452n(strMessage.replace("a,,,", ""), "127"), "127");
                    CompressHelper compressHelper2 = new CompressHelper(payActivityFragment.this.m15366r());
                    compressHelper2.m71789I0(payActivityFragment.this, compressHelper2.m71874o0("addBuy|||||" + new VBHelper(payActivityFragment.this.m15366r()).m73451m() + "|||||" + strM73452n)).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.1.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(String str) throws Throwable {
                        }
                    }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.1.2
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(Throwable th) throws Throwable {
                        }
                    }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.1.3
                        @Override // io.reactivex.rxjava3.functions.Action
                        public void run() throws Throwable {
                        }
                    });
                }
                return super.onConsoleMessage(consoleMessage);
            }
        });
        this.f90796f4.setWebViewClient(new WebViewClient() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.2
            @Override // android.webkit.WebViewClient
            public void onPageFinished(WebView webView, String str) {
                iMDLogger.m73550f("WEEB", "Finished " + str);
                payActivityFragment.this.m73346N2();
                payActivityFragment.this.f90796f4.requestFocus(TsExtractor.f30466L);
            }

            @Override // android.webkit.WebViewClient
            public void onPageStarted(WebView webView, String str, Bitmap bitmap) {
                iMDLogger.m73550f("WEEB", "Started " + str);
            }

            @Override // android.webkit.WebViewClient
            public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest webResourceRequest) {
                WebResourceResponse webResourceResponseM73348O2 = payActivityFragment.this.m73348O2(webView, webResourceRequest);
                return webResourceResponseM73348O2 != null ? webResourceResponseM73348O2 : super.shouldInterceptRequest(webView, webResourceRequest);
            }

            @Override // android.webkit.WebViewClient
            public boolean shouldOverrideUrlLoading(WebView webView, String str) {
                iMDLogger.m73554j("WEEB", "Override : " + str);
                Uri uri = Uri.parse(str);
                String scheme = uri.getScheme();
                String schemeSpecificPart = uri.getSchemeSpecificPart();
                iMDLogger.m73554j("Pay", "Scheme : " + scheme + ", Resource : " + schemeSpecificPart);
                if (!schemeSpecificPart.contains("//imedicaldoctor.net/confirmaaip.php?command=") && !schemeSpecificPart.contains("//imedicaldoctor.net/confirmaip.php")) {
                    return false;
                }
                CompressHelper.m71767x2(payActivityFragment.this.m15366r(), "Please wait 10 second", 1);
                payActivityFragment.this.f90796f4.setVisibility(4);
                payActivityFragment.this.f90795e4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.payActivityFragment.2.1
                    @Override // java.lang.Runnable
                    public void run() {
                        payActivityFragment.this.m73346N2();
                        Intent intent = new Intent();
                        intent.putExtra("result", 1);
                        if (payActivityFragment.this.m15366r() != null) {
                            payActivityFragment.this.m15366r().setResult(1, intent);
                            payActivityFragment.this.m15366r().finish();
                            payActivityFragment.this.m15366r().overridePendingTransition(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
                        }
                    }
                }, 10000L);
                return false;
            }

            @Override // android.webkit.WebViewClient
            public WebResourceResponse shouldInterceptRequest(WebView webView, String str) {
                return super.shouldInterceptRequest(webView, str);
            }
        });
        String string = m15387y().getString("Type");
        if (string.equals("account")) {
            actionBarM1122F0.mo910A0("Buying Account");
            String str = "command=" + vBHelper.m73452n(m15387y().getString("AccountCommand"), "127");
            this.f90796f4.postUrl(compressHelper.m71790J() + "/buyaaip.php", str.getBytes());
        } else if (string.equals("credit")) {
            actionBarM1122F0.mo910A0("Buying " + m15387y().getString("Price") + " Toman Credit");
            this.f90796f4.loadUrl(compressHelper.m71790J() + "/buyaip.php?user=" + vBHelper.m73451m() + "&price=" + m15387y().getString("Price"));
        }
        this.f90796f4.requestFocus(TsExtractor.f30466L);
        m73347P2();
        return this.f90795e4;
    }
}

package net.imedicaldoctor.imd.Fragments.Lexi;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPODxViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPOIDViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPORxViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPOTableViewerActivityFragment;
import net.imedicaldoctor.imd.Fragments.Lexi.LXViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;

/* loaded from: classes3.dex */
public class LXSectionsViewer extends DialogFragment {

    /* renamed from: F4 */
    private ArrayList<Bundle> f88344F4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f88344F4 = m15387y().getParcelableArrayList("fields");
        new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new ArrayAdapter<Bundle>(m15366r(), C5562R.layout.list_view_item_simple_text, C5562R.id.text, this.f88344F4) { // from class: net.imedicaldoctor.imd.Fragments.Lexi.LXSectionsViewer.1
            @Override // android.widget.ArrayAdapter, android.widget.Adapter
            public View getView(int i2, View view, ViewGroup viewGroup) {
                View view2 = super.getView(i2, view, viewGroup);
                ((TextView) view2.findViewById(C5562R.id.text)).setText(((Bundle) LXSectionsViewer.this.f88344F4.get(i2)).getString("label"));
                return view2;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Lexi.LXSectionsViewer.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                ViewerHelperFragment viewerHelperFragment;
                StringBuilder sb;
                String string = ((Bundle) ((ArrayAdapter) adapterView.getAdapter()).getItem(i2)).getString("sequence");
                if (LXSectionsViewer.this.m15351l0().getClass().equals(LXViewer.LXViewerFragment.class)) {
                    ((LXViewer.LXViewerFragment) LXSectionsViewer.this.m15351l0()).mo71967C3("f" + string);
                } else if (LXSectionsViewer.this.m15351l0().getClass().equals(EPODxViewerActivityFragment.class)) {
                    ((EPODxViewerActivityFragment) LXSectionsViewer.this.m15351l0()).mo71967C3("f" + string);
                } else {
                    if (LXSectionsViewer.this.m15351l0().getClass().equals(EPORxViewerActivityFragment.class)) {
                        viewerHelperFragment = (EPORxViewerActivityFragment) LXSectionsViewer.this.m15351l0();
                        sb = new StringBuilder();
                    } else if (LXSectionsViewer.this.m15351l0().getClass().equals(EPOIDViewerActivityFragment.class)) {
                        viewerHelperFragment = (EPOIDViewerActivityFragment) LXSectionsViewer.this.m15351l0();
                        sb = new StringBuilder();
                    } else if (LXSectionsViewer.this.m15351l0().getClass().equals(EPOTableViewerActivityFragment.class)) {
                        viewerHelperFragment = (EPOTableViewerActivityFragment) LXSectionsViewer.this.m15351l0();
                        sb = new StringBuilder();
                    }
                    sb.append("f");
                    sb.append(string);
                    viewerHelperFragment.mo71967C3(sb.toString());
                }
                LXSectionsViewer.this.mo15203M2();
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class EPOPillActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88149A4;

    /* renamed from: B4 */
    public String f88150B4;

    /* renamed from: C4 */
    public Button f88151C4;

    /* renamed from: D4 */
    public String f88152D4;

    /* renamed from: E4 */
    public Bundle f88153E4;

    /* renamed from: F4 */
    public Bundle f88154F4;

    /* renamed from: G4 */
    public Bundle f88155G4;

    /* renamed from: H4 */
    public String f88156H4;

    /* renamed from: I4 */
    public String f88157I4;

    /* renamed from: J4 */
    public String f88158J4;

    /* renamed from: K4 */
    public ArrayList<Bundle> f88159K4;

    public class InputTextViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f88165I;

        /* renamed from: J */
        public EditText f88166J;

        public InputTextViewHolder(View view) {
            super(view);
            this.f88165I = (TextView) view.findViewById(C5562R.id.text_view);
            this.f88166J = (EditText) view.findViewById(C5562R.id.text_edit);
        }
    }

    public class PillIDAdapter extends RecyclerView.Adapter {
        public PillIDAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return i2 < 2 ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            MaterialRippleLayout materialRippleLayout;
            View.OnClickListener onClickListener;
            if (viewHolder.m27811F() == 0) {
                InputTextViewHolder inputTextViewHolder = (InputTextViewHolder) viewHolder;
                inputTextViewHolder.f88166J.setHint("type");
                inputTextViewHolder.f88166J.addTextChangedListener(new TextWatcher() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.PillIDAdapter.1
                    @Override // android.text.TextWatcher
                    public void afterTextChanged(Editable editable) {
                        EPOPillActivityFragment.this.m72218j3();
                    }

                    @Override // android.text.TextWatcher
                    public void beforeTextChanged(CharSequence charSequence, int i3, int i4, int i5) {
                    }

                    @Override // android.text.TextWatcher
                    public void onTextChanged(CharSequence charSequence, int i3, int i4, int i5) {
                    }
                });
                inputTextViewHolder.f88165I.setText(i2 == 0 ? "Imprint - Side 1" : "Imprint - Side 2");
                if (i2 == 0) {
                    inputTextViewHolder.f88166J.addTextChangedListener(new TextWatcher() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.PillIDAdapter.2
                        @Override // android.text.TextWatcher
                        public void afterTextChanged(Editable editable) {
                            EPOPillActivityFragment.this.f88156H4 = editable.toString();
                        }

                        @Override // android.text.TextWatcher
                        public void beforeTextChanged(CharSequence charSequence, int i3, int i4, int i5) {
                        }

                        @Override // android.text.TextWatcher
                        public void onTextChanged(CharSequence charSequence, int i3, int i4, int i5) {
                        }
                    });
                }
                if (i2 == 1) {
                    inputTextViewHolder.f88166J.addTextChangedListener(new TextWatcher() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.PillIDAdapter.3
                        @Override // android.text.TextWatcher
                        public void afterTextChanged(Editable editable) {
                            EPOPillActivityFragment.this.f88157I4 = editable.toString();
                        }

                        @Override // android.text.TextWatcher
                        public void beforeTextChanged(CharSequence charSequence, int i3, int i4, int i5) {
                        }

                        @Override // android.text.TextWatcher
                        public void onTextChanged(CharSequence charSequence, int i3, int i4, int i5) {
                        }
                    });
                    return;
                }
                return;
            }
            SettingViewHolder settingViewHolder = (SettingViewHolder) viewHolder;
            if (i2 == 2) {
                settingViewHolder.f88175I.setText("Shape");
                Bundle bundle = EPOPillActivityFragment.this.f88153E4;
                if (bundle == null) {
                    settingViewHolder.f88177K.setText("Any Shape");
                } else {
                    settingViewHolder.f88177K.setText(bundle.getString("STRING_TEXT"));
                }
                materialRippleLayout = settingViewHolder.f88176J;
                onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.PillIDAdapter.4
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        EPOPillActivityFragment.this.m72221m3("shape");
                    }
                };
            } else if (i2 == 3) {
                settingViewHolder.f88175I.setText("Color");
                Bundle bundle2 = EPOPillActivityFragment.this.f88154F4;
                if (bundle2 == null) {
                    settingViewHolder.f88177K.setText("Any Color");
                } else {
                    settingViewHolder.f88177K.setText(bundle2.getString("STRING_TEXT"));
                }
                materialRippleLayout = settingViewHolder.f88176J;
                onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.PillIDAdapter.5
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        EPOPillActivityFragment.this.m72221m3("color");
                    }
                };
            } else {
                if (i2 != 4) {
                    return;
                }
                settingViewHolder.f88175I.setText("Score");
                Bundle bundle3 = EPOPillActivityFragment.this.f88155G4;
                if (bundle3 == null) {
                    settingViewHolder.f88177K.setText("Any Score");
                } else {
                    settingViewHolder.f88177K.setText(bundle3.getString("STRING_TEXT"));
                }
                materialRippleLayout = settingViewHolder.f88176J;
                onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.PillIDAdapter.6
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        EPOPillActivityFragment.this.m72221m3("score");
                    }
                };
            }
            materialRippleLayout.setOnClickListener(onClickListener);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return EPOPillActivityFragment.this.new InputTextViewHolder(LayoutInflater.from(EPOPillActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_input_text, viewGroup, false));
            }
            if (i2 != 1) {
                return null;
            }
            return EPOPillActivityFragment.this.new SettingViewHolder(LayoutInflater.from(EPOPillActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_text_setting, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return 5;
        }
    }

    public class SettingViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f88175I;

        /* renamed from: J */
        public MaterialRippleLayout f88176J;

        /* renamed from: K */
        public TextView f88177K;

        public SettingViewHolder(View view) {
            super(view);
            this.f88175I = (TextView) view.findViewById(C5562R.id.text_view);
            this.f88177K = (TextView) view.findViewById(C5562R.id.subtext_view);
            this.f88176J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_pill_identifier, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88156H4 = "";
        this.f88157I4 = "";
        this.f88152D4 = "RX.sqlite";
        SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88799s4.setVisibility(8);
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f88798r4.setTitle("Pill Identifier");
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(true, false);
        relativeLayout.setVisibility(0);
        Button button = (Button) this.f88797q4.findViewById(C5562R.id.result_button);
        this.f88151C4 = button;
        button.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Bundle bundle2 = new Bundle();
                bundle2.putString("Query", EPOPillActivityFragment.this.f88158J4);
                EPOPillActivityFragment ePOPillActivityFragment = EPOPillActivityFragment.this;
                ePOPillActivityFragment.f88791k4.m71775B1(ePOPillActivityFragment.f88788h4, "pillid-adf", null, null, bundle2);
            }
        });
        PillIDAdapter pillIDAdapter = new PillIDAdapter();
        this.f88792l4 = pillIDAdapter;
        this.f88803w4.setAdapter(pillIDAdapter);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88149A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88149A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: c3 */
    public void mo72171c3() {
        this.f88800t4.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.pill_id_icon));
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "";
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0044  */
    /* JADX WARN: Removed duplicated region for block: B:16:0x006f  */
    /* JADX WARN: Removed duplicated region for block: B:18:0x0075  */
    /* JADX WARN: Removed duplicated region for block: B:21:0x0091  */
    /* JADX WARN: Removed duplicated region for block: B:24:0x00ad  */
    /* renamed from: i3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m72217i3() {
        /*
            r6 = this;
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.lang.String r1 = r6.f88156H4
            java.lang.String r2 = r6.f88157I4
            int r3 = r1.length()
            java.lang.String r4 = "))"
            if (r3 <= 0) goto L3e
            java.lang.String r1 = r6.m72219k3(r1)
            if (r1 != 0) goto L1f
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
        L1c:
            r6.f88159K4 = r0
            return
        L1f:
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            java.lang.String r5 = "(imprint1_string_id in ("
            r3.append(r5)
            r3.append(r1)
            java.lang.String r5 = ") OR imprint2_string_id in ("
            r3.append(r5)
            r3.append(r1)
            r3.append(r4)
            java.lang.String r1 = r3.toString()
            r0.add(r1)
        L3e:
            int r1 = r2.length()
            if (r1 <= 0) goto L6f
            java.lang.String r1 = r6.m72219k3(r2)
            if (r1 != 0) goto L50
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            goto L1c
        L50:
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = "(imprint2_string_id in ("
            r2.append(r3)
            r2.append(r1)
            java.lang.String r3 = ") OR imprint1_string_id in ("
            r2.append(r3)
            r2.append(r1)
            r2.append(r4)
            java.lang.String r1 = r2.toString()
            r0.add(r1)
        L6f:
            android.os.Bundle r1 = r6.f88153E4
            java.lang.String r2 = "ID"
            if (r1 == 0) goto L8d
            java.lang.String r1 = r1.getString(r2)
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            java.lang.String r4 = "shape_string_id="
            r3.append(r4)
            r3.append(r1)
            java.lang.String r1 = r3.toString()
            r0.add(r1)
        L8d:
            android.os.Bundle r1 = r6.f88155G4
            if (r1 == 0) goto La9
            java.lang.String r1 = r1.getString(r2)
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            java.lang.String r4 = "score_string_id="
            r3.append(r4)
            r3.append(r1)
            java.lang.String r1 = r3.toString()
            r0.add(r1)
        La9:
            android.os.Bundle r1 = r6.f88154F4
            if (r1 == 0) goto Lc5
            java.lang.String r1 = r1.getString(r2)
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = "color_string_id="
            r2.append(r3)
            r2.append(r1)
            java.lang.String r1 = r2.toString()
            r0.add(r1)
        Lc5:
            java.lang.String r1 = " AND "
            java.lang.String r0 = org.apache.commons.lang3.StringUtils.join(r0, r1)
            r6.f88158J4 = r0
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r2 = "Select id from pill_pictures where "
            r1.append(r2)
            r1.append(r0)
            java.lang.String r0 = r1.toString()
            net.imedicaldoctor.imd.Data.CompressHelper r1 = r6.f88791k4
            android.os.Bundle r2 = r6.f88788h4
            java.lang.String r3 = r6.f88152D4
            r4 = 1
            java.util.ArrayList r0 = r1.m71822X(r2, r0, r3, r4)
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.m72217i3():void");
    }

    /* renamed from: j3 */
    public void m72218j3() {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.2
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                EPOPillActivityFragment.this.m72217i3();
                observableEmitter.onNext("asdf");
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.3
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                if (EPOPillActivityFragment.this.f88159K4.size() == 0) {
                    EPOPillActivityFragment.this.f88151C4.setEnabled(false);
                    EPOPillActivityFragment.this.f88151C4.setBackgroundColor(Color.rgb(100, 100, 100));
                    EPOPillActivityFragment.this.f88151C4.setText("Nothing Found");
                    return;
                }
                EPOPillActivityFragment.this.f88151C4.setText(EPOPillActivityFragment.this.f88159K4.size() + " Drugs Found");
                EPOPillActivityFragment.this.f88151C4.setEnabled(true);
                EPOPillActivityFragment.this.f88151C4.setBackgroundColor(Color.rgb(64, 140, 83));
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.4
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
            }
        }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillActivityFragment.5
            @Override // io.reactivex.rxjava3.functions.Action
            public void run() throws Throwable {
            }
        });
    }

    /* renamed from: k3 */
    public String m72219k3(String str) {
        if (str == null || str.length() == 0) {
            return null;
        }
        CompressHelper compressHelper = this.f88791k4;
        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71819W(this.f88788h4, "select  group_concat(id) as ids from imprint_strings where string_text like '%" + str + "%'", this.f88152D4));
        if (bundleM71890s1 == null) {
            return null;
        }
        String string = bundleM71890s1.getString("ids");
        if (string.length() == 0) {
            return null;
        }
        return string;
    }

    /* renamed from: l3 */
    public void m72220l3(String str, Bundle bundle) {
        if (str.equals("shape")) {
            this.f88153E4 = bundle;
        }
        if (str.equals("color")) {
            this.f88154F4 = bundle;
        }
        if (str.equals("score")) {
            this.f88155G4 = bundle;
        }
        this.f88803w4.setAdapter(this.f88792l4);
        m72218j3();
    }

    /* renamed from: m3 */
    public void m72221m3(String str) {
        Bundle bundle;
        Bundle bundle2;
        Bundle bundle3;
        m72468V2();
        ArrayList<Bundle> arrayListM71822X = this.f88791k4.m71822X(this.f88788h4, "select * from " + str + "_strings", this.f88152D4, true);
        String string = (!str.equals("shape") || (bundle3 = this.f88153E4) == null) ? "" : bundle3.getString("ID");
        if (str.equals("score") && (bundle2 = this.f88155G4) != null) {
            string = bundle2.getString("ID");
        }
        if (str.equals("color") && (bundle = this.f88154F4) != null) {
            string = bundle.getString("ID");
        }
        if (string.length() == 0) {
            string = "-1";
        }
        EPOPillSelectDialog ePOPillSelectDialog = new EPOPillSelectDialog();
        Bundle bundle4 = new Bundle();
        bundle4.putParcelableArrayList("Items", arrayListM71822X);
        bundle4.putString("Category", str);
        bundle4.putString("Selected", string);
        ePOPillSelectDialog.m15342i2(bundle4);
        ePOPillSelectDialog.mo15218Z2(true);
        ePOPillSelectDialog.m15245A2(this, 0);
        ePOPillSelectDialog.mo15222e3(m15283M(), "EPOPillSelectDialog");
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestManager;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextImageViewHolder;

/* loaded from: classes3.dex */
public class EPOPillResultActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public ArrayList<Bundle> f88179X4;

    /* renamed from: Y4 */
    public RecyclerView f88180Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88181Z4;

    /* renamed from: a5 */
    public ChaptersAdapter f88182a5;

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: K4 */
    public void m72225K4(int i2) {
        ArrayList<Bundle> arrayList = this.f88181Z4;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", this.f88181Z4);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: J4 */
    public void m72226J4() {
        this.f88180Y4.setItemAnimator(new DefaultItemAnimator());
        this.f88180Y4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88180Y4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88180Y4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        String string = m15387y().getString("Query");
        this.f88179X4 = this.f89579Q4.m71822X(this.f89566D4, "select drug.name as drugName, genericname_strings.string_text as genericName, formulation_strings.string_text,pill_pictures.filename from (( pill_pictures inner join genericname_strings on genericname_strings.id=pill_pictures.genericname_string_id) inner join drug on drug.id=pill_pictures.drug_id) inner join formulation_strings on formulation_strings.id=pill_pictures.formulation_string_id where " + string + " order by drugName collate nocase asc", "RX.sqlite", true);
        this.f89568F4 = "Found " + this.f88179X4.size() + " Drugs";
        this.f88181Z4 = new ArrayList<>();
        Iterator<Bundle> it2 = this.f88179X4.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            Bundle bundle2 = new Bundle();
            bundle2.putString("ImagePath", "http://www.epocrates.com/pillimages/" + next.getString("FILENAME") + ".jpg");
            bundle2.putString("DescriptionHTML", "<font color=\"#000099\"><b>" + next.getString("drugName") + "</b></font><br/>" + next.getString("genericName") + "<br/>" + next.getString("STRING_TEXT"));
            this.f88181Z4.add(bundle2);
        }
        ChaptersAdapter chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88179X4, "", C5562R.layout.list_view_item_pill_image) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillResultActivityFragment.1
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, Bundle bundle3, final int i2) {
                RippleTextImageViewHolder rippleTextImageViewHolder = (RippleTextImageViewHolder) viewHolder;
                Bundle bundle4 = EPOPillResultActivityFragment.this.f88179X4.get(i2);
                rippleTextImageViewHolder.f101512I.setText(Html.fromHtml("<font color=\"#000099\"><b>" + bundle4.getString("drugName") + "</b></font><br/>" + bundle4.getString("genericName") + "<br/>" + bundle4.getString("STRING_TEXT")));
                RequestManager requestManagerM30041G = Glide.m30041G(EPOPillResultActivityFragment.this.m15366r());
                StringBuilder sb = new StringBuilder();
                sb.append("http://www.epocrates.com/pillimages/");
                sb.append(bundle4.getString("FILENAME"));
                sb.append("_thumb.jpg");
                requestManagerM30041G.mo30129t(sb.toString()).m30165B2(rippleTextImageViewHolder.f101513J);
                rippleTextImageViewHolder.f101514K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOPillResultActivityFragment.1.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        EPOPillResultActivityFragment.this.m72225K4(i2);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view2) {
                return new RippleTextImageViewHolder(view2);
            }
        };
        this.f88182a5 = chaptersAdapter;
        this.f88180Y4.setAdapter(chaptersAdapter);
        m72226J4();
        mo72642f3(C5562R.menu.empty);
        m15358o2(false);
        m72786G3();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3;
        ArrayList<Bundle> arrayList = this.f88179X4;
        if (arrayList == null || arrayList.size() == 0 || (bundleM72839v3 = m72839v3(this.f88179X4)) == null) {
            return;
        }
        Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
    }
}

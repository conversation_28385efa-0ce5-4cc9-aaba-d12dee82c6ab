package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class EPOInteractResultActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f88089X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88090Y4;

    /* renamed from: Z4 */
    public String f88091Z4;

    /* renamed from: a5 */
    public Bundle f88092a5;

    /* renamed from: b5 */
    public Bundle f88093b5;

    /* renamed from: c5 */
    public ArrayList<String> f88094c5;

    /* renamed from: d5 */
    public NotStickySectionAdapter f88095d5;

    /* renamed from: e5 */
    public ArrayList<Bundle> f88096e5;

    /* renamed from: f5 */
    public ArrayList<Bundle> f88097f5;

    /* renamed from: I4 */
    public void m72199I4() {
        this.f88089X4.setItemAnimator(new DefaultItemAnimator());
        this.f88089X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88089X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88089X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        this.f88092a5 = new Bundle();
        this.f88094c5 = new ArrayList<>();
        this.f88093b5 = new Bundle();
        this.f88091Z4 = "RX.sqlite";
        for (String str : StringUtils.splitByWholeSeparator(this.f89567E4.split("-")[1], ";;;;;")) {
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, ",,,,,");
            this.f88092a5.putString(strArrSplitByWholeSeparator[0], strArrSplitByWholeSeparator[1]);
            this.f88094c5.add(strArrSplitByWholeSeparator[0]);
        }
        String strJoin = StringUtils.join(this.f88094c5, ",");
        this.f88090Y4 = this.f89579Q4.m71822X(this.f89566D4, "Select * from pill_pictures where drug_id in (" + strJoin + ")", this.f88091Z4, true);
        ArrayList<Bundle> arrayListM71822X = this.f89579Q4.m71822X(this.f89566D4, "SELECT                     ID,                     DRUG_ID AS DRUG_0_ID,                     INTERACTING_DRUG_ID AS DRUG_1_ID,                     DDI_ID,                     GROUP_0_ID,                     GROUP_1_ID                     FROM (                     SELECT DISTINCT                     tDID.ID,                     MIN(d1.ID, d2.ID) AS DRUG_ID,                     MAX(d1.ID, d2.ID) AS INTERACTING_DRUG_ID,                     tDID.DDI_ID,                     DDI.GROUP_0_ID,                     DDI.GROUP_1_ID                     FROM                     DRUG_TO_INTERACTING_DRUG tDID                     JOIN DDI ON tDID.DDI_ID = DDI.ID                     JOIN DRUG d1 ON d1.ID = tDID.DRUG_0_ID OR d1.GENERIC_ID = tDID.DRUG_0_ID OR d1.ID = tDID.DRUG_1_ID OR d1.GENERIC_ID = tDID.DRUG_1_ID                     JOIN DRUG d2 ON                     CASE WHEN d1.ID = tDID.DRUG_0_ID OR d1.GENERIC_ID = tDID.DRUG_0_ID                     THEN d2.ID = tDID.DRUG_1_ID OR d2.GENERIC_ID = tDID.DRUG_1_ID                     ELSE d2.ID = tDID.DRUG_0_ID OR d2.GENERIC_ID = tDID.DRUG_0_ID                     END                     WHERE                     tDID.DRUG_0_ID IN (" + strJoin + ")                     AND                     tDID.DRUG_1_ID IN (" + strJoin + ")                     AND                     DRUG_0_ID <> DRUG_1_ID                     AND                     d1.ID IN (" + strJoin + ")                     AND                     d2.ID IN (" + strJoin + ")                     ORDER BY CATEGORY_ID, d1.name, d2.name                     )", this.f88091Z4, true);
        ArrayList arrayList = new ArrayList();
        Iterator<Bundle> it2 = arrayListM71822X.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            arrayList.add(next.getString("DDI_ID"));
            String string = next.getString("DRUG_0_ID");
            String string2 = next.getString("DRUG_1_ID");
            String string3 = next.getString("GROUP_0_ID");
            String string4 = next.getString("GROUP_1_ID");
            String str2 = string3 + "-" + string4;
            if (!this.f88093b5.containsKey(str2)) {
                this.f88093b5.putString(str2, string + "-" + string2);
            }
            String str3 = string4 + "-" + string3;
            if (!this.f88093b5.containsKey(str3)) {
                this.f88093b5.putString(str3, string + "-" + string2);
            }
        }
        ArrayList<Bundle> arrayListM71822X2 = this.f89579Q4.m71822X(this.f89566D4, "Select * from DDI where id in (" + StringUtils.join(arrayList, ",") + ") order by category_id", this.f88091Z4, true);
        this.f88096e5 = arrayListM71822X2;
        this.f89568F4 = arrayListM71822X2.size() == 0 ? "No Interaction Found" : "Found " + this.f88096e5.size() + " Interactions";
        this.f88097f5 = this.f89579Q4.m71887r2(this.f88096e5, "CATEGORY_ID");
        NotStickySectionAdapter notStickySectionAdapter = new NotStickySectionAdapter(m15366r(), this.f88097f5, "title", C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractResultActivityFragment.1
            @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
            /* renamed from: f0 */
            public void mo72200f0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(EPOInteractResultActivityFragment.this.f88093b5.getString(bundle2.getString("GROUP_1_ID") + "-" + bundle2.getString("GROUP_0_ID")), "-");
                int i3 = 0;
                String string5 = EPOInteractResultActivityFragment.this.f88092a5.getString(strArrSplitByWholeSeparator2[0]);
                String string6 = EPOInteractResultActivityFragment.this.f88092a5.getString(strArrSplitByWholeSeparator2[1]);
                rippleTextFullViewHolder.f101499I.setText(string5);
                rippleTextFullViewHolder.f101500J.setText(string6);
                String string7 = bundle2.getString("CATEGORY_ID");
                if (string7.equals(IcyHeaders.f28171a3)) {
                    i3 = C5562R.drawable.xinteraction;
                } else if (string7.equals(ExifInterface.f16317Y4)) {
                    i3 = C5562R.drawable.dinteraction;
                } else if (string7.equals(ExifInterface.f16326Z4)) {
                    i3 = C5562R.drawable.cinteraction;
                } else if (string7.equals("4")) {
                    i3 = C5562R.drawable.binteraction;
                } else if (string7.equals("5")) {
                    i3 = C5562R.drawable.ainteraction;
                }
                rippleTextFullViewHolder.f101501K.setImageDrawable(EPOInteractResultActivityFragment.this.m15320b0().getDrawable(i3));
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractResultActivityFragment.1.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        EPOInteractResultActivityFragment ePOInteractResultActivityFragment = EPOInteractResultActivityFragment.this;
                        ePOInteractResultActivityFragment.f89579Q4.m71772A1(ePOInteractResultActivityFragment.f89566D4, "interactview-" + bundle2.getString("ID"), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
            /* renamed from: i0 */
            public String mo72201i0(String str4) {
                return str4.equals("5") ? "Caution Advised" : str4.equals("4") ? "Therapeutic Advantage" : str4.equals(ExifInterface.f16326Z4) ? "Monitor / Modify Tx" : str4.equals(ExifInterface.f16317Y4) ? "Avoid / Use Alternative" : str4.equals(IcyHeaders.f28171a3) ? "Contraindicated" : str4;
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
            /* renamed from: k0 */
            public RecyclerView.ViewHolder mo72202k0(View view2) {
                return new RippleTextFullViewHolder(view2);
            }
        };
        this.f88095d5 = notStickySectionAdapter;
        this.f88089X4.setAdapter(notStickySectionAdapter);
        m72199I4();
        mo72642f3(C5562R.menu.favorite);
        m15358o2(false);
        m72786G3();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3;
        ArrayList<Bundle> arrayList = this.f88090Y4;
        if (arrayList == null || arrayList.size() == 0 || (bundleM72839v3 = m72839v3(this.f88090Y4)) == null) {
            return;
        }
        Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
    }
}

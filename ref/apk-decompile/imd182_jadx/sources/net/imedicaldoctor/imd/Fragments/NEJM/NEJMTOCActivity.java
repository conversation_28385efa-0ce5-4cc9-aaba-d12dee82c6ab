package net.imedicaldoctor.imd.Fragments.NEJM;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.core.view.ViewCompat;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.tool.xml.html.HTML;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class NEJMTOCActivity extends iMDActivity {

    public static class NEJMTOCFragment extends SearchHelperFragment {

        /* renamed from: D4 */
        private static String f88627D4;

        /* renamed from: A4 */
        private String f88628A4;

        /* renamed from: B4 */
        private String f88629B4;

        /* renamed from: C4 */
        private String f88630C4;

        public class NEJMTOCAdapter extends RecyclerView.Adapter {

            /* renamed from: d */
            public Context f88634d;

            /* renamed from: e */
            public ArrayList<Bundle> f88635e;

            /* renamed from: f */
            public String f88636f;

            public NEJMTOCAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
                this.f88634d = context;
                this.f88635e = arrayList;
                this.f88636f = str;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: C */
            public int mo26845C(int i2) {
                return 1;
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
                TextView textView;
                int i3;
                RippleTextSubtitleViewHolder rippleTextSubtitleViewHolder = (RippleTextSubtitleViewHolder) viewHolder;
                final Bundle bundle = this.f88635e.get(i2);
                String string = bundle.getString(this.f88636f);
                if (string.length() == 0) {
                    string = "TOC and Adverts";
                }
                if (NEJMTOCFragment.this.f88628A4.equals("0")) {
                    rippleTextSubtitleViewHolder.f88641I.setText(string);
                    rippleTextSubtitleViewHolder.f88642J.setText("");
                    rippleTextSubtitleViewHolder.f88642J.setVisibility(8);
                    if (i2 % 2 == 0) {
                        textView = rippleTextSubtitleViewHolder.f88641I;
                        i3 = ViewCompat.f13527y;
                    } else {
                        textView = rippleTextSubtitleViewHolder.f88641I;
                        i3 = -12303292;
                    }
                    textView.setTextColor(i3);
                } else {
                    rippleTextSubtitleViewHolder.f88641I.setText(string);
                    rippleTextSubtitleViewHolder.f88642J.setVisibility(0);
                    if (bundle.getString("subtitle").length() == 0) {
                        rippleTextSubtitleViewHolder.f88642J.setVisibility(8);
                    } else {
                        rippleTextSubtitleViewHolder.f88642J.setVisibility(0);
                        rippleTextSubtitleViewHolder.f88642J.setText(bundle.getString("subtitle"));
                    }
                }
                rippleTextSubtitleViewHolder.f88643K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.NEJM.NEJMTOCActivity.NEJMTOCFragment.NEJMTOCAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        NEJMTOCAdapter.this.m72415d0(bundle, i2);
                    }
                });
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
                return NEJMTOCFragment.this.new RippleTextSubtitleViewHolder(LayoutInflater.from(this.f88634d).inflate(C5562R.layout.list_view_item_ripple_text_subtitle, viewGroup, false));
            }

            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                return this.f88635e.size();
            }

            /* renamed from: d0 */
            public void m72415d0(Bundle bundle, int i2) {
                Bundle bundle2;
                CompressHelper compressHelper;
                if (NEJMTOCFragment.this.f88628A4.equals("0")) {
                    bundle2 = new Bundle();
                    bundle2.putBundle("DB", NEJMTOCFragment.this.f88788h4);
                    bundle2.putString("ParentId", bundle.getString("title"));
                    compressHelper = new CompressHelper(NEJMTOCFragment.this.m15366r());
                } else if (NEJMTOCFragment.this.f88628A4.equals("Issues")) {
                    bundle2 = new Bundle();
                    bundle2.putBundle("DB", NEJMTOCFragment.this.f88788h4);
                    bundle2.putString("ParentId", bundle.getString("title"));
                    bundle2.putString("Issue", bundle.getString("issueName"));
                    compressHelper = new CompressHelper(NEJMTOCFragment.this.m15366r());
                } else {
                    if (NEJMTOCFragment.this.f88629B4 == null || NEJMTOCFragment.this.f88630C4 != null) {
                        NEJMTOCFragment nEJMTOCFragment = NEJMTOCFragment.this;
                        nEJMTOCFragment.f88791k4.m71772A1(nEJMTOCFragment.f88788h4, bundle.getString("pid"), null, null);
                        return;
                    }
                    bundle2 = new Bundle();
                    bundle2.putBundle("DB", NEJMTOCFragment.this.f88788h4);
                    bundle2.putString("ParentId", bundle.getString("title"));
                    bundle2.putString("Issue", NEJMTOCFragment.this.f88629B4);
                    bundle2.putString("IssueSection", bundle.getString("title"));
                    compressHelper = new CompressHelper(NEJMTOCFragment.this.m15366r());
                }
                compressHelper.m71798N(NEJMTOCActivity.class, NEJMTOCFragment.class, bundle2);
            }
        }

        public class RippleTextSubtitleViewHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f88641I;

            /* renamed from: J */
            public TextView f88642J;

            /* renamed from: K */
            public MaterialRippleLayout f88643K;

            public RippleTextSubtitleViewHolder(View view) {
                super(view);
                this.f88641I = (TextView) view.findViewById(C5562R.id.text_view);
                this.f88642J = (TextView) view.findViewById(C5562R.id.sub_text_view);
                this.f88643K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            }
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72462O2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            CompressHelper compressHelper;
            Bundle bundle2;
            StringBuilder sb;
            String str;
            ArrayList<Bundle> arrayListM71817V;
            CompressHelper compressHelper2;
            Bundle bundle3;
            String str2;
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72462O2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            if (m15387y() == null || !m15387y().containsKey("ParentId")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
                this.f88628A4 = "0";
            } else {
                if (m15387y().getString("ParentId").equals("0")) {
                    appBarLayout.m35746D(true, false);
                    relativeLayout.setVisibility(0);
                } else {
                    appBarLayout.m35746D(false, false);
                    appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.NEJM.NEJMTOCActivity.NEJMTOCFragment.1
                        @Override // java.lang.Runnable
                        public void run() {
                            relativeLayout.setVisibility(0);
                        }
                    }, 800L);
                }
                this.f88628A4 = m15387y().getString("ParentId");
            }
            if (m15387y() != null && m15387y().containsKey("Issue")) {
                this.f88629B4 = m15387y().getString("Issue");
            }
            if (m15387y() != null && m15387y().containsKey("IssueSection")) {
                this.f88630C4 = m15387y().getString("IssueSection");
            }
            if (this.f88628A4.equals("0")) {
                ArrayList arrayList = new ArrayList(Arrays.asList("Perspective", "Original Articles", "Review Article", "Images in Clinical Medicine", "Case Records of the Massachusetts General Hospital", "Editorial", "Correspondence", "Corrections", "Clinical Implications of Basic Research", "Editorials", "Special Article", "Clinical Therapeutics", "Clinical Practice", "Clinical Problem-Solving", "Review Articles", "Clinical Decisions", "Health Policy Report", "Correction", "Videos in Clinical Medicine", "Sounding Board", "Health Law, Ethics, and Human Rights", "Special Report", "Medicine and Society", "Special Articles", "Special Reports", "Occasional Notes", "Statistics in Medicine"));
                Collections.sort(arrayList);
                ArrayList<Bundle> arrayList2 = new ArrayList<>();
                Bundle bundle4 = new Bundle();
                bundle4.putString("title", "Issues");
                arrayList2.add(bundle4);
                Iterator it2 = arrayList.iterator();
                while (it2.hasNext()) {
                    String str3 = (String) it2.next();
                    Bundle bundle5 = new Bundle();
                    bundle5.putString("title", str3);
                    arrayList2.add(bundle5);
                }
                this.f88794n4 = arrayList2;
            } else {
                if (this.f88628A4.equals("Issues")) {
                    compressHelper2 = this.f88791k4;
                    bundle3 = this.f88788h4;
                    str2 = "Select subtitle as title,title as subtitle,issueName from issues order by publishedDate desc";
                } else {
                    if (this.f88629B4 == null) {
                        compressHelper = this.f88791k4;
                        bundle2 = this.f88788h4;
                        sb = new StringBuilder();
                        sb.append("Select title,issueTitle as subtitle,pid from contents where sectionName = '");
                        str = this.f88628A4;
                    } else if (this.f88630C4 != null) {
                        compressHelper = this.f88791k4;
                        bundle2 = this.f88788h4;
                        sb = new StringBuilder();
                        sb.append("Select title,issueTitle as subtitle,pid from contents where issueName='");
                        sb.append(this.f88629B4);
                        sb.append("' AND sectionName = '");
                        str = this.f88630C4;
                    } else {
                        compressHelper2 = this.f88791k4;
                        bundle3 = this.f88788h4;
                        str2 = "Select distinct(sectionName) as title,'' as subtitle from contents where issueName='" + this.f88629B4 + "'";
                    }
                    sb.append(str);
                    sb.append("' order by issueDate desc");
                    arrayListM71817V = compressHelper.m71817V(bundle2, sb.toString());
                    this.f88794n4 = arrayListM71817V;
                }
                arrayListM71817V = compressHelper2.m71817V(bundle3, str2);
                this.f88794n4 = arrayListM71817V;
            }
            this.f88792l4 = new NEJMTOCAdapter(m15366r(), this.f88794n4, "title");
            this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.NEJM.NEJMTOCActivity.NEJMTOCFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
                /* renamed from: e0 */
                public void mo71953e0(Bundle bundle6, int i2) {
                    NEJMTOCFragment.this.m72468V2();
                    String string = bundle6.getString("type");
                    String string2 = bundle6.getString("contentId");
                    bundle6.getString(HTML.Tag.f74369V);
                    if (string.equals(IcyHeaders.f28171a3)) {
                        new CompressHelper(NEJMTOCFragment.this.m15366r()).m71772A1(NEJMTOCFragment.this.f88788h4, string2, null, null);
                    } else if (string.equals("5")) {
                        CompressHelper compressHelper3 = new CompressHelper(NEJMTOCFragment.this.m15366r());
                        NEJMTOCFragment nEJMTOCFragment = NEJMTOCFragment.this;
                        compressHelper3.m71772A1(nEJMTOCFragment.f88788h4, string2, nEJMTOCFragment.m72466T2(bundle6.getString("subText")), null);
                    }
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(false);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id, Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new NEJMTOCFragment());
    }
}

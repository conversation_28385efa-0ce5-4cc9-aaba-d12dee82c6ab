package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Lexi.LXSectionsViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class EPORxViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public int f88214X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88215Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88216Z4;

    /* renamed from: a5 */
    public String f88217a5;

    /* renamed from: b5 */
    public String f88218b5;

    /* renamed from: c5 */
    public Bundle f88219c5;

    /* renamed from: d5 */
    public ArrayList<Bundle> f88220d5;

    /* renamed from: I4 */
    public void m72232I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f88220d5.add(bundle);
    }

    /* renamed from: J4 */
    public void m72233J4(String str, String str2) {
        Bundle bundle = new Bundle();
        bundle.putString("Title", str);
        bundle.putString("Content", str2);
        this.f88216Z4.add(bundle);
    }

    /* renamed from: K4 */
    public String m72234K4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88214X4 + 1;
        this.f88214X4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded3\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded3(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: L4 */
    public String m72235L4(String str, String str2, String str3, String str4) {
        int i2 = this.f88214X4 + 1;
        this.f88214X4 = i2;
        return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + String.valueOf(i2) + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
    }

    /* renamed from: M4 */
    public String m72236M4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88214X4 + 1;
        this.f88214X4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: N4 */
    public String m72237N4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88214X4 + 1;
        this.f88214X4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded2\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded2(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: O4 */
    public String m72238O4(String str, int i2) {
        StringBuilder sb;
        String strM72234K4;
        Iterator<Bundle> it2 = this.f89579Q4.m71822X(this.f89566D4, "SELECT * FROM  drug_aggregate where parent_id=" + str, this.f88217a5, true).iterator();
        String string = "";
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String strM72239P4 = m72239P4(next.getString("AGGREGATE_STRING_ID"));
            String strM72238O4 = m72238O4(next.getString("ID"), i2 + 1);
            if (strM72239P4.length() == 0) {
                sb = new StringBuilder();
                sb.append(string);
                strM72234K4 = m72235L4(strM72238O4, "", "", "");
            } else if (strM72238O4.length() == 0) {
                sb = new StringBuilder();
                sb.append(string);
                strM72234K4 = m72235L4(strM72239P4, "", "", "");
            } else {
                sb = new StringBuilder();
                sb.append(string);
                strM72234K4 = m72234K4(strM72239P4, "", "LTR", strM72238O4, "", "margin-left: " + (i2 * 5) + CSS.Value.f74124h0, "");
            }
            sb.append(strM72234K4);
            string = sb.toString();
        }
        return string;
    }

    /* renamed from: P4 */
    public String m72239P4(String str) {
        return m72241R4(str, "aggregate");
    }

    /* renamed from: Q4 */
    public String m72240Q4(String str) {
        return m72241R4(str, "bbw");
    }

    /* renamed from: R4 */
    public String m72241R4(String str, String str2) {
        if (str != null && str.length() != 0) {
            CompressHelper compressHelper = this.f89579Q4;
            Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71819W(this.f89566D4, "select * from " + str2 + "_string where id=" + str, this.f88217a5));
            if (bundleM71890s1 != null && bundleM71890s1.size() != 0) {
                return bundleM71890s1.getString("STRING");
            }
        }
        return "";
    }

    /* renamed from: S4 */
    public String m72242S4(String str) {
        Iterator<Bundle> it2 = this.f89579Q4.m71822X(this.f89566D4, "SELECT * FROM  drug_aggregate where drug_id=%@ AND aggregate_type_id=" + this.f88218b5, this.f88217a5, true).iterator();
        String str2 = "";
        while (it2.hasNext()) {
            Bundle next = it2.next();
            str2 = str2 + m72237N4(m72239P4(next.getString("AGGREGATE_STRING_ID")), "", "LTR", m72238O4(next.getString("ID"), 1), "", "margin-left: 20px", "");
        }
        return str2;
    }

    /* renamed from: T4 */
    public String m72243T4(String str) {
        return m72241R4(str, "pricing");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        this.f89565C4 = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f88219c5 = new Bundle();
        m72835r4(this.f89565C4, bundle);
        this.f88217a5 = "RX.sqlite";
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxViewerActivityFragment.1
            /* JADX WARN: Multi-variable type inference failed */
            /* JADX WARN: Type inference failed for: r6v4, types: [java.util.ArrayList] */
            @Override // java.lang.Runnable
            public void run() {
                RunnableC48481 runnableC48481;
                String string;
                String str;
                String str2;
                String str3;
                String str4;
                String str5;
                String str6;
                ArrayList<Bundle> arrayList;
                String str7;
                String str8;
                String str9;
                String str10;
                String str11;
                String str12;
                Bundle bundle2;
                String str13;
                ArrayList arrayList2;
                String string2;
                String str14;
                String str15;
                String str16;
                RunnableC48481 runnableC484812;
                String str17;
                String str18;
                String str19;
                String str20;
                String str21;
                String str22;
                String str23;
                String str24;
                String str25;
                String string3;
                String str26;
                String str27;
                String str28;
                String str29;
                String str30;
                String string4;
                String str31;
                String str32;
                String str33;
                String str34;
                String str35;
                String str36;
                String string5;
                String str37;
                String str38;
                String str39;
                Bundle bundle3;
                String str40;
                String str41;
                String str42;
                String str43;
                String str44;
                String str45;
                String str46;
                Iterator<Bundle> it2;
                String str47;
                String str48;
                String str49;
                String str50;
                Bundle bundle4;
                String str51;
                String str52 = "DRUG_TYPE";
                String str53 = "GENERIC_ID";
                try {
                    String str54 = EPORxViewerActivityFragment.this.f89563A4;
                    if (str54 != null && str54.length() != 0) {
                        return;
                    }
                    EPORxViewerActivityFragment ePORxViewerActivityFragment = EPORxViewerActivityFragment.this;
                    ePORxViewerActivityFragment.f88214X4 = 0;
                    ePORxViewerActivityFragment.f88216Z4 = new ArrayList<>();
                    EPORxViewerActivityFragment.this.f88220d5 = new ArrayList<>();
                    String str55 = EPORxViewerActivityFragment.this.f89567E4.split("-")[1];
                    EPORxViewerActivityFragment ePORxViewerActivityFragment2 = EPORxViewerActivityFragment.this;
                    ePORxViewerActivityFragment2.f88218b5 = str55;
                    ?? M71822X = ePORxViewerActivityFragment2.f89579Q4.m71822X(ePORxViewerActivityFragment2.f89566D4, "Select * from drug where ID=" + str55, EPORxViewerActivityFragment.this.f88217a5, true);
                    try {
                        if (M71822X == 0 || M71822X.size() == 0) {
                            EPORxViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                            return;
                        }
                        Bundle bundle5 = (Bundle) M71822X.get(0);
                        String str56 = "0";
                        String str57 = "ID";
                        String str58 = "NAME";
                        if (bundle5.getString("GENERIC_ID").length() == 0 || bundle5.getString("GENERIC_ID").equals("0")) {
                            string = bundle5.getString("ID");
                            bundle5.getString("NAME");
                        } else {
                            string = bundle5.getString("GENERIC_ID");
                            try {
                                EPORxViewerActivityFragment ePORxViewerActivityFragment3 = EPORxViewerActivityFragment.this;
                                CompressHelper compressHelper = ePORxViewerActivityFragment3.f89579Q4;
                                compressHelper.m71890s1(compressHelper.m71822X(ePORxViewerActivityFragment3.f89566D4, "SELECT  DRUG.ID ,  DRUG.CLINICAL_ID ,  DRUG.GENERIC_ID ,  DRUG.NAME ,  DRUG.DRUG_TYPE ,  DRUG.ACTIVE ,  DRUG.ADULT_DSG_ID ,  DRUG.PEDS_DSG_ID ,  DRUG.MFR_STRING_ID ,  DRUG.BBW_ID   FROM DRUG   WHERE  ID =  " + str55, EPORxViewerActivityFragment.this.f88217a5, true)).getString("NAME");
                            } catch (Exception e2) {
                                FirebaseCrashlytics.m48010d().m48016g(e2);
                                e2.printStackTrace();
                            }
                        }
                        String str59 = string;
                        EPORxViewerActivityFragment.this.f89568F4 = bundle5.getString("NAME");
                        EPORxViewerActivityFragment ePORxViewerActivityFragment4 = EPORxViewerActivityFragment.this;
                        Iterator<Bundle> it3 = ePORxViewerActivityFragment4.f89579Q4.m71822X(ePORxViewerActivityFragment4.f89566D4, "SELECT  ADULT_DOSING.ID ,  ADULT_DOSING.DRUG_ID ,  ADULT_DOSING.DISPLAY_ORDER ,  ADULT_DOSING.INDICATION_STRING_ID ,  ADULT_DOSING.DOSING_STRING_ID ,  ADULT_DOSING.INFO_STRING_ID   FROM ADULT_DOSING   WHERE  DRUG_ID =  " + str55 + " ORDER BY  ADULT_DOSING.DISPLAY_ORDER", EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                        String str60 = "";
                        String str61 = "";
                        while (true) {
                            str = "</div>";
                            str2 = "<div class=\"cellTitle\">";
                            str3 = str53;
                            str4 = "DOSING_STRING_ID";
                            str5 = str52;
                            str6 = "INDICATION_STRING_ID";
                            if (!it3.hasNext()) {
                                break;
                            }
                            Bundle next = it3.next();
                            str61 = str61 + EPORxViewerActivityFragment.this.m72237N4(EPORxViewerActivityFragment.this.m72245V4(next.getString("INDICATION_STRING_ID")), "", "LTR", "<div class=\"cellTitle\">" + EPORxViewerActivityFragment.this.m72245V4(next.getString("DOSING_STRING_ID")) + "</div>" + EPORxViewerActivityFragment.this.m72245V4(next.getString("INFO_STRING_ID")), "", "margin-left: 20px", "");
                            str60 = str60;
                            str57 = str57;
                            str58 = str58;
                            str53 = str3;
                            str52 = str5;
                            bundle5 = bundle5;
                            str59 = str59;
                            str56 = str56;
                        }
                        Bundle bundle6 = bundle5;
                        String str62 = str59;
                        String str63 = str58;
                        String str64 = str56;
                        String str65 = str57;
                        String str66 = str60;
                        if (str61.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Adult Dosing", str61);
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment5 = EPORxViewerActivityFragment.this;
                        Iterator<Bundle> it4 = ePORxViewerActivityFragment5.f89579Q4.m71822X(ePORxViewerActivityFragment5.f89566D4, "SELECT  PEDS_DOSING.ID ,  PEDS_DOSING.DRUG_ID ,  PEDS_DOSING.DISPLAY_ORDER ,  PEDS_DOSING.INDICATION_STRING_ID ,  PEDS_DOSING.DOSING_STRING_ID ,  PEDS_DOSING.INFO_STRING_ID   FROM PEDS_DOSING   WHERE  DRUG_ID =  " + str55 + "  ORDER BY  PEDS_DOSING.DISPLAY_ORDER", EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                        String str67 = str66;
                        while (it4.hasNext()) {
                            Bundle next2 = it4.next();
                            str67 = str67 + EPORxViewerActivityFragment.this.m72237N4(EPORxViewerActivityFragment.this.m72245V4(next2.getString(str6)), "", "LTR", str2 + EPORxViewerActivityFragment.this.m72245V4(next2.getString(str4)) + str + EPORxViewerActivityFragment.this.m72245V4(next2.getString("INFO_STRING_ID")), "", "margin-left: 20px", "");
                            str65 = str65;
                            str66 = str66;
                            str4 = str4;
                            str2 = str2;
                            str = str;
                            str6 = str6;
                        }
                        String str68 = str6;
                        String str69 = str4;
                        String str70 = str2;
                        String str71 = str;
                        String str72 = str66;
                        String str73 = str65;
                        if (str67.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Pediatric Dosing", str67);
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment6 = EPORxViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71822X = ePORxViewerActivityFragment6.f89579Q4.m71822X(ePORxViewerActivityFragment6.f89566D4, "SELECT  DRUG_CLASS.ID ,  DRUG_CLASS.NAME   FROM DRUG_TO_DRUG_CLASS   JOIN DRUG_CLASS ON  DRUG_TO_DRUG_CLASS.DRUG_ID =  " + str55 + "   AND DRUG_CLASS.ID = DRUG_TO_DRUG_CLASS.DRUG_CLASS_ID    ORDER BY  DRUG_CLASS.NAME COLLATE NOCASE", EPORxViewerActivityFragment.this.f88217a5, true);
                        ArrayList arrayList3 = new ArrayList();
                        Iterator<Bundle> it5 = arrayListM71822X.iterator();
                        while (it5.hasNext()) {
                            Bundle next3 = it5.next();
                            next3.getString(str63);
                            EPORxViewerActivityFragment ePORxViewerActivityFragment7 = EPORxViewerActivityFragment.this;
                            arrayList3.addAll(ePORxViewerActivityFragment7.f89579Q4.m71822X(ePORxViewerActivityFragment7.f89566D4, "SELECT  DRUG_CLASS.ID ,  DRUG_CLASS.NAME   FROM DRUG_CLASS_RELATIONSHIP   JOIN DRUG_CLASS ON  DRUG_CLASS_RELATIONSHIP.CLASS_0_ID =  " + next3.getString(str73) + "   AND DRUG_CLASS.ID = DRUG_CLASS_RELATIONSHIP.CLASS_1_ID   UNION SELECT  DRUG_CLASS.ID ,  DRUG_CLASS.NAME   FROM DRUG_CLASS_RELATIONSHIP   JOIN DRUG_CLASS ON  DRUG_CLASS_RELATIONSHIP.CLASS_1_ID =  " + next3.getString(str73) + "   AND DRUG_CLASS.ID = DRUG_CLASS_RELATIONSHIP.CLASS_0_ID   order by name asc", EPORxViewerActivityFragment.this.f88217a5, true));
                        }
                        Bundle bundle7 = bundle6;
                        if (bundle7.getString(str5).equals("6")) {
                            arrayList = arrayListM71822X;
                            str7 = "<ul>";
                            str8 = "\">";
                            String str74 = ",,,,,";
                            str9 = str5;
                            str10 = "margin-left: 20px";
                            EPORxViewerActivityFragment ePORxViewerActivityFragment8 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it6 = ePORxViewerActivityFragment8.f89579Q4.m71822X(ePORxViewerActivityFragment8.f89566D4, "SELECT  DRUG.ID ,  DRUG.CLINICAL_ID ,  DRUG.GENERIC_ID ,  DRUG.NAME ,  DRUG.DRUG_TYPE ,  DRUG.ACTIVE ,  DRUG.ADULT_DSG_ID ,  DRUG.PEDS_DSG_ID ,  DRUG.MFR_STRING_ID ,  DRUG.BBW_ID   FROM DRUG   WHERE  GENERIC_ID =  " + bundle7.getString(str3) + "   AND   ACTIVE = 1      ORDER BY  DRUG.NAME COLLATE NOCASE", EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String str75 = str72;
                            while (it6.hasNext()) {
                                Bundle next4 = it6.next();
                                str75 = str75 + "<li><a href=\"rx://" + next4.getString(str73) + str8 + next4.getString(str63) + "</a><br/></li>";
                            }
                            if (str75.length() > 0) {
                                try {
                                    StringBuilder sb = new StringBuilder();
                                    sb.append(str7);
                                    sb.append(str75);
                                    str11 = "</ul>";
                                    sb.append(str11);
                                    String string6 = sb.toString();
                                    StringBuilder sb2 = new StringBuilder();
                                    sb2.append(str72);
                                    str12 = str72;
                                    bundle2 = bundle7;
                                    str13 = "<li><a href=\"grp://";
                                    arrayList2 = arrayList3;
                                    sb2.append(EPORxViewerActivityFragment.this.m72237N4("Other OTCs with Same Active Ingredients", "", "LTR", string6, "", str10, ""));
                                    string2 = sb2.toString();
                                } catch (Exception e3) {
                                    e = e3;
                                    runnableC48481 = this;
                                    Exception exc = e;
                                    exc.printStackTrace();
                                    EPORxViewerActivityFragment.this.f89595p4 = exc.getLocalizedMessage();
                                }
                            } else {
                                bundle2 = bundle7;
                                str13 = "<li><a href=\"grp://";
                                str12 = str72;
                                str11 = "</ul>";
                                arrayList2 = arrayList3;
                                string2 = str12;
                            }
                            Iterator<Bundle> it7 = arrayList.iterator();
                            String string7 = str12;
                            while (it7.hasNext()) {
                                Bundle next5 = it7.next();
                                StringBuilder sb3 = new StringBuilder();
                                sb3.append(string7);
                                sb3.append(str13);
                                sb3.append(next5.getString(str73));
                                String str76 = str74;
                                sb3.append(str76);
                                sb3.append(next5.getString(str63));
                                sb3.append(str8);
                                sb3.append(next5.getString(str63));
                                sb3.append("</a><br/></li>");
                                string7 = sb3.toString();
                                str74 = str76;
                            }
                            String str77 = str74;
                            if (string7.length() > 0) {
                                String str78 = str7 + string7 + str11;
                                StringBuilder sb4 = new StringBuilder();
                                sb4.append(string2);
                                try {
                                    str15 = str63;
                                    runnableC484812 = this;
                                    str14 = str73;
                                    str16 = str77;
                                    sb4.append(EPORxViewerActivityFragment.this.m72237N4("Same Subclass", "", "LTR", str78, "", str10, ""));
                                    string2 = sb4.toString();
                                } catch (Exception e4) {
                                    e = e4;
                                    runnableC48481 = this;
                                    Exception exc2 = e;
                                    exc2.printStackTrace();
                                    EPORxViewerActivityFragment.this.f89595p4 = exc2.getLocalizedMessage();
                                }
                            } else {
                                str14 = str73;
                                str15 = str63;
                                str16 = str77;
                                runnableC484812 = this;
                            }
                            Iterator it8 = arrayList2.iterator();
                            String str79 = str12;
                            while (it8.hasNext()) {
                                Bundle bundle8 = (Bundle) it8.next();
                                str79 = str79 + str13 + bundle8.getString("DRUG_CLASS.ID") + str16 + bundle8.getString("DRUG_CLASS.NAME") + str8 + bundle8.getString("DRUG_CLASS.NAME") + "</a><br/></li>";
                            }
                            if (str79.length() > 0) {
                                string2 = string2 + EPORxViewerActivityFragment.this.m72237N4("Related Subclasses", "", "LTR", str7 + str79 + str11, "", str10, "");
                            }
                        } else {
                            Iterator<Bundle> it9 = arrayListM71822X.iterator();
                            String str80 = str72;
                            while (it9.hasNext()) {
                                Iterator<Bundle> it10 = it9;
                                Bundle next6 = it9.next();
                                str80 = str80 + "<li><a href=\"grp://" + next6.getString(str73) + ",,,,," + next6.getString(str63) + "\">" + next6.getString(str63) + "</a></li>";
                                it9 = it10;
                                bundle7 = bundle7;
                            }
                            Bundle bundle9 = bundle7;
                            if (str80.length() > 0) {
                                String str81 = "<ul>" + str80 + "</ul>";
                                StringBuilder sb5 = new StringBuilder();
                                sb5.append(str72);
                                arrayList = arrayListM71822X;
                                str7 = "<ul>";
                                str50 = ",,,,,";
                                str49 = str72;
                                bundle4 = bundle9;
                                str51 = "</ul>";
                                str8 = "\">";
                                str9 = str5;
                                str10 = "margin-left: 20px";
                                sb5.append(EPORxViewerActivityFragment.this.m72237N4("Subclasses", "", "LTR", str81, "", "margin-left: 20px", ""));
                                string2 = sb5.toString();
                            } else {
                                arrayList = arrayListM71822X;
                                str49 = str72;
                                str7 = "<ul>";
                                str50 = ",,,,,";
                                str9 = str5;
                                str10 = "margin-left: 20px";
                                bundle4 = bundle9;
                                str51 = "</ul>";
                                str8 = "\">";
                                string2 = str49;
                            }
                            bundle2 = bundle4;
                            str15 = str63;
                            str12 = str49;
                            str11 = str51;
                            runnableC484812 = this;
                            String str82 = str50;
                            str14 = str73;
                            str16 = str82;
                        }
                        if (string2.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Alternatives", string2);
                        }
                        Bundle bundle10 = bundle2;
                        String str83 = str9;
                        if (bundle10.getString(str83).equals("6")) {
                            String strM72242S4 = EPORxViewerActivityFragment.this.m72242S4(IcyHeaders.f28171a3);
                            if (strM72242S4.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Uses", strM72242S4);
                            }
                            String strM72242S42 = EPORxViewerActivityFragment.this.m72242S4("5");
                            if (strM72242S42.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Dosing", strM72242S42);
                            }
                            String strM72242S43 = EPORxViewerActivityFragment.this.m72242S4("9");
                            if (strM72242S43.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Formulations", strM72242S43);
                            }
                        }
                        String string8 = bundle10.getString("BBW_ID");
                        String str84 = str64;
                        String str85 = "DESCRIPTION_STRING_ID";
                        String str86 = " order by display_order";
                        if (string8.equals(str84) || string8.length() == 0) {
                            str17 = str83;
                            str18 = str7;
                            str19 = " order by display_order";
                            str20 = "DESCRIPTION_STRING_ID";
                            str21 = str11;
                            str22 = str84;
                        } else {
                            EPORxViewerActivityFragment ePORxViewerActivityFragment9 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it11 = ePORxViewerActivityFragment9.f89579Q4.m71822X(ePORxViewerActivityFragment9.f89566D4, "Select * from BBW_ENTRY where bbw_id=" + string8 + " order by display_order", EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String str87 = str12;
                            while (it11.hasNext()) {
                                Bundle next7 = it11.next();
                                str87 = str87 + EPORxViewerActivityFragment.this.m72237N4(EPORxViewerActivityFragment.this.m72240Q4(next7.getString("HEADER_STRING_ID")), "", "LTR", EPORxViewerActivityFragment.this.m72240Q4(next7.getString(str85)), "", str10, "");
                                str86 = str86;
                                str84 = str84;
                                str7 = str7;
                                str11 = str11;
                                str83 = str83;
                                str85 = str85;
                            }
                            str17 = str83;
                            str18 = str7;
                            str19 = str86;
                            str20 = str85;
                            str21 = str11;
                            str22 = str84;
                            if (str87.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Black Box Warnings", str87);
                            }
                        }
                        String string9 = bundle10.getString("CLINICAL_ID");
                        EPORxViewerActivityFragment ePORxViewerActivityFragment10 = EPORxViewerActivityFragment.this;
                        CompressHelper compressHelper2 = ePORxViewerActivityFragment10.f89579Q4;
                        Bundle bundleM71890s1 = compressHelper2.m71890s1(compressHelper2.m71822X(ePORxViewerActivityFragment10.f89566D4, "SELECT  CLINICAL.ID ,  CLINICAL.NAME ,  CLINICAL.TYPE ,  CLINICAL.METABOLISM_STRING_ID ,  CLINICAL.EXCRETION_STRING_ID ,  CLINICAL.MECHANISM_STRING_ID ,  CLINICAL.PREG_ENTRY_ID ,  CLINICAL.LACT_ENTRY_ID ,  CLINICAL.DEA_ENTRY_ID   FROM CLINICAL   WHERE  ID =  " + string9, EPORxViewerActivityFragment.this.f88217a5, true));
                        if (bundleM71890s1 != null) {
                            String strM72245V4 = EPORxViewerActivityFragment.this.m72245V4(bundleM71890s1.getString("METABOLISM_STRING_ID"));
                            String strM72245V42 = EPORxViewerActivityFragment.this.m72245V4(bundleM71890s1.getString("EXCRETION_STRING_ID"));
                            String strM72245V43 = EPORxViewerActivityFragment.this.m72245V4(bundleM71890s1.getString("MECHANISM_STRING_ID"));
                            if (strM72245V4.length() > 0) {
                                StringBuilder sb6 = new StringBuilder();
                                String str88 = str12;
                                sb6.append(str88);
                                str24 = str19;
                                str25 = str88;
                                str23 = string9;
                                sb6.append(EPORxViewerActivityFragment.this.m72237N4("Metabolism", "", "LTR", strM72245V4, "", str10, ""));
                                string3 = sb6.toString();
                            } else {
                                str23 = string9;
                                str24 = str19;
                                str25 = str12;
                                string3 = str25;
                            }
                            if (strM72245V42.length() > 0) {
                                string3 = string3 + EPORxViewerActivityFragment.this.m72237N4("Excretion", "", "LTR", strM72245V42, "", str10, "");
                            }
                            if (strM72245V43.length() > 0) {
                                string3 = string3 + EPORxViewerActivityFragment.this.m72237N4("Mechanism of Action", "", "LTR", strM72245V43, "", str10, "");
                            }
                        } else {
                            str23 = string9;
                            str24 = str19;
                            str25 = str12;
                            string3 = str25;
                        }
                        ArrayList arrayList4 = new ArrayList();
                        Iterator<Bundle> it12 = arrayList.iterator();
                        while (it12.hasNext()) {
                            Bundle next8 = it12.next();
                            StringBuilder sb7 = new StringBuilder();
                            sb7.append("<a href=\"grp://");
                            String str89 = str14;
                            sb7.append(next8.getString(str89));
                            sb7.append(str16);
                            String str90 = str15;
                            sb7.append(next8.getString(str90));
                            sb7.append(str8);
                            sb7.append(next8.getString(str90));
                            sb7.append("</a>");
                            arrayList4.add(sb7.toString());
                            str15 = str90;
                            str14 = str89;
                        }
                        String str91 = str15;
                        String str92 = str14;
                        if (arrayList4.size() > 0) {
                            String strJoin = StringUtils.join(arrayList4, "; ");
                            StringBuilder sb8 = new StringBuilder();
                            sb8.append(string3);
                            str26 = str8;
                            str27 = str91;
                            str28 = str92;
                            sb8.append(EPORxViewerActivityFragment.this.m72237N4("Subclasses", "", "LTR", strJoin, "", str10, ""));
                            string3 = sb8.toString();
                        } else {
                            str26 = str8;
                            str27 = str91;
                            str28 = str92;
                        }
                        if (string3.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Pharmacology", string3);
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment11 = EPORxViewerActivityFragment.this;
                        CompressHelper compressHelper3 = ePORxViewerActivityFragment11.f89579Q4;
                        Bundle bundleM71890s12 = compressHelper3.m71890s1(compressHelper3.m71822X(ePORxViewerActivityFragment11.f89566D4, "SELECT  PREGNANCY_ENTRY.ID ,  PREGNANCY_ENTRY.DISPLAY_ORDER ,  PREGNANCY_ENTRY.TERM ,  PREGNANCY_ENTRY.DEFINITION   FROM CLINICAL_PREGNANCY   JOIN PREGNANCY_ENTRY ON  CLINICAL_PREGNANCY.CLINICAL_ID =  " + str62 + "   AND PREGNANCY_ENTRY.ID = CLINICAL_PREGNANCY.PREG_ENTRY_ID    ORDER BY  PREGNANCY_ENTRY.DISPLAY_ORDER", EPORxViewerActivityFragment.this.f88217a5, true));
                        if (bundleM71890s12 != null) {
                            String str93 = "<b>" + bundleM71890s12.getString("TERM") + ": </b>" + bundleM71890s12.getString("DEFINITION");
                            StringBuilder sb9 = new StringBuilder();
                            sb9.append(str25);
                            str29 = str28;
                            str30 = str62;
                            sb9.append(EPORxViewerActivityFragment.this.m72237N4("Pregnancy", "", "LTR", str93, "", str10, ""));
                            string4 = sb9.toString();
                        } else {
                            str29 = str28;
                            str30 = str62;
                            string4 = str25;
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment12 = EPORxViewerActivityFragment.this;
                        CompressHelper compressHelper4 = ePORxViewerActivityFragment12.f89579Q4;
                        Bundle bundleM71890s13 = compressHelper4.m71890s1(compressHelper4.m71822X(ePORxViewerActivityFragment12.f89566D4, "SELECT  LACTATION_ENTRY.ID ,  LACTATION_ENTRY.DISPLAY_ORDER ,  LACTATION_ENTRY.TERM ,  LACTATION_ENTRY.DEFINITION   FROM CLINICAL_LACTATION   JOIN LACTATION_ENTRY ON  CLINICAL_LACTATION.CLINICAL_ID =  " + str30 + "  AND LACTATION_ENTRY.ID = CLINICAL_LACTATION.LACT_ENTRY_ID    ORDER BY  LACTATION_ENTRY.DISPLAY_ORDER", EPORxViewerActivityFragment.this.f88217a5, true));
                        if (bundleM71890s13 != null) {
                            string4 = string4 + EPORxViewerActivityFragment.this.m72237N4("Lactation", "", "LTR", "<b>" + bundleM71890s13.getString("TERM") + ": </b>" + bundleM71890s13.getString("DEFINITION"), "", str10, "");
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment13 = EPORxViewerActivityFragment.this;
                        CompressHelper compressHelper5 = ePORxViewerActivityFragment13.f89579Q4;
                        Bundle bundle11 = ePORxViewerActivityFragment13.f89566D4;
                        StringBuilder sb10 = new StringBuilder();
                        sb10.append("SELECT  SAFETY.ID ,  SAFETY.DRUG_ID ,  SAFETY.DISPLAY_ORDER ,  SAFETY.HEADER_STRING_ID ,  SAFETY.SAFETY_STRING_ID   FROM SAFETY   WHERE  DRUG_ID =  ");
                        String str94 = str55;
                        sb10.append(str94);
                        sb10.append(" ORDER BY  SAFETY.DISPLAY_ORDER");
                        for (Iterator<Bundle> it13 = compressHelper5.m71822X(bundle11, sb10.toString(), EPORxViewerActivityFragment.this.f88217a5, true).iterator(); it13.hasNext(); it13 = it13) {
                            Bundle next9 = it13.next();
                            string4 = string4 + EPORxViewerActivityFragment.this.m72237N4(EPORxViewerActivityFragment.this.m72244U4(next9.getString("HEADER_STRING_ID")), "", "LTR", EPORxViewerActivityFragment.this.m72244U4(next9.getString("SAFETY_STRING_ID")), "", str10, "");
                            str94 = str94;
                        }
                        String str95 = str94;
                        if (string4.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Safety/Monitoring", string4);
                        }
                        String string10 = bundle10.getString("MFR_STRING_ID");
                        String str96 = (string10.length() == 0 || string10.equals(str22)) ? str25 : str25 + EPORxViewerActivityFragment.this.m72237N4("Manufacturer", "", "LTR", EPORxViewerActivityFragment.this.m72245V4(string10), "", str10, "");
                        EPORxViewerActivityFragment ePORxViewerActivityFragment14 = EPORxViewerActivityFragment.this;
                        CompressHelper compressHelper6 = ePORxViewerActivityFragment14.f89579Q4;
                        Bundle bundle12 = ePORxViewerActivityFragment14.f89566D4;
                        StringBuilder sb11 = new StringBuilder();
                        sb11.append("SELECT  DEA_ENTRY.ID ,  DEA_ENTRY.DISPLAY_ORDER ,  DEA_ENTRY.TERM ,  DEA_ENTRY.DEFINITION   FROM CLINICAL_DEA   JOIN DEA_ENTRY ON  CLINICAL_DEA.CLINICAL_ID =  ");
                        String str97 = str23;
                        sb11.append(str97);
                        sb11.append("   AND DEA_ENTRY.ID = CLINICAL_DEA.DEA_ENTRY_ID    ORDER BY  DEA_ENTRY.DISPLAY_ORDER");
                        Bundle bundleM71890s14 = compressHelper6.m71890s1(compressHelper6.m71822X(bundle12, sb11.toString(), EPORxViewerActivityFragment.this.f88217a5, true));
                        if (bundleM71890s14 != null) {
                            str96 = str96 + EPORxViewerActivityFragment.this.m72237N4("DEA/FDA", "", "LTR", "<b>" + bundleM71890s14.getString("TERM") + " :</b>" + bundleM71890s14.getString("DEFINITION"), "", str10, "");
                        }
                        String str98 = str96;
                        EPORxViewerActivityFragment ePORxViewerActivityFragment15 = EPORxViewerActivityFragment.this;
                        Iterator<Bundle> it14 = ePORxViewerActivityFragment15.f89579Q4.m71822X(ePORxViewerActivityFragment15.f89566D4, "SELECT  PRICING_STRENGTH.PRICING_STRENGTH_ID ,  PRICING_STRENGTH.DRUG_ID ,  PRICING_STRENGTH.HEADER_ID ,  PRICING_STRENGTH.SOURCE_ID ,  PRICING_STRENGTH.UNIT_ID ,  PRICING_STRENGTH.COMBO_ID ,  PRICING_STRENGTH.FORM_ID ,  PRICING_STRENGTH.STRENGTH ,  PRICING_STRENGTH.QUANTITY ,  PRICING_STRENGTH.PRICE   FROM PRICING_STRENGTH   WHERE  DRUG_ID =  " + str95 + "  ORDER BY  PRICING_STRENGTH.FORM_ID ,  PRICING_STRENGTH.STRENGTH", EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                        String str99 = str25;
                        while (it14.hasNext()) {
                            Bundle next10 = it14.next();
                            String strM72243T4 = EPORxViewerActivityFragment.this.m72243T4(next10.getString("HEADER_ID"));
                            String strM72243T42 = EPORxViewerActivityFragment.this.m72243T4(next10.getString("SOURCE_ID"));
                            String strM72243T43 = EPORxViewerActivityFragment.this.m72243T4(next10.getString("UNIT_ID"));
                            EPORxViewerActivityFragment ePORxViewerActivityFragment16 = EPORxViewerActivityFragment.this;
                            CompressHelper compressHelper7 = ePORxViewerActivityFragment16.f89579Q4;
                            Bundle bundle13 = ePORxViewerActivityFragment16.f89566D4;
                            Bundle bundle14 = bundle10;
                            StringBuilder sb12 = new StringBuilder();
                            String str100 = str30;
                            sb12.append("SELECT  FORMULATION.ID ,  FORMULATION.STRING ,  FORMULATION.ABBR   FROM FORMULATION   WHERE  ID = ");
                            sb12.append(next10.getString("FORM_ID"));
                            String str101 = str95;
                            Bundle bundleM71890s15 = compressHelper7.m71890s1(compressHelper7.m71822X(bundle13, sb12.toString(), EPORxViewerActivityFragment.this.f88217a5, true));
                            str99 = str99 + EPORxViewerActivityFragment.this.m72237N4(strM72243T4, "", "LTR", strM72243T42 + "<br/>[" + bundleM71890s15.getString("STRING") + "] " + next10.getString("STRENGTH") + strM72243T43 + " (" + next10.getString("QUANTITY") + StringUtils.SPACE + bundleM71890s15.getString("ABBR") + "): " + next10.getString("PRICE") + "$", "", str10, "");
                            bundle10 = bundle14;
                            str30 = str100;
                            str95 = str101;
                        }
                        String str102 = str95;
                        String str103 = str30;
                        Bundle bundle15 = bundle10;
                        if (str99.length() > 0) {
                            str98 = str98 + str99;
                        }
                        if (str98.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Manufacturer/Pricing", str98);
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment17 = EPORxViewerActivityFragment.this;
                        CompressHelper compressHelper8 = ePORxViewerActivityFragment17.f89579Q4;
                        Bundle bundle16 = ePORxViewerActivityFragment17.f89566D4;
                        StringBuilder sb13 = new StringBuilder();
                        sb13.append("select * from contraindication inner join general_string on contraindication.description_string_id = general_string.id where clinical_id=");
                        sb13.append(str97);
                        String str104 = str24;
                        sb13.append(str104);
                        Iterator<Bundle> it15 = compressHelper8.m71822X(bundle16, sb13.toString(), EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                        String str105 = str25;
                        while (it15.hasNext()) {
                            str105 = str105 + "<li>" + it15.next().getString("STRING") + "</li>";
                        }
                        if (str105.length() > 0) {
                            StringBuilder sb14 = new StringBuilder();
                            sb14.append("<ul style=\"margin-left=20px;margin-top:10px\">");
                            sb14.append(str105);
                            str31 = str21;
                            sb14.append(str31);
                            EPORxViewerActivityFragment.this.m72233J4("Contraindications/Cautions", sb14.toString());
                        } else {
                            str31 = str21;
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment18 = EPORxViewerActivityFragment.this;
                        Iterator<Bundle> it16 = ePORxViewerActivityFragment18.f89579Q4.m71822X(ePORxViewerActivityFragment18.f89566D4, "select * from serious_reaction inner join general_string on serious_reaction.description_string_id = general_string.id where clinical_id=" + str97 + str104, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                        String str106 = str25;
                        while (it16.hasNext()) {
                            str106 = str106 + "<li>" + it16.next().getString("STRING") + "</li>";
                        }
                        if (str106.length() > 0) {
                            StringBuilder sb15 = new StringBuilder();
                            String str107 = str18;
                            sb15.append(str107);
                            sb15.append(str106);
                            sb15.append(str31);
                            String string11 = sb15.toString();
                            StringBuilder sb16 = new StringBuilder();
                            sb16.append(str25);
                            str33 = str25;
                            str32 = str31;
                            str34 = "<li>";
                            str36 = str107;
                            str35 = "STRING";
                            sb16.append(EPORxViewerActivityFragment.this.m72237N4("Serious Reactions", "", "LTR", string11, "", str10, ""));
                            string5 = sb16.toString();
                        } else {
                            str32 = str31;
                            str33 = str25;
                            str34 = "<li>";
                            str35 = "STRING";
                            str36 = str18;
                            string5 = str33;
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment19 = EPORxViewerActivityFragment.this;
                        Iterator<Bundle> it17 = ePORxViewerActivityFragment19.f89579Q4.m71822X(ePORxViewerActivityFragment19.f89566D4, "select * from common_reaction inner join general_string on common_reaction.description_string_id = general_string.id where clinical_id=" + str97 + str104, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                        String str108 = str33;
                        while (it17.hasNext()) {
                            str108 = str108 + str34 + it17.next().getString(str35) + "</li>";
                        }
                        if (str108.length() > 0) {
                            StringBuilder sb17 = new StringBuilder();
                            str38 = str36;
                            sb17.append(str38);
                            sb17.append(str108);
                            str37 = str32;
                            sb17.append(str37);
                            string5 = string5 + EPORxViewerActivityFragment.this.m72237N4("Common Reactions", "", "LTR", sb17.toString(), "", str10, "");
                        } else {
                            str37 = str32;
                            str38 = str36;
                        }
                        if (string5.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Adverse Reactions", string5);
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment20 = EPORxViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71822X2 = ePORxViewerActivityFragment20.f89579Q4.m71822X(ePORxViewerActivityFragment20.f89566D4, "Select * from pill_pictures where drug_id=" + str102, EPORxViewerActivityFragment.this.f88217a5, true);
                        EPORxViewerActivityFragment.this.f88215Y4 = arrayListM71822X2;
                        Iterator<Bundle> it18 = arrayListM71822X2.iterator();
                        String str109 = str33;
                        while (it18.hasNext()) {
                            str109 = str109 + "<div style=\"width:90%%;overflow: scroll;\"><img src=\"" + ("http://www.epocrates.com/pillimages/" + it18.next().getString("FILENAME") + ".jpg") + "\" /></div>";
                        }
                        if (str109.length() > 0) {
                            EPORxViewerActivityFragment.this.m72233J4("Pill Picture", "</br>" + str109);
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment21 = EPORxViewerActivityFragment.this;
                        CompressHelper compressHelper9 = ePORxViewerActivityFragment21.f89579Q4;
                        Bundle bundle17 = ePORxViewerActivityFragment21.f89566D4;
                        StringBuilder sb18 = new StringBuilder();
                        sb18.append("SELECT DDI.ID AS DDI_ID,                    DRUG_TO_DDI_GROUP.GROUP_ID AS GROUP_0_ID,                    CASE WHEN DDI.GROUP_0_ID = DRUG_TO_DDI_GROUP.GROUP_ID THEN DDI.GROUP_1_ID ELSE DDI.GROUP_0_ID END AS GROUP_1_ID,                    DDI_GROUP.NAME AS GROUP_1_NAME,                    DDI.CATEGORY_ID,                    DDI.ACTION_STRING_ID,                    DDI.EFFECT_STRING_ID,                    DDI.MECHANISM_STRING_ID,                    (select name from ddi_category where ddi_category.id=ddi.category_id) as CATEGORY,                     (select name from ddi_group where ddi_group.id=DRUG_TO_DDI_GROUP.GROUP_ID) as GROUP_0_NAME,                     (select string from general_string where general_string.id=ddi.action_string_id) as ACTION_STRING,                     (select string from general_string where general_string.id=ddi.effect_string_id) as EFFECT_STRING,                     (select string from general_string where general_string.id=ddi.mechanism_string_id) as MECHANISM_STRING                     FROM DRUG                     JOIN DRUG_TO_DDI_GROUP ON DRUG_TO_DDI_GROUP.DRUG_ID = DRUG.ID                     JOIN DDI ON DDI.GROUP_0_ID = DRUG_TO_DDI_GROUP.GROUP_ID OR DDI.GROUP_1_ID = DRUG_TO_DDI_GROUP.GROUP_ID                     JOIN DDI_GROUP ON DDI_GROUP.ID = CASE WHEN DDI.GROUP_0_ID = DRUG_TO_DDI_GROUP.GROUP_ID THEN DDI.GROUP_1_ID ELSE DDI.GROUP_0_ID END                    WHERE DRUG.ID = ");
                        String str110 = str103;
                        sb18.append(str110);
                        sb18.append("                    ORDER BY CATEGORY_ID, GROUP_1_NAME, GROUP_0_NAME");
                        ArrayList<Bundle> arrayListM71822X3 = compressHelper9.m71822X(bundle17, sb18.toString(), EPORxViewerActivityFragment.this.f88217a5, true);
                        EPORxViewerActivityFragment.this.m72233J4("Drug Interaction", (arrayListM71822X3 == null || arrayListM71822X3.size() == 0) ? "<div style=\"margin-left:15px;margin-top:5px;color:grey;\">No Interactions Found</div>" : "<div style=\"margin-left:15px;margin-top:5px\"><a href=\"interaction://\">See Interactions</a></div>");
                        Bundle bundle18 = bundle15;
                        String str111 = str17;
                        String str112 = "<ul style=\"margin-left:10px;margin-top:5px\">";
                        if (bundle18.getString(str111).equals("7")) {
                            EPORxViewerActivityFragment ePORxViewerActivityFragment22 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it19 = ePORxViewerActivityFragment22.f89579Q4.m71822X(ePORxViewerActivityFragment22.f89566D4, "select * from altmed_use where drug_id=" + str110 + str104, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String str113 = str33;
                            while (it19.hasNext()) {
                                String str114 = str20;
                                str113 = str113 + str34 + EPORxViewerActivityFragment.this.m72245V4(it19.next().getString(str114)) + "</li>";
                                str20 = str114;
                            }
                            String str115 = str20;
                            if (str113.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Reported Uses", "<ul style=\"margin-left:10px;margin-top:5px\">" + str113 + str37);
                            }
                            EPORxViewerActivityFragment ePORxViewerActivityFragment23 = EPORxViewerActivityFragment.this;
                            String str116 = str115;
                            Iterator<Bundle> it20 = ePORxViewerActivityFragment23.f89579Q4.m71822X(ePORxViewerActivityFragment23.f89566D4, "select * from altmed_dose where drug_id=" + str110 + str104, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String string12 = str33;
                            while (it20.hasNext()) {
                                Bundle next11 = it20.next();
                                String str117 = str68;
                                String strM72245V44 = EPORxViewerActivityFragment.this.m72245V4(next11.getString(str117));
                                String str118 = str111;
                                String str119 = str69;
                                String strM72245V45 = EPORxViewerActivityFragment.this.m72245V4(next11.getString(str119));
                                str69 = str119;
                                String strM72245V46 = EPORxViewerActivityFragment.this.m72245V4(next11.getString("MSG_STRING_ID"));
                                StringBuilder sb19 = new StringBuilder();
                                String str120 = str70;
                                sb19.append(str120);
                                sb19.append(strM72245V45);
                                String str121 = str71;
                                sb19.append(str121);
                                sb19.append(strM72245V46);
                                String string13 = sb19.toString();
                                StringBuilder sb20 = new StringBuilder();
                                sb20.append(string12);
                                str70 = str120;
                                sb20.append(EPORxViewerActivityFragment.this.m72237N4(strM72245V44, "", "LTR", string13, "", str10, ""));
                                string12 = sb20.toString();
                                str110 = str110;
                                str116 = str116;
                                str111 = str118;
                                str68 = str117;
                                str112 = str112;
                                str37 = str37;
                                str71 = str121;
                                str38 = str38;
                                bundle18 = bundle18;
                            }
                            str39 = str111;
                            String str122 = str38;
                            String str123 = str112;
                            Bundle bundle19 = bundle18;
                            str40 = str110;
                            String str124 = str37;
                            String str125 = str116;
                            if (string12.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Reported Doses", string12);
                            }
                            EPORxViewerActivityFragment ePORxViewerActivityFragment24 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it21 = ePORxViewerActivityFragment24.f89579Q4.m71822X(ePORxViewerActivityFragment24.f89566D4, "select * from altmed_caution_header where drug_id=" + str40 + str104, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            while (it21.hasNext()) {
                                Bundle next12 = it21.next();
                                String strM72245V47 = EPORxViewerActivityFragment.this.m72245V4(next12.getString(str125));
                                EPORxViewerActivityFragment ePORxViewerActivityFragment25 = EPORxViewerActivityFragment.this;
                                CompressHelper compressHelper10 = ePORxViewerActivityFragment25.f89579Q4;
                                Bundle bundle20 = ePORxViewerActivityFragment25.f89566D4;
                                StringBuilder sb21 = new StringBuilder();
                                sb21.append("Select * from altmed_caution where caution_header_id=");
                                String str126 = str29;
                                sb21.append(next12.getString(str126));
                                sb21.append(str104);
                                Iterator<Bundle> it22 = compressHelper10.m71822X(bundle20, sb21.toString(), EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                                String str127 = str33;
                                while (it22.hasNext()) {
                                    str127 = str127 + str34 + EPORxViewerActivityFragment.this.m72245V4(it22.next().getString(str125)) + "</li>";
                                }
                                if (str127.length() > 0) {
                                    StringBuilder sb22 = new StringBuilder();
                                    String str128 = str122;
                                    sb22.append(str128);
                                    sb22.append(str127);
                                    String str129 = str124;
                                    sb22.append(str129);
                                    String string14 = sb22.toString();
                                    StringBuilder sb23 = new StringBuilder();
                                    sb23.append(string12);
                                    it2 = it21;
                                    str47 = str129;
                                    str48 = str128;
                                    str46 = str126;
                                    sb23.append(EPORxViewerActivityFragment.this.m72237N4(strM72245V47, "", "LTR", string14, "", str10, ""));
                                    string12 = sb23.toString();
                                } else {
                                    str46 = str126;
                                    it2 = it21;
                                    str47 = str124;
                                    str48 = str122;
                                }
                                str122 = str48;
                                it21 = it2;
                                str124 = str47;
                                str29 = str46;
                            }
                            String str130 = str124;
                            str43 = str29;
                            if (string12.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Cautions", string12);
                            }
                            EPORxViewerActivityFragment ePORxViewerActivityFragment26 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it23 = ePORxViewerActivityFragment26.f89579Q4.m71822X(ePORxViewerActivityFragment26.f89566D4, "select * from altmed_reaction where drug_id=" + str40 + str104, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String str131 = str33;
                            while (it23.hasNext()) {
                                str131 = str131 + str34 + EPORxViewerActivityFragment.this.m72245V4(it23.next().getString(str125)) + "</li>";
                            }
                            if (str131.length() > 0) {
                                StringBuilder sb24 = new StringBuilder();
                                str44 = str123;
                                sb24.append(str44);
                                sb24.append(str131);
                                str45 = str130;
                                sb24.append(str45);
                                EPORxViewerActivityFragment.this.m72233J4("Adverse Reactions", sb24.toString());
                            } else {
                                str44 = str123;
                                str45 = str130;
                            }
                            EPORxViewerActivityFragment ePORxViewerActivityFragment27 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it24 = ePORxViewerActivityFragment27.f89579Q4.m71822X(ePORxViewerActivityFragment27.f89566D4, "select * from drug where generic_id=" + str40 + " order by name collate nocase asc", EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String str132 = str33;
                            while (it24.hasNext()) {
                                String str133 = str27;
                                String string15 = it24.next().getString(str133);
                                Bundle bundle21 = bundle19;
                                if (!string15.equals(bundle21.getString(str133))) {
                                    str132 = str132 + str34 + string15 + "</li>";
                                }
                                bundle19 = bundle21;
                                str27 = str133;
                            }
                            String str134 = str27;
                            Bundle bundle22 = bundle19;
                            if (str132.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Synonyms", str44 + str132 + str45);
                            }
                            EPORxViewerActivityFragment ePORxViewerActivityFragment28 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it25 = ePORxViewerActivityFragment28.f89579Q4.m71822X(ePORxViewerActivityFragment28.f89566D4, "select * from altmed_other_info where drug_id=" + str40 + str104, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String str135 = str33;
                            while (it25.hasNext()) {
                                Bundle next13 = it25.next();
                                str135 = str135 + EPORxViewerActivityFragment.this.m72237N4(EPORxViewerActivityFragment.this.m72245V4(next13.getString("HEADER_STRING_ID")), "", "LTR", EPORxViewerActivityFragment.this.m72245V4(next13.getString(str125)), "", str10, "");
                                bundle22 = bundle22;
                                str45 = str45;
                                str134 = str134;
                                it25 = it25;
                                str125 = str125;
                            }
                            bundle3 = bundle22;
                            str42 = str134;
                            str41 = str45;
                            if (str135.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Other Info", str135);
                            }
                        } else {
                            str39 = str111;
                            bundle3 = bundle18;
                            str40 = str110;
                            str41 = str37;
                            str42 = str27;
                            str43 = str29;
                            str44 = "<ul style=\"margin-left:10px;margin-top:5px\">";
                        }
                        if (bundle3.getString(str39).equals(ExifInterface.f16326Z4)) {
                            EPORxViewerActivityFragment ePORxViewerActivityFragment29 = EPORxViewerActivityFragment.this;
                            Iterator<Bundle> it26 = ePORxViewerActivityFragment29.f89579Q4.m71822X(ePORxViewerActivityFragment29.f89566D4, "Select * from drug where generic_id = " + str40, EPORxViewerActivityFragment.this.f88217a5, true).iterator();
                            String string16 = str33;
                            while (it26.hasNext()) {
                                Bundle next14 = it26.next();
                                StringBuilder sb25 = new StringBuilder();
                                sb25.append(string16);
                                sb25.append("<li><a href=\"rx://");
                                String str136 = str43;
                                sb25.append(next14.getString(str136));
                                String str137 = str26;
                                sb25.append(str137);
                                sb25.append(next14.getString(str42));
                                sb25.append("</a></li>");
                                string16 = sb25.toString();
                                str43 = str136;
                                str26 = str137;
                            }
                            if (string16.length() > 0) {
                                EPORxViewerActivityFragment.this.m72233J4("Please See", str44 + string16 + str41);
                            }
                        }
                        EPORxViewerActivityFragment ePORxViewerActivityFragment30 = EPORxViewerActivityFragment.this;
                        String strM72817d4 = ePORxViewerActivityFragment30.m72817d4(ePORxViewerActivityFragment30.m15366r(), "EPOHeader.css");
                        EPORxViewerActivityFragment ePORxViewerActivityFragment31 = EPORxViewerActivityFragment.this;
                        String strM72817d42 = ePORxViewerActivityFragment31.m72817d4(ePORxViewerActivityFragment31.m15366r(), "EPOFooter.css");
                        String str138 = str33;
                        String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", EPORxViewerActivityFragment.this.f89568F4).replace("[include]", str138);
                        EPORxViewerActivityFragment ePORxViewerActivityFragment32 = EPORxViewerActivityFragment.this;
                        ePORxViewerActivityFragment32.f88216Z4 = ePORxViewerActivityFragment32.m72246W4(ePORxViewerActivityFragment32.f88216Z4);
                        Iterator<Bundle> it27 = EPORxViewerActivityFragment.this.f88216Z4.iterator();
                        String str139 = str138;
                        while (it27.hasNext()) {
                            Bundle next15 = it27.next();
                            String string17 = next15.getString("Title");
                            str139 = str139 + EPORxViewerActivityFragment.this.m72236M4(string17, "", "LTR", next15.getString("Content"), "", "margin-left: 10px", "");
                            EPORxViewerActivityFragment ePORxViewerActivityFragment33 = EPORxViewerActivityFragment.this;
                            ePORxViewerActivityFragment33.m72232I4(string17, ePORxViewerActivityFragment33.f88214X4);
                        }
                        EPORxViewerActivityFragment.this.f89563A4 = strReplace + str139.replace("..", ".") + strM72817d42;
                    } catch (Exception e5) {
                        e = e5;
                        runnableC48481 = M71822X;
                    }
                } catch (Exception e6) {
                    e = e6;
                    runnableC48481 = this;
                    Exception exc22 = e;
                    exc22.printStackTrace();
                    EPORxViewerActivityFragment.this.f89595p4 = exc22.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPORxViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = EPORxViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    EPORxViewerActivityFragment ePORxViewerActivityFragment = EPORxViewerActivityFragment.this;
                    ePORxViewerActivityFragment.m72780C4(ePORxViewerActivityFragment.f89595p4);
                    return;
                }
                String strM71752f1 = CompressHelper.m71752f1(EPORxViewerActivityFragment.this.f89566D4);
                EPORxViewerActivityFragment ePORxViewerActivityFragment2 = EPORxViewerActivityFragment.this;
                ePORxViewerActivityFragment2.m72795O3(ePORxViewerActivityFragment2.f89563A4, strM71752f1);
                EPORxViewerActivityFragment.this.m72836s4();
                EPORxViewerActivityFragment.this.m72831p4();
                EPORxViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                EPORxViewerActivityFragment.this.m15358o2(false);
                EPORxViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    /* renamed from: U4 */
    public String m72244U4(String str) {
        return m72241R4(str, "safety");
    }

    /* renamed from: V4 */
    public String m72245V4(String str) {
        return m72241R4(str, "general");
    }

    /* renamed from: W4 */
    public ArrayList<Bundle> m72246W4(ArrayList<Bundle> arrayList) {
        String[] strArr = {"Adult Dosing", "Pediatric Dosing", "Alternatives", "Uses", "Dosing", "Reported Uses", "Reported Doses", "Cautions", "Formulations", "Black Box Warnings", "Contraindications/Cautions", "Adverse Reactions", "Drug Interaction", "Safety/Monitoring", "Pharmacology", "Manufacturer/Pricing", "Synonyms", "Other Info", "Pill Picture"};
        ArrayList<Bundle> arrayList2 = new ArrayList<>();
        for (int i2 = 0; i2 < 19; i2++) {
            Bundle bundleM71759q1 = CompressHelper.m71759q1(arrayList, "Title", strArr[i2]);
            if (bundleM71759q1 != null) {
                arrayList2.add(bundleM71759q1);
                arrayList.remove(bundleM71759q1);
            }
        }
        arrayList2.addAll(arrayList);
        return arrayList2;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_menu) {
            LXSectionsViewer lXSectionsViewer = new LXSectionsViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("fields", this.f88220d5);
            lXSectionsViewer.m15342i2(bundle);
            lXSectionsViewer.mo15218Z2(true);
            lXSectionsViewer.m15245A2(this, 0);
            lXSectionsViewer.mo15222e3(m15283M(), "RxSection");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        if (menu.findItem(C5562R.id.action_gallery) != null) {
            menu.removeItem(C5562R.id.action_gallery);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3 = m72839v3(this.f88215Y4);
        if (bundleM72839v3 != null) {
            Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        if (this.f89579Q4.m71800N1(this.f89566D4, str)) {
            return true;
        }
        if (str2.equals("grp")) {
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3.replace("//", ""), ",,,,,");
            Bundle bundle = new Bundle();
            bundle.putBundle("DB", this.f89566D4);
            bundle.putString("ParentId", strArrSplitByWholeSeparator[0]);
            this.f89579Q4.m71798N(EPORxListActivity.class, EPORxListActivityFragment.class, bundle);
        } else if (str2.equals("rx")) {
            String strReplace = str3.replace("//", "");
            this.f89579Q4.m71772A1(this.f89566D4, "rx-" + strReplace, null, null);
        } else if (str2.equals("interaction")) {
            str3.replace("//", "");
            this.f89579Q4.m71772A1(this.f89566D4, "interact-" + this.f88218b5, null, null);
        }
        return true;
    }
}

package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.animation.Animator;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.core.internal.view.SupportMenu;
import androidx.exifinterface.media.ExifInterface;
import androidx.fragment.app.Fragment;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.IOException;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.payActivity;
import net.imedicaldoctor.imd.Views.ProgressBarCircularIndeterminate;
import net.imedicaldoctor.imd.iMDActivity;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class registerActivity extends iMDActivity {

    public static class registerFragment extends Fragment {

        /* renamed from: f4 */
        private String f89906f4;

        /* renamed from: g4 */
        private TextView f89907g4;

        /* renamed from: h4 */
        private View f89908h4;

        /* renamed from: e4 */
        private final int f89905e4 = 0;

        /* renamed from: i4 */
        public BroadcastReceiver f89909i4 = new C52814();

        /* renamed from: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity$registerFragment$4 */
        class C52814 extends BroadcastReceiver {
            C52814() {
            }

            @Override // android.content.BroadcastReceiver
            public void onReceive(Context context, Intent intent) {
                if (intent.getIntExtra("result", 0) == 1) {
                    registerFragment.this.m72949Y2();
                    registerFragment.this.m72951b3("Successsful . Login Now");
                    new Timer().schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.4.1
                        @Override // java.util.TimerTask, java.lang.Runnable
                        public void run() {
                            registerFragment.this.f89907g4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.4.1.1
                                @Override // java.lang.Runnable
                                public void run() {
                                    registerFragment.this.m15366r().finish();
                                }
                            });
                        }
                    }, 1000L);
                } else {
                    registerFragment.this.m72943S2();
                    registerFragment.this.m72951b3("Failed . " + intent.getStringExtra("message"));
                }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:36:0x013d  */
        /* JADX WARN: Removed duplicated region for block: B:39:0x0145  */
        /* JADX WARN: Removed duplicated region for block: B:53:0x014a A[EXC_TOP_SPLITTER, SYNTHETIC] */
        /* JADX WARN: Type inference failed for: r2v0, types: [net.imedicaldoctor.imd.Data.CompressHelper] */
        /* JADX WARN: Type inference failed for: r2v11, types: [java.net.HttpURLConnection, java.net.URLConnection] */
        /* JADX WARN: Type inference failed for: r2v3 */
        /* renamed from: J2 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private java.lang.String m72934J2(java.lang.String r10) throws java.lang.Throwable {
            /*
                Method dump skipped, instructions count: 369
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.m72934J2(java.lang.String):java.lang.String");
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: S2 */
        public void m72943S2() {
            if (m15366r() == null) {
                return;
            }
            try {
                TextView textView = (TextView) m15366r().findViewById(C5562R.id.status_label);
                textView.setVisibility(0);
                textView.setTextColor(SupportMenu.f12679c);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: T2 */
        public String m72944T2(int i2) {
            return ((EditText) this.f89908h4.findViewById(i2)).getText().toString();
        }

        /* renamed from: U2 */
        private void m72945U2() {
            ((ProgressBarCircularIndeterminate) m15366r().findViewById(C5562R.id.progress_bar)).setVisibility(8);
        }

        /* renamed from: V2 */
        private void m72946V2() {
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: W2 */
        public void m72947W2() {
            try {
                InputMethodManager inputMethodManager = (InputMethodManager) m15366r().getSystemService("input_method");
                if (m15366r().getCurrentFocus() != null) {
                    inputMethodManager.hideSoftInputFromWindow(m15366r().getCurrentFocus().getWindowToken(), 0);
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        /* renamed from: X2 */
        private void m72948X2(final View view, long j2) {
            view.animate().alpha(0.0f).setDuration(j2).setListener(new Animator.AnimatorListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.5
                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationCancel(Animator animator) {
                }

                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationEnd(Animator animator) {
                    view.setVisibility(8);
                }

                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationRepeat(Animator animator) {
                }

                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationStart(Animator animator) {
                }
            });
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: Y2 */
        public void m72949Y2() {
            TextView textView = (TextView) m15366r().findViewById(C5562R.id.status_label);
            textView.setVisibility(0);
            textView.setTextColor(-16711936);
        }

        /* renamed from: Z2 */
        private void m72950Z2(String str) {
            mo15256D2(new Intent("android.intent.action.VIEW", Uri.parse(str)));
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: b3 */
        public void m72951b3(String str) {
            m72945U2();
            TextView textView = this.f89907g4;
            if (str != null) {
                textView.setText(str);
                this.f89907g4.setVisibility(0);
            } else {
                textView.setText("");
                this.f89907g4.setVisibility(8);
                this.f89906f4 = str;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: c3 */
        public void m72952c3() {
            ProgressBarCircularIndeterminate progressBarCircularIndeterminate = (ProgressBarCircularIndeterminate) m15366r().findViewById(C5562R.id.progress_bar);
            progressBarCircularIndeterminate.setVisibility(0);
            progressBarCircularIndeterminate.setBackgroundColor(Color.parseColor("#1e88e5"));
            this.f89907g4.setVisibility(8);
        }

        /* renamed from: d3 */
        private void m72953d3() {
        }

        /* renamed from: e3 */
        private void m72954e3(String str) {
            m72945U2();
            this.f89907g4.setVisibility(0);
            this.f89906f4 = null;
            this.f89907g4.setText(str);
            m72943S2();
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: L0 */
        public void mo15281L0(int i2, int i3, Intent intent) {
            if (intent == null) {
                return;
            }
            if (intent.getIntExtra("result", 0) == 1) {
                m72949Y2();
                m72951b3("Successsful . Login Now");
                new Timer().schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.3
                    @Override // java.util.TimerTask, java.lang.Runnable
                    public void run() {
                        registerFragment.this.f89907g4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.3.1
                            @Override // java.lang.Runnable
                            public void run() {
                                registerFragment.this.m15366r().finish();
                                registerFragment.this.m15366r().overridePendingTransition(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
                            }
                        });
                    }
                }, ExoPlayer.f21773a1);
            } else {
                m72943S2();
                m72951b3("Failed . " + intent.getStringExtra("message"));
            }
            super.mo15281L0(i2, i3, intent);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: Q0 */
        public void mo15207Q0(Bundle bundle) {
            super.mo15207Q0(bundle);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            View view = this.f89908h4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_register, viewGroup, false);
            this.f89908h4 = viewInflate;
            this.f89907g4 = (TextView) viewInflate.findViewById(C5562R.id.status_label);
            final CompressHelper compressHelper = new CompressHelper(m15366r());
            RadioGroup radioGroup = (RadioGroup) this.f89908h4.findViewById(C5562R.id.radio_group);
            radioGroup.check(C5562R.id.radio_1);
            m72955a3();
            radioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.1
                @Override // android.widget.RadioGroup.OnCheckedChangeListener
                public void onCheckedChanged(RadioGroup radioGroup2, int i2) {
                    registerFragment.this.m72955a3();
                }
            });
            ((Button) viewInflate.findViewById(C5562R.id.register_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.2
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    String str;
                    final String str2;
                    registerFragment.this.m72947W2();
                    final String strM72944T2 = registerFragment.this.m72944T2(C5562R.id.user_text);
                    final String strM72944T22 = registerFragment.this.m72944T2(C5562R.id.password_text);
                    final String strM72944T23 = registerFragment.this.m72944T2(C5562R.id.mail_text);
                    final String strM72944T24 = registerFragment.this.m72944T2(C5562R.id.mobile_text);
                    if (strM72944T2.length() == 0) {
                        registerFragment.this.m72943S2();
                        registerFragment.this.m72951b3("Username can't be empty");
                        return;
                    }
                    if (strM72944T22.length() == 0) {
                        registerFragment.this.m72943S2();
                        registerFragment.this.m72951b3("Password can't be empty");
                        return;
                    }
                    if (strM72944T23.length() == 0) {
                        registerFragment.this.m72943S2();
                        registerFragment.this.m72951b3("Mail can't be empty");
                        return;
                    }
                    if (strM72944T24.length() == 0) {
                        registerFragment.this.m72943S2();
                        registerFragment.this.m72951b3("Mobile can't be empty");
                        return;
                    }
                    int checkedRadioButtonId = ((RadioGroup) registerFragment.this.f89908h4.findViewById(C5562R.id.radio_group)).getCheckedRadioButtonId();
                    if (checkedRadioButtonId != C5562R.id.radio_1) {
                        if (checkedRadioButtonId == C5562R.id.radio_2) {
                            str = IcyHeaders.f28171a3;
                        } else {
                            if (checkedRadioButtonId == C5562R.id.radio_3) {
                                str = ExifInterface.f16317Y4;
                            }
                            str2 = "0";
                        }
                        str2 = str;
                    } else {
                        str2 = "0";
                    }
                    registerFragment.this.m72949Y2();
                    registerFragment.this.m72952c3();
                    compressHelper.m71874o0("checkRegister|||||" + strM72944T2 + "|||||" + strM72944T22 + "|||||" + strM72944T23 + "|||||" + strM72944T24).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.2.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(String str3) throws Throwable {
                            registerFragment registerfragment;
                            String str4;
                            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, "|||||");
                            if (!strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                                if (strArrSplitByWholeSeparator.length == 1) {
                                    registerFragment.this.m72943S2();
                                    registerfragment = registerFragment.this;
                                    str4 = strArrSplitByWholeSeparator[0];
                                } else {
                                    registerFragment.this.m72943S2();
                                    registerfragment = registerFragment.this;
                                    str4 = strArrSplitByWholeSeparator[1];
                                }
                                registerfragment.m72951b3(str4);
                                return;
                            }
                            registerFragment.this.m72949Y2();
                            registerFragment.this.m72951b3("Successfull , Redirecting ...");
                            Intent intent = new Intent(registerFragment.this.m15366r(), (Class<?>) payActivity.class);
                            intent.putExtra("AccountCommand", strM72944T2 + "|||||" + strM72944T22 + "|||||" + strM72944T23 + "|||||" + strM72944T24 + "|||||" + str2);
                            intent.putExtra("Type", "account");
                            registerFragment.this.startActivityForResult(intent, 1);
                            registerFragment.this.m15366r().overridePendingTransition(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out);
                        }
                    }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.registerActivity.registerFragment.2.2
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(Throwable th) throws Throwable {
                            try {
                                CompressHelper.m71767x2(registerFragment.this.m15366r(), "Error occured on contacting server, try again later.", 1);
                                registerFragment.this.m72943S2();
                                registerFragment.this.m72951b3("Error occured");
                            } catch (Exception e2) {
                                FirebaseCrashlytics.m48010d().m48016g(e2);
                            }
                        }
                    });
                }
            });
            return viewInflate;
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: V0 */
        public void mo15306V0() {
            super.mo15306V0();
        }

        /* renamed from: a3 */
        public void m72955a3() {
            String str;
            RadioGroup radioGroup = (RadioGroup) this.f89908h4.findViewById(C5562R.id.radio_group);
            Button button = (Button) this.f89908h4.findViewById(C5562R.id.register_button);
            int checkedRadioButtonId = radioGroup.getCheckedRadioButtonId();
            RadioButton radioButton = (RadioButton) this.f89908h4.findViewById(C5562R.id.radio_2);
            RadioButton radioButton2 = (RadioButton) this.f89908h4.findViewById(C5562R.id.radio_3);
            if (checkedRadioButtonId == C5562R.id.radio_1) {
                button.setText("Register - 10,000 Toman");
                radioButton.setChecked(false);
                radioButton2.setChecked(false);
            } else {
                if (checkedRadioButtonId == C5562R.id.radio_2) {
                    str = "Register - 17,000 Toman";
                } else if (checkedRadioButtonId != C5562R.id.radio_3) {
                    return;
                } else {
                    str = "Register - 249,000 Toman";
                }
                button.setText(str);
            }
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: p1 */
        public void mo15361p1(View view, Bundle bundle) {
            super.mo15361p1(view, bundle);
        }
    }

    /* renamed from: b1 */
    public static Bitmap m72932b1(Context context, String str) {
        try {
            return BitmapFactory.decodeStream(context.getAssets().open(str));
        } catch (IOException unused) {
            return null;
        }
    }

    /* renamed from: c1 */
    public void m72933c1(ColorStateList colorStateList) {
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, android.view.ComponentActivity, android.app.Activity
    public void onBackPressed() {
        super.onBackPressed();
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_register);
        if (bundle == null) {
            m15416k0().m15664u().m15827o("register").m15818f(C5562R.id.container, new registerFragment()).mo15164r();
        }
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        return true;
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        menuItem.getItemId();
        return super.onOptionsItemSelected(menuItem);
    }
}

package net.imedicaldoctor.imd.Fragments.UWorld;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.widget.Toolbar;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import com.itextpdf.tool.xml.html.HTML;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.HeaderCellViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class UWTestResultActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f89313X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f89314Y4;

    /* renamed from: Z4 */
    public UWTestResultAdapter f89315Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f89316a5;

    /* renamed from: b5 */
    public ArrayList<Bundle> f89317b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f89318c5;

    /* renamed from: d5 */
    public Bundle f89319d5;

    /* renamed from: j5 */
    public double f89325j5;

    /* renamed from: k5 */
    public int f89326k5;

    /* renamed from: l5 */
    public int f89327l5;

    /* renamed from: v5 */
    public boolean f89337v5;

    /* renamed from: w5 */
    public ArrayList<String> f89338w5;

    /* renamed from: x5 */
    public ArrayList<String> f89339x5;

    /* renamed from: y5 */
    public ArrayList<String> f89340y5;

    /* renamed from: e5 */
    public int f89320e5 = 0;

    /* renamed from: f5 */
    public int f89321f5 = 0;

    /* renamed from: g5 */
    public int f89322g5 = 0;

    /* renamed from: h5 */
    public double f89323h5 = 0.0d;

    /* renamed from: i5 */
    public int f89324i5 = 0;

    /* renamed from: m5 */
    public final int f89328m5 = 0;

    /* renamed from: n5 */
    public final int f89329n5 = 4;

    /* renamed from: o5 */
    public final int f89330o5 = 1;

    /* renamed from: p5 */
    public final int f89331p5 = 10;

    /* renamed from: q5 */
    public final int f89332q5 = 100;

    /* renamed from: r5 */
    public final int f89333r5 = 2;

    /* renamed from: s5 */
    public final int f89334s5 = 3;

    /* renamed from: t5 */
    public final int f89335t5 = 40;

    /* renamed from: u5 */
    public final int f89336u5 = 5;

    public class CategoryViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f89342I;

        /* renamed from: J */
        public TextView f89343J;

        /* renamed from: K */
        public ProgressBar f89344K;

        /* renamed from: L */
        public MaterialRippleLayout f89345L;

        public CategoryViewHolder(View view) {
            super(view);
            this.f89342I = (TextView) view.findViewById(C5562R.id.title);
            this.f89343J = (TextView) view.findViewById(C5562R.id.subtitle);
            this.f89344K = (ProgressBar) view.findViewById(C5562R.id.progress_bar);
            this.f89345L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class QuestionViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f89347I;

        /* renamed from: J */
        public TextView f89348J;

        /* renamed from: K */
        public TextView f89349K;

        /* renamed from: L */
        public TextView f89350L;

        /* renamed from: M */
        public ImageView f89351M;

        /* renamed from: N */
        public TextView f89352N;

        /* renamed from: O */
        public MaterialRippleLayout f89353O;

        /* renamed from: P */
        public TextView f89354P;

        public QuestionViewHolder(View view) {
            super(view);
            this.f89347I = (TextView) view.findViewById(C5562R.id.text_number);
            this.f89348J = (TextView) view.findViewById(C5562R.id.text_title);
            this.f89349K = (TextView) view.findViewById(C5562R.id.text_correct);
            this.f89353O = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f89350L = (TextView) view.findViewById(C5562R.id.text_time);
            this.f89351M = (ImageView) view.findViewById(C5562R.id.image_view);
            this.f89352N = (TextView) view.findViewById(C5562R.id.correct_label);
            this.f89354P = (TextView) view.findViewById(C5562R.id.time_label);
        }
    }

    public class ResultScoreViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f89356I;

        /* renamed from: J */
        public RelativeLayout f89357J;

        /* renamed from: K */
        public TextView f89358K;

        /* renamed from: L */
        public RelativeLayout f89359L;

        /* renamed from: M */
        public TextView f89360M;

        /* renamed from: N */
        public RelativeLayout f89361N;

        /* renamed from: O */
        public MaterialRippleLayout f89362O;

        /* renamed from: P */
        public Button f89363P;

        /* renamed from: Q */
        public Button f89364Q;

        /* renamed from: R */
        public Button f89365R;

        public ResultScoreViewHolder(View view) {
            super(view);
            this.f89356I = (TextView) view.findViewById(C5562R.id.score1);
            this.f89358K = (TextView) view.findViewById(C5562R.id.score2);
            this.f89360M = (TextView) view.findViewById(C5562R.id.score3);
            this.f89363P = (Button) view.findViewById(C5562R.id.button_all);
            this.f89364Q = (Button) view.findViewById(C5562R.id.button_incorrect);
            this.f89365R = (Button) view.findViewById(C5562R.id.button_correct);
            this.f89357J = (RelativeLayout) view.findViewById(C5562R.id.circle1);
            this.f89359L = (RelativeLayout) view.findViewById(C5562R.id.circle2);
            this.f89361N = (RelativeLayout) view.findViewById(C5562R.id.circle3);
            this.f89362O = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class ResultTestScoreViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f89367I;

        /* renamed from: J */
        public TextView f89368J;

        /* renamed from: K */
        public TextView f89369K;

        /* renamed from: L */
        public MaterialRippleLayout f89370L;

        public ResultTestScoreViewHolder(View view) {
            super(view);
            this.f89367I = (TextView) view.findViewById(C5562R.id.text_date);
            this.f89368J = (TextView) view.findViewById(C5562R.id.text_info1);
            this.f89369K = (TextView) view.findViewById(C5562R.id.text_info2);
            this.f89370L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class UWTestResultAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public String f89372d;

        public UWTestResultAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            if (i2 == 0) {
                return 0;
            }
            if (i2 == 1) {
                return 4;
            }
            if (i2 == 2) {
                return 1;
            }
            if (i2 >= 3 && i2 <= UWTestResultActivityFragment.this.f89316a5.size() + 2) {
                return 2;
            }
            if (i2 == UWTestResultActivityFragment.this.f89316a5.size() + 3) {
                return 3;
            }
            if (i2 == UWTestResultActivityFragment.this.f89316a5.size() + 4) {
                return 10;
            }
            if (i2 >= UWTestResultActivityFragment.this.f89316a5.size() + 5 && i2 <= UWTestResultActivityFragment.this.f89316a5.size() + 4 + UWTestResultActivityFragment.this.f89317b5.size()) {
                return 40;
            }
            if (i2 == UWTestResultActivityFragment.this.f89316a5.size() + 5 + UWTestResultActivityFragment.this.f89317b5.size()) {
                return 100;
            }
            return (i2 < (UWTestResultActivityFragment.this.f89316a5.size() + 6) + UWTestResultActivityFragment.this.f89317b5.size() || i2 > ((UWTestResultActivityFragment.this.f89316a5.size() + 5) + UWTestResultActivityFragment.this.f89317b5.size()) + UWTestResultActivityFragment.this.f89318c5.size()) ? 0 : 5;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            Bundle bundle;
            TextView textView;
            String string;
            String str;
            StringBuilder sb;
            String string2;
            ImageView imageView;
            Resources resourcesM15320b0;
            int i3;
            int iM27811F = viewHolder.m27811F();
            if (iM27811F == 3) {
                textView = ((HeaderCellViewHolder) viewHolder).f101460I;
                sb = new StringBuilder();
                sb.append(UWTestResultActivityFragment.this.f89320e5);
                sb.append(" Correct . ");
                sb.append(UWTestResultActivityFragment.this.f89321f5);
                sb.append(" Incorrect . ");
                sb.append(UWTestResultActivityFragment.this.f89322g5);
                string2 = " Omitted";
            } else if (iM27811F == 1) {
                textView = ((HeaderCellViewHolder) viewHolder).f101460I;
                sb = new StringBuilder();
                sb.append(UWTestResultActivityFragment.this.f89316a5.size());
                string2 = " Questions";
            } else if (iM27811F == 10) {
                textView = ((HeaderCellViewHolder) viewHolder).f101460I;
                sb = new StringBuilder();
                sb.append(UWTestResultActivityFragment.this.f89317b5.size());
                string2 = " Subjects";
            } else if (iM27811F == 100) {
                textView = ((HeaderCellViewHolder) viewHolder).f101460I;
                sb = new StringBuilder();
                sb.append(UWTestResultActivityFragment.this.f89318c5.size());
                string2 = " Systems";
            } else {
                if (iM27811F == 2) {
                    final int i4 = i2 - 3;
                    Bundle bundle2 = UWTestResultActivityFragment.this.f89316a5.get(i4);
                    QuestionViewHolder questionViewHolder = (QuestionViewHolder) viewHolder;
                    questionViewHolder.f89347I.setText(String.valueOf(i2 - 2));
                    questionViewHolder.f89348J.setText(bundle2.getString("title") + " (" + bundle2.getString("id") + ")");
                    if (UWTestResultActivityFragment.this.f89337v5) {
                        int iFloatValue = (int) ((Float.valueOf(bundle2.getString("corrTaken")).floatValue() / Float.valueOf(bundle2.getString("pplTaken")).floatValue()) * 100.0f);
                        questionViewHolder.f89349K.setText(iFloatValue + CSS.Value.f74136n0);
                    } else {
                        questionViewHolder.f89349K.setVisibility(8);
                        questionViewHolder.f89352N.setVisibility(8);
                    }
                    if (bundle2.getString(HTML.Tag.f74358P0).isEmpty()) {
                        questionViewHolder.f89350L.setVisibility(8);
                        questionViewHolder.f89354P.setVisibility(8);
                    } else {
                        questionViewHolder.f89350L.setVisibility(0);
                        questionViewHolder.f89354P.setVisibility(0);
                        questionViewHolder.f89350L.setText(bundle2.getString(HTML.Tag.f74358P0) + " sec");
                    }
                    String string3 = bundle2.getString("selectedAnswer");
                    String string4 = bundle2.getString("corrAnswer");
                    if (string3.length() == 0) {
                        imageView = questionViewHolder.f89351M;
                        resourcesM15320b0 = UWTestResultActivityFragment.this.m15320b0();
                        i3 = C5562R.drawable.omitted_icon;
                    } else if (string3.equals(string4)) {
                        imageView = questionViewHolder.f89351M;
                        resourcesM15320b0 = UWTestResultActivityFragment.this.m15320b0();
                        i3 = C5562R.drawable.correct_icon;
                    } else {
                        imageView = questionViewHolder.f89351M;
                        resourcesM15320b0 = UWTestResultActivityFragment.this.m15320b0();
                        i3 = C5562R.drawable.incorrect_icon;
                    }
                    imageView.setImageDrawable(resourcesM15320b0.getDrawable(i3));
                    questionViewHolder.f89353O.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestResultActivityFragment.UWTestResultAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            Bundle bundle3 = new Bundle();
                            bundle3.putInt("gotoQIndex", i4);
                            UWTestResultActivityFragment uWTestResultActivityFragment = UWTestResultActivityFragment.this;
                            uWTestResultActivityFragment.f89579Q4.m71775B1(uWTestResultActivityFragment.f89566D4, "test-" + UWTestResultActivityFragment.this.f89319d5.getString("id"), null, null, bundle3);
                        }
                    });
                    return;
                }
                if (iM27811F != 0) {
                    if (iM27811F == 4) {
                        ResultScoreViewHolder resultScoreViewHolder = (ResultScoreViewHolder) viewHolder;
                        resultScoreViewHolder.f89356I.setText(UWTestResultActivityFragment.this.f89319d5.getString("score") + CSS.Value.f74136n0);
                        if (UWTestResultActivityFragment.this.f89337v5) {
                            resultScoreViewHolder.f89359L.setVisibility(0);
                            resultScoreViewHolder.f89361N.setVisibility(0);
                            resultScoreViewHolder.f89358K.setText(String.valueOf(UWTestResultActivityFragment.this.f89327l5 + CSS.Value.f74136n0));
                            resultScoreViewHolder.f89360M.setText(String.valueOf(UWTestResultActivityFragment.this.f89326k5));
                            resultScoreViewHolder.f89362O.requestLayout();
                        } else {
                            resultScoreViewHolder.f89359L.setVisibility(8);
                            resultScoreViewHolder.f89361N.setVisibility(8);
                            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) resultScoreViewHolder.f89357J.getLayoutParams();
                            layoutParams.weight = 3.0f;
                            resultScoreViewHolder.f89357J.setLayoutParams(layoutParams);
                        }
                        resultScoreViewHolder.f89363P.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestResultActivityFragment.UWTestResultAdapter.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                UWTestResultActivityFragment uWTestResultActivityFragment = UWTestResultActivityFragment.this;
                                uWTestResultActivityFragment.m72635M4("QIDs", StringUtils.join(uWTestResultActivityFragment.f89338w5, ","));
                            }
                        });
                        resultScoreViewHolder.f89365R.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestResultActivityFragment.UWTestResultAdapter.3
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                UWTestResultActivityFragment uWTestResultActivityFragment = UWTestResultActivityFragment.this;
                                uWTestResultActivityFragment.m72635M4("QIDs", StringUtils.join(uWTestResultActivityFragment.f89339x5, ","));
                            }
                        });
                        resultScoreViewHolder.f89364Q.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestResultActivityFragment.UWTestResultAdapter.4
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                UWTestResultActivityFragment uWTestResultActivityFragment = UWTestResultActivityFragment.this;
                                uWTestResultActivityFragment.m72635M4("QIDs", StringUtils.join(uWTestResultActivityFragment.f89340y5, ","));
                            }
                        });
                        return;
                    }
                    if (iM27811F == 40 || iM27811F == 5) {
                        if (iM27811F == 40) {
                            UWTestResultActivityFragment uWTestResultActivityFragment = UWTestResultActivityFragment.this;
                            bundle = uWTestResultActivityFragment.f89317b5.get(i2 - (uWTestResultActivityFragment.f89316a5.size() + 5));
                        } else {
                            UWTestResultActivityFragment uWTestResultActivityFragment2 = UWTestResultActivityFragment.this;
                            bundle = uWTestResultActivityFragment2.f89318c5.get(i2 - ((uWTestResultActivityFragment2.f89316a5.size() + 6) + UWTestResultActivityFragment.this.f89317b5.size()));
                        }
                        Bundle bundle3 = bundle;
                        CategoryViewHolder categoryViewHolder = (CategoryViewHolder) viewHolder;
                        float fFloatValue = Float.valueOf(bundle3.getString("correct")).floatValue();
                        float fFloatValue2 = Float.valueOf(bundle3.getString("total_questions")).floatValue();
                        int i5 = (int) ((fFloatValue / fFloatValue2) * 100.0f);
                        categoryViewHolder.f89344K.setProgress(i5);
                        categoryViewHolder.f89344K.setMax(100);
                        categoryViewHolder.f89342I.setText(bundle3.getString("name") + " (" + i5 + "%)");
                        textView = categoryViewHolder.f89343J;
                        string = "Total: " + ((int) fFloatValue2) + ", Correct: " + ((int) fFloatValue) + ", Incorrect: " + bundle3.getString("incorrect");
                        textView.setText(string);
                    }
                    return;
                }
                ResultTestScoreViewHolder resultTestScoreViewHolder = (ResultTestScoreViewHolder) viewHolder;
                String str2 = "Test #" + UWTestResultActivityFragment.this.f89319d5.getString("id");
                String string5 = UWTestResultActivityFragment.this.f89319d5.getString("createdDate");
                String string6 = UWTestResultActivityFragment.this.f89319d5.getString("qIds");
                String[] strArrSplit = string5.split("\\|");
                if (strArrSplit.length > 1) {
                    str = strArrSplit[1];
                    String str3 = strArrSplit[0];
                } else {
                    str = "";
                }
                UWTestResultActivityFragment uWTestResultActivityFragment3 = UWTestResultActivityFragment.this;
                String strM72639N4 = uWTestResultActivityFragment3.m72639N4(uWTestResultActivityFragment3.f89319d5.getString("createdDate"));
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(string6, ",");
                Objects.requireNonNull(strArrSplitByWholeSeparator);
                String str4 = strArrSplitByWholeSeparator.length + " Qs, " + UWTestResultActivityFragment.this.f89319d5.getString("mode");
                if (!UWTestResultActivityFragment.this.f89319d5.getString("mode").equals("Reading") && !str.isEmpty()) {
                    str4 = str4 + ", " + str.replace(":", "':") + "\"";
                }
                resultTestScoreViewHolder.f89367I.setText(str2);
                resultTestScoreViewHolder.f89368J.setText(str4 + "\nCreated: " + strM72639N4);
                textView = resultTestScoreViewHolder.f89369K;
                sb = new StringBuilder();
                sb.append(UWTestResultActivityFragment.this.f89319d5.getString("subject"));
                sb.append(" | ");
                string2 = UWTestResultActivityFragment.this.f89319d5.getString("system");
            }
            sb.append(string2);
            string = sb.toString();
            textView.setText(string);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 3) {
                return new HeaderCellViewHolder(LayoutInflater.from(UWTestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_footer, viewGroup, false));
            }
            if (i2 == 1 || i2 == 10 || i2 == 100) {
                return new HeaderCellViewHolder(LayoutInflater.from(UWTestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 2) {
                return UWTestResultActivityFragment.this.new QuestionViewHolder(LayoutInflater.from(UWTestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_uworld_quetion, viewGroup, false));
            }
            if (i2 == 0) {
                return UWTestResultActivityFragment.this.new ResultTestScoreViewHolder(LayoutInflater.from(UWTestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_uworld_result_test, viewGroup, false));
            }
            if (i2 == 4) {
                return UWTestResultActivityFragment.this.new ResultScoreViewHolder(LayoutInflater.from(UWTestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_uworld_result_scores, viewGroup, false));
            }
            if (i2 == 40) {
                return UWTestResultActivityFragment.this.new CategoryViewHolder(LayoutInflater.from(UWTestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_uworld_result_category, viewGroup, false));
            }
            if (i2 != 5) {
                return null;
            }
            return UWTestResultActivityFragment.this.new CategoryViewHolder(LayoutInflater.from(UWTestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_uworld_result_category, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return UWTestResultActivityFragment.this.f89316a5.size() + 5 + UWTestResultActivityFragment.this.f89317b5.size() + 1 + UWTestResultActivityFragment.this.f89318c5.size();
        }

        /* renamed from: d0 */
        public String m72643d0(String str) {
            return str;
        }

        /* renamed from: e0 */
        public void m72644e0(RecyclerView.ViewHolder viewHolder, Bundle bundle, int i2) {
        }

        /* renamed from: f0 */
        public void m72645f0(Bundle bundle, int i2) {
        }

        /* renamed from: g0 */
        public RecyclerView.ViewHolder m72646g0(View view) {
            return new RippleTextViewHolder(view);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: M4 */
    public void m72635M4(String str, String str2) {
        ((ClipboardManager) m15366r().getSystemService("clipboard")).setPrimaryClip(ClipData.newPlainText(str, str2));
        Toast.makeText(m15366r(), "Copied to clipboard", 0).show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: O4 */
    public /* synthetic */ void m72636O4(int i2) {
        super.mo72642f3(i2);
    }

    /* renamed from: K4 */
    public void m72637K4() {
        this.f89313X4.setItemAnimator(new DefaultItemAnimator());
        this.f89313X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f89313X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: L4 */
    public void m72638L4() throws Resources.NotFoundException {
        Toolbar toolbar = (Toolbar) this.f89565C4.findViewById(C5562R.id.toolbar);
        this.f89574L4 = toolbar;
        if (toolbar == null) {
            return;
        }
        this.f89575M4 = (ImageView) this.f89565C4.findViewById(C5562R.id.toolbar_image_view);
        TextView textView = (TextView) this.f89565C4.findViewById(C5562R.id.toolbar_text_view);
        this.f89576N4 = textView;
        if (textView != null) {
            this.f89576N4.setTypeface(Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf"));
            this.f89576N4.setText(this.f89568F4);
        }
        if (this.f89575M4 != null) {
            m72641Q4();
        }
        mo72759u4();
        this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestResultActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                UWTestResultActivityFragment uWTestResultActivityFragment = UWTestResultActivityFragment.this;
                if (uWTestResultActivityFragment.f89575M4 != null) {
                    uWTestResultActivityFragment.m72641Q4();
                }
            }
        }, 1000L);
    }

    /* renamed from: N4 */
    public String m72639N4(String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ");
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm").format(simpleDateFormat.parse(str));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return str;
        }
    }

    /* renamed from: P4 */
    public void m72640P4(String str, String str2) {
        String[] strArrSplit = str2.split(",");
        StringBuilder sb = new StringBuilder("CASE questions.id ");
        for (int i2 = 0; i2 < strArrSplit.length; i2++) {
            sb.append("WHEN ");
            sb.append(strArrSplit[i2]);
            sb.append(" THEN ");
            sb.append(i2);
            sb.append(StringUtils.SPACE);
        }
        sb.append("END");
        this.f89316a5 = this.f89579Q4.m71817V(this.f89566D4, "SELECT questions.id, pplTaken, corrTaken, title, selectedAnswer, corrAnswer, time FROM Questions LEFT OUTER JOIN (SELECT * FROM logs WHERE testId=" + str + ") AS logs2 ON questions.id = logs2.qid WHERE questions.id IN (" + str2 + ") ORDER BY " + sb.toString());
    }

    /* renamed from: Q4 */
    public void m72641Q4() {
        try {
            Glide.m30040F(this).mo30129t(CompressHelper.m71724C(this.f89566D4)).mo30182a(new RequestOptions().m31361u()).m30165B2(this.f89575M4);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return CompressHelper.m71724C(this.f89566D4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        ArrayList<String> arrayList;
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f89313X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        String str = this.f89567E4.split("-")[1];
        this.f89337v5 = !this.f89579Q4.m71817V(this.f89566D4, "select count(distinct  corrTaken) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3);
        CompressHelper compressHelper = this.f89579Q4;
        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from tests where id=" + str));
        this.f89319d5 = bundleM71890s1;
        m72640P4(str, bundleM71890s1.getString("qIds"));
        this.f89568F4 = "Test #" + str + " Result";
        this.f89324i5 = this.f89316a5.size();
        ArrayList arrayList2 = new ArrayList();
        this.f89338w5 = new ArrayList<>();
        this.f89339x5 = new ArrayList<>();
        this.f89340y5 = new ArrayList<>();
        Iterator<Bundle> it2 = this.f89316a5.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            Bundle bundle2 = new Bundle();
            bundle2.putDouble("ppltaken", Double.valueOf(next.getString("pplTaken")).doubleValue());
            bundle2.putDouble("corrTaken", Double.valueOf(next.getString("corrTaken")).doubleValue());
            arrayList2.add(bundle2);
            this.f89338w5.add(next.getString("id"));
            if (next.getString("selectedAnswer").length() == 0) {
                this.f89322g5++;
            } else {
                if (next.getString("selectedAnswer").equals(next.getString("corrAnswer"))) {
                    this.f89320e5++;
                    arrayList = this.f89339x5;
                } else {
                    this.f89321f5++;
                    arrayList = this.f89340y5;
                }
                arrayList.add(next.getString("id"));
            }
        }
        this.f89326k5 = (int) USMLECalculator.m72571b(this.f89320e5, this.f89321f5, this.f89322g5, arrayList2, 230.0d, 15.0d);
        this.f89327l5 = (int) USMLECalculator.m72570a(this.f89320e5, this.f89321f5, this.f89322g5, arrayList2, 1.0d, 15.0d);
        if (this.f89326k5 < 0) {
            this.f89326k5 = 0;
        }
        this.f89317b5 = this.f89579Q4.m71817V(this.f89566D4, "SELECT s.name AS name, COUNT(q.id) AS total_questions, SUM(CASE WHEN l.selectedAnswer = q.corrAns THEN 1 ELSE 0 END) AS correct, SUM(CASE WHEN l.selectedAnswer != q.corrAns AND l.selectedAnswer IS NOT NULL THEN 1 ELSE 0 END) AS incorrect, SUM(CASE WHEN l.selectedAnswer IS NULL THEN 1 ELSE 0 END) AS skipped FROM logs l INNER JOIN Questions q ON l.qid = q.id INNER JOIN Subjects s ON q.subId = s.id INNER JOIN Tests t ON l.testId = t.id WHERE l.testId = " + str + " GROUP BY s.name ORDER BY (SUM(CASE WHEN l.selectedAnswer = q.corrAns THEN 1 ELSE 0 END) * 1.0 / COUNT(q.id)) DESC;");
        this.f89318c5 = this.f89579Q4.m71817V(this.f89566D4, "SELECT sys.name AS name, COUNT(q.id) AS total_questions, SUM(CASE WHEN l.selectedAnswer = q.corrAns THEN 1 ELSE 0 END) AS correct, SUM(CASE WHEN l.selectedAnswer != q.corrAns AND l.selectedAnswer IS NOT NULL THEN 1 ELSE 0 END) AS incorrect, SUM(CASE WHEN l.selectedAnswer IS NULL THEN 1 ELSE 0 END) AS skipped FROM logs l INNER JOIN Questions q ON l.qid = q.id INNER JOIN Systems sys ON q.sysId = sys.id INNER JOIN Tests t ON l.testId = t.id WHERE l.testId = " + str + " GROUP BY sys.name ORDER BY (SUM(CASE WHEN l.selectedAnswer = q.corrAns THEN 1 ELSE 0 END) * 1.0 / COUNT(q.id)) DESC;");
        if (this.f89317b5 == null) {
            this.f89317b5 = new ArrayList<>();
        }
        if (this.f89318c5 == null) {
            this.f89318c5 = new ArrayList<>();
        }
        UWTestResultAdapter uWTestResultAdapter = new UWTestResultAdapter();
        this.f89315Z4 = uWTestResultAdapter;
        this.f89313X4.setAdapter(uWTestResultAdapter);
        m72637K4();
        mo72642f3(C5562R.menu.empty);
        mo72759u4();
        m15358o2(false);
        m72786G3();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: f3 */
    public void mo72642f3(final int i2) {
        new Handler().postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.i
            @Override // java.lang.Runnable
            public final void run() {
                this.f89466s.m72636O4(i2);
            }
        }, 100L);
    }
}

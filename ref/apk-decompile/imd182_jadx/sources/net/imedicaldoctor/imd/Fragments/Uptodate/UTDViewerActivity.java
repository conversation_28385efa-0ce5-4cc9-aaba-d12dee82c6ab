package net.imedicaldoctor.imd.Fragments.Uptodate;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.appcompat.app.AlertDialog;
import androidx.exifinterface.media.ExifInterface;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.html.HTML;
import fi.iki.elonen.NanoHTTPD;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Random;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.UWorld.C5150h;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.GeneralDialogFragment;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import okio.BufferedSink;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class UTDViewerActivity extends ViewerHelperActivity {

    public static class UTDViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private GeneralDialogFragment f89516X4;

        /* renamed from: Y4 */
        private String f89517Y4;

        /* renamed from: Z4 */
        private String f89518Z4 = null;

        /* renamed from: a5 */
        private String f89519a5 = null;

        /* renamed from: b5 */
        private String f89520b5 = null;

        /* renamed from: c5 */
        private String f89521c5;

        /* renamed from: d5 */
        private String f89522d5;

        /* renamed from: e5 */
        private String f89523e5;

        /* renamed from: f5 */
        private String f89524f5;

        /* renamed from: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity$UTDViewerFragment$2 */
        class RunnableC51702 implements Runnable {
            RunnableC51702() {
            }

            @Override // java.lang.Runnable
            public void run() {
                AlertDialog.Builder builderMo1109s;
                DialogInterface.OnClickListener onClickListener;
                String str = UTDViewerFragment.this.f89595p4;
                if (str == null || str.length() <= 0) {
                    UTDViewerFragment.this.f89579Q4.m71903x1();
                    UTDViewerFragment.this.m72827m4("Topic");
                    new File(CompressHelper.m71753g1(UTDViewerFragment.this.f89566D4, "base"));
                    UTDViewerFragment uTDViewerFragment = UTDViewerFragment.this;
                    uTDViewerFragment.f89569G4.loadDataWithBaseURL("file:///android_asset/", uTDViewerFragment.f89563A4, NanoHTTPD.f77082p, "utf-8", null);
                    UTDViewerFragment.this.m72836s4();
                    UTDViewerFragment.this.m72831p4();
                    UTDViewerFragment.this.mo72642f3(C5562R.menu.menu_utdviewer);
                    UTDViewerFragment.this.m15358o2(false);
                    UTDViewerFragment.this.m72786G3();
                    return;
                }
                if (UTDViewerFragment.this.f89595p4.equals(IcyHeaders.f28171a3)) {
                    builderMo1109s = new AlertDialog.Builder(UTDViewerFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("The database is corrupt . it may happen after delta update or as a result of bad installation or a cleaner app in your device . you must delete and redownload this database. what do you want to do ?").mo1106p("Delete", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.3
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            new AlertDialog.Builder(UTDViewerFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you sure ? this will delete uptodate database ...").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.3.2
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                    UTDViewerFragment.this.m72798Q2(new File(UTDViewerFragment.this.f89566D4.getString("Path")));
                                    LocalBroadcastManager.m16410b(UTDViewerFragment.this.m15366r()).m16413d(new Intent("reload"));
                                    UTDViewerFragment.this.f89579Q4.m71830Z1(false);
                                    UTDViewerFragment.this.f89579Q4.m71830Z1(true);
                                }
                            }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.3.1
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                }
                            }).m1090I();
                        }
                    }).mo1109s("More Info", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            UTDViewerFragment.this.m72811a4("http://imedicaldoctor.net/faq#null");
                            UTDViewerFragment.this.f89579Q4.m71821W1(false);
                        }
                    });
                    onClickListener = new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                        }
                    };
                } else if (!UTDViewerFragment.this.f89595p4.equals(ExifInterface.f16317Y4)) {
                    UTDViewerFragment uTDViewerFragment2 = UTDViewerFragment.this;
                    uTDViewerFragment2.m72780C4(uTDViewerFragment2.f89595p4);
                    return;
                } else {
                    builderMo1109s = new AlertDialog.Builder(UTDViewerFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Document can't be found . if this happens a lot your database is corrupted. what do you want to do ?").mo1106p("Delete", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.6
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            new AlertDialog.Builder(UTDViewerFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you sure ? this will delete uptodate database ...").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.6.2
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                    UTDViewerFragment.this.m72798Q2(new File(UTDViewerFragment.this.f89566D4.getString("Path")));
                                    LocalBroadcastManager.m16410b(UTDViewerFragment.this.m15366r()).m16413d(new Intent("reload"));
                                    UTDViewerFragment.this.f89579Q4.m71830Z1(true);
                                    UTDViewerFragment.this.f89579Q4.m71830Z1(false);
                                }
                            }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.6.1
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                }
                            }).m1090I();
                        }
                    }).mo1109s("More Info", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.5
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            UTDViewerFragment.this.m72811a4("http://imedicaldoctor.net/faq#null");
                            UTDViewerFragment.this.f89579Q4.m71821W1(false);
                        }
                    });
                    onClickListener = new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.2.4
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            UTDViewerFragment.this.f89579Q4.m71821W1(false);
                        }
                    };
                }
                builderMo1109s.mo1115y("OK", onClickListener).m1090I();
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: A3 */
        public void mo72743A3(int i2) {
            if (this.f89570H4.length() > 0) {
                try {
                    this.f89569G4.m73433g("setHighlightClass(\"" + this.f89570H4.getString(this.f89593n4) + "\")");
                    this.f89569G4.m73433g("var obj = document.getElementById(\"" + this.f89570H4.getString(i2) + "\");obj.className = \"highlightedCurrent\";obj.scrollIntoView(true);");
                    this.f89593n4 = i2;
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
            m72805U2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() throws JSONException, IOException {
            JSONArray jSONArray;
            CompressHelper compressHelper;
            Bundle bundle;
            String str;
            String str2;
            String strM71753g1;
            if (this.f89520b5 == null) {
                return null;
            }
            try {
                jSONArray = new JSONArray(this.f89520b5);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                jSONArray = null;
            }
            if (jSONArray == null || jSONArray.length() == 0) {
                return null;
            }
            ArrayList arrayList = new ArrayList();
            for (int i2 = 0; i2 < jSONArray.length(); i2++) {
                try {
                    JSONArray jSONArray2 = jSONArray.getJSONObject(i2).getJSONArray("graphics");
                    for (int i3 = 0; i3 < jSONArray2.length(); i3++) {
                        JSONObject jSONObject = jSONArray2.getJSONObject(i3);
                        jSONObject.getJSONObject("graphicInfo").getString("displayName");
                        arrayList.add(jSONObject.getJSONObject("graphicInfo").getString("id"));
                    }
                } catch (Exception e3) {
                    FirebaseCrashlytics.m48010d().m48016g(e3);
                }
            }
            Random random = new Random();
            int i4 = 0;
            while (true) {
                int iNextInt = random.nextInt(arrayList.size());
                if (i4 > arrayList.size() - 1) {
                    return null;
                }
                i4++;
                String str3 = (String) arrayList.get(iNextInt);
                if (this.f89566D4.getString("Name").equals("utdadvanced")) {
                    compressHelper = this.f89579Q4;
                    bundle = this.f89566D4;
                    str = "select * from graphic_asset where id =" + str3;
                    str2 = "lab.db";
                } else {
                    compressHelper = this.f89579Q4;
                    bundle = this.f89566D4;
                    str = "select * from graphic_asset where id =" + str3;
                    str2 = "utdasset.sqlite";
                }
                Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71819W(bundle, str, str2));
                if (bundleM71890s1 != null) {
                    try {
                        JSONObject jSONObject2 = new JSONObject(new String(this.f89579Q4.m71897v(bundleM71890s1.getString("payload"), bundleM71890s1.getString("id"), "127")));
                        if (jSONObject2.has("base64Image")) {
                            String string = jSONObject2.getString("base64Image");
                            strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "background.png");
                            File file = new File(strM71753g1);
                            if (file.exists() && !file.delete()) {
                                iMDLogger.m73550f("ImagePathForToolbar", "Not Deleted");
                            }
                            try {
                                FileOutputStream fileOutputStream = new FileOutputStream(file);
                                try {
                                    BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75780p(fileOutputStream));
                                    try {
                                        bufferedSinkM75768d.write(Base64.decode(string, 0));
                                        bufferedSinkM75768d.flush();
                                        bufferedSinkM75768d.close();
                                        fileOutputStream.close();
                                        break;
                                    } finally {
                                    }
                                } catch (Throwable th) {
                                    try {
                                        fileOutputStream.close();
                                    } catch (Throwable th2) {
                                        th.addSuppressed(th2);
                                    }
                                    throw th;
                                }
                            } catch (IOException e4) {
                                e4.printStackTrace();
                            }
                        } else {
                            continue;
                        }
                    } catch (Exception e5) {
                        FirebaseCrashlytics.m48010d().m48016g(e5);
                    }
                }
            }
            return strM71753g1;
        }

        /* renamed from: R4 */
        public void m72744R4(String str) {
            JSONArray jSONArray;
            ArrayList arrayList = new ArrayList();
            try {
                jSONArray = new JSONArray(this.f89520b5);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                jSONArray = null;
            }
            if (jSONArray == null || jSONArray.length() == 0) {
                return;
            }
            new ArrayList();
            for (int i2 = 0; i2 < jSONArray.length(); i2++) {
                try {
                    JSONArray jSONArray2 = jSONArray.getJSONObject(i2).getJSONArray("graphics");
                    for (int i3 = 0; i3 < jSONArray2.length(); i3++) {
                        arrayList.add(jSONArray2.getJSONObject(i3).getJSONObject("graphicInfo").getString("id"));
                    }
                } catch (Exception e3) {
                    FirebaseCrashlytics.m48010d().m48016g(e3);
                }
            }
            this.f89579Q4.m71772A1(this.f89566D4, "Graphic-" + str + "-" + C5150h.m72696a(",", arrayList) + "-" + arrayList.indexOf(str), null, null);
        }

        /* renamed from: S4 */
        public void m72745S4(String str) {
            this.f89579Q4.m71772A1(this.f89566D4, "Topic-" + str, null, null);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_utdviewer, menu);
            m72833q4(menu);
            MenuItem menuItemFindItem = menu.findItem(C5562R.id.action_relatedTopics);
            MenuItem menuItemFindItem2 = menu.findItem(C5562R.id.action_relatedGraphics);
            MenuItem menuItemFindItem3 = menu.findItem(C5562R.id.action_relatedCalcs);
            MenuItem menuItemFindItem4 = menu.findItem(C5562R.id.action_menu);
            MenuItem menuItemFindItem5 = menu.findItem(C5562R.id.action_references);
            if (this.f89518Z4 == null) {
                menuItemFindItem.setVisible(false);
            }
            if (this.f89520b5 == null) {
                menuItemFindItem2.setVisible(false);
            }
            if (this.f89519a5 == null) {
                menuItemFindItem3.setVisible(false);
            }
            if (this.f89517Y4 == null) {
                menuItemFindItem4.setVisible(false);
            }
            if (this.f89521c5 == null) {
                menuItemFindItem5.setVisible(false);
            }
        }

        /* renamed from: T4 */
        public void m72746T4(String str) {
            String string;
            String string2;
            String string3;
            try {
                JSONObject jSONObject = new JSONObject(Uri.decode(str.substring(21)));
                if (jSONObject.getJSONObject(HTML.Tag.f74333D).getString("assetType").equals("graphic")) {
                    JSONArray jSONArray = jSONObject.getJSONArray("data");
                    ArrayList arrayList = new ArrayList();
                    for (int i2 = 0; i2 < jSONArray.length(); i2++) {
                        arrayList.add(jSONArray.getJSONObject(i2).getString("id"));
                    }
                    this.f89579Q4.m71772A1(this.f89566D4, "Graphic-" + TextUtils.join(",,,,", arrayList), null, null);
                    return;
                }
                try {
                    string = jSONObject.getJSONObject(HTML.Tag.f74333D).getString("topicId");
                } catch (Exception unused) {
                    string = jSONObject.getJSONArray("data").getJSONObject(0).getString("id");
                }
                try {
                    string2 = jSONObject.getJSONArray("data").getJSONObject(0).getString("subtype");
                } catch (Exception unused2) {
                    string2 = "";
                }
                if (!string2.equals("narrative_icg")) {
                    try {
                        try {
                            string3 = jSONObject.getJSONObject(HTML.Tag.f74333D).getString(HTML.Tag.f74369V);
                        } catch (Exception unused3) {
                            string3 = jSONObject.getJSONArray("data").getJSONObject(0).getString(HTML.Tag.f74369V);
                        }
                    } catch (Exception unused4) {
                        string3 = null;
                    }
                    if (this.f89524f5.equals(string)) {
                        mo71967C3(string3);
                        return;
                    }
                    iMDLogger.m73554j("Activity Type", m15366r().getClass().toString());
                    this.f89579Q4.m71772A1(this.f89566D4, "Topic-" + string, null, string3);
                    return;
                }
                ArrayList arrayList2 = new ArrayList(Collections2.m42365d(((iMD) m15366r().getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.8
                    @Override // com.google.common.base.Predicate
                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                    public boolean apply(Bundle bundle) {
                        return bundle.getString("Name").equals("utdpathways.db");
                    }
                }));
                if (arrayList2.size() == 0) {
                    CompressHelper.m71767x2(m15366r(), "You must install Uptodate Pathways Database", 1);
                    return;
                }
                Bundle bundle = (Bundle) arrayList2.get(0);
                CompressHelper compressHelper = this.f89579Q4;
                ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(bundle, "Select * from docs where path = '" + ("base/" + string + ".html") + "'");
                if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                    this.f89579Q4.m71772A1(bundle, arrayListM71817V.get(0).getString("id"), null, "");
                    return;
                }
                CompressHelper.m71767x2(m15366r(), "Sorry, Document not available", 1);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("ViewerActivity , onMenuURLClicked", e2.toString());
            }
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (m15387y() == null) {
                return viewInflate;
            }
            if (bundle != null) {
                this.f89523e5 = bundle.getString("mLastMajorUpdates");
                this.f89522d5 = bundle.getString("mContributers");
                this.f89520b5 = bundle.getString("mRelatedGraphics");
                this.f89519a5 = bundle.getString("mRelatedCalcs");
                this.f89518Z4 = bundle.getString("mRelatedTopics");
                this.f89521c5 = bundle.getString("mReferences");
                this.f89517Y4 = bundle.getString("mMenuHTML");
            }
            this.f89524f5 = this.f89567E4.split("-")[1];
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.1
                @Override // java.lang.Runnable
                public void run() throws JSONException {
                    String strM71879p1;
                    Bundle bundleM71845f0;
                    try {
                        String str = UTDViewerFragment.this.f89563A4;
                        if (str != null && str.length() != 0) {
                            return;
                        }
                        if (UTDViewerFragment.this.f89566D4.getString("Name").equals("uptodateonline")) {
                            try {
                                strM71879p1 = UTDViewerFragment.this.f89579Q4.m71879p1(UTDViewerFragment.this.f89579Q4.m71790J() + "/utdonline/topic/" + UTDViewerFragment.this.f89524f5 + ".txt", "topic-" + UTDViewerFragment.this.f89524f5 + UTDViewerFragment.this.f89566D4.getString("Version"));
                            } catch (Exception unused) {
                                strM71879p1 = null;
                            }
                            if (strM71879p1 == null) {
                                UTDViewerFragment.this.f89595p4 = "Error in retrieving data from server";
                                return;
                            }
                        } else {
                            if (!new File(CompressHelper.m71753g1(UTDViewerFragment.this.f89566D4, "utdasset.sqlite")).exists()) {
                                UTDViewerFragment.this.f89595p4 = IcyHeaders.f28171a3;
                                return;
                            }
                            UTDViewerFragment uTDViewerFragment = UTDViewerFragment.this;
                            uTDViewerFragment.f89524f5 = uTDViewerFragment.f89524f5.replace(StringUtils.SPACE, "");
                            if (UTDViewerFragment.this.f89566D4.getString("Name").equals("utdadvanced")) {
                                UTDViewerFragment uTDViewerFragment2 = UTDViewerFragment.this;
                                bundleM71845f0 = uTDViewerFragment2.f89579Q4.m71845f0(uTDViewerFragment2.f89566D4, "select * from topic_asset where id = " + UTDViewerFragment.this.f89524f5, "lab.db");
                            } else {
                                UTDViewerFragment uTDViewerFragment3 = UTDViewerFragment.this;
                                bundleM71845f0 = uTDViewerFragment3.f89579Q4.m71845f0(uTDViewerFragment3.f89566D4, "select * from topic_asset where id = " + UTDViewerFragment.this.f89524f5, "utdasset.sqlite");
                            }
                            if (bundleM71845f0 == null) {
                                UTDViewerFragment.this.f89595p4 = ExifInterface.f16317Y4;
                                return;
                            }
                            strM71879p1 = bundleM71845f0.getString("payload");
                        }
                        UTDViewerFragment uTDViewerFragment4 = UTDViewerFragment.this;
                        JSONObject jSONObject = new JSONObject(new String(uTDViewerFragment4.f89579Q4.m71897v(strM71879p1, uTDViewerFragment4.f89524f5, "127")));
                        if (jSONObject.has("outlineHtml")) {
                            UTDViewerFragment.this.f89517Y4 = jSONObject.getString("outlineHtml");
                        }
                        if (jSONObject.has("referenceHtml")) {
                            UTDViewerFragment.this.f89521c5 = jSONObject.getString("referenceHtml");
                        }
                        String string = jSONObject.getString("bodyHtml");
                        if (jSONObject.has("relatedTopics")) {
                            UTDViewerFragment.this.f89518Z4 = jSONObject.getJSONObject("relatedTopics").getString("topics");
                        }
                        if (jSONObject.has("relatedCalculators")) {
                            UTDViewerFragment.this.f89519a5 = jSONObject.getJSONObject("relatedCalculators").getString("topics");
                        }
                        if (jSONObject.has("relatedGraphics")) {
                            UTDViewerFragment.this.f89520b5 = jSONObject.getString("relatedGraphics");
                        }
                        if (jSONObject.has("contributors")) {
                            UTDViewerFragment.this.f89522d5 = jSONObject.getString("contributors");
                        }
                        UTDViewerFragment.this.f89523e5 = jSONObject.getJSONObject("topicInfo").getString("lastMajorUpdateMs");
                        UTDViewerFragment.this.f89568F4 = jSONObject.getJSONObject("topicInfo").getString("title");
                        UTDViewerFragment uTDViewerFragment5 = UTDViewerFragment.this;
                        String strM72817d4 = uTDViewerFragment5.m72817d4(uTDViewerFragment5.m15366r(), "UTDHeader.css");
                        UTDViewerFragment uTDViewerFragment6 = UTDViewerFragment.this;
                        String strReplace = uTDViewerFragment6.m72817d4(uTDViewerFragment6.m15366r(), "UTDFooter.css").replace("[year]", new SimpleDateFormat("yyyy").format(new Date()));
                        String strReplace2 = strM72817d4.replace("[Title]", UTDViewerFragment.this.f89568F4).replace("[size]", "200");
                        UTDViewerFragment.this.f89563A4 = strReplace2 + string + strReplace;
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        UTDViewerFragment.this.f89595p4 = e2.getLocalizedMessage();
                    }
                }
            }, new RunnableC51702());
            return viewInflate;
        }

        /* renamed from: U4 */
        public void m72747U4() throws JSONException {
            if (this.f89522d5 == null) {
                return;
            }
            try {
                JSONArray jSONArray = new JSONArray(this.f89522d5);
                String str = "";
                for (int i2 = 0; i2 < jSONArray.length(); i2++) {
                    JSONObject jSONObject = jSONArray.getJSONObject(i2);
                    ArrayList arrayList = new ArrayList();
                    for (int i3 = 0; i3 < jSONObject.getJSONArray("contributorList").length(); i3++) {
                        ArrayList arrayList2 = new ArrayList();
                        JSONObject jSONObject2 = jSONObject.getJSONArray("contributorList").getJSONObject(i3);
                        for (int i4 = 0; i4 < jSONObject2.getJSONArray("associations").length(); i4++) {
                            arrayList2.add(jSONObject2.getJSONArray("associations").getString(i4));
                        }
                        arrayList.add("<i>" + jSONObject2.getString("name") + "</i><br>" + TextUtils.join("<br>", arrayList2));
                    }
                    String str2 = "<b>" + jSONObject.getString("headingTitle") + "</b><br>" + TextUtils.join(StringUtils.f103471LF, arrayList);
                    str = str.length() == 0 ? str2 : str + "<br>" + str2;
                }
                new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l(Html.fromHtml(str)).mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.3
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i5) {
                    }
                }).m1090I();
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        /* renamed from: V4 */
        public void m72748V4() throws JSONException {
            if (this.f89522d5 == null) {
                return;
            }
            try {
                JSONArray jSONArray = new JSONArray(this.f89522d5);
                String str = "";
                for (int i2 = 0; i2 < jSONArray.length(); i2++) {
                    JSONObject jSONObject = jSONArray.getJSONObject(i2);
                    ArrayList arrayList = new ArrayList();
                    for (int i3 = 0; i3 < jSONObject.getJSONArray("contributorList").length(); i3++) {
                        JSONObject jSONObject2 = jSONObject.getJSONArray("contributorList").getJSONObject(i3);
                        arrayList.add("<b>" + jSONObject2.getString("name") + "</b><br>" + jSONObject2.getString("disclosure"));
                    }
                    String strJoin = TextUtils.join("<br>", arrayList);
                    str = str.length() == 0 ? strJoin : str + "<br>" + strJoin;
                }
                new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l(Html.fromHtml(str)).mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.4
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i4) {
                    }
                }).m1090I();
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_menu) {
                UTDMenuFragment uTDMenuFragment = new UTDMenuFragment();
                Bundle bundle = new Bundle();
                bundle.putString(HTML.Tag.f74425y, this.f89517Y4);
                bundle.putString("title", this.f89568F4);
                uTDMenuFragment.m15342i2(bundle);
                uTDMenuFragment.m15245A2(this, 0);
                uTDMenuFragment.mo15222e3(m15283M(), HTML.Tag.f74422w0);
                return true;
            }
            if (itemId == C5562R.id.action_references) {
                UTDMenuFragment uTDMenuFragment2 = new UTDMenuFragment();
                Bundle bundle2 = new Bundle();
                bundle2.putString(HTML.Tag.f74425y, this.f89521c5);
                bundle2.putString("title", this.f89568F4);
                uTDMenuFragment2.m15342i2(bundle2);
                uTDMenuFragment2.m15245A2(this, 0);
                uTDMenuFragment2.mo15222e3(m15283M(), "references");
                return true;
            }
            if (itemId == C5562R.id.action_relatedTopics) {
                UTDRelatedTopicsFragment uTDRelatedTopicsFragment = new UTDRelatedTopicsFragment();
                Bundle bundle3 = new Bundle();
                bundle3.putString("RELATED", this.f89518Z4);
                uTDRelatedTopicsFragment.m15342i2(bundle3);
                uTDRelatedTopicsFragment.m15245A2(this, 0);
                uTDRelatedTopicsFragment.mo15222e3(m15283M(), "related");
                return true;
            }
            if (itemId == C5562R.id.action_relatedCalcs) {
                UTDRelatedTopicsFragment uTDRelatedTopicsFragment2 = new UTDRelatedTopicsFragment();
                Bundle bundle4 = new Bundle();
                bundle4.putString("RELATED", this.f89519a5);
                bundle4.putString("CALC", "");
                uTDRelatedTopicsFragment2.m15342i2(bundle4);
                uTDRelatedTopicsFragment2.m15245A2(this, 0);
                uTDRelatedTopicsFragment2.mo15222e3(m15283M(), "relatedcalc");
                return true;
            }
            if (itemId != C5562R.id.action_relatedGraphics) {
                return super.mo15329e1(menuItem);
            }
            UTDRelatedGraphicsFragment uTDRelatedGraphicsFragment = new UTDRelatedGraphicsFragment();
            Bundle bundle5 = new Bundle();
            bundle5.putString("RELATED", this.f89520b5);
            uTDRelatedGraphicsFragment.m15342i2(bundle5);
            uTDRelatedGraphicsFragment.m15245A2(this, 0);
            uTDRelatedGraphicsFragment.mo15222e3(m15283M(), "related");
            return true;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            MenuItem menuItemFindItem = menu.findItem(C5562R.id.action_relatedTopics);
            MenuItem menuItemFindItem2 = menu.findItem(C5562R.id.action_relatedGraphics);
            MenuItem menuItemFindItem3 = menu.findItem(C5562R.id.action_relatedCalcs);
            MenuItem menuItemFindItem4 = menu.findItem(C5562R.id.action_menu);
            MenuItem menuItemFindItem5 = menu.findItem(C5562R.id.action_references);
            if (this.f89518Z4 == null) {
                menuItemFindItem.setVisible(false);
            }
            if (this.f89520b5 == null) {
                menuItemFindItem2.setVisible(false);
            }
            if (this.f89519a5 == null) {
                menuItemFindItem3.setVisible(false);
            }
            if (this.f89517Y4 == null) {
                menuItemFindItem4.setVisible(false);
            }
            if (this.f89521c5 == null) {
                menuItemFindItem5.setVisible(false);
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws JSONException {
            String string;
            String string2;
            String string3;
            String string4;
            int i2 = 0;
            if (str2.equals("uptodatewebaction")) {
                if (!str3.equals("//zoomOut") && !str3.equals("//zoomIn") && !str3.equals("//zoomFinished")) {
                    if (str3.equals("//contributors")) {
                        m72747U4();
                    } else if (str3.equals("//contributorsDisclosure")) {
                        m72748V4();
                    } else if (str3.startsWith("//lastUpdated")) {
                        String string5 = this.f89566D4.getString("Version");
                        Date date = new Date(Long.parseLong(this.f89523e5));
                        CompressHelper.m71767x2(m15366r(), "All topics are updated as new evidence becomes available and our peer review process is complete. Literature review current through " + string5.split(StringUtils.SPACE)[0] + ". This topic was last updated: " + new SimpleDateFormat("yyyy-MM-dd").format(date), 1);
                    }
                }
            } else if (str2.equals("uptodateappaction")) {
                try {
                    JSONObject jSONObject = new JSONObject(Uri.decode(Uri.parse(str).toString().substring(21)));
                    try {
                        string = jSONObject.getJSONObject(HTML.Tag.f74333D).getString("assetType");
                    } catch (Exception unused) {
                        string = jSONObject.getJSONArray("data").getJSONObject(0).getString("type");
                    }
                    if (string.equals("graphic")) {
                        JSONArray jSONArray = jSONObject.getJSONArray("data");
                        ArrayList arrayList = new ArrayList();
                        while (i2 < jSONArray.length()) {
                            arrayList.add(jSONArray.getJSONObject(i2).getString("id"));
                            i2++;
                        }
                        this.f89579Q4.m71772A1(this.f89566D4, "Graphic-" + TextUtils.join(",,,,", arrayList), null, null);
                        return true;
                    }
                    if (string.equals("topic")) {
                        try {
                            string2 = jSONObject.getJSONObject(HTML.Tag.f74333D).getString("topicId");
                        } catch (Exception unused2) {
                            string2 = jSONObject.getJSONArray("data").getJSONObject(0).getString("id");
                        }
                        try {
                            string3 = jSONObject.getJSONArray("data").getJSONObject(0).getString("subtype");
                        } catch (Exception unused3) {
                            string3 = "";
                        }
                        if (string3.equals("narrative_icg")) {
                            ArrayList arrayList2 = new ArrayList(Collections2.m42365d(((iMD) m15366r().getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.5
                                @Override // com.google.common.base.Predicate
                                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                                public boolean apply(Bundle bundle) {
                                    return bundle.getString("Name").equals("utdpathways.db");
                                }
                            }));
                            if (arrayList2.size() == 0) {
                                CompressHelper.m71767x2(m15366r(), "You must install Uptodate Pathways Database", 1);
                                return true;
                            }
                            Bundle bundle = (Bundle) arrayList2.get(0);
                            CompressHelper compressHelper = this.f89579Q4;
                            ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(bundle, "Select * from docs where path = '" + ("base/" + string2 + ".html") + "'");
                            if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
                                CompressHelper.m71767x2(m15366r(), "Sorry, Document not available", 1);
                            } else {
                                this.f89579Q4.m71772A1(bundle, arrayListM71817V.get(0).getString("id"), null, "");
                            }
                        } else {
                            try {
                                try {
                                    string4 = jSONObject.getJSONObject(HTML.Tag.f74333D).getString(HTML.Tag.f74369V);
                                } catch (Exception unused4) {
                                    string4 = jSONObject.getJSONArray("data").getJSONObject(0).getString(HTML.Tag.f74369V);
                                }
                            } catch (Exception unused5) {
                                string4 = null;
                            }
                            if (this.f89524f5.equals(string2)) {
                                mo71967C3(string4);
                                return true;
                            }
                            iMDLogger.m73554j("Activity Type", m15366r().getClass().toString());
                            this.f89579Q4.m71772A1(this.f89566D4, "Topic-" + string2, null, string4);
                        }
                    } else if (string.equals("abstract")) {
                        ArrayList arrayList3 = new ArrayList();
                        JSONArray jSONArray2 = jSONObject.getJSONArray("data");
                        while (i2 < jSONArray2.length()) {
                            arrayList3.add(jSONArray2.getJSONObject(i2).getString("id"));
                            i2++;
                        }
                        String strJoin = TextUtils.join(",", arrayList3);
                        final ProgressDialog progressDialogShow = ProgressDialog.show(m15366r(), "Loading", "Please wait...", true);
                        this.f89579Q4.m71874o0("abstracts|||||" + strJoin).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.6
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(String str4) throws Throwable {
                                if (str4.length() == 0) {
                                    progressDialogShow.dismiss();
                                    CompressHelper.m71767x2(UTDViewerFragment.this.m15366r(), "Abstract Not Found", 1);
                                    return;
                                }
                                String[] strArrSplitByWholeSeparatorPreserveAllTokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(str4, ";;;;;");
                                if (strArrSplitByWholeSeparatorPreserveAllTokens.length == 0) {
                                    progressDialogShow.dismiss();
                                    CompressHelper.m71767x2(UTDViewerFragment.this.m15366r(), "Abstract Not Found", 1);
                                    return;
                                }
                                String str5 = "";
                                for (String str6 : strArrSplitByWholeSeparatorPreserveAllTokens) {
                                    String[] strArrSplit = str6.split("<div class=\"abstract\">");
                                    str5 = str5 + ("<div id=\"abstracts\">" + (strArrSplit.length > 1 ? strArrSplit[strArrSplit.length - 1].split("<div id=\"autoCompleteList\">")[0] : "") + "</div>");
                                }
                                if (str5.length() == 0) {
                                    progressDialogShow.dismiss();
                                    CompressHelper.m71767x2(UTDViewerFragment.this.m15366r(), "Abstract Not Found", 1);
                                    return;
                                }
                                UTDViewerFragment uTDViewerFragment = UTDViewerFragment.this;
                                String strM72817d4 = uTDViewerFragment.m72817d4(uTDViewerFragment.m15366r(), "UTDHeader.css");
                                UTDViewerFragment uTDViewerFragment2 = UTDViewerFragment.this;
                                UTDViewerFragment.this.m72783E4(strM72817d4.replace("[Title]", UTDViewerFragment.this.f89568F4).replace("[size]", "200") + str5 + uTDViewerFragment2.m72817d4(uTDViewerFragment2.m15366r(), "UTDFooter.css").replace("[year]", new SimpleDateFormat("yyyy").format(new Date())), "ABSTRACT");
                                progressDialogShow.dismiss();
                            }
                        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity.UTDViewerFragment.7
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(Throwable th) throws Throwable {
                                progressDialogShow.dismiss();
                                CompressHelper.m71767x2(UTDViewerFragment.this.m15366r(), "Error in contacting server", 1);
                            }
                        });
                    } else if (string.equals("external")) {
                        mo15256D2(new Intent("android.intent.action.VIEW", Uri.parse(StringUtils.splitByWholeSeparator(this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(URLDecoder.decode(jSONObject.getJSONArray("data").getJSONObject(0).getString("url")), "?target_url=")), "&token=")[0])));
                    }
                } catch (Exception unused6) {
                }
            }
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new UTDViewerFragment(), bundle);
    }
}

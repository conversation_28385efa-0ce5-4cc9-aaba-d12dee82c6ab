package net.imedicaldoctor.imd.Fragments.DRE;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.core.content.res.ResourcesCompat;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import java.text.SimpleDateFormat;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import org.apache.commons.lang3.StringUtils;
import saman.zamani.persiandate.PersianDate;
import saman.zamani.persiandate.PersianDateFormat;

/* loaded from: classes3.dex */
public class DRETestsListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f87827A4;

    /* renamed from: B4 */
    public String f87828B4;

    /* renamed from: C4 */
    public boolean f87829C4;

    public class TestScoreViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f87837I;

        /* renamed from: J */
        public TextView f87838J;

        /* renamed from: K */
        public TextView f87839K;

        /* renamed from: L */
        public TextView f87840L;

        /* renamed from: M */
        public ImageView f87841M;

        /* renamed from: N */
        public TextView f87842N;

        /* renamed from: O */
        public MaterialRippleLayout f87843O;

        public TestScoreViewHolder(View view) {
            super(view);
            this.f87837I = (TextView) view.findViewById(C5562R.id.text_date);
            this.f87838J = (TextView) view.findViewById(C5562R.id.text_info1);
            this.f87839K = (TextView) view.findViewById(C5562R.id.text_info2);
            this.f87843O = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f87840L = (TextView) view.findViewById(C5562R.id.text_score);
            this.f87841M = (ImageView) view.findViewById(C5562R.id.image_view);
            this.f87842N = (TextView) view.findViewById(C5562R.id.text_resume);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(false, false);
        appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestsListActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                relativeLayout.setVisibility(0);
            }
        }, 800L);
        this.f87829C4 = this.f88791k4.m71817V(this.f88788h4, "Select CorrPerc from Questions limit 1") == null;
        this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select * from tests order by id desc");
        ChaptersAdapter chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_dre_test) { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestsListActivityFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                TextView textView;
                String string;
                MaterialRippleLayout materialRippleLayout;
                View.OnClickListener onClickListener;
                TestScoreViewHolder testScoreViewHolder = (TestScoreViewHolder) viewHolder;
                testScoreViewHolder.f87837I.setTypeface(ResourcesCompat.m7158j(DRETestsListActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87838J.setTypeface(ResourcesCompat.m7158j(DRETestsListActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87839K.setTypeface(ResourcesCompat.m7158j(DRETestsListActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87842N.setTypeface(ResourcesCompat.m7158j(DRETestsListActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87840L.setTypeface(ResourcesCompat.m7158j(DRETestsListActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87837I.setText(DRETestsListActivityFragment.this.m72067i3(bundle2.getString("createdDate")));
                int length = StringUtils.splitByWholeSeparator(bundle2.getString("qIds"), ",").length;
                String str = bundle2.getString("mode").equals("Testing") ? "امتحان" : "مطالعه";
                testScoreViewHolder.f87838J.setText(length + " سوال. حالت مطالعه: " + str);
                if (DRETestsListActivityFragment.this.f87829C4) {
                    textView = testScoreViewHolder.f87839K;
                    string = bundle2.getString("subject");
                } else {
                    textView = testScoreViewHolder.f87839K;
                    string = bundle2.getString("subject") + " , " + bundle2.getString("system");
                }
                textView.setText(string);
                if (bundle2.getString("done").equals(IcyHeaders.f28171a3)) {
                    testScoreViewHolder.f87841M.setImageDrawable(DRETestsListActivityFragment.this.m15320b0().getDrawable(C5562R.drawable.circle_green));
                    testScoreViewHolder.f87842N.setText("نمره");
                    testScoreViewHolder.f87840L.setVisibility(0);
                    testScoreViewHolder.f87840L.setText(bundle2.getString("score") + CSS.Value.f74136n0);
                    materialRippleLayout = testScoreViewHolder.f87843O;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestsListActivityFragment.2.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            DRETestsListActivityFragment dRETestsListActivityFragment = DRETestsListActivityFragment.this;
                            dRETestsListActivityFragment.f88791k4.m71772A1(dRETestsListActivityFragment.f88788h4, "testresult-" + bundle2.getString("id"), null, null);
                        }
                    };
                } else {
                    testScoreViewHolder.f87841M.setImageDrawable(DRETestsListActivityFragment.this.m15320b0().getDrawable(C5562R.drawable.circle_blue));
                    testScoreViewHolder.f87842N.setText("ادامه");
                    testScoreViewHolder.f87840L.setVisibility(8);
                    materialRippleLayout = testScoreViewHolder.f87843O;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestsListActivityFragment.2.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            DRETestsListActivityFragment dRETestsListActivityFragment = DRETestsListActivityFragment.this;
                            dRETestsListActivityFragment.f88791k4.m71772A1(dRETestsListActivityFragment.f88788h4, "test-" + bundle2.getString("id"), null, null);
                        }
                    };
                }
                materialRippleLayout.setOnClickListener(onClickListener);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                return DRETestsListActivityFragment.this.new TestScoreViewHolder(view);
            }
        };
        this.f88792l4 = chaptersAdapter;
        chaptersAdapter.f101434h = "آزمونی وجود ندارد";
        this.f88803w4.setAdapter(chaptersAdapter);
        m72461N2();
        m15358o2(false);
        this.f88799s4.setVisibility(8);
        this.f88798r4.setTitle("آزمون های پیشین");
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f87827A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f87827A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "";
    }

    /* renamed from: i3 */
    public String m72067i3(String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ");
        new SimpleDateFormat("MM dd,yyyy HH:mm:ss");
        try {
            return new PersianDateFormat().m78973b(new PersianDate(Long.valueOf(simpleDateFormat.parse(str).getTime())));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return str;
        }
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Resources;
import android.database.SQLException;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Parcelable;
import android.os.Process;
import android.support.v4.media.session.PlaybackStateCompat;
import android.text.TextUtils;
import android.text.format.Formatter;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SwitchCompat;
import androidx.core.internal.view.SupportMenu;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.media3.extractor.text.ttml.TtmlNode;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import at.grabner.circleprogress.BuildConfig;
import com.basusingh.beautifulprogressdialog.BeautifulProgressDialog;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.xml.xmp.DublinCoreProperties;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.requery.android.database.sqlite.SQLiteDatabase;
import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.Views.ButtonFloatHelp;
import net.imedicaldoctor.imd.Views.ButtonFloatHelpBadge;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import yuku.ambilwarna.AmbilWarnaDialog;

/* loaded from: classes3.dex */
public class accountFragment extends Fragment {

    /* renamed from: G4 */
    public static final String f89927G4 = "Setting_SelectDownloadPath";

    /* renamed from: H4 */
    public static final String f89928H4 = "Setting_Selectlandingpage";

    /* renamed from: I4 */
    public static final String f89929I4 = "Setting_MainServer";

    /* renamed from: J4 */
    public static final String f89930J4 = "Setting_DownloadServer";

    /* renamed from: K4 */
    public static final String f89931K4 = "Setting_FullscreenMode";

    /* renamed from: L4 */
    public static final String f89932L4 = "Setting_HideListOnSelect";

    /* renamed from: M4 */
    public static final String f89933M4 = "Setting_HideStatusBar";

    /* renamed from: N4 */
    public static final String f89934N4 = "Setting_DynamicRipple";

    /* renamed from: O4 */
    public static final String f89935O4 = "Setting_CollapseSearchResults";

    /* renamed from: P4 */
    public static final String f89936P4 = "Setting_CollapseContentSearch";

    /* renamed from: Q4 */
    public static final String f89937Q4 = "Setting_LockinFullscreen";

    /* renamed from: R4 */
    public static final String f89938R4 = "Setting_UseLessSpace";

    /* renamed from: S4 */
    public static final String f89939S4 = "Setting_EnableSwipeDelete";

    /* renamed from: T4 */
    public static final String f89940T4 = "Setting_UseDeltaUpdate";

    /* renamed from: U4 */
    public static final String f89941U4 = "Setting_UseCollapsingToolbar";

    /* renamed from: V4 */
    public static final String f89942V4 = "Setting_newDocumentOpen";

    /* renamed from: W4 */
    public static final String f89943W4 = "Setting_UseLastRed";

    /* renamed from: X4 */
    public static final String f89944X4 = "Setting_UseDefaultFont";

    /* renamed from: Y4 */
    public static final String f89945Y4 = "Setting_LoadDownloadsAutomatically";

    /* renamed from: Z4 */
    public static final String f89946Z4 = "Setting_JustifyTexts";

    /* renamed from: a5 */
    public static final String f89947a5 = "Setting_OpenLastTopic";

    /* renamed from: b5 */
    public static final String f89948b5 = "Setting_SaveLogs";

    /* renamed from: c5 */
    public static final String f89949c5 = "Setting_EnableWakeLock";

    /* renamed from: d5 */
    public static final String f89950d5 = "Setting_DarkTheme";

    /* renamed from: e5 */
    public static final String f89951e5 = "Setting_AutomaticBackups";

    /* renamed from: f5 */
    public static final String f89952f5 = "Setting_LineHeight";

    /* renamed from: g5 */
    public static final String f89953g5 = "Setting_ShowPopup";

    /* renamed from: h5 */
    public static final String f89954h5 = "Setting_AutomaticHighlight";

    /* renamed from: i5 */
    public static final String f89955i5 = "Setting_CheckUpdate";

    /* renamed from: j5 */
    public static final String f89956j5 = "Setting_StartFileWebServer";

    /* renamed from: k5 */
    public static final String f89957k5 = "Setting_BackupHighlightsFAvorites";

    /* renamed from: l5 */
    public static final String f89958l5 = "Setting_Restore";

    /* renamed from: m5 */
    public static final String f89959m5 = "Setting_DeleteTemp";

    /* renamed from: n5 */
    public static final String f89960n5 = "Setting_Background";

    /* renamed from: B4 */
    private Boolean f89962B4;

    /* renamed from: C4 */
    Typeface f89963C4;

    /* renamed from: e4 */
    private View f89967e4;

    /* renamed from: f4 */
    private RecyclerView f89968f4;

    /* renamed from: g4 */
    private SwipeRefreshLayout f89969g4;

    /* renamed from: h4 */
    private ArrayList<String> f89970h4;

    /* renamed from: i4 */
    private Boolean f89971i4;

    /* renamed from: j4 */
    private String f89972j4;

    /* renamed from: k4 */
    private ArrayList<Bundle> f89973k4;

    /* renamed from: l4 */
    private ArrayList<String> f89974l4;

    /* renamed from: m4 */
    private Bundle f89975m4;

    /* renamed from: n4 */
    private String f89976n4;

    /* renamed from: o4 */
    private boolean f89977o4;

    /* renamed from: p4 */
    public CompressHelper f89978p4;

    /* renamed from: q4 */
    public VBHelper f89979q4;

    /* renamed from: r4 */
    private Activity f89980r4;

    /* renamed from: s4 */
    private ButtonFloatHelp f89981s4;

    /* renamed from: t4 */
    private ButtonFloatHelpBadge f89982t4;

    /* renamed from: u4 */
    private String f89983u4;

    /* renamed from: v4 */
    private String f89984v4;

    /* renamed from: w4 */
    private FileWebServer f89985w4;

    /* renamed from: x4 */
    private ArrayList<String> f89986x4;

    /* renamed from: y4 */
    public int f89987y4 = 0;

    /* renamed from: z4 */
    public int f89988z4 = 0;

    /* renamed from: A4 */
    public int f89961A4 = 0;

    /* renamed from: D4 */
    private final BroadcastReceiver f89964D4 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.5
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            iMDLogger.m73548d("account", "let's referesh accounts");
            try {
                accountFragment.this.m72961N2();
                accountFragment.this.f89968f4.getAdapter().m27491G();
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }
    };

    /* renamed from: E4 */
    private final BroadcastReceiver f89965E4 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.6
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            accountFragment.this.f89969g4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.6.1
                @Override // java.lang.Runnable
                public void run() {
                    accountFragment.this.f89969g4.setRefreshing(true);
                    accountFragment.this.m72960M2();
                }
            });
        }
    };

    /* renamed from: F4 */
    public BroadcastReceiver f89966F4 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.7
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            iMDLogger.m73554j("downloadFragment", "Received reload Downloads");
            accountFragment.this.m72988K2();
        }
    };

    public class AccountAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        HashMap<String, Integer> f90002d = new HashMap<>();

        /* renamed from: net.imedicaldoctor.imd.Fragments.accountFragment$AccountAdapter$17 */
        class ViewOnClickListenerC530117 implements View.OnClickListener {
            ViewOnClickListenerC530117() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                new AlertDialog.Builder(accountFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you sure? your information won't be deleted. ").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.17.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                        accountFragment.this.f89978p4.m71874o0("RemoveDevice|||||" + accountFragment.this.f89979q4.m73451m()).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.17.2.1
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(String str) throws Throwable {
                                if (!str.contains("1|||||")) {
                                    iMDLogger.m73550f("Error", str);
                                    CompressHelper.m71767x2(accountFragment.this.m15366r(), "Error occured", 0);
                                } else {
                                    accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().remove("DS").commit();
                                    accountFragment.this.f89978p4.m71894t2(null);
                                    accountFragment.this.f89978p4.m71901w2("Logout Successful", new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.17.2.1.1
                                        @Override // java.lang.Runnable
                                        public void run() {
                                            Process.killProcess(Process.myPid());
                                        }
                                    });
                                }
                            }
                        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.17.2.2
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(Throwable th) throws Throwable {
                                try {
                                    th.printStackTrace();
                                    CompressHelper.m71767x2(accountFragment.this.m15366r(), "Error occured ", 0);
                                } catch (Exception unused) {
                                }
                            }
                        });
                    }
                }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.17.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                    }
                }).m1090I();
            }
        }

        /* renamed from: net.imedicaldoctor.imd.Fragments.accountFragment$AccountAdapter$27 */
        class ViewOnClickListenerC531227 implements View.OnClickListener {
            ViewOnClickListenerC531227() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                if (accountFragment.this.f89972j4.length() > 0) {
                    return;
                }
                new AlertDialog.Builder(accountFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("This will backup your favorites & highlights to the iMD Server and will give you a identifier to restore it later.").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.27.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                        CompressHelper compressHelper = accountFragment.this.f89978p4;
                        String strM71889s0 = compressHelper.m71889s0(compressHelper.m71823X0(), "select dbName,dbTitle,dbAddress,dbDate,dbDocName from favorites", "dbName,dbTitle,dbAddress,dbDate,dbDocName", null);
                        Bundle bundle = new Bundle();
                        bundle.putString("text", "");
                        accountFragment accountfragment = accountFragment.this;
                        try {
                            String str = "SaveToFileZip|||||" + CompressHelper.m71732G0(strM71889s0 + "###" + accountfragment.f89978p4.m71889s0(accountfragment.m72993q3(), "select dbName,dbTitle,dbAddress,dbDate,dbDocName,type,text,note,save from highlight", "dbName,dbTitle,dbAddress,dbDate,dbDocName,type,text,note,save", bundle)) + "|||||" + accountFragment.this.f89978p4.m71905y1() + "|||||Account Page";
                            final ProgressDialog progressDialogShow = ProgressDialog.show(accountFragment.this.m15366r(), "Backing up", "Please wait...", true);
                            accountFragment.this.f89978p4.m71874o0(str).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.27.2.1
                                @Override // io.reactivex.rxjava3.functions.Consumer
                                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                                public void accept(String str2) throws Throwable {
                                    progressDialogShow.dismiss();
                                    accountFragment.this.f89972j4 = "Backup identifier : " + str2;
                                    accountFragment.this.f89968f4.getAdapter().m27491G();
                                }
                            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.27.2.2
                                @Override // io.reactivex.rxjava3.functions.Consumer
                                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                                public void accept(Throwable th) throws Throwable {
                                    progressDialogShow.dismiss();
                                    CompressHelper.m71767x2(accountFragment.this.m15366r(), "Error in contacting server", 1);
                                }
                            });
                        } catch (Exception e2) {
                            CompressHelper.m71767x2(accountFragment.this.m15366r(), "Error in compressing data. " + e2.getMessage(), 1);
                        }
                    }
                }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.27.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                    }
                }).m1090I();
            }
        }

        /* renamed from: net.imedicaldoctor.imd.Fragments.accountFragment$AccountAdapter$3 */
        class ViewOnClickListenerC53153 implements View.OnClickListener {
            ViewOnClickListenerC53153() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                final EditText editText = new EditText(accountFragment.this.m15366r());
                editText.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.black));
                new AlertDialog.Builder(accountFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Enter Credit Serial Number").setView(editText).mo1115y("Add Credit", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.3.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                        String string = editText.getText().toString();
                        if (string.length() == 0) {
                            CompressHelper.m71767x2(accountFragment.this.m15366r(), "You must enter a serial number", 1);
                            return;
                        }
                        accountFragment.this.f89978p4.m71874o0("AddCredit|||||" + accountFragment.this.f89979q4.m73451m() + "|||||" + string).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.3.2.1
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(String str) throws Throwable {
                                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "|||||");
                                if (!strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                                    CompressHelper.m71767x2(accountFragment.this.m15366r(), strArrSplitByWholeSeparator[1], 1);
                                    return;
                                }
                                accountFragment.this.f89978p4.m71894t2(strArrSplitByWholeSeparator[1]);
                                accountFragment.this.m72961N2();
                                accountFragment.this.m72987J2();
                            }
                        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.3.2.2
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(Throwable th) throws Throwable {
                                try {
                                    th.printStackTrace();
                                    CompressHelper.m71767x2(accountFragment.this.m15366r(), "Error occured on contacting server, try again later.", 1);
                                } catch (Exception unused) {
                                }
                            }
                        });
                    }
                }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.3.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                    }
                }).m1090I();
            }
        }

        public AccountAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            return i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            Bundle bundleM73000d0 = m73000d0(i2, accountFragment.this.f89970h4);
            if (!bundleM73000d0.getString("Type").equals("Header") && bundleM73000d0.getString("Type").equals("Item")) {
                String string = bundleM73000d0.getString("Section");
                int i3 = bundleM73000d0.getInt("Index");
                if (string.equals("Account Information") || string.equals("Help")) {
                    return 1;
                }
                if (string.equals("Your Databases")) {
                    return accountFragment.this.f89974l4.contains(TtmlNode.f29738r0) ? 1 : 2;
                }
                if (string.equals("About Us")) {
                    if (i3 == 0) {
                        return 3;
                    }
                    return i3 > 5 ? 1 : 4;
                }
                if (string.equals("Settings")) {
                    String str = (String) accountFragment.this.f89986x4.get(i3);
                    if (str.equals(accountFragment.f89929I4)) {
                        return 8;
                    }
                    if (str.equals(accountFragment.f89928H4)) {
                        return 5;
                    }
                    if (str.equals(accountFragment.f89930J4)) {
                        return 8;
                    }
                    if (str.equals(accountFragment.f89931K4) || str.equals(accountFragment.f89932L4) || str.equals(accountFragment.f89933M4) || str.equals(accountFragment.f89934N4) || str.equals(accountFragment.f89935O4) || str.equals(accountFragment.f89936P4) || str.equals(accountFragment.f89937Q4) || str.equals(accountFragment.f89938R4) || str.equals(accountFragment.f89939S4) || str.equals(accountFragment.f89940T4) || str.equals(accountFragment.f89941U4) || str.equals(accountFragment.f89953g5) || str.equals(accountFragment.f89942V4) || str.equals(accountFragment.f89943W4) || str.equals(accountFragment.f89944X4) || str.equals(accountFragment.f89945Y4) || str.equals(accountFragment.f89946Z4) || str.equals(accountFragment.f89947a5) || str.equals(accountFragment.f89948b5) || str.equals(accountFragment.f89949c5) || str.equals(accountFragment.f89950d5) || str.equals(accountFragment.f89951e5)) {
                        return 6;
                    }
                    if (str.equals(accountFragment.f89955i5) || str.equals(accountFragment.f89956j5) || str.equals(accountFragment.f89957k5) || str.equals(accountFragment.f89958l5) || str.equals(accountFragment.f89959m5)) {
                        return 1;
                    }
                    return (str.equals(accountFragment.f89960n5) || str.equals(accountFragment.f89952f5)) ? 8 : 5;
                }
                if (string.equals("")) {
                    return (i3 == 1 || i3 == 3) ? 7 : 1;
                }
            }
            return 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) throws Resources.NotFoundException {
            SwitchCompat switchCompat;
            View.OnClickListener onClickListener;
            MaterialRippleLayout materialRippleLayout;
            View.OnClickListener viewOnClickListenerC530117;
            View view;
            View.OnLongClickListener onLongClickListener;
            TextView textView;
            String str;
            Button button;
            View.OnClickListener onClickListener2;
            TextView textView2;
            int i3;
            AccountTextViewHolder accountTextViewHolder;
            TextView textView3;
            int color;
            MaterialRippleLayout materialRippleLayout2;
            int color2;
            TextView textView4;
            String str2;
            int i4;
            StringBuilder sb;
            String str3;
            Bundle bundleM73000d0 = m73000d0(i2, accountFragment.this.f89970h4);
            if (bundleM73000d0.getString("Type").equals("Header")) {
                HeaderCellViewHolder headerCellViewHolder = (HeaderCellViewHolder) viewHolder;
                headerCellViewHolder.f33076a.setOnClickListener(null);
                String string = bundleM73000d0.getString("Text");
                if (string.equals("Settings")) {
                    try {
                        i4 = accountFragment.this.f89980r4.getPackageManager().getPackageInfo(accountFragment.this.f89980r4.getPackageName(), 0).versionCode;
                    } catch (Exception unused) {
                        i4 = 0;
                    }
                    headerCellViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            accountFragment.this.f89971i4 = Boolean.valueOf(!r2.f89971i4.booleanValue());
                            accountFragment.this.f89968f4.getAdapter().m27491G();
                        }
                    });
                    if (accountFragment.this.f89971i4.booleanValue()) {
                        sb = new StringBuilder();
                        sb.append(string);
                        str3 = " ( Tap to Show )";
                    } else {
                        sb = new StringBuilder();
                        sb.append(string);
                        str3 = " ( Tap to Hide )";
                    }
                    sb.append(str3);
                    string = sb.toString() + " - App Version : " + i4;
                } else if (string.equals("Help")) {
                    string = "Help";
                }
                headerCellViewHolder.f90146I.setText(string);
            }
            if (bundleM73000d0.getString("Type").equals("Item")) {
                String string2 = bundleM73000d0.getString("Section");
                int i5 = bundleM73000d0.getInt("Index");
                if (string2.equals("Account Information")) {
                    accountTextViewHolder = (AccountTextViewHolder) viewHolder;
                    accountTextViewHolder.f90135J.setOnClickListener(null);
                    String strM71831a = accountFragment.this.f89978p4.m71831a();
                    if (!strM71831a.equals("All")) {
                        if (strM71831a.equals("Simple")) {
                            if (i5 == 0) {
                                accountTextViewHolder.f90134I.setText("Your Account Credit Is");
                                accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                materialRippleLayout2 = accountTextViewHolder.f90135J;
                                color2 = Color.parseColor("#ff1cab47");
                                materialRippleLayout2.setBackgroundColor(color2);
                            }
                            if (i5 != 1) {
                                if (i5 == 2) {
                                    accountTextViewHolder.f90134I.setText("Buy Credit");
                                    accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                    accountTextViewHolder.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.red_dark));
                                    materialRippleLayout = accountTextViewHolder.f90135J;
                                    viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.2
                                        @Override // android.view.View.OnClickListener
                                        public void onClick(View view2) {
                                            CompressHelper compressHelper = accountFragment.this.f89978p4;
                                            StringBuilder sb2 = new StringBuilder();
                                            sb2.append("http://imedicaldoctor.net/buycredit.php?user=");
                                            accountFragment accountfragment = accountFragment.this;
                                            sb2.append(accountfragment.f89979q4.m73452n(accountfragment.f89983u4, "127"));
                                            compressHelper.m71803P(sb2.toString());
                                        }
                                    };
                                } else if (i5 == 3) {
                                    accountTextViewHolder.f90134I.setText("Enter Credit Serial");
                                    accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                    accountTextViewHolder.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.blue));
                                    materialRippleLayout = accountTextViewHolder.f90135J;
                                    viewOnClickListenerC530117 = new ViewOnClickListenerC53153();
                                } else {
                                    if (i5 != 4) {
                                        return;
                                    }
                                    accountTextViewHolder.f90134I.setText("Upgrade to VIP Account");
                                    accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                    accountTextViewHolder.f90135J.setBackgroundColor(Color.parseColor("#c8a900"));
                                    materialRippleLayout = accountTextViewHolder.f90135J;
                                    viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.4
                                        @Override // android.view.View.OnClickListener
                                        public void onClick(View view2) {
                                            CompressHelper compressHelper = accountFragment.this.f89978p4;
                                            StringBuilder sb2 = new StringBuilder();
                                            sb2.append("http://imedicaldoctor.net/extendsubscription.php?user=");
                                            accountFragment accountfragment = accountFragment.this;
                                            sb2.append(accountfragment.f89979q4.m73452n(accountfragment.f89983u4, "127"));
                                            compressHelper.m71803P(sb2.toString());
                                        }
                                    };
                                }
                                materialRippleLayout.setOnClickListener(viewOnClickListenerC530117);
                                return;
                            }
                            accountTextViewHolder.f90134I.setText(accountFragment.this.f89976n4 + " Toman");
                            textView3 = accountTextViewHolder.f90134I;
                            color = accountFragment.this.m15320b0().getColor(C5562R.color.black);
                        } else if (strM71831a.contains("Active|")) {
                            if (i5 == 0) {
                                textView4 = accountTextViewHolder.f90134I;
                                str2 = "VIP Account - Active Till";
                            } else {
                                if (i5 != 1) {
                                    if (i5 == 2) {
                                        accountTextViewHolder.f90134I.setText("Extend Subscription");
                                        accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                        accountTextViewHolder.f90135J.setBackgroundColor(Color.parseColor("#0a7229"));
                                        materialRippleLayout = accountTextViewHolder.f90135J;
                                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.5
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                CompressHelper compressHelper = accountFragment.this.f89978p4;
                                                StringBuilder sb2 = new StringBuilder();
                                                sb2.append("http://imedicaldoctor.net/extendsubscription.php?user=");
                                                accountFragment accountfragment = accountFragment.this;
                                                sb2.append(accountfragment.f89979q4.m73452n(accountfragment.f89983u4, "127"));
                                                compressHelper.m71803P(sb2.toString());
                                            }
                                        };
                                        materialRippleLayout.setOnClickListener(viewOnClickListenerC530117);
                                        return;
                                    }
                                    return;
                                }
                                Date date = new Date(Long.parseLong(TextUtils.split(strM71831a.replace("|", ":"), ":")[1]) * 1000);
                                long jConvert = TimeUnit.DAYS.convert(Math.abs(new Date().getTime() - date.getTime()), TimeUnit.MILLISECONDS);
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                accountTextViewHolder.f90134I.setText(simpleDateFormat.format(date) + " - " + jConvert + " Days Remaining");
                                textView3 = accountTextViewHolder.f90134I;
                                color = accountFragment.this.m15320b0().getColor(C5562R.color.black);
                            }
                        } else {
                            if (!strM71831a.contains("Expired|")) {
                                return;
                            }
                            if (i5 == 0) {
                                accountTextViewHolder.f90134I.setText("VIP Account - Expired At");
                                accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                materialRippleLayout2 = accountTextViewHolder.f90135J;
                                color2 = accountFragment.this.m15320b0().getColor(C5562R.color.red_dark);
                                materialRippleLayout2.setBackgroundColor(color2);
                            }
                            if (i5 != 1) {
                                if (i5 == 2) {
                                    accountTextViewHolder.f90134I.setText("Extend Subscription");
                                    accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                    accountTextViewHolder.f90135J.setBackgroundColor(Color.parseColor("#0a7229"));
                                    materialRippleLayout = accountTextViewHolder.f90135J;
                                    viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.6
                                        @Override // android.view.View.OnClickListener
                                        public void onClick(View view2) {
                                            CompressHelper compressHelper = accountFragment.this.f89978p4;
                                            StringBuilder sb2 = new StringBuilder();
                                            sb2.append("http://imedicaldoctor.net/extendsubscription.php?user=");
                                            accountFragment accountfragment = accountFragment.this;
                                            sb2.append(accountfragment.f89979q4.m73452n(accountfragment.f89983u4, "127"));
                                            compressHelper.m71803P(sb2.toString());
                                        }
                                    };
                                    materialRippleLayout.setOnClickListener(viewOnClickListenerC530117);
                                    return;
                                }
                                return;
                            }
                            accountTextViewHolder.f90134I.setText(new SimpleDateFormat("yyyy-MM-dd").format(new Date(Long.parseLong(TextUtils.split(strM71831a.replace("|", ":"), ":")[1]) * 1000)));
                            textView3 = accountTextViewHolder.f90134I;
                            color = accountFragment.this.m15320b0().getColor(C5562R.color.black);
                        }
                        textView3.setTextColor(color);
                    } else {
                        if (i5 != 0) {
                            return;
                        }
                        textView4 = accountTextViewHolder.f90134I;
                        str2 = "VIP Account Forever";
                    }
                    textView4.setText(str2);
                    accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                    materialRippleLayout2 = accountTextViewHolder.f90135J;
                    color2 = Color.parseColor("#c8a900");
                    materialRippleLayout2.setBackgroundColor(color2);
                }
                if (string2.equals("Help")) {
                    accountTextViewHolder = (AccountTextViewHolder) viewHolder;
                    if (i5 == 0) {
                        accountTextViewHolder.f90134I.setText("راهنمای استفاده از نرم افزار");
                        accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.black));
                        accountTextViewHolder.f90134I.setTypeface(accountFragment.this.f89963C4);
                        accountTextViewHolder.f90135J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.7
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                accountFragment.this.f89978p4.m71803P("http://imedicaldoctor.net/guide-android.php");
                            }
                        });
                    } else {
                        accountTextViewHolder.f90134I.setText("سوالات و مشکلات شایع");
                        accountTextViewHolder.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.black));
                        accountTextViewHolder.f90134I.setTypeface(accountFragment.this.f89963C4);
                        accountTextViewHolder.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                        materialRippleLayout = accountTextViewHolder.f90135J;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.8
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                accountFragment.this.f89978p4.m71803P("http://imedicaldoctor.net/faq.php");
                            }
                        };
                    }
                } else if (string2.equals("Your Databases")) {
                    if (!accountFragment.this.f89974l4.contains(TtmlNode.f29738r0)) {
                        DatabaseButtonCellViewHolder databaseButtonCellViewHolder = (DatabaseButtonCellViewHolder) viewHolder;
                        final Bundle bundle = (Bundle) accountFragment.this.f89973k4.get(i5);
                        databaseButtonCellViewHolder.f90139I.setText(bundle.getString("Title"));
                        m73003g0(databaseButtonCellViewHolder.f90141K, bundle.getString("IconName"));
                        databaseButtonCellViewHolder.f90140J.setVisibility(8);
                        if (accountFragment.this.f89975m4.containsKey(bundle.getString("name"))) {
                            databaseButtonCellViewHolder.f90140J.setVisibility(0);
                            databaseButtonCellViewHolder.f90140J.setText("Valid until " + CompressHelper.m71746c1(accountFragment.this.f89975m4.getBundle(bundle.getString("name")).getString(DublinCoreProperties.f73850d)));
                            if (accountFragment.this.f89975m4.getBundle(bundle.getString("name")).getInt("expired") == 1) {
                                databaseButtonCellViewHolder.f90140J.setText("Subscription Ended on " + CompressHelper.m71746c1(accountFragment.this.f89975m4.getBundle(bundle.getString("name")).getString(DublinCoreProperties.f73850d)));
                                textView2 = databaseButtonCellViewHolder.f90140J;
                                i3 = SupportMenu.f12679c;
                            } else {
                                textView2 = databaseButtonCellViewHolder.f90140J;
                                i3 = -********;
                            }
                            textView2.setTextColor(i3);
                        } else {
                            databaseButtonCellViewHolder.f90140J.setVisibility(8);
                        }
                        final Bundle bundleM71826Y0 = accountFragment.this.f89978p4.m71826Y0("Name", bundle.getString("name"));
                        if (bundleM71826Y0 == null) {
                            databaseButtonCellViewHolder.f90142L.setText("Download");
                            button = databaseButtonCellViewHolder.f90142L;
                            onClickListener2 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.11
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view2) throws Resources.NotFoundException {
                                    ((mainActivity) accountFragment.this.m15366r()).f90750y3.setCurrentItem(4);
                                    accountFragment.this.f89967e4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.11.1
                                        @Override // java.lang.Runnable
                                        public void run() {
                                            downloadFragment downloadfragment = ((iMD) accountFragment.this.m15366r().getApplicationContext()).f101675c3;
                                            if (downloadfragment.m73207K3()) {
                                                downloadfragment.m73209M2();
                                            }
                                            downloadfragment.f90505r4.m40224D(0).m40291r();
                                            downloadfragment.f90500m4.m2508k0(bundle.getString("Title"), true);
                                        }
                                    }, 1000L);
                                }
                            };
                        } else if (bundleM71826Y0.getString("Version").compareTo(bundle.getString("Version")) >= 0) {
                            databaseButtonCellViewHolder.f90142L.setText("Open");
                            databaseButtonCellViewHolder.f90142L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.9
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view2) throws Resources.NotFoundException {
                                    ((mainActivity) accountFragment.this.m15366r()).f90750y3.setCurrentItem(1);
                                    accountFragment.this.f89967e4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.9.1
                                        @Override // java.lang.Runnable
                                        public void run() {
                                            ViewOnClickListenerC53549 viewOnClickListenerC53549 = ViewOnClickListenerC53549.this;
                                            accountFragment.this.f89978p4.m71909z1(bundleM71826Y0);
                                        }
                                    }, 1000L);
                                }
                            });
                            return;
                        } else {
                            databaseButtonCellViewHolder.f90142L.setText("Update");
                            button = databaseButtonCellViewHolder.f90142L;
                            onClickListener2 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.10
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view2) throws Resources.NotFoundException {
                                    ((mainActivity) accountFragment.this.m15366r()).f90750y3.setCurrentItem(4);
                                    accountFragment.this.f89967e4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.10.1
                                        @Override // java.lang.Runnable
                                        public void run() {
                                            downloadFragment downloadfragment = ((iMD) accountFragment.this.m15366r().getApplicationContext()).f101675c3;
                                            downloadfragment.f90505r4.m40224D(0).m40291r();
                                            downloadfragment.f90500m4.m2508k0(bundle.getString("Title"), true);
                                        }
                                    }, 1000L);
                                }
                            };
                        }
                        button.setOnClickListener(onClickListener2);
                        return;
                    }
                    accountTextViewHolder = (AccountTextViewHolder) viewHolder;
                    accountTextViewHolder.f90135J.setOnClickListener(null);
                    accountTextViewHolder.f90134I.setText("You Are Subscribed To All Databases");
                    textView3 = accountTextViewHolder.f90134I;
                    color = accountFragment.this.m15320b0().getColor(C5562R.color.grey);
                    textView3.setTextColor(color);
                } else if (string2.equals("About Us")) {
                    if (i5 == 0) {
                        ((SimpleTextViewHolder) viewHolder).f90159I.setText("iMD - Medical Resources");
                        return;
                    }
                    if (i5 == 1) {
                        SocialCellViewHolder socialCellViewHolder = (SocialCellViewHolder) viewHolder;
                        socialCellViewHolder.f90161I.setText("http://imedicaldoctor.net");
                        socialCellViewHolder.f90162J.setImageResource(C5562R.drawable.imd);
                        materialRippleLayout = socialCellViewHolder.f90163K;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.12
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                accountFragment.this.f89978p4.m71803P("http://imedicaldoctor.net");
                            }
                        };
                    } else if (i5 == 2) {
                        SocialCellViewHolder socialCellViewHolder2 = (SocialCellViewHolder) viewHolder;
                        socialCellViewHolder2.f90161I.setText("<EMAIL>");
                        socialCellViewHolder2.f90162J.setImageResource(C5562R.drawable.gmail);
                        materialRippleLayout = socialCellViewHolder2.f90163K;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.13
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                accountFragment.this.f89978p4.m71803P("mailto:<EMAIL>");
                            }
                        };
                    } else if (i5 == 3) {
                        SocialCellViewHolder socialCellViewHolder3 = (SocialCellViewHolder) viewHolder;
                        socialCellViewHolder3.f90161I.setText("Telegram Channel");
                        socialCellViewHolder3.f90162J.setImageResource(C5562R.drawable.td_logo);
                        materialRippleLayout = socialCellViewHolder3.f90163K;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.14
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                accountFragment.this.f89978p4.m71803P("http://imedicaldoctor.net/t.php");
                            }
                        };
                    } else if (i5 == 4) {
                        SocialCellViewHolder socialCellViewHolder4 = (SocialCellViewHolder) viewHolder;
                        socialCellViewHolder4.f90161I.setText("Telegram Group");
                        socialCellViewHolder4.f90162J.setImageResource(C5562R.drawable.td_logo);
                        materialRippleLayout = socialCellViewHolder4.f90163K;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.15
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                accountFragment.this.f89978p4.m71803P("http://imedicaldoctor.net/telegramandroid.php");
                            }
                        };
                    } else if (i5 == 5) {
                        SocialCellViewHolder socialCellViewHolder5 = (SocialCellViewHolder) viewHolder;
                        socialCellViewHolder5.f90161I.setText("@imedicaldoctor");
                        socialCellViewHolder5.f90162J.setImageResource(C5562R.drawable.insta);
                        materialRippleLayout = socialCellViewHolder5.f90163K;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.16
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                accountFragment.this.f89978p4.m71803P("https://www.instagram.com/imedicaldoctor/");
                            }
                        };
                    } else {
                        if (i5 != 6) {
                            if (i5 == 7) {
                                AccountTextViewHolder accountTextViewHolder2 = (AccountTextViewHolder) viewHolder;
                                accountTextViewHolder2.f90134I.setText("Exit app");
                                accountFragment.this.f89962B4 = Boolean.FALSE;
                                accountTextViewHolder2.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                accountTextViewHolder2.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.darkGrey));
                                accountTextViewHolder2.f90135J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.18
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view2) {
                                        if (accountFragment.this.f89962B4.booleanValue()) {
                                            return;
                                        }
                                        Process.killProcess(Process.myPid());
                                    }
                                });
                                view = accountTextViewHolder2.f90135J;
                                onLongClickListener = new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.19
                                    @Override // android.view.View.OnLongClickListener
                                    public boolean onLongClick(View view2) {
                                        accountFragment.this.f89962B4 = Boolean.TRUE;
                                        accountFragment.this.f89978p4.m71896u2(accountFragment.this.f89978p4.m71797M1() + "/zlogs.db", "*/*");
                                        return true;
                                    }
                                };
                                view.setOnLongClickListener(onLongClickListener);
                                return;
                            }
                            return;
                        }
                        AccountTextViewHolder accountTextViewHolder3 = (AccountTextViewHolder) viewHolder;
                        accountTextViewHolder3.f90134I.setText("Log Out");
                        accountTextViewHolder3.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                        accountTextViewHolder3.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.red));
                        materialRippleLayout = accountTextViewHolder3.f90135J;
                        viewOnClickListenerC530117 = new ViewOnClickListenerC530117();
                    }
                } else {
                    if (!string2.equals("Settings")) {
                        return;
                    }
                    String str4 = (String) accountFragment.this.f89986x4.get(i5);
                    if (str4.equals(accountFragment.f89927G4)) {
                        SettingCellViewHolder settingCellViewHolder = (SettingCellViewHolder) viewHolder;
                        settingCellViewHolder.f90151I.setText("Select Download Path");
                        materialRippleLayout = settingCellViewHolder.f90152J;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.20
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) throws InterruptedException, IOException {
                                HashSet<String> hashSetM71875o1 = accountFragment.this.f89978p4.m71875o1();
                                ArrayList<? extends Parcelable> arrayList = new ArrayList<>();
                                Iterator<String> it2 = hashSetM71875o1.iterator();
                                while (it2.hasNext()) {
                                    String next = it2.next();
                                    if (!next.contains("/.") && !next.contains("/sdcard/external_sd") && !next.contains("/mnt/sdcard/external_sd")) {
                                        Bundle bundle2 = new Bundle();
                                        bundle2.putString("Path", next);
                                        long usableSpace = new File(next).getUsableSpace();
                                        String str5 = new DecimalFormat("#,##0").format((usableSpace / PlaybackStateCompat.f536p3) / PlaybackStateCompat.f536p3) + " MB";
                                        bundle2.putString("Title", accountFragment.this.f89978p4.m71805Q(next));
                                        bundle2.putString("Size", str5 + " - " + next);
                                        arrayList.add(bundle2);
                                    }
                                }
                                String strM71904y = accountFragment.this.f89978p4.m71904y();
                                fileSizeSettingsList filesizesettingslist = new fileSizeSettingsList();
                                Bundle bundle3 = new Bundle();
                                bundle3.putString("type", "DownloadPath");
                                bundle3.putParcelableArrayList("items", arrayList);
                                bundle3.putString("titleProperty", "Title");
                                bundle3.putString("selected", strM71904y);
                                filesizesettingslist.m15342i2(bundle3);
                                filesizesettingslist.mo15218Z2(true);
                                filesizesettingslist.m15245A2(accountFragment.this, 0);
                                filesizesettingslist.mo15222e3(accountFragment.this.m15283M(), "SettingListDownloadPath");
                            }
                        };
                    } else if (str4.equals(accountFragment.f89928H4)) {
                        SettingCellViewHolder settingCellViewHolder2 = (SettingCellViewHolder) viewHolder;
                        settingCellViewHolder2.f90151I.setText("Select Landing Page");
                        materialRippleLayout = settingCellViewHolder2.f90152J;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.21
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                ArrayList<? extends Parcelable> arrayList = new ArrayList<>();
                                String[] strArr = {"Titles", "Databases", "Favorites", "Content", "Store", "Account"};
                                for (int i6 = 0; i6 < 6; i6++) {
                                    arrayList.add(accountFragment.this.m72990m3(strArr[i6]));
                                }
                                String string3 = accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("Tab", "");
                                if (string3.length() == 0) {
                                    string3 = strArr[1];
                                }
                                settingsList settingslist = new settingsList();
                                Bundle bundle2 = new Bundle();
                                bundle2.putString("type", "Tab");
                                bundle2.putParcelableArrayList("items", arrayList);
                                bundle2.putString("titleProperty", "Title");
                                bundle2.putString("selected", string3);
                                settingslist.m15342i2(bundle2);
                                settingslist.mo15218Z2(true);
                                settingslist.m15245A2(accountFragment.this, 0);
                                settingslist.mo15222e3(accountFragment.this.m15283M(), "SettingListTab");
                            }
                        };
                    } else if (str4.equals(accountFragment.f89929I4)) {
                        accountFragment.this.f89987y4 = i2;
                        SettingDetailCellViewHolder settingDetailCellViewHolder = (SettingDetailCellViewHolder) viewHolder;
                        settingDetailCellViewHolder.f90154I.setText("Main Server");
                        settingDetailCellViewHolder.f90155J.setText(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("MainServer", "Iran"));
                        settingDetailCellViewHolder.f90156K.setVisibility(8);
                        materialRippleLayout = settingDetailCellViewHolder.f90157L;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.22
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                ArrayList<? extends Parcelable> arrayList = new ArrayList<>();
                                String[] strArr = {"Germany", "Iran"};
                                for (int i6 = 0; i6 < 2; i6++) {
                                    arrayList.add(accountFragment.this.m72990m3(strArr[i6]));
                                }
                                String string3 = accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("MainServer", "Iran");
                                settingsList settingslist = new settingsList();
                                Bundle bundle2 = new Bundle();
                                bundle2.putString("type", "MainDL");
                                bundle2.putParcelableArrayList("items", arrayList);
                                bundle2.putString("titleProperty", "Title");
                                bundle2.putString("selected", string3);
                                settingslist.m15342i2(bundle2);
                                settingslist.mo15218Z2(true);
                                settingslist.m15245A2(accountFragment.this, 0);
                                settingslist.mo15222e3(accountFragment.this.m15283M(), "SettingListTab");
                            }
                        };
                    } else {
                        if (!str4.equals(accountFragment.f89930J4)) {
                            if (str4.equals(accountFragment.f89955i5)) {
                                AccountTextViewHolder accountTextViewHolder4 = (AccountTextViewHolder) viewHolder;
                                accountTextViewHolder4.f90134I.setText("Check App Update");
                                accountTextViewHolder4.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                accountTextViewHolder4.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.darkGrey));
                                accountTextViewHolder4.f90135J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.24
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view2) {
                                        if (accountFragment.this.f89980r4 == null) {
                                            return;
                                        }
                                        ((mainActivity) accountFragment.this.f89980r4).m73327l1(true);
                                    }
                                });
                                view = accountTextViewHolder4.f90135J;
                                onLongClickListener = new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.25
                                    @Override // android.view.View.OnLongClickListener
                                    public boolean onLongClick(View view2) {
                                        AccountAdapter.this.m73001e0(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.25.1
                                            @Override // java.lang.Runnable
                                            public void run() throws InterruptedException, IOException {
                                                AccountAdapter.this.m73002f0();
                                            }
                                        }, null);
                                        return true;
                                    }
                                };
                            } else if (str4.equals(accountFragment.f89956j5)) {
                                AccountTextViewHolder accountTextViewHolder5 = (AccountTextViewHolder) viewHolder;
                                accountTextViewHolder5.f90134I.setText(accountFragment.this.f89984v4);
                                accountTextViewHolder5.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                accountTextViewHolder5.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.material_orange_800));
                                materialRippleLayout = accountTextViewHolder5.f90135J;
                                viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.26
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view2) throws InterruptedException {
                                        accountFragment.this.m72986t3();
                                    }
                                };
                            } else if (str4.equals(accountFragment.f89957k5)) {
                                AccountTextViewHolder accountTextViewHolder6 = (AccountTextViewHolder) viewHolder;
                                accountTextViewHolder6.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                accountTextViewHolder6.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.green));
                                if (accountFragment.this.f89972j4.length() == 0) {
                                    textView = accountTextViewHolder6.f90134I;
                                    str = "Backup Favorites & Highlights";
                                } else {
                                    textView = accountTextViewHolder6.f90134I;
                                    str = accountFragment.this.f89972j4;
                                }
                                textView.setText(str);
                                accountTextViewHolder6.f90135J.setOnClickListener(new ViewOnClickListenerC531227());
                                view = accountTextViewHolder6.f90135J;
                                onLongClickListener = new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.28
                                    @Override // android.view.View.OnLongClickListener
                                    public boolean onLongClick(View view2) {
                                        new AlertDialog.Builder(accountFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("This will delete all of your favorites. are you sure ?").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.28.2
                                            @Override // android.content.DialogInterface.OnClickListener
                                            public void onClick(DialogInterface dialogInterface, int i6) {
                                                CompressHelper compressHelper = accountFragment.this.f89978p4;
                                                compressHelper.m71881q(compressHelper.m71823X0(), "delete from favorites");
                                                accountFragment.this.m72994r3();
                                            }
                                        }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.28.1
                                            @Override // android.content.DialogInterface.OnClickListener
                                            public void onClick(DialogInterface dialogInterface, int i6) {
                                            }
                                        }).m1090I();
                                        return true;
                                    }
                                };
                            } else if (str4.equals(accountFragment.f89958l5)) {
                                AccountTextViewHolder accountTextViewHolder7 = (AccountTextViewHolder) viewHolder;
                                accountTextViewHolder7.f90134I.setText("Restore Favorites & Highlights");
                                accountTextViewHolder7.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                accountTextViewHolder7.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.dark_blue));
                                accountTextViewHolder7.f90135J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.29
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view2) {
                                        final EditText editText = new EditText(accountFragment.this.m15366r());
                                        editText.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.black));
                                        new AlertDialog.Builder(accountFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Enter Backup Identifier.").setView(editText).mo1115y("Replace", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.29.3
                                            @Override // android.content.DialogInterface.OnClickListener
                                            public void onClick(DialogInterface dialogInterface, int i6) {
                                                accountFragment.this.m72989O2(editText.getText().toString(), true);
                                            }
                                        }).mo1106p("Merge", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.29.2
                                            @Override // android.content.DialogInterface.OnClickListener
                                            public void onClick(DialogInterface dialogInterface, int i6) {
                                                accountFragment.this.m72989O2(editText.getText().toString(), false);
                                            }
                                        }).mo1109s("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.29.1
                                            @Override // android.content.DialogInterface.OnClickListener
                                            public void onClick(DialogInterface dialogInterface, int i6) {
                                            }
                                        }).m1090I();
                                    }
                                });
                                view = accountTextViewHolder7.f90135J;
                                onLongClickListener = new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.30
                                    @Override // android.view.View.OnLongClickListener
                                    public boolean onLongClick(View view2) {
                                        new AlertDialog.Builder(accountFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("This will delete all of your highlights and notes. are you sure ?").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.30.2
                                            @Override // android.content.DialogInterface.OnClickListener
                                            public void onClick(DialogInterface dialogInterface, int i6) {
                                                accountFragment accountfragment = accountFragment.this;
                                                accountfragment.f89978p4.m71881q(accountfragment.m72993q3(), "delete from highlights");
                                            }
                                        }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.30.1
                                            @Override // android.content.DialogInterface.OnClickListener
                                            public void onClick(DialogInterface dialogInterface, int i6) {
                                            }
                                        }).m1090I();
                                        return true;
                                    }
                                };
                            } else {
                                if (!str4.equals(accountFragment.f89959m5)) {
                                    if (str4.equals(accountFragment.f89931K4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder.f90148I.setText("Fullscreen Mode");
                                        settingCellSwitchViewHolder.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("Fullscreen", true));
                                        switchCompat = settingCellSwitchViewHolder.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.32
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("Fullscreen", settingCellSwitchViewHolder.f90149J.isChecked()).commit();
                                                CompressHelper.m71767x2(accountFragment.this.m15366r(), "You must restart your app for this change to take effect", 0);
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89932L4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder2 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder2.f90148I.setText("Hide List On Select");
                                        settingCellSwitchViewHolder2.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("HideList", true));
                                        switchCompat = settingCellSwitchViewHolder2.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.33
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("HideList", settingCellSwitchViewHolder2.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89933M4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder3 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder3.f90148I.setText("Hide Status Bar");
                                        settingCellSwitchViewHolder3.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false));
                                        switchCompat = settingCellSwitchViewHolder3.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.34
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("HideStatusBar", settingCellSwitchViewHolder3.f90149J.isChecked()).commit();
                                                CompressHelper.m71767x2(accountFragment.this.m15366r(), "You must restart your app for this change to take effect", 0);
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89934N4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder4 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder4.f90148I.setText("Dynamic Ripple Colors");
                                        settingCellSwitchViewHolder4.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("ripple", true));
                                        switchCompat = settingCellSwitchViewHolder4.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.35
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("ripple", settingCellSwitchViewHolder4.f90149J.isChecked()).commit();
                                                CompressHelper.m71767x2(accountFragment.this.m15366r(), "You must restart your app for this change to take effect", 0);
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89935O4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder5 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder5.f90148I.setText("Collapse Search Results");
                                        settingCellSwitchViewHolder5.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("SearchCollapsed", false));
                                        switchCompat = settingCellSwitchViewHolder5.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.36
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("SearchCollapsed", settingCellSwitchViewHolder5.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89936P4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder6 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder6.f90148I.setText("Collapse Content Results");
                                        settingCellSwitchViewHolder6.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("ContentCollapsed", false));
                                        switchCompat = settingCellSwitchViewHolder6.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.37
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("ContentCollapsed", settingCellSwitchViewHolder6.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89937Q4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder7 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder7.f90148I.setText("Lock in Fullscreen");
                                        settingCellSwitchViewHolder7.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("lockfull", true));
                                        switchCompat = settingCellSwitchViewHolder7.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.38
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("lockfull", settingCellSwitchViewHolder7.f90149J.isChecked()).commit();
                                                CompressHelper.m71767x2(accountFragment.this.m15366r(), "You must restart your app for this change to take effect", 0);
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89938R4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder8 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder8.f90148I.setText("Use less space for install");
                                        settingCellSwitchViewHolder8.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("lessspace", false));
                                        switchCompat = settingCellSwitchViewHolder8.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.39
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("lessspace", settingCellSwitchViewHolder8.f90149J.isChecked()).commit();
                                                if (settingCellSwitchViewHolder8.f90149J.isChecked()) {
                                                    CompressHelper.m71767x2(accountFragment.this.m15366r(), "This may cause problems on install process", 0);
                                                }
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89939S4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder9 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder9.f90148I.setText("Enable Swipe to Delete");
                                        settingCellSwitchViewHolder9.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("swipedelete", true));
                                        switchCompat = settingCellSwitchViewHolder9.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.40
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("swipedelete", settingCellSwitchViewHolder9.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89940T4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder10 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder10.f90148I.setText("Use Delta Update");
                                        settingCellSwitchViewHolder10.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("delta", false));
                                        switchCompat = settingCellSwitchViewHolder10.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.41
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("delta", settingCellSwitchViewHolder10.f90149J.isChecked()).commit();
                                                if (settingCellSwitchViewHolder10.f90149J.isChecked()) {
                                                    CompressHelper.m71767x2(accountFragment.this.m15366r(), "On some devices delta update can corrupt the main database and you must download the whole database again", 1);
                                                } else {
                                                    LocalBroadcastManager.m16410b(accountFragment.this.m15366r()).m16413d(new Intent("reloadDownloads"));
                                                }
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89941U4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder11 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder11.f90148I.setText("Use Collapsing Toolbar");
                                        settingCellSwitchViewHolder11.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("NestedScroll", true));
                                        switchCompat = settingCellSwitchViewHolder11.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.42
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("NestedScroll", settingCellSwitchViewHolder11.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89953g5)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder12 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder12.f90148I.setText("Open Tables as Popup");
                                        settingCellSwitchViewHolder12.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("showpopup", true));
                                        switchCompat = settingCellSwitchViewHolder12.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.43
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("showpopup", settingCellSwitchViewHolder12.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89942V4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder13 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder13.f90148I.setText("New Document Loading");
                                        settingCellSwitchViewHolder13.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("newdocument", false));
                                        switchCompat = settingCellSwitchViewHolder13.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.44
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("newdocument", settingCellSwitchViewHolder13.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89943W4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder14 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder14.f90148I.setText("Use Last Red Highlight as Starting Point");
                                        settingCellSwitchViewHolder14.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("lastred", false));
                                        switchCompat = settingCellSwitchViewHolder14.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.45
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("lastred", settingCellSwitchViewHolder14.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89944X4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder15 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder15.f90148I.setText("Use Default System Font");
                                        settingCellSwitchViewHolder15.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("defaultfont", false));
                                        switchCompat = settingCellSwitchViewHolder15.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.46
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("defaultfont", settingCellSwitchViewHolder15.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89945Y4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder16 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder16.f90148I.setText("Load Download List Automatically");
                                        settingCellSwitchViewHolder16.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("loaddownload", false));
                                        settingCellSwitchViewHolder16.f90149J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.47
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("loaddownload", settingCellSwitchViewHolder16.f90149J.isChecked()).commit();
                                            }
                                        });
                                        view = settingCellSwitchViewHolder16.f90148I;
                                        onLongClickListener = new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.48
                                            @Override // android.view.View.OnLongClickListener
                                            public boolean onLongClick(View view2) {
                                                FragmentActivity fragmentActivityM15366r;
                                                String str5;
                                                boolean z = !accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("showfreeversion", false);
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("showfreeversion", z).commit();
                                                if (z) {
                                                    fragmentActivityM15366r = accountFragment.this.m15366r();
                                                    str5 = "Showing Last Version : True";
                                                } else {
                                                    fragmentActivityM15366r = accountFragment.this.m15366r();
                                                    str5 = "Showing Last Version : False";
                                                }
                                                Toast.makeText(fragmentActivityM15366r, str5, 1).show();
                                                LocalBroadcastManager.m16410b(accountFragment.this.m15366r()).m16413d(new Intent("reloadDownloads"));
                                                return true;
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89946Z4)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder17 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder17.f90148I.setText("Justify Texts");
                                        settingCellSwitchViewHolder17.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("justify", true));
                                        switchCompat = settingCellSwitchViewHolder17.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.49
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("justify", settingCellSwitchViewHolder17.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89947a5)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder18 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder18.f90148I.setText("Open Last Topic after crash");
                                        settingCellSwitchViewHolder18.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("openaftercrash", true));
                                        switchCompat = settingCellSwitchViewHolder18.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.50
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("openaftercrash", settingCellSwitchViewHolder18.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89948b5)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder19 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder19.f90148I.setText("Save Logs");
                                        settingCellSwitchViewHolder19.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("savelogs", false));
                                        switchCompat = settingCellSwitchViewHolder19.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.51
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("savelogs", settingCellSwitchViewHolder19.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89949c5)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder20 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder20.f90148I.setText("Enable Wake Lock");
                                        settingCellSwitchViewHolder20.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("wakelock", true));
                                        switchCompat = settingCellSwitchViewHolder20.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.52
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("wakelock", settingCellSwitchViewHolder20.f90149J.isChecked()).commit();
                                            }
                                        };
                                    } else if (str4.equals(accountFragment.f89950d5)) {
                                        final SettingCellSwitchViewHolder settingCellSwitchViewHolder21 = (SettingCellSwitchViewHolder) viewHolder;
                                        settingCellSwitchViewHolder21.f90148I.setText("Dark Theme (works sometimes)");
                                        settingCellSwitchViewHolder21.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("dark", false));
                                        switchCompat = settingCellSwitchViewHolder21.f90149J;
                                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.53
                                            @Override // android.view.View.OnClickListener
                                            public void onClick(View view2) {
                                                accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("dark", settingCellSwitchViewHolder21.f90149J.isChecked()).commit();
                                                accountFragment.this.m15366r().getSharedPreferences("default_preferences", 0).getBoolean("dark", false);
                                                CompressHelper.m71767x2(accountFragment.this.m15366r(), "closing app for changes to take effect", 2);
                                                accountFragment.this.f89967e4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.53.1
                                                    @Override // java.lang.Runnable
                                                    public void run() {
                                                        Process.killProcess(Process.myPid());
                                                    }
                                                }, ExoPlayer.f21773a1);
                                            }
                                        };
                                    } else {
                                        if (str4.equals(accountFragment.f89960n5)) {
                                            final SettingDetailCellViewHolder settingDetailCellViewHolder2 = (SettingDetailCellViewHolder) viewHolder;
                                            settingDetailCellViewHolder2.f90154I.setText("Document Color");
                                            settingDetailCellViewHolder2.f90155J.setText(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("background_color", "#ffffff"));
                                            settingDetailCellViewHolder2.f90156K.setVisibility(0);
                                            settingDetailCellViewHolder2.f90156K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.54
                                                @Override // android.view.View.OnClickListener
                                                public void onClick(View view2) {
                                                    accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().remove("background_color").commit();
                                                    settingDetailCellViewHolder2.f90155J.setText("#ffffff");
                                                }
                                            });
                                            settingDetailCellViewHolder2.f90157L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.55
                                                @Override // android.view.View.OnClickListener
                                                public void onClick(View view2) {
                                                    new AmbilWarnaDialog(accountFragment.this.m15366r(), Color.parseColor(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("background_color", "#ffffff")), new AmbilWarnaDialog.OnAmbilWarnaListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.55.1
                                                        @Override // yuku.ambilwarna.AmbilWarnaDialog.OnAmbilWarnaListener
                                                        /* renamed from: a */
                                                        public void mo73014a(AmbilWarnaDialog ambilWarnaDialog, int i6) {
                                                            accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putString("background_color", String.format("#%06X", Integer.valueOf(******** & i6))).commit();
                                                            accountFragment.this.f89968f4.getAdapter().m27492H(i2);
                                                        }

                                                        @Override // yuku.ambilwarna.AmbilWarnaDialog.OnAmbilWarnaListener
                                                        /* renamed from: b */
                                                        public void mo73015b(AmbilWarnaDialog ambilWarnaDialog) {
                                                        }
                                                    }).m79003v();
                                                }
                                            });
                                            return;
                                        }
                                        if (str4.equals(accountFragment.f89952f5)) {
                                            accountFragment.this.f89961A4 = i2;
                                            final SettingDetailCellViewHolder settingDetailCellViewHolder3 = (SettingDetailCellViewHolder) viewHolder;
                                            settingDetailCellViewHolder3.f90154I.setText("Line Height");
                                            settingDetailCellViewHolder3.f90155J.setText(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("line_height", "Default"));
                                            settingDetailCellViewHolder3.f90156K.setVisibility(0);
                                            settingDetailCellViewHolder3.f90156K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.56
                                                @Override // android.view.View.OnClickListener
                                                public void onClick(View view2) {
                                                    accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().remove("line_height").commit();
                                                    settingDetailCellViewHolder3.f90155J.setText("Default");
                                                }
                                            });
                                            materialRippleLayout = settingDetailCellViewHolder3.f90157L;
                                            viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.57
                                                @Override // android.view.View.OnClickListener
                                                public void onClick(View view2) {
                                                    ArrayList<? extends Parcelable> arrayList = new ArrayList<>();
                                                    String[] strArr = {"1.0", BuildConfig.f35224f, "1.4", "1.6", "1.8", "2.0", "2.2", "2.4"};
                                                    for (int i6 = 0; i6 < 8; i6++) {
                                                        arrayList.add(accountFragment.this.m72990m3(strArr[i6]));
                                                    }
                                                    String string3 = accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("line_height", "Default");
                                                    settingsList settingslist = new settingsList();
                                                    Bundle bundle2 = new Bundle();
                                                    bundle2.putString("type", "LineHeight");
                                                    bundle2.putParcelableArrayList("items", arrayList);
                                                    bundle2.putString("titleProperty", "Title");
                                                    bundle2.putString("selected", string3);
                                                    settingslist.m15342i2(bundle2);
                                                    settingslist.mo15218Z2(true);
                                                    settingslist.m15245A2(accountFragment.this, 0);
                                                    settingslist.mo15222e3(accountFragment.this.m15283M(), "SettingListTab");
                                                }
                                            };
                                        } else {
                                            if (!str4.equals(accountFragment.f89951e5)) {
                                                return;
                                            }
                                            final SettingCellSwitchViewHolder settingCellSwitchViewHolder22 = (SettingCellSwitchViewHolder) viewHolder;
                                            settingCellSwitchViewHolder22.f90148I.setText("Automatic QBank Backups");
                                            settingCellSwitchViewHolder22.f90149J.setChecked(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("qbankbackup", true));
                                            switchCompat = settingCellSwitchViewHolder22.f90149J;
                                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.58
                                                @Override // android.view.View.OnClickListener
                                                public void onClick(View view2) {
                                                    accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("qbankbackup", settingCellSwitchViewHolder22.f90149J.isChecked()).commit();
                                                }
                                            };
                                        }
                                    }
                                    switchCompat.setOnClickListener(onClickListener);
                                    return;
                                }
                                AccountTextViewHolder accountTextViewHolder8 = (AccountTextViewHolder) viewHolder;
                                accountTextViewHolder8.f90134I.setText("Delete Temp Files");
                                accountTextViewHolder8.f90134I.setTextColor(accountFragment.this.m15320b0().getColor(C5562R.color.white));
                                accountTextViewHolder8.f90135J.setBackgroundColor(accountFragment.this.m15320b0().getColor(C5562R.color.red));
                                materialRippleLayout = accountTextViewHolder8.f90135J;
                                viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.31
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view2) {
                                        Iterator<String> it2 = accountFragment.this.f89978p4.m71875o1().iterator();
                                        while (it2.hasNext()) {
                                            String next = it2.next();
                                            String[] list = new File(next).list(new FilenameFilter() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.31.1
                                                @Override // java.io.FilenameFilter
                                                public boolean accept(File file, String str5) {
                                                    return str5.endsWith(".zip") || str5.endsWith(".zipp") || str5.endsWith(".download") || str5.endsWith(".md5") || str5.endsWith(".1") || str5.endsWith(".2") || str5.endsWith(".3") || str5.endsWith(".4") || str5.endsWith(".5") || str5.endsWith(".6") || str5.endsWith(".7") || str5.endsWith(".8") || str5.endsWith(".9") || str5.endsWith(".10");
                                                }
                                            });
                                            if (list != null && list.length != 0) {
                                                for (String str5 : list) {
                                                    new File(next + "/" + str5).delete();
                                                }
                                            }
                                        }
                                        new File(accountFragment.this.f89978p4.m71797M1() + "/zlogs.db").delete();
                                        CompressHelper.m71767x2(accountFragment.this.f89980r4, "All temp files deleted.", 1);
                                        new File(accountFragment.this.f89978p4.m71778D0()).delete();
                                    }
                                };
                            }
                            view.setOnLongClickListener(onLongClickListener);
                            return;
                        }
                        accountFragment.this.f89988z4 = i2;
                        SettingDetailCellViewHolder settingDetailCellViewHolder4 = (SettingDetailCellViewHolder) viewHolder;
                        settingDetailCellViewHolder4.f90154I.setText("Download Server");
                        settingDetailCellViewHolder4.f90155J.setText(accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("DownloadServer", "dl").equals("dl") ? "Germany" : "Iran");
                        settingDetailCellViewHolder4.f90156K.setVisibility(8);
                        materialRippleLayout = settingDetailCellViewHolder4.f90157L;
                        viewOnClickListenerC530117 = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.23
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                ArrayList<? extends Parcelable> arrayList = new ArrayList<>();
                                String[] strArr = {"Germany", "Iran"};
                                for (int i6 = 0; i6 < 2; i6++) {
                                    arrayList.add(accountFragment.this.m72990m3(strArr[i6]));
                                }
                                String str5 = accountFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("DownloadServer", "dl").equals("dl") ? "Germany" : "Iran";
                                settingsList settingslist = new settingsList();
                                Bundle bundle2 = new Bundle();
                                bundle2.putString("type", "DL");
                                bundle2.putParcelableArrayList("items", arrayList);
                                bundle2.putString("titleProperty", "Title");
                                bundle2.putString("selected", str5);
                                settingslist.m15342i2(bundle2);
                                settingslist.mo15218Z2(true);
                                settingslist.m15245A2(accountFragment.this, 0);
                                settingslist.mo15222e3(accountFragment.this.m15283M(), "SettingListTab");
                            }
                        };
                    }
                }
                materialRippleLayout.setOnClickListener(viewOnClickListenerC530117);
                return;
                materialRippleLayout2 = accountTextViewHolder.f90135J;
                color2 = accountFragment.this.m15320b0().getColor(C5562R.color.white);
                materialRippleLayout2.setBackgroundColor(color2);
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            switch (i2) {
                case 0:
                    return new HeaderCellViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
                case 1:
                    AccountTextViewHolder accountTextViewHolder = accountFragment.this.new AccountTextViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_account_text, viewGroup, false));
                    accountTextViewHolder.f90135J.setRippleColor(accountFragment.this.m15320b0().getColor(C5562R.color.material_grey_300));
                    return accountTextViewHolder;
                case 2:
                    return new DatabaseButtonCellViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_button, viewGroup, false));
                case 3:
                    return accountFragment.this.new SimpleTextViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text, viewGroup, false));
                case 4:
                    return new SocialCellViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_social, viewGroup, false));
                case 5:
                    return accountFragment.this.new SettingCellViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_setting_text, viewGroup, false));
                case 6:
                    return accountFragment.this.new SettingCellSwitchViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_setting_text_switch, viewGroup, false));
                case 7:
                    return accountFragment.this.new SeparatorViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_separator, viewGroup, false));
                case 8:
                    return accountFragment.this.new SettingDetailCellViewHolder(LayoutInflater.from(accountFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_setting_text_detail, viewGroup, false));
                default:
                    return null;
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return m73007k0(accountFragment.this.f89970h4);
        }

        /* renamed from: d0 */
        public Bundle m73000d0(int i2, ArrayList<String> arrayList) {
            Iterator<String> it2 = arrayList.iterator();
            int i3 = 0;
            while (it2.hasNext()) {
                String next = it2.next();
                if (i2 == i3) {
                    Bundle bundle = new Bundle();
                    if (next.endsWith("Information")) {
                        next = next + " - " + accountFragment.this.f89983u4;
                    }
                    bundle.putString("Text", next);
                    bundle.putString("Type", "Header");
                    return bundle;
                }
                int iM73004h0 = i3 + m73004h0(next);
                if (i2 <= iM73004h0) {
                    int iM73004h02 = (i2 - (iM73004h0 - m73004h0(next))) - 1;
                    Bundle bundle2 = new Bundle();
                    bundle2.putString("Section", next);
                    bundle2.putInt("Index", iM73004h02);
                    bundle2.putString("Type", "Item");
                    return bundle2;
                }
                i3 = iM73004h0 + 1;
            }
            return null;
        }

        /* renamed from: e0 */
        public void m73001e0(final Runnable runnable, final Runnable runnable2) {
            final BeautifulProgressDialog beautifulProgressDialog = new BeautifulProgressDialog(accountFragment.this.m15366r(), BeautifulProgressDialog.f37991q, null);
            beautifulProgressDialog.m30023p("loading-1.json");
            beautifulProgressDialog.m30024q(true);
            new Thread(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.59
                @Override // java.lang.Runnable
                public void run() throws InterruptedException {
                    try {
                        Thread.sleep(500L);
                        accountFragment.this.f89967e4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.59.1
                            @Override // java.lang.Runnable
                            public void run() {
                                if (accountFragment.this.f89977o4) {
                                    return;
                                }
                                beautifulProgressDialog.m30028w();
                            }
                        });
                    } catch (InterruptedException unused) {
                    }
                }
            }).start();
            Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.60
                @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
                /* renamed from: a */
                public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                    try {
                        runnable.run();
                        observableEmitter.onNext("asdfadf");
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        accountFragment.this.f89977o4 = true;
                        beautifulProgressDialog.m30009a();
                    }
                }
            }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.61
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(String str) throws Throwable {
                    accountFragment.this.f89977o4 = true;
                    beautifulProgressDialog.m30009a();
                    try {
                        runnable2.run();
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                    }
                }
            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountAdapter.62
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Throwable th) throws Throwable {
                    accountFragment.this.f89977o4 = true;
                    beautifulProgressDialog.m30009a();
                    th.printStackTrace();
                    FirebaseCrashlytics.m48010d().m48016g(th);
                    runnable2.run();
                }
            });
        }

        /* renamed from: f0 */
        public void m73002f0() throws InterruptedException, IOException {
            HashSet<String> hashSetM71875o1 = accountFragment.this.f89978p4.m71875o1();
            new ArrayList();
            Iterator<String> it2 = hashSetM71875o1.iterator();
            while (it2.hasNext()) {
                m73006j0(new File(it2.next()));
            }
        }

        /* renamed from: g0 */
        public void m73003g0(ImageView imageView, String str) {
            RequestBuilder<Drawable> requestBuilderMo30129t;
            ArrayList arrayList = new ArrayList();
            arrayList.add("visualdx.png");
            arrayList.add("uptodate.png");
            arrayList.add("irandarou.png");
            if (arrayList.contains(str)) {
                requestBuilderMo30129t = Glide.m30040F(accountFragment.this).mo30122g(Uri.parse("file:///android_asset/" + str));
            } else {
                requestBuilderMo30129t = Glide.m30040F(accountFragment.this).mo30129t(accountFragment.this.f89978p4.m71790J() + "/Icons/" + str);
            }
            requestBuilderMo30129t.m30165B2(imageView);
        }

        /* renamed from: h0 */
        public int m73004h0(String str) {
            ArrayList arrayList;
            if (str.equals("Account Information")) {
                String strM71831a = accountFragment.this.f89978p4.m71831a();
                if (strM71831a.equals("All")) {
                    return 1;
                }
                return strM71831a.equals("Simple") ? 5 : 3;
            }
            if (str.equals("Help")) {
                return 2;
            }
            if (str.equals("Your Databases")) {
                if (accountFragment.this.f89974l4 == null) {
                    return 0;
                }
                if (accountFragment.this.f89974l4.contains(TtmlNode.f29738r0)) {
                    return 1;
                }
                if (accountFragment.this.f89973k4 == null) {
                    return 0;
                }
                arrayList = accountFragment.this.f89973k4;
            } else {
                if (str.equals("About Us")) {
                    return 8;
                }
                if (!str.equals("Settings")) {
                    return str.equals("") ? 5 : 0;
                }
                if (accountFragment.this.f89971i4.booleanValue()) {
                    return 0;
                }
                arrayList = accountFragment.this.f89986x4;
            }
            return arrayList.size();
        }

        /* renamed from: i0 */
        public void m73005i0(Intent intent) {
        }

        /* renamed from: j0 */
        public void m73006j0(File file) {
            try {
                if (!file.isDirectory()) {
                    if (file.canRead() && file.canWrite()) {
                        return;
                    }
                    file.setReadable(true, false);
                    file.setWritable(true, false);
                    return;
                }
                if (!file.canRead() || !file.canWrite() || !file.canExecute()) {
                    file.setReadable(true, false);
                    file.setWritable(true, false);
                    file.setExecutable(true, false);
                }
                File[] fileArrListFiles = file.listFiles();
                if (fileArrListFiles != null) {
                    for (File file2 : fileArrListFiles) {
                        m73006j0(file2);
                    }
                }
            } catch (Exception unused) {
            }
        }

        /* renamed from: k0 */
        public int m73007k0(ArrayList<String> arrayList) {
            int iM73004h0 = 0;
            if (arrayList == null) {
                return 0;
            }
            Iterator<String> it2 = arrayList.iterator();
            while (it2.hasNext()) {
                iM73004h0 = iM73004h0 + m73004h0(it2.next()) + 1;
            }
            return iM73004h0;
        }
    }

    public class AccountTextViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90134I;

        /* renamed from: J */
        private final MaterialRippleLayout f90135J;

        public AccountTextViewHolder(View view) {
            super(view);
            this.f90134I = (TextView) view.findViewById(C5562R.id.text);
            MaterialRippleLayout materialRippleLayout = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f90135J = materialRippleLayout;
            materialRippleLayout.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.AccountTextViewHolder.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                }
            });
        }
    }

    public static class DatabaseButtonCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90139I;

        /* renamed from: J */
        public TextView f90140J;

        /* renamed from: K */
        public ImageView f90141K;

        /* renamed from: L */
        public Button f90142L;

        public DatabaseButtonCellViewHolder(View view) {
            super(view);
            this.f90139I = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90140J = (TextView) view.findViewById(C5562R.id.database_subtitle);
            this.f90141K = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90142L = (Button) view.findViewById(C5562R.id.button);
        }
    }

    public static class DatabaseCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90143I;

        /* renamed from: J */
        public TextView f90144J;

        /* renamed from: K */
        public ImageView f90145K;

        public DatabaseCellViewHolder(View view) {
            super(view);
            this.f90143I = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90144J = (TextView) view.findViewById(C5562R.id.database_subtitle);
            this.f90145K = (ImageView) view.findViewById(C5562R.id.database_image);
        }
    }

    public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90146I;

        public HeaderCellViewHolder(View view) {
            super(view);
            this.f90146I = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    public class SeparatorViewHolder extends RecyclerView.ViewHolder {
        public SeparatorViewHolder(View view) {
            super(view);
        }
    }

    public class SettingCellSwitchViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90148I;

        /* renamed from: J */
        private final SwitchCompat f90149J;

        public SettingCellSwitchViewHolder(View view) {
            super(view);
            this.f90148I = (TextView) view.findViewById(C5562R.id.text);
            this.f90149J = (SwitchCompat) view.findViewById(C5562R.id.switch_view);
        }
    }

    public class SettingCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90151I;

        /* renamed from: J */
        private final MaterialRippleLayout f90152J;

        public SettingCellViewHolder(View view) {
            super(view);
            this.f90151I = (TextView) view.findViewById(C5562R.id.text);
            this.f90152J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class SettingDetailCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90154I;

        /* renamed from: J */
        private final TextView f90155J;

        /* renamed from: K */
        private final Button f90156K;

        /* renamed from: L */
        private final MaterialRippleLayout f90157L;

        public SettingDetailCellViewHolder(View view) {
            super(view);
            this.f90154I = (TextView) view.findViewById(C5562R.id.text);
            this.f90157L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f90155J = (TextView) view.findViewById(C5562R.id.detail_text);
            this.f90156K = (Button) view.findViewById(C5562R.id.reset_button);
        }
    }

    public class SimpleTextViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90159I;

        public SimpleTextViewHolder(View view) {
            super(view);
            this.f90159I = (TextView) view.findViewById(C5562R.id.text);
        }
    }

    public static class SocialCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90161I;

        /* renamed from: J */
        public ImageView f90162J;

        /* renamed from: K */
        public MaterialRippleLayout f90163K;

        public SocialCellViewHolder(View view) {
            super(view);
            this.f90161I = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90162J = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90163K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    /* renamed from: L2 */
    private void m72959L2() {
        ArrayList<String> arrayList = new ArrayList<>();
        this.f89986x4 = arrayList;
        arrayList.add(f89927G4);
        this.f89986x4.add(f89929I4);
        this.f89986x4.add(f89930J4);
        this.f89986x4.add(f89932L4);
        this.f89986x4.add(f89935O4);
        this.f89986x4.add(f89936P4);
        this.f89986x4.add(f89937Q4);
        this.f89986x4.add(f89939S4);
        this.f89986x4.add(f89941U4);
        this.f89986x4.add(f89953g5);
        this.f89986x4.add(f89942V4);
        this.f89986x4.add(f89943W4);
        this.f89986x4.add(f89944X4);
        this.f89986x4.add(f89946Z4);
        this.f89986x4.add(f89947a5);
        this.f89986x4.add(f89949c5);
        this.f89986x4.add(f89950d5);
        this.f89986x4.add(f89952f5);
        this.f89986x4.add(f89960n5);
        this.f89986x4.add(f89951e5);
        this.f89986x4.add(f89955i5);
        this.f89986x4.add(f89956j5);
        this.f89986x4.add(f89957k5);
        this.f89986x4.add(f89958l5);
        this.f89986x4.add(f89959m5);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: M2 */
    public void m72960M2() {
        int i2 = 0;
        try {
            i2 = m15366r().getPackageManager().getPackageInfo(m15366r().getPackageName(), 0).versionCode;
        } catch (Exception unused) {
        }
        this.f89978p4.m71874o0("ActivationCode|||||" + this.f89979q4.m73451m() + "|||||android-" + i2).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.2
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "|||||");
                if (strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                    accountFragment.this.f89978p4.m71894t2(strArrSplitByWholeSeparator[1]);
                    accountFragment.this.m72961N2();
                    accountFragment.this.m72987J2();
                    accountFragment.this.f89969g4.setRefreshing(false);
                    return;
                }
                if (strArrSplitByWholeSeparator[0].equals("0")) {
                    accountFragment.this.f89978p4.m71894t2(null);
                    iMDLogger.m73550f("system finish", "CheckActivationCode : " + str);
                    accountFragment.this.m15366r().finish();
                    System.exit(0);
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.3
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                try {
                    th.printStackTrace();
                    accountFragment.this.f89969g4.setRefreshing(false);
                } catch (Exception unused2) {
                }
            }
        }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.4
            @Override // io.reactivex.rxjava3.functions.Action
            public void run() throws Throwable {
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: N2 */
    public void m72961N2() {
        try {
            VBHelper vBHelper = this.f89979q4;
            String[] strArrSplit = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::");
            String[] strArrSplit2 = TextUtils.split(strArrSplit[3], ",");
            this.f89976n4 = strArrSplit[5];
            this.f89974l4 = new ArrayList<>(Arrays.asList(strArrSplit2));
            ArrayList<String> arrayList = new ArrayList<>();
            this.f89975m4 = new Bundle();
            Iterator<String> it2 = this.f89974l4.iterator();
            while (it2.hasNext()) {
                String next = it2.next();
                if (next.contains("$$$")) {
                    int i2 = 0;
                    String strReplace = StringUtils.splitByWholeSeparator(next, "$$$")[0];
                    if (strReplace.contains("-expired")) {
                        strReplace = strReplace.replace("-expired", "");
                        i2 = 1;
                    }
                    arrayList.add(strReplace);
                    Bundle bundle = new Bundle();
                    bundle.putString(DublinCoreProperties.f73850d, StringUtils.splitByWholeSeparator(next, "$$$")[1]);
                    bundle.putInt("expired", i2);
                    this.f89975m4.putBundle(strReplace, bundle);
                } else {
                    arrayList.add(next);
                }
            }
            this.f89974l4 = arrayList;
            if (strArrSplit.length > 9) {
                this.f89983u4 = strArrSplit[9];
            } else {
                this.f89983u4 = "";
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            this.f89976n4 = "0";
            this.f89974l4 = new ArrayList<>();
        }
    }

    /* renamed from: o3 */
    private String m72985o3() {
        return Formatter.formatIpAddress(((WifiManager) this.f89980r4.getApplicationContext().getSystemService("wifi")).getConnectionInfo().getIpAddress());
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: t3 */
    public void m72986t3() throws InterruptedException {
        File file = new File(this.f89978p4.m71797M1());
        if (this.f89985w4 == null || this.f89984v4.equals("Start File Web Server")) {
            if (this.f89985w4 == null) {
                this.f89985w4 = new FileWebServer(m15366r(), 1080, file);
            }
            try {
                this.f89985w4.mo58318L();
                this.f89984v4 = "http://" + m72985o3() + ":1080/";
            } catch (IOException e2) {
                e2.printStackTrace();
                this.f89984v4 = "Failed to start server";
            }
        } else {
            this.f89985w4.mo58321O();
            this.f89984v4 = "Start File Web Server";
        }
        this.f89968f4.getAdapter().m27491G();
    }

    /* renamed from: J2 */
    public void m72987J2() {
        m72988K2();
    }

    /* renamed from: K2 */
    public void m72988K2() {
        String str = this.f89978p4.m71816U1() + "/DBs.db";
        if (new File(str).exists()) {
            try {
                this.f89973k4 = this.f89978p4.m71825Y(str, "select id,Title,name,max(Version) as Version, IconName,folderSize, url, fileSize, md5,price from Dbs where name in ('" + TextUtils.join("','", this.f89974l4) + "') group by name order by title asc");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("LoadDBS", "Error in querying db. let's delete that");
                new File(str).delete();
            }
            iMDLogger.m73550f("LoadDBs", "Load DBS Completed");
            this.f89968f4.getAdapter().m27491G();
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: M0 */
    public void mo15284M0(Activity activity) {
        super.mo15284M0(activity);
        this.f89980r4 = activity;
    }

    /* renamed from: O2 */
    public void m72989O2(String str, final boolean z) {
        if (str.length() == 0) {
            this.f89978p4.m71898v2("You must enter a backup identifier");
            return;
        }
        final ProgressDialog progressDialogShow = ProgressDialog.show(m15366r(), "Restoring", "Please wait...", true);
        this.f89978p4.m71874o0("LoadFromFileZip|||||" + str).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.8
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str2) throws Throwable {
                String strM71738M0 = CompressHelper.m71738M0(str2);
                if (strM71738M0.length() == 0) {
                    progressDialogShow.dismiss();
                    accountFragment.this.f89978p4.m71898v2("Identifier not found");
                    return;
                }
                String[] strArrSplitByWholeSeparatorPreserveAllTokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(strM71738M0, "###");
                if (accountFragment.this.f89978p4.m71892t(strArrSplitByWholeSeparatorPreserveAllTokens[0]) != 5) {
                    progressDialogShow.dismiss();
                    CompressHelper.m71767x2(accountFragment.this.m15366r(), "This backup code is not created here.", 1);
                    return;
                }
                if (z) {
                    CompressHelper compressHelper = accountFragment.this.f89978p4;
                    compressHelper.m71881q(compressHelper.m71823X0(), "Delete from favorites");
                    accountFragment accountfragment = accountFragment.this;
                    accountfragment.f89978p4.m71881q(accountfragment.m72993q3(), "Delete from highlight");
                }
                CompressHelper compressHelper2 = accountFragment.this.f89978p4;
                compressHelper2.m71867m0(strArrSplitByWholeSeparatorPreserveAllTokens[0], compressHelper2.m71823X0(), "favorites", "dbName,dbTitle,dbAddress,dbDate,dbDocName", null);
                accountFragment accountfragment2 = accountFragment.this;
                accountfragment2.f89978p4.m71867m0(strArrSplitByWholeSeparatorPreserveAllTokens[1], accountfragment2.m72993q3(), "highlight", "dbName,dbTitle,dbAddress,dbDate,dbDocName,type,text,note,save", null);
                progressDialogShow.dismiss();
                accountFragment.this.f89978p4.m71898v2("Restore successful");
                accountFragment.this.m72994r3();
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.9
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                th.printStackTrace();
                FirebaseCrashlytics.m48010d().m48016g(th);
                progressDialogShow.dismiss();
                accountFragment.this.f89978p4.m71898v2("Error in contacting server");
            }
        });
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: Q0 */
    public void mo15207Q0(Bundle bundle) {
        super.mo15207Q0(bundle);
        LocalBroadcastManager.m16410b(m15307V1()).m16412c(this.f89964D4, new IntentFilter("referesh.account"));
        LocalBroadcastManager.m16410b(m15307V1()).m16412c(this.f89965E4, new IntentFilter("referesh.account.visible"));
        LocalBroadcastManager.m16410b(m15307V1()).m16412c(this.f89966F4, new IntentFilter("reloadaccountdownloads"));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menu.clear();
        menuInflater.inflate(C5562R.menu.menu_account, menu);
        try {
            m15366r().setTitle("Account & Settings");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        super.mo15301T0(menu, menuInflater);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View view = this.f89967e4;
        if (view != null) {
            return view;
        }
        this.f89971i4 = Boolean.FALSE;
        this.f89972j4 = "";
        this.f89963C4 = Typeface.createFromAsset(m15366r().getAssets(), "fonts/IRANSans(FaNum).ttf");
        this.f89984v4 = "Start File Web Server";
        m72959L2();
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_account, viewGroup, false);
        this.f89978p4 = new CompressHelper(m15366r());
        this.f89979q4 = new VBHelper(m15366r());
        this.f89967e4 = viewInflate;
        m72992p3();
        RecyclerView recyclerView = (RecyclerView) this.f89967e4.findViewById(C5562R.id.recycler_view);
        this.f89968f4 = recyclerView;
        recyclerView.m27459p(new CustomItemDecoration(m15366r()));
        this.f89968f4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        this.f89968f4.setAdapter(new AccountAdapter());
        SwipeRefreshLayout swipeRefreshLayout = (SwipeRefreshLayout) this.f89967e4.findViewById(C5562R.id.swipeRefreshLayout);
        this.f89969g4 = swipeRefreshLayout;
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() { // from class: net.imedicaldoctor.imd.Fragments.accountFragment.1
            @Override // androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
            /* renamed from: a */
            public void mo28299a() {
                accountFragment.this.m72960M2();
            }
        });
        ArrayList<String> arrayList = new ArrayList<>();
        this.f89970h4 = arrayList;
        arrayList.add("Account Information");
        this.f89970h4.add("Your Databases");
        this.f89970h4.add("Settings");
        this.f89970h4.add("About Us");
        ButtonFloatHelp buttonFloatHelp = (ButtonFloatHelp) this.f89967e4.findViewById(C5562R.id.help);
        this.f89981s4 = buttonFloatHelp;
        buttonFloatHelp.setVisibility(8);
        ButtonFloatHelpBadge buttonFloatHelpBadge = (ButtonFloatHelpBadge) this.f89967e4.findViewById(C5562R.id.helpBadge);
        this.f89982t4 = buttonFloatHelpBadge;
        buttonFloatHelpBadge.setVisibility(8);
        this.f89973k4 = new ArrayList<>();
        m72961N2();
        m72987J2();
        m15358o2(true);
        return viewInflate;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: V0 */
    public void mo15306V0() {
        LocalBroadcastManager.m16410b(m15307V1()).m16415f(this.f89964D4);
        LocalBroadcastManager.m16410b(m15307V1()).m16415f(this.f89965E4);
        LocalBroadcastManager.m16410b(m15307V1()).m16415f(this.f89966F4);
        super.mo15306V0();
    }

    /* renamed from: m3 */
    public Bundle m72990m3(String str) {
        Bundle bundle = new Bundle();
        bundle.putString("Title", str);
        return bundle;
    }

    /* renamed from: n3 */
    public String m72991n3() {
        Iterator<Bundle> it2 = CompressHelper.f87345t.iterator();
        int i2 = 0;
        String str = "";
        while (it2.hasNext()) {
            Bundle next = it2.next();
            i2++;
            str = str + i2 + ". " + ((((("Title : " + next.getString("Title") + " | ") + "Type : " + next.getString("Type") + " | ") + "Name : " + next.getString("Name") + " | ") + "Version : " + next.getString("Version") + " | ") + "Path : " + next.getString("Path")) + "\n\n";
        }
        return str.trim();
    }

    /* renamed from: p3 */
    public void m72992p3() {
        try {
            InputMethodManager inputMethodManager = (InputMethodManager) m15366r().getSystemService("input_method");
            if (m15366r().getCurrentFocus() != null) {
                inputMethodManager.hideSoftInputFromWindow(m15366r().getCurrentFocus().getWindowToken(), 0);
            }
            if (m15366r().getCurrentFocus() != null) {
                m15366r().getCurrentFocus().clearFocus();
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: q3 */
    public String m72993q3() throws SQLException {
        String str = this.f89978p4.m71797M1() + "/highlights.db";
        if (!new File(str).exists()) {
            SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("create virtual table highlight using fts4 (dbName, dbTitle, dbAddress, dbDate, dbDocName, type, text, note, save)");
        }
        return str;
    }

    /* renamed from: r3 */
    public void m72994r3() {
        iMDLogger.m73548d("sendFavorite", "Sending FavoriteChanged message");
        Intent intent = new Intent("net.imedicaldoctor.imd.favorite");
        intent.putExtra("Test", "Random data for test");
        LocalBroadcastManager.m16410b(m15366r()).m16413d(intent);
    }

    /* JADX WARN: Removed duplicated region for block: B:42:0x01b5 A[PHI: r0
      0x01b5: PHI (r0v11 java.lang.String) = (r0v9 java.lang.String), (r0v8 java.lang.String) binds: [B:47:0x01fd, B:41:0x01b3] A[DONT_GENERATE, DONT_INLINE]] */
    /* renamed from: s3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m72995s3(android.os.Bundle r6, java.lang.String r7) {
        /*
            Method dump skipped, instructions count: 513
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.accountFragment.m72995s3(android.os.Bundle, java.lang.String):void");
    }
}

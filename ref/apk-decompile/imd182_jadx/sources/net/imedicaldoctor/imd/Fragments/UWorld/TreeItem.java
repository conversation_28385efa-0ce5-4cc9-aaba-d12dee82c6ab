package net.imedicaldoctor.imd.Fragments.UWorld;

import java.util.ArrayList;
import java.util.List;

/* loaded from: classes3.dex */
public class TreeItem {

    /* renamed from: a */
    public String f89168a;

    /* renamed from: b */
    public boolean f89169b;

    /* renamed from: c */
    public boolean f89170c;

    /* renamed from: g */
    public int f89174g;

    /* renamed from: d */
    public boolean f89171d = false;

    /* renamed from: e */
    public boolean f89172e = false;

    /* renamed from: f */
    public boolean f89173f = false;

    /* renamed from: h */
    public List<TreeItem> f89175h = new ArrayList();

    public TreeItem(String str, boolean z, int i2, boolean z2) {
        this.f89168a = str;
        this.f89169b = z;
        this.f89170c = z2;
        this.f89174g = i2;
    }
}

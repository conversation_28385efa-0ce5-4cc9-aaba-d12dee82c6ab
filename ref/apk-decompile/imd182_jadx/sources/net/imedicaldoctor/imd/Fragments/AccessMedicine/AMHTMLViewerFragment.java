package net.imedicaldoctor.imd.Fragments.AccessMedicine;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.File;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class AMHTMLViewerFragment extends ViewerHelperFragment {
    /* renamed from: I4 */
    public void m71954I4() {
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return CompressHelper.m71724C(this.f89566D4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(C5562R.menu.menu_amviewer, menu);
        m72833q4(menu);
        mo71957e3(menu);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String str;
        String str2 = "";
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_table_viewer, viewGroup, false);
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return viewInflate;
        }
        try {
            new CompressHelper(m15366r());
            String str3 = this.f89563A4;
            if (str3 == null || str3.length() == 0) {
                if (this.f89567E4.equals(Annotation.f68285k3)) {
                    this.f89567E4 = CompressHelper.m71750e2(new File(CompressHelper.m71753g1(this.f89566D4, "temp.html")));
                }
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(this.f89567E4.replace("html-", ""), ",,,,,");
                this.f89568F4 = strArrSplitByWholeSeparator[0];
                String str4 = strArrSplitByWholeSeparator[1];
                if (this.f89566D4.getString("Type").equals("accessmedicine")) {
                    String strM72817d4 = m72817d4(m15366r(), "AMHeader.css");
                    String strM72817d42 = m72817d4(m15366r(), "AMFooter.css");
                    String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", this.f89568F4);
                    m72824l3("mgh.Popup.Dialog.js");
                    str2 = strReplace;
                    str = strM72817d42;
                } else {
                    str = "";
                }
                m72826m3();
                this.f89563A4 = str2 + str4 + str;
            }
            m72795O3(this.f89563A4, CompressHelper.m71753g1(this.f89566D4, "base"));
            m72836s4();
            if (m15387y() == null || !m15387y().containsKey("Dialog")) {
                m72831p4();
                mo72642f3(C5562R.menu.menu_htmlviewer);
            } else {
                if (this.f89568F4 == null) {
                    this.f89568F4 = "Unknown";
                }
                this.f89574L4.setTitle(this.f89568F4);
            }
            m15358o2(false);
            m72786G3();
            return viewInflate;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            m72779B4(e2);
            return viewInflate;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        this.f89569G4.m73433g("adjustTableLinks()");
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
        menu.removeItem(C5562R.id.action_menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: t3 */
    public void mo71958t3(String str) {
        m71954I4();
        super.mo71958t3(str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: v4 */
    public boolean mo71959v4() {
        return false;
    }

    /* JADX WARN: Removed duplicated region for block: B:49:0x0190 A[PHI: r8
      0x0190: PHI (r8v12 java.lang.String) = (r8v11 java.lang.String), (r8v14 java.lang.String) binds: [B:48:0x018e, B:39:0x0155] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:50:0x0194 A[PHI: r8 r9
      0x0194: PHI (r8v13 java.lang.String) = (r8v11 java.lang.String), (r8v14 java.lang.String) binds: [B:48:0x018e, B:39:0x0155] A[DONT_GENERATE, DONT_INLINE]
      0x0194: PHI (r9v3 android.os.Bundle) = (r9v2 android.os.Bundle), (r9v6 android.os.Bundle) binds: [B:48:0x018e, B:39:0x0155] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean mo71960y4(android.webkit.WebView r8, java.lang.String r9, java.lang.String r10, java.lang.String r11) {
        /*
            Method dump skipped, instructions count: 416
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.AccessMedicine.AMHTMLViewerFragment.mo71960y4(android.webkit.WebView, java.lang.String, java.lang.String, java.lang.String):boolean");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: z4 */
    public boolean mo71961z4() {
        if (m15387y() == null || !m15387y().containsKey("Dialog")) {
            return m15246B().getSharedPreferences("default_preferences", 0).getBoolean("NestedScroll", true);
        }
        return false;
    }
}

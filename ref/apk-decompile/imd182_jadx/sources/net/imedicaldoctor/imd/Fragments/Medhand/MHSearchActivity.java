package net.imedicaldoctor.imd.Fragments.Medhand;

import android.content.res.Resources;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class MHSearchActivity extends iMDActivity {

    public static class MHSearchFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private AsyncTask f88471A4;

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72463P2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_mhsearch_new, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72463P2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            appBarLayout.m35746D(false, false);
            appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Medhand.MHSearchActivity.MHSearchFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    relativeLayout.setVisibility(0);
                }
            }, 800L);
            this.f88792l4 = new StatusAdapter(m15366r(), "Search Book");
            this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "Text", "table") { // from class: net.imedicaldoctor.imd.Fragments.Medhand.MHSearchActivity.MHSearchFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
                /* renamed from: e0 */
                public void mo71953e0(Bundle bundle2, int i2) {
                    MHSearchFragment.this.m72468V2();
                    MHSearchFragment mHSearchFragment = MHSearchFragment.this;
                    mHSearchFragment.f88791k4.m71772A1(mHSearchFragment.f88788h4, bundle2.getString("URL"), null, null);
                }
            };
            ((Button) this.f88797q4.findViewById(C5562R.id.show_book)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Medhand.MHSearchActivity.MHSearchFragment.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    MHSearchFragment mHSearchFragment = MHSearchFragment.this;
                    mHSearchFragment.f88791k4.m71772A1(mHSearchFragment.f88788h4, "index.html", null, null);
                }
            });
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(true);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71819W(this.f88788h4, "select rowid as _id,* from search where text match '" + str + "*'", "fsearch.db");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new MHSearchFragment());
    }
}

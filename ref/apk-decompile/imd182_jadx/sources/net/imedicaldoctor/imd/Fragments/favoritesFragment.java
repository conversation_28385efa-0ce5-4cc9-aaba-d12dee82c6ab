package net.imedicaldoctor.imd.Fragments;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.BitmapFactory;
import android.graphics.drawable.NinePatchDrawable;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.core.view.ViewCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.exoplayer.audio.SilenceSkippingAudioProcessor;
import androidx.media3.extractor.AacUtil;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter;
import com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemViewHolder;
import com.h6ah4i.android.widget.advrecyclerview.draggable.ItemDraggableRange;
import com.h6ah4i.android.widget.advrecyclerview.draggable.RecyclerViewDragDropManager;
import com.h6ah4i.android.widget.advrecyclerview.utils.AbstractDraggableItemViewHolder;
import com.itextpdf.text.pdf.PdfBoolean;
import com.itextpdf.tool.xml.css.CSS;
import com.timehop.stickyheadersrecyclerview.ItemVisibilityAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersDecoration;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersTouchListener;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.HeaderCellViewHolder;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMDLogger;
import okio.BufferedSink;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class favoritesFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    private FavoriteAdapter f90619A4;

    /* renamed from: B4 */
    private ArrayList<Bundle> f90620B4;

    /* renamed from: C4 */
    private ArrayList<Bundle> f90621C4;

    /* renamed from: D4 */
    private boolean f90622D4;

    /* renamed from: E4 */
    private RecyclerView f90623E4;

    /* renamed from: F4 */
    private String f90624F4;

    /* renamed from: G4 */
    private boolean f90625G4;

    /* renamed from: H4 */
    private Activity f90626H4;

    /* renamed from: I4 */
    private boolean f90627I4;

    /* renamed from: J4 */
    private MenuItem f90628J4;

    /* renamed from: K4 */
    private MenuItem f90629K4;

    /* renamed from: L4 */
    private ArrayList<String> f90630L4;

    /* renamed from: M4 */
    private StickyRecyclerHeadersDecoration f90631M4;

    /* renamed from: N4 */
    StickyRecyclerHeadersTouchListener f90632N4;

    /* renamed from: O4 */
    private RecyclerViewDragDropManager f90633O4;

    /* renamed from: P4 */
    private boolean f90634P4;

    /* renamed from: Q4 */
    public CompressHelper f90635Q4;

    /* renamed from: R4 */
    private final BroadcastReceiver f90636R4 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.6
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            iMDLogger.m73548d("favorite", "received favorite changed message");
            favoritesFragment.this.m73300m3();
        }
    };

    public static class AddCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90659I;

        public AddCellViewHolder(View view) {
            super(view);
            this.f90659I = (TextView) view.findViewById(C5562R.id.text);
        }
    }

    public static class EditFavoriteCellViewHolder extends AbstractDraggableItemViewHolder implements DraggableItemViewHolder {

        /* renamed from: J */
        public TextView f90660J;

        /* renamed from: K */
        public ImageView f90661K;

        /* renamed from: L */
        public LinearLayout f90662L;

        /* renamed from: M */
        public ImageView f90663M;

        public EditFavoriteCellViewHolder(View view) {
            super(view);
            this.f90660J = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90661K = (ImageView) view.findViewById(C5562R.id.drag_indicator);
            this.f90662L = (LinearLayout) view.findViewById(C5562R.id.container_view);
            this.f90663M = (ImageView) view.findViewById(C5562R.id.remove_icon);
        }
    }

    public class EditFavoritesAdapter extends RecyclerView.Adapter implements DraggableItemAdapter {

        /* renamed from: d */
        HashMap<String, Integer> f90664d;

        /* renamed from: e */
        int f90665e;

        public EditFavoritesAdapter() {
            HashMap<String, Integer> map = new HashMap<>();
            this.f90664d = map;
            this.f90665e = 0;
            map.put("AddSection", Integer.valueOf(AacUtil.f27532f));
            this.f90664d.put("EditSectionas", Integer.valueOf(this.f90665e));
            this.f90665e++;
            if (favoritesFragment.this.f90621C4 == null) {
                return;
            }
            Iterator it2 = favoritesFragment.this.f90621C4.iterator();
            while (it2.hasNext()) {
                Bundle bundle = (Bundle) it2.next();
                this.f90664d.put("EditSection" + bundle.getString("title"), Integer.valueOf(this.f90665e));
                this.f90665e = this.f90665e + 1;
            }
            Iterator it3 = favoritesFragment.this.f90621C4.iterator();
            while (it3.hasNext()) {
                Bundle bundle2 = (Bundle) it3.next();
                this.f90664d.put("Section" + bundle2.getString("title"), Integer.valueOf(this.f90665e));
                this.f90665e = this.f90665e + 1;
                for (int i2 = 0; i2 < bundle2.getParcelableArrayList("items").size(); i2++) {
                    Bundle bundle3 = (Bundle) bundle2.getParcelableArrayList("items").get(i2);
                    this.f90664d.put("Database" + bundle3.getString("dbName") + bundle3.getString("dbAddress"), Integer.valueOf(this.f90665e));
                    this.f90665e = this.f90665e + 1;
                }
            }
            mo26852a0(true);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            StringBuilder sb;
            String str;
            String string;
            Integer num;
            if (i2 < favoritesFragment.this.f90621C4.size() + 1) {
                if (i2 == 0) {
                    num = this.f90664d.get("EditSectionas");
                    return num.intValue();
                }
                string = "EditSection" + ((Bundle) favoritesFragment.this.f90621C4.get(i2 - 1)).getString("title");
            } else {
                if (i2 == favoritesFragment.this.f90621C4.size() + 1) {
                    return SilenceSkippingAudioProcessor.f23265A;
                }
                int size = (i2 - favoritesFragment.this.f90621C4.size()) - 2;
                favoritesFragment favoritesfragment = favoritesFragment.this;
                Bundle bundleM73299l3 = favoritesfragment.m73299l3(size, favoritesfragment.f90621C4);
                if (bundleM73299l3.containsKey("Item")) {
                    bundleM73299l3 = bundleM73299l3.getBundle("Item");
                    sb = new StringBuilder();
                    sb.append("Database");
                    sb.append(bundleM73299l3.getString("dbName"));
                    str = "dbAddress";
                } else {
                    sb = new StringBuilder();
                    sb.append("Section");
                    str = "Title";
                }
                sb.append(bundleM73299l3.getString(str));
                string = sb.toString();
                if (!this.f90664d.containsKey(string)) {
                    return -1L;
                }
            }
            num = this.f90664d.get(string);
            return num.intValue();
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            if (i2 < favoritesFragment.this.f90621C4.size() + 1) {
                return i2 == 0 ? 1 : 2;
            }
            if (i2 == favoritesFragment.this.f90621C4.size() + 1) {
                return 3;
            }
            int size = (i2 - favoritesFragment.this.f90621C4.size()) - 2;
            favoritesFragment favoritesfragment = favoritesFragment.this;
            return favoritesfragment.m73299l3(size, favoritesfragment.f90621C4).containsKey("Title") ? 1 : 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            if (viewHolder.m27811F() == 3) {
                AddCellViewHolder addCellViewHolder = (AddCellViewHolder) viewHolder;
                addCellViewHolder.f90659I.setText("Add Section");
                addCellViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        final EditText editText = new EditText(favoritesFragment.this.m15366r());
                        editText.setTextColor(favoritesFragment.this.m15320b0().getColor(C5562R.color.black));
                        new AlertDialog.Builder(favoritesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Enter Section Name").setView(editText).mo1115y("Add", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.1.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                                String string = editText.getText().toString();
                                if (string.length() == 0) {
                                    CompressHelper.m71767x2(favoritesFragment.this.m15366r(), "You must enter a name for the section", 1);
                                    return;
                                }
                                if (EditFavoritesAdapter.this.f90664d.containsKey("EditSection" + string)) {
                                    CompressHelper.m71767x2(favoritesFragment.this.m15366r(), "You already have a section with this name", 1);
                                    return;
                                }
                                Bundle bundle = new Bundle();
                                bundle.putString("title", string);
                                bundle.putParcelableArrayList("items", new ArrayList<>());
                                favoritesFragment.this.f90621C4.add(bundle);
                                favoritesFragment.this.f90623E4.getAdapter().m27491G();
                                EditFavoritesAdapter editFavoritesAdapter = EditFavoritesAdapter.this;
                                editFavoritesAdapter.f90665e++;
                                editFavoritesAdapter.f90664d.put("EditSection" + string, Integer.valueOf(EditFavoritesAdapter.this.f90665e));
                            }
                        }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.1.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                            }
                        }).m1090I();
                    }
                });
                return;
            }
            if (i2 < favoritesFragment.this.f90621C4.size() + 1) {
                if (i2 == 0) {
                    ((HeaderCellViewHolder) viewHolder).f101460I.setText("Sections");
                    return;
                }
                final String string = ((Bundle) favoritesFragment.this.f90621C4.get(i2 - 1)).getString("title");
                EditHeaderCellViewHolder editHeaderCellViewHolder = (EditHeaderCellViewHolder) viewHolder;
                editHeaderCellViewHolder.f90688J.setText(string);
                editHeaderCellViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        final EditText editText = new EditText(favoritesFragment.this.m15366r());
                        editText.setText(string);
                        editText.setTextColor(favoritesFragment.this.m15320b0().getColor(C5562R.color.black));
                        new AlertDialog.Builder(favoritesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Edit Section Name").setView(editText).mo1115y("Edit", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.2.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                                String string2 = editText.getText().toString();
                                if (string2.length() == 0) {
                                    CompressHelper.m71767x2(favoritesFragment.this.m15366r(), "You must enter a name for the section", 1);
                                    return;
                                }
                                if (string2.equals(string)) {
                                    return;
                                }
                                if (EditFavoritesAdapter.this.f90664d.containsKey("EditSection" + string2)) {
                                    CompressHelper.m71767x2(favoritesFragment.this.m15366r(), "You already have a section with this name", 1);
                                    return;
                                }
                                Bundle bundle = (Bundle) favoritesFragment.this.f90621C4.get(i2 - 1);
                                bundle.remove("title");
                                bundle.putString("title", string2);
                                favoritesFragment.this.f90623E4.getAdapter().m27491G();
                                EditFavoritesAdapter editFavoritesAdapter = EditFavoritesAdapter.this;
                                editFavoritesAdapter.f90665e++;
                                editFavoritesAdapter.f90664d.put("EditSection" + string2, Integer.valueOf(EditFavoritesAdapter.this.f90665e));
                            }
                        }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.2.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                            }
                        }).m1090I();
                    }
                });
                editHeaderCellViewHolder.f90691M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        ArrayList parcelableArrayList = ((Bundle) favoritesFragment.this.f90621C4.get(i2 - 1)).getParcelableArrayList("items");
                        if (parcelableArrayList != null && parcelableArrayList.size() > 0) {
                            CompressHelper.m71767x2(favoritesFragment.this.m15366r(), "First delete favorite items inside this section", 1);
                        } else {
                            favoritesFragment.this.f90621C4.remove(i2 - 1);
                            favoritesFragment.this.f90623E4.getAdapter().m27491G();
                        }
                    }
                });
                return;
            }
            int size = (i2 - favoritesFragment.this.f90621C4.size()) - 2;
            favoritesFragment favoritesfragment = favoritesFragment.this;
            final Bundle bundleM73299l3 = favoritesfragment.m73299l3(size, favoritesfragment.f90621C4);
            if (!bundleM73299l3.containsKey("Item")) {
                ((HeaderCellViewHolder) viewHolder).f101460I.setText(bundleM73299l3.getString("Title"));
                return;
            }
            Bundle bundle = bundleM73299l3.getBundle("Item");
            final EditFavoriteCellViewHolder editFavoriteCellViewHolder = (EditFavoriteCellViewHolder) viewHolder;
            editFavoriteCellViewHolder.f90660J.setText(bundle.getString("dbDocName"));
            editFavoriteCellViewHolder.f90663M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    int i3 = bundleM73299l3.getInt("Section");
                    int i4 = bundleM73299l3.getInt("Row");
                    Bundle bundle2 = bundleM73299l3.getBundle("Item");
                    ((Bundle) favoritesFragment.this.f90621C4.get(i3)).getParcelableArrayList("items").remove(i4);
                    CompressHelper compressHelper = favoritesFragment.this.f90635Q4;
                    compressHelper.m71881q(compressHelper.m71823X0(), "Delete from favorites where _id=" + bundle2.getString("_id"));
                    favoritesFragment.this.f90623E4.getAdapter().m27491G();
                }
            });
            final String string2 = bundle.getString("dbDocName");
            editFavoriteCellViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.5
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    final EditText editText = new EditText(favoritesFragment.this.m15366r());
                    editText.setText(string2);
                    editText.setTextColor(favoritesFragment.this.m15320b0().getColor(C5562R.color.black));
                    new AlertDialog.Builder(favoritesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Edit Document Name").setView(editText).mo1115y("Edit", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.5.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i3) {
                            String string3 = editText.getText().toString();
                            if (string3.length() == 0) {
                                CompressHelper.m71767x2(favoritesFragment.this.m15366r(), "You must enter a name for the document", 1);
                                return;
                            }
                            if (string3.equals(string2)) {
                                return;
                            }
                            Bundle bundle2 = (Bundle) ((Bundle) favoritesFragment.this.f90621C4.get(bundleM73299l3.getInt("Section"))).getParcelableArrayList("items").get(bundleM73299l3.getInt("Row"));
                            bundle2.remove("dbDocName");
                            bundle2.putString("dbDocName", string3);
                            editFavoriteCellViewHolder.f90660J.setText(string3);
                            EditFavoritesAdapter editFavoritesAdapter = EditFavoritesAdapter.this;
                            editFavoritesAdapter.f90665e++;
                            editFavoritesAdapter.f90664d.put("Database" + bundle2.getString("dbName") + bundle2.getString("dbAddress"), Integer.valueOf(EditFavoritesAdapter.this.f90665e));
                        }
                    }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.EditFavoritesAdapter.5.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i3) {
                        }
                    }).m1090I();
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new EditFavoriteCellViewHolder(LayoutInflater.from(favoritesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_favorite_edit, viewGroup, false));
            }
            if (i2 == 1) {
                return new HeaderCellViewHolder(LayoutInflater.from(favoritesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 2) {
                return new EditHeaderCellViewHolder(LayoutInflater.from(favoritesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header_edit, viewGroup, false));
            }
            if (i2 == 3) {
                return new AddCellViewHolder(LayoutInflater.from(favoritesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_add, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            if (favoritesFragment.this.f90621C4 == null || favoritesFragment.this.f90621C4.size() == 0) {
                return 0;
            }
            favoritesFragment favoritesfragment = favoritesFragment.this;
            return favoritesfragment.m73295J3(favoritesfragment.f90621C4) + favoritesFragment.this.f90621C4.size() + 2;
        }

        @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter
        /* renamed from: h */
        public void mo50466h(int i2, int i3) {
            if (i2 > 0 && i2 < favoritesFragment.this.f90621C4.size() + 1) {
                int i4 = i2 - 1;
                Bundle bundle = (Bundle) favoritesFragment.this.f90621C4.get(i4);
                favoritesFragment.this.f90621C4.remove(i4);
                favoritesFragment.this.f90621C4.add(i3 - 1, bundle);
                return;
            }
            int size = (i2 - favoritesFragment.this.f90621C4.size()) - 2;
            int size2 = (i3 - favoritesFragment.this.f90621C4.size()) - 2;
            favoritesFragment favoritesfragment = favoritesFragment.this;
            Bundle bundleM73299l3 = favoritesfragment.m73299l3(size, favoritesfragment.f90621C4);
            int i5 = bundleM73299l3.getInt("Row");
            int i6 = bundleM73299l3.getInt("Section");
            Bundle bundle2 = (Bundle) ((Bundle) favoritesFragment.this.f90621C4.get(i6)).getParcelableArrayList("items").get(i5);
            favoritesFragment favoritesfragment2 = favoritesFragment.this;
            Bundle bundleM73299l32 = favoritesfragment2.m73299l3(size2, favoritesfragment2.f90621C4);
            int i7 = bundleM73299l32.getInt("Row");
            int i8 = bundleM73299l32.getInt("Section");
            iMDLogger.m73550f("Staring Drag", "Section " + i6 + " , Row : " + i5 + ", Section2:" + i8 + ", row2:" + i7);
            ((Bundle) favoritesFragment.this.f90621C4.get(i6)).getParcelableArrayList("items").remove(i5);
            if (bundleM73299l32.containsKey("Title")) {
                int i9 = bundleM73299l32.getInt("Row2");
                int i10 = bundleM73299l32.getInt("Section2");
                iMDLogger.m73550f("Staring Drag", "Section22:" + i10 + ", row22:" + i9);
                if (i6 >= i8) {
                    if (((Bundle) favoritesFragment.this.f90621C4.get(i10)).getParcelableArrayList("items").size() == 0) {
                        i9 = 0;
                    }
                    ((Bundle) favoritesFragment.this.f90621C4.get(i10)).getParcelableArrayList("items").add(i9, bundle2);
                    return;
                }
            }
            ((Bundle) favoritesFragment.this.f90621C4.get(i8)).getParcelableArrayList("items").add(i7, bundle2);
        }

        @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter
        /* renamed from: k */
        public boolean mo50467k(RecyclerView.ViewHolder viewHolder, int i2, int i3, int i4) {
            ImageView imageView;
            LinearLayout linearLayout;
            if (viewHolder.m27811F() == 1 || viewHolder.m27811F() == 3) {
                return false;
            }
            if (viewHolder.m27811F() == 2) {
                EditHeaderCellViewHolder editHeaderCellViewHolder = (EditHeaderCellViewHolder) viewHolder;
                imageView = editHeaderCellViewHolder.f90689K;
                linearLayout = editHeaderCellViewHolder.f90690L;
            } else {
                EditFavoriteCellViewHolder editFavoriteCellViewHolder = (EditFavoriteCellViewHolder) viewHolder;
                imageView = editFavoriteCellViewHolder.f90661K;
                linearLayout = editFavoriteCellViewHolder.f90662L;
            }
            int left = linearLayout.getLeft() + ((int) (ViewCompat.m9034B0(linearLayout) + 0.5f));
            linearLayout.getTop();
            ViewCompat.m9038C0(linearLayout);
            boolean zM72761a = ViewUtils.m72761a(imageView, i3 - left, 50);
            iMDLogger.m73550f("CanDrag", zM72761a ? PdfBoolean.f69890l3 : "false");
            return zM72761a;
        }

        @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter
        /* renamed from: w */
        public ItemDraggableRange mo50468w(RecyclerView.ViewHolder viewHolder, int i2) {
            if (i2 <= 0 || i2 >= favoritesFragment.this.f90621C4.size() + 1) {
                return new ItemDraggableRange(favoritesFragment.this.f90621C4.size() + 3, mo26171b() - 1);
            }
            iMDLogger.m73554j("RequestingRange", "Range requested");
            return new ItemDraggableRange(1, favoritesFragment.this.f90621C4.size());
        }
    }

    public static class EditHeaderCellViewHolder extends AbstractDraggableItemViewHolder implements DraggableItemViewHolder {

        /* renamed from: J */
        public TextView f90688J;

        /* renamed from: K */
        public ImageView f90689K;

        /* renamed from: L */
        public LinearLayout f90690L;

        /* renamed from: M */
        public ImageView f90691M;

        public EditHeaderCellViewHolder(View view) {
            super(view);
            this.f90688J = (TextView) view.findViewById(C5562R.id.header_text);
            this.f90689K = (ImageView) view.findViewById(C5562R.id.drag_indicator);
            this.f90690L = (LinearLayout) view.findViewById(C5562R.id.container_view);
            this.f90691M = (ImageView) view.findViewById(C5562R.id.remove_icon);
        }
    }

    public class EmptyViewHolder extends RecyclerView.ViewHolder {
        public EmptyViewHolder(View view) {
            super(view);
        }
    }

    public class FavoriteAdapter extends RecyclerView.Adapter implements StickyRecyclerHeadersAdapter {
        public FavoriteAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            favoritesFragment favoritesfragment;
            ArrayList<Bundle> arrayList;
            if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90621C4;
            } else {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90620B4;
            }
            return favoritesfragment.m73298k3(i2, arrayList).containsKey("Item") ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            favoritesFragment favoritesfragment;
            ArrayList<Bundle> arrayList;
            if (viewHolder.m27811F() == 1) {
                return;
            }
            FavoriteItemViewHolder favoriteItemViewHolder = (FavoriteItemViewHolder) viewHolder;
            if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90621C4;
            } else {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90620B4;
            }
            Bundle bundle = favoritesfragment.m73298k3(i2, arrayList).getBundle("Item");
            favoriteItemViewHolder.f90698I.setText(bundle.getString("dbDocName"));
            final String string = bundle.getString("_id");
            if (favoritesFragment.this.f90627I4) {
                favoriteItemViewHolder.f90699J.setVisibility(0);
                favoriteItemViewHolder.f90699J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.FavoriteAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        favoritesFragment.this.m73288C3(string);
                        favoritesFragment.this.m73300m3();
                    }
                });
                favoriteItemViewHolder.f90700K.setRippleOverlay(false);
            } else {
                favoriteItemViewHolder.f90700K.setRippleOverlay(true);
                favoriteItemViewHolder.f90699J.setVisibility(8);
            }
            favoriteItemViewHolder.f90700K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.FavoriteAdapter.2
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    favoritesFragment favoritesfragment2;
                    int i3;
                    ArrayList<Bundle> arrayList2;
                    CompressHelper compressHelper;
                    if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                        favoritesfragment2 = favoritesFragment.this;
                        i3 = i2;
                        arrayList2 = favoritesfragment2.f90621C4;
                    } else {
                        favoritesfragment2 = favoritesFragment.this;
                        i3 = i2;
                        arrayList2 = favoritesfragment2.f90620B4;
                    }
                    Bundle bundle2 = favoritesfragment2.m73298k3(i3, arrayList2).getBundle("Item");
                    Bundle bundleM71855i1 = favoritesFragment.this.f90635Q4.m71855i1(bundle2.getString("dbName"));
                    if (bundleM71855i1 == null) {
                        CompressHelper.m71767x2(favoritesFragment.this.m73289D3(), "You don't have this database installed", 1);
                        return;
                    }
                    String string2 = bundleM71855i1.getString("Type");
                    String string3 = bundle2.getString("dbAddress");
                    favoritesFragment.this.f88788h4 = bundleM71855i1;
                    if (string2.equals("uworld")) {
                        compressHelper = favoritesFragment.this.f90635Q4;
                        string3 = "question-" + string3;
                    } else {
                        compressHelper = favoritesFragment.this.f90635Q4;
                    }
                    compressHelper.m71772A1(bundleM71855i1, string3, null, null);
                    favoritesFragment.this.m72468V2();
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 1) {
                return favoritesFragment.this.new EmptyViewHolder(LayoutInflater.from(favoritesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_header_keeper, viewGroup, false));
            }
            View viewInflate = LayoutInflater.from(favoritesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_favorite, viewGroup, false);
            favoritesFragment favoritesfragment = favoritesFragment.this;
            return favoritesfragment.new FavoriteItemViewHolder(favoritesfragment.m15366r(), viewInflate);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            favoritesFragment favoritesfragment;
            ArrayList<Bundle> arrayList;
            if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90621C4;
            } else {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90620B4;
            }
            return favoritesfragment.m73294I3(arrayList);
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: o */
        public RecyclerView.ViewHolder mo58201o(ViewGroup viewGroup) {
            View viewInflate = LayoutInflater.from(favoritesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_search_header, viewGroup, false);
            favoritesFragment favoritesfragment = favoritesFragment.this;
            return favoritesfragment.new SearchHeaderViewHolder(favoritesfragment.m15366r(), viewInflate);
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: p */
        public void mo58202p(RecyclerView.ViewHolder viewHolder, int i2) throws IOException {
            favoritesFragment favoritesfragment;
            ArrayList<Bundle> arrayList;
            SearchHeaderViewHolder searchHeaderViewHolder = (SearchHeaderViewHolder) viewHolder;
            if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90621C4;
            } else {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90620B4;
            }
            Bundle bundleM73298k3 = favoritesfragment.m73298k3(i2, arrayList);
            viewHolder.f33076a.setTag(Integer.valueOf(i2));
            Bundle bundleM71859j1 = favoritesFragment.this.f90635Q4.m71859j1(bundleM73298k3.getString("Database"));
            if (bundleM71859j1 == null) {
                searchHeaderViewHolder.f90712I.setVisibility(8);
                searchHeaderViewHolder.f90713J.setText(bundleM73298k3.getString("Database"));
                return;
            }
            searchHeaderViewHolder.f90712I.setVisibility(0);
            searchHeaderViewHolder.f90713J.setText(bundleM71859j1.getString("Title"));
            String strM71724C = CompressHelper.m71724C(bundleM71859j1);
            if (!strM71724C.contains("file:///android_asset/")) {
                searchHeaderViewHolder.f90712I.setImageURI(Uri.parse(strM71724C));
                return;
            }
            try {
                InputStream inputStreamOpen = favoritesFragment.this.m15366r().getAssets().open(strM71724C.replace("file:///android_asset/", ""));
                searchHeaderViewHolder.f90712I.setImageBitmap(BitmapFactory.decodeStream(inputStreamOpen));
                inputStreamOpen.close();
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
            }
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: r */
        public long mo58203r(int i2) {
            favoritesFragment favoritesfragment;
            ArrayList<Bundle> arrayList;
            if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90621C4;
            } else {
                favoritesfragment = favoritesFragment.this;
                arrayList = favoritesfragment.f90620B4;
            }
            return favoritesfragment.m73297j3(i2, arrayList);
        }
    }

    public class FavoriteItemViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90698I;

        /* renamed from: J */
        public ImageView f90699J;

        /* renamed from: K */
        public MaterialRippleLayout f90700K;

        public FavoriteItemViewHolder(Context context, View view) {
            super(view);
            this.f90698I = (TextView) view.findViewById(C5562R.id.text);
            this.f90700K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f90699J = (ImageView) view.findViewById(C5562R.id.remove_icon);
        }
    }

    public class HeaderViewHolder {

        /* renamed from: a */
        public final TextView f90702a;

        /* renamed from: b */
        public final ImageView f90703b;

        /* renamed from: c */
        public final ImageView f90704c;

        public HeaderViewHolder(View view) {
            this.f90702a = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90703b = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90704c = (ImageView) view.findViewById(C5562R.id.icon);
        }
    }

    public class ListViewItemContentSearchPlaceHolder {

        /* renamed from: a */
        public TextView f90706a;

        /* renamed from: b */
        public TextView f90707b;

        public ListViewItemContentSearchPlaceHolder(View view) {
            this.f90706a = (TextView) view.findViewById(C5562R.id.title_text);
            this.f90707b = (TextView) view.findViewById(C5562R.id.subtitle_text);
        }
    }

    public class ListViewItemFavoritePlaceHolder {

        /* renamed from: a */
        public TextView f90709a;

        /* renamed from: b */
        public ImageView f90710b;

        public ListViewItemFavoritePlaceHolder(View view) {
            this.f90709a = (TextView) view.findViewById(C5562R.id.text);
            this.f90710b = (ImageView) view.findViewById(C5562R.id.remove_icon);
        }
    }

    public class SearchHeaderViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public ImageView f90712I;

        /* renamed from: J */
        public TextView f90713J;

        /* renamed from: K */
        public ImageView f90714K;

        public SearchHeaderViewHolder(Context context, View view) {
            super(view);
            this.f90713J = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90712I = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90714K = (ImageView) view.findViewById(C5562R.id.icon);
        }
    }

    /* renamed from: B3 */
    public void m73287B3() {
        if (this.f90634P4) {
            return;
        }
        this.f90623E4.m27459p(this.f90631M4);
        this.f90634P4 = true;
    }

    /* renamed from: C3 */
    public void m73288C3(String str) {
        CompressHelper compressHelper = this.f90635Q4;
        compressHelper.m71881q(compressHelper.m71823X0(), "Delete from favorites where _id=" + str);
    }

    /* renamed from: D3 */
    public Activity m73289D3() {
        return this.f90626H4;
    }

    /* renamed from: E3 */
    public ArrayList<Bundle> m73290E3() throws JSONException {
        String str = this.f90635Q4.m71797M1() + "/favorites.json";
        ArrayList<Bundle> arrayListM73296i3 = m73296i3();
        if (arrayListM73296i3 == null) {
            return null;
        }
        if (new File(str).exists()) {
            try {
                JSONArray jSONArray = new JSONArray(new String(this.f90635Q4.m71840c2(str)));
                ArrayList<Bundle> arrayList = new ArrayList<>();
                int i2 = 0;
                while (i2 < jSONArray.length()) {
                    JSONObject jSONObject = jSONArray.getJSONObject(i2);
                    JSONArray jSONArray2 = jSONObject.getJSONArray("items");
                    ArrayList<? extends Parcelable> arrayList2 = new ArrayList<>();
                    int i3 = 0;
                    while (i3 < jSONArray2.length()) {
                        final JSONObject jSONObject2 = jSONArray2.getJSONObject(i3);
                        ArrayList arrayList3 = new ArrayList(Collections2.m42365d(arrayListM73296i3, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.4
                            @Override // com.google.common.base.Predicate
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public boolean apply(Bundle bundle) {
                                try {
                                    if (bundle.getString("dbName").equals(jSONObject2.getString("Database"))) {
                                        return bundle.getString("dbAddress").equals(jSONObject2.getString("Address"));
                                    }
                                    return false;
                                } catch (Exception e2) {
                                    FirebaseCrashlytics.m48010d().m48016g(e2);
                                    iMDLogger.m73550f("Error in filtering", e2.getLocalizedMessage());
                                    return false;
                                }
                            }
                        }));
                        JSONArray jSONArray3 = jSONArray;
                        if (arrayList3.size() == 1) {
                            Bundle bundle = (Bundle) arrayList3.get(0);
                            arrayListM73296i3.remove(bundle);
                            bundle.remove("dbDocName");
                            bundle.putString("dbDocName", jSONObject2.getString("Title"));
                            arrayList2.add(bundle);
                        }
                        i3++;
                        jSONArray = jSONArray3;
                    }
                    JSONArray jSONArray4 = jSONArray;
                    Bundle bundle2 = new Bundle();
                    bundle2.putString("title", jSONObject.getString("title"));
                    bundle2.putParcelableArrayList("items", arrayList2);
                    arrayList.add(bundle2);
                    i2++;
                    jSONArray = jSONArray4;
                }
                if (arrayListM73296i3.size() > 0) {
                    ArrayList<Bundle> arrayListM71887r2 = this.f90635Q4.m71887r2(arrayListM73296i3, "dbTitle");
                    for (int i4 = 0; i4 < arrayListM71887r2.size(); i4++) {
                        final Bundle bundle3 = arrayListM71887r2.get(i4);
                        ArrayList arrayList4 = new ArrayList(Collections2.m42365d(arrayList, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.5
                            @Override // com.google.common.base.Predicate
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public boolean apply(Bundle bundle4) {
                                return bundle4.getString("title").equals(bundle3.getString("title"));
                            }
                        }));
                        if (arrayList4.size() == 0) {
                            Bundle bundle4 = new Bundle();
                            bundle4.putString("title", bundle3.getString("title"));
                            bundle4.putParcelableArrayList("items", bundle3.getParcelableArrayList("items"));
                            arrayList.add(bundle4);
                        } else {
                            Bundle bundle5 = (Bundle) arrayList4.get(0);
                            ArrayList<? extends Parcelable> parcelableArrayList = bundle5.getParcelableArrayList("items");
                            parcelableArrayList.addAll(bundle3.getParcelableArrayList("items"));
                            int iIndexOf = arrayList.indexOf(bundle5);
                            Bundle bundle6 = new Bundle();
                            bundle6.putString("title", bundle5.getString("title"));
                            bundle6.putParcelableArrayList("items", parcelableArrayList);
                            arrayList.remove(iIndexOf);
                            arrayList.add(iIndexOf, bundle6);
                        }
                    }
                }
                return arrayList;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("Error in Reading Json", e2.getLocalizedMessage());
            }
        }
        return this.f90635Q4.m71887r2(arrayListM73296i3, "dbTitle");
    }

    /* renamed from: F3 */
    public void m73291F3() {
        if (this.f90634P4) {
            this.f90623E4.m27380A1(this.f90631M4);
            this.f90634P4 = false;
        }
    }

    /* renamed from: G3 */
    public void m73292G3(ArrayList<Bundle> arrayList) throws JSONException, IOException {
        ArrayList<Bundle> arrayList2 = arrayList;
        if (arrayList2 == null) {
            return;
        }
        String str = this.f90635Q4.m71797M1() + "/favorites.json";
        ArrayList arrayList3 = new ArrayList();
        JSONArray jSONArray = new JSONArray();
        int i2 = 0;
        while (i2 < arrayList.size()) {
            Bundle bundle = arrayList2.get(i2);
            ArrayList parcelableArrayList = bundle.getParcelableArrayList("items");
            ArrayList<? extends Parcelable> arrayList4 = new ArrayList<>();
            JSONArray jSONArray2 = new JSONArray();
            int i3 = 0;
            while (i3 < parcelableArrayList.size()) {
                Bundle bundle2 = (Bundle) parcelableArrayList.get(i3);
                Bundle bundle3 = new Bundle();
                bundle3.putString("Database", bundle2.getString("dbName"));
                ArrayList arrayList5 = parcelableArrayList;
                bundle3.putString("Address", bundle2.getString("dbAddress"));
                String str2 = str;
                int i4 = i2;
                bundle3.putString("Title", bundle2.getString("dbDocName"));
                arrayList4.add(bundle3);
                try {
                    JSONObject jSONObject = new JSONObject();
                    jSONObject.put("Database", bundle2.getString("dbName"));
                    jSONObject.put("Address", bundle2.getString("dbAddress"));
                    jSONObject.put("Title", bundle2.getString("dbDocName"));
                    jSONArray2.put(jSONObject);
                } catch (Exception unused) {
                }
                i3++;
                parcelableArrayList = arrayList5;
                str = str2;
                i2 = i4;
            }
            String str3 = str;
            int i5 = i2;
            Bundle bundle4 = new Bundle();
            bundle4.putString("title", bundle.getString("title"));
            bundle4.putParcelableArrayList("items", arrayList4);
            arrayList3.add(bundle4);
            try {
                JSONObject jSONObject2 = new JSONObject();
                jSONObject2.put("title", bundle.getString("title"));
                jSONObject2.put("items", jSONArray2);
                jSONArray.put(jSONObject2);
            } catch (Exception unused2) {
            }
            i2 = i5 + 1;
            arrayList2 = arrayList;
            str = str3;
        }
        String str4 = str;
        String string = jSONArray.toString();
        if (new File(str4).exists()) {
            new File(str4).delete();
        }
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(new File(str4));
            try {
                BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75780p(fileOutputStream));
                try {
                    bufferedSinkM75768d.mo75454W0(string);
                    bufferedSinkM75768d.flush();
                    bufferedSinkM75768d.close();
                    fileOutputStream.close();
                } finally {
                }
            } finally {
            }
        } catch (IOException e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in writing json", e2.getLocalizedMessage());
        }
    }

    /* renamed from: H3 */
    public void m73293H3() {
        ArrayList<Bundle> arrayList = this.f90621C4;
        if (arrayList == null || arrayList.size() == 0) {
            mo72473f3("No Favorites");
        } else {
            mo72472e3();
        }
    }

    /* renamed from: I3 */
    public int m73294I3(ArrayList<Bundle> arrayList) {
        int size = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (next != null && next.getParcelableArrayList("items") != null) {
                size += this.f90630L4.contains(next.getString("title")) ? 1 : next.getParcelableArrayList("items").size();
            }
        }
        return size;
    }

    /* renamed from: J3 */
    public int m73295J3(ArrayList<Bundle> arrayList) {
        int size = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            size = size + it2.next().getParcelableArrayList("items").size() + 1;
        }
        return size;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: M0 */
    public void mo15284M0(Activity activity) {
        iMDLogger.m73554j("favoritesFragment", "OnAttach");
        super.mo15284M0(activity);
        this.f90626H4 = activity;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: Q0 */
    public void mo15207Q0(Bundle bundle) {
        super.mo15207Q0(bundle);
        LocalBroadcastManager.m16410b(m73289D3()).m16412c(this.f90636R4, new IntentFilter("net.imedicaldoctor.imd.favorite"));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        try {
            m15366r().setTitle("");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        menuInflater.inflate(C5562R.menu.menu_favorites, menu);
        final MenuItem menuItemFindItem = menu.findItem(C5562R.id.action_edit);
        final MenuItem menuItemFindItem2 = menu.findItem(C5562R.id.action_done);
        this.f90628J4 = menuItemFindItem;
        this.f90629K4 = menuItemFindItem2;
        menuItemFindItem.setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.7
            @Override // android.view.MenuItem.OnMenuItemClickListener
            public boolean onMenuItemClick(MenuItem menuItem) {
                favoritesFragment.this.f88799s4.clearFocus();
                if (favoritesFragment.this.f90620B4 != null && favoritesFragment.this.f90620B4.size() != 0) {
                    favoritesFragment.this.f88799s4.m2508k0("", false);
                    favoritesFragment.this.f88799s4.clearFocus();
                    favoritesFragment.this.f90620B4 = null;
                    favoritesFragment.this.m73293H3();
                    favoritesFragment.this.m72468V2();
                }
                menuItemFindItem.setVisible(false);
                menuItemFindItem2.setVisible(true);
                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(favoritesFragment.this.m15366r());
                favoritesFragment.this.f90633O4 = new RecyclerViewDragDropManager();
                favoritesFragment.this.f90633O4.m50571R((NinePatchDrawable) favoritesFragment.this.m15320b0().getDrawable(C5562R.drawable.material_shadow_z3_9));
                RecyclerView.Adapter adapterM50582o = favoritesFragment.this.f90633O4.m50582o(favoritesFragment.this.new EditFavoritesAdapter());
                favoritesFragment.this.m73291F3();
                favoritesFragment.this.f90623E4.setLayoutManager(linearLayoutManager);
                favoritesFragment.this.f90623E4.setAdapter(adapterM50582o);
                favoritesFragment.this.f90633O4.m50579h(favoritesFragment.this.f90623E4);
                return false;
            }
        });
        menuItemFindItem2.setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.8
            @Override // android.view.MenuItem.OnMenuItemClickListener
            public boolean onMenuItemClick(MenuItem menuItem) throws JSONException, IOException {
                if (favoritesFragment.this.f90620B4 != null && favoritesFragment.this.f90620B4.size() != 0) {
                    favoritesFragment.this.f88799s4.m2508k0("", false);
                    favoritesFragment.this.f88799s4.clearFocus();
                    favoritesFragment.this.f90620B4 = null;
                    favoritesFragment.this.m73293H3();
                    favoritesFragment.this.m72468V2();
                }
                menuItemFindItem.setVisible(true);
                menuItemFindItem2.setVisible(false);
                favoritesFragment.this.f90633O4.m50570N();
                favoritesFragment.this.m73287B3();
                favoritesFragment.this.f90631M4.m58207n();
                favoritesFragment.this.f90619A4.m27491G();
                favoritesFragment.this.f90627I4 = false;
                favoritesFragment favoritesfragment = favoritesFragment.this;
                favoritesfragment.m73292G3(favoritesfragment.f90621C4);
                favoritesFragment.this.m73293H3();
                return false;
            }
        });
        menu.findItem(C5562R.id.action_navigation).setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.9
            @Override // android.view.MenuItem.OnMenuItemClickListener
            public boolean onMenuItemClick(MenuItem menuItem) {
                ((mainActivity) favoritesFragment.this.m15366r()).m73332w1();
                return true;
            }
        });
        final SearchView searchView = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        searchView.setIconifiedByDefault(false);
        searchView.setQueryHint("Search Favorites");
        final String str = this.f90624F4;
        this.f88799s4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.10
            @Override // java.lang.Runnable
            public void run() {
                favoritesFragment.this.f90625G4 = true;
                favoritesFragment.this.f88799s4.m2508k0(str, false);
                String str2 = favoritesFragment.this.f88786f4;
                if (str2 == null || str2.length() <= 0) {
                    return;
                }
                if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                    favoritesFragment favoritesfragment = favoritesFragment.this;
                    favoritesfragment.f88799s4.m2508k0(favoritesfragment.f88786f4, true);
                } else {
                    favoritesFragment favoritesfragment2 = favoritesFragment.this;
                    favoritesfragment2.f88799s4.m2508k0(favoritesfragment2.f88786f4, false);
                    favoritesFragment.this.mo72472e3();
                }
                favoritesFragment.this.m72468V2();
            }
        }, 10L);
        this.f90625G4 = false;
        ((ImageView) searchView.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.11
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                searchView.m2508k0("", false);
                searchView.clearFocus();
                favoritesFragment.this.f90620B4 = null;
                favoritesFragment.this.m73293H3();
                favoritesFragment.this.m72468V2();
                favoritesFragment.this.f90619A4.m27491G();
                favoritesFragment.this.f90631M4.m58207n();
            }
        });
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.12
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str2) {
                if (!favoritesFragment.this.f90625G4) {
                    return true;
                }
                if (str2.length() != 0) {
                    favoritesFragment.this.f90624F4 = str2;
                    favoritesFragment.this.f88786f4 = str2;
                    new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.12.1
                        @Override // android.os.AsyncTask
                        protected Object doInBackground(Object[] objArr) {
                            ArrayList<Bundle> arrayList = CompressHelper.f87345t;
                            ArrayList arrayList2 = new ArrayList();
                            for (int i2 = 0; i2 < arrayList.size(); i2++) {
                                arrayList2.add("WHEN '" + arrayList.get(i2).getString("Name").replace("'", "''") + "' THEN " + i2);
                            }
                            String str3 = "case dbName " + StringUtils.join(arrayList2, StringUtils.SPACE) + " end";
                            CompressHelper compressHelper = favoritesFragment.this.f90635Q4;
                            ArrayList<Bundle> arrayListM71825Y = compressHelper.m71825Y(compressHelper.m71823X0(), "Select * from favorites where dbDocName like '%" + str2 + "%' order by " + str3);
                            favoritesFragment favoritesfragment = favoritesFragment.this;
                            favoritesfragment.f90620B4 = favoritesfragment.f90635Q4.m71887r2(arrayListM71825Y, "dbTitle");
                            return null;
                        }

                        @Override // android.os.AsyncTask
                        protected void onPostExecute(Object obj) {
                            if (favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) {
                                favoritesFragment.this.mo72473f3("Nothing Found");
                                return;
                            }
                            favoritesFragment.this.mo72472e3();
                            favoritesFragment.this.f90619A4.m27491G();
                            favoritesFragment.this.f90631M4.m58207n();
                        }

                        @Override // android.os.AsyncTask
                        protected void onPreExecute() {
                            favoritesFragment.this.mo72473f3("Searching ...");
                        }
                    }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                    return false;
                }
                favoritesFragment.this.m73293H3();
                favoritesFragment.this.f90620B4 = null;
                favoritesFragment.this.f90619A4.m27491G();
                favoritesFragment.this.f90631M4.m58207n();
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str2) {
                return false;
            }
        });
        m15366r().setTitle("");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws JSONException, IOException {
        View view = this.f88797q4;
        if (view != null) {
            return view;
        }
        this.f90630L4 = new ArrayList<>();
        this.f90635Q4 = new CompressHelper(m15366r());
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_favorites, viewGroup, false);
        this.f88797q4 = viewInflate;
        if (bundle != null && bundle.containsKey("Position")) {
            this.f88785e4 = bundle.getInt("Position");
        }
        if (bundle != null && bundle.containsKey("Query")) {
            this.f88786f4 = bundle.getString("Query");
        }
        if (bundle != null && bundle.containsKey("mIsSubmitted")) {
            this.f90622D4 = bundle.getBoolean("mIsSubmitted");
        }
        this.f90623E4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f90620B4 = new ArrayList<>();
        if (bundle != null && bundle.containsKey("mResults")) {
            this.f90620B4 = bundle.getParcelableArrayList("mResults");
        }
        this.f90621C4 = new ArrayList<>();
        if (bundle != null && bundle.containsKey("mFavorites")) {
            this.f90621C4 = bundle.getParcelableArrayList("mFavorites");
        }
        this.f90619A4 = new FavoriteAdapter();
        ArrayList<Bundle> arrayList = this.f90621C4;
        if (arrayList == null || arrayList.size() == 0) {
            ArrayList<Bundle> arrayListM73290E3 = m73290E3();
            this.f90621C4 = arrayListM73290E3;
            m73292G3(arrayListM73290E3);
        }
        StickyRecyclerHeadersDecoration stickyRecyclerHeadersDecoration = new StickyRecyclerHeadersDecoration(this.f90619A4, new ItemVisibilityAdapter() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.1
            @Override // com.timehop.stickyheadersrecyclerview.ItemVisibilityAdapter
            /* renamed from: a */
            public boolean mo58199a(int i2) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) favoritesFragment.this.f90623E4.getLayoutManager();
                linearLayoutManager.m27176B2();
                linearLayoutManager.m27178E2();
                boolean z = linearLayoutManager.m27176B2() <= i2 && linearLayoutManager.m27178E2() >= i2;
                iMDLogger.m73550f(CSS.Property.f74043m0, i2 + " visible + " + Boolean.valueOf(z));
                return z;
            }
        });
        this.f90631M4 = stickyRecyclerHeadersDecoration;
        StickyRecyclerHeadersTouchListener stickyRecyclerHeadersTouchListener = new StickyRecyclerHeadersTouchListener(this.f90623E4, stickyRecyclerHeadersDecoration);
        this.f90632N4 = stickyRecyclerHeadersTouchListener;
        stickyRecyclerHeadersTouchListener.m58212h(new StickyRecyclerHeadersTouchListener.OnHeaderClickListener() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.2
            @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersTouchListener.OnHeaderClickListener
            /* renamed from: a */
            public void mo58213a(View view2, int i2, long j2) {
                String strM71836b1 = favoritesFragment.this.f90635Q4.m71836b1(((Bundle) ((favoritesFragment.this.f90620B4 == null || favoritesFragment.this.f90620B4.size() == 0) ? favoritesFragment.this.f90621C4 : favoritesFragment.this.f90620B4).get((int) j2)).getString("title"));
                if (favoritesFragment.this.f90630L4.contains(strM71836b1)) {
                    favoritesFragment.this.f90630L4.remove(strM71836b1);
                } else {
                    favoritesFragment.this.f90630L4.add(strM71836b1);
                }
                favoritesFragment.this.f90623E4.getAdapter().m27491G();
            }
        });
        this.f90623E4.m27466s(this.f90632N4);
        m73287B3();
        this.f90623E4.setLayoutManager(new LinearLayoutManager(m15366r()));
        this.f90623E4.setItemAnimator(new DefaultItemAnimator());
        this.f90623E4.m27459p(new CustomItemDecoration(m15366r()));
        this.f90623E4.setAdapter(this.f90619A4);
        this.f90619A4.m27502Z(new RecyclerView.AdapterDataObserver() { // from class: net.imedicaldoctor.imd.Fragments.favoritesFragment.3
            @Override // androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
            /* renamed from: a */
            public void mo27287a() {
                favoritesFragment.this.f90631M4.m58207n();
            }
        });
        m15358o2(true);
        m73293H3();
        return viewInflate;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: V0 */
    public void mo15306V0() {
        super.mo15306V0();
        LocalBroadcastManager.m16410b(m73289D3()).m16415f(this.f90636R4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: e3 */
    public void mo72472e3() {
        RecyclerView recyclerView;
        RecyclerView.Adapter adapter = this.f90623E4.getAdapter();
        FavoriteAdapter favoriteAdapter = this.f90619A4;
        if (adapter != favoriteAdapter) {
            m73287B3();
            recyclerView = this.f90623E4;
            favoriteAdapter = this.f90619A4;
        } else {
            recyclerView = this.f90623E4;
        }
        recyclerView.setAdapter(favoriteAdapter);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: f3 */
    public void mo72473f3(String str) {
        try {
            if (!str.equals("Searching")) {
                this.f90631M4.m58207n();
                m73291F3();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        this.f90623E4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        this.f90623E4.setAdapter(new StatusAdapter(m15366r(), str));
    }

    /* renamed from: i3 */
    public ArrayList<Bundle> m73296i3() {
        ArrayList<Bundle> arrayList = CompressHelper.f87345t;
        Log.e("Speed", "Favorites sortedDatabases");
        ArrayList arrayList2 = new ArrayList();
        if (arrayList != null) {
            for (int i2 = 0; i2 < arrayList.size(); i2++) {
                arrayList2.add("WHEN '" + arrayList.get(i2).getString("Name").replace("'", "''") + "' THEN " + i2);
            }
        }
        if (arrayList2.size() <= 0) {
            return null;
        }
        String str = "case dbName " + StringUtils.join(arrayList2, StringUtils.SPACE) + " end";
        CompressHelper compressHelper = this.f90635Q4;
        return compressHelper.m71825Y(compressHelper.m71823X0(), "select * from favorites order by " + str);
    }

    /* renamed from: j3 */
    public int m73297j3(int i2, ArrayList<Bundle> arrayList) {
        int size = 0;
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            Bundle bundle = arrayList.get(i3);
            size += this.f90630L4.contains(bundle.getString("title")) ? 1 : bundle.getParcelableArrayList("items").size();
            if (i2 < size) {
                return i3;
            }
        }
        return 0;
    }

    /* renamed from: k3 */
    public Bundle m73298k3(int i2, ArrayList<Bundle> arrayList) {
        Iterator<Bundle> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String strM71836b1 = this.f90635Q4.m71836b1(next.getString("title"));
            int size = this.f90630L4.contains(strM71836b1) ? 1 : next.getParcelableArrayList("items").size();
            i3 += size;
            if (i2 < i3) {
                int i4 = i2 - (i3 - size);
                Bundle bundle = new Bundle();
                bundle.putString("Database", this.f90635Q4.m71836b1(next.getString("title")));
                if (this.f90630L4.contains(strM71836b1)) {
                    if (i4 == 0) {
                        return bundle;
                    }
                    i4--;
                }
                bundle.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(i4));
                return bundle;
            }
        }
        return null;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: l1 */
    public void mo15352l1() {
        super.mo15352l1();
        m72468V2();
    }

    /* renamed from: l3 */
    public Bundle m73299l3(int i2, ArrayList<Bundle> arrayList) {
        Iterator<Bundle> it2 = arrayList.iterator();
        int i3 = 0;
        int i4 = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Title", this.f90635Q4.m71836b1(next.getString("title")));
                bundle.putInt("Row", 0);
                bundle.putInt("Section", i4);
                bundle.putInt("Row2", 1);
                bundle.putInt("Section2", i4 - 1);
                return bundle;
            }
            int size = i3 + next.getParcelableArrayList("items").size();
            if (i2 <= size) {
                int size2 = (i2 - (size - next.getParcelableArrayList("items").size())) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                bundle2.putInt("Row", size2);
                bundle2.putInt("Section", i4);
                return bundle2;
            }
            i3 = size + 1;
            i4++;
        }
        return null;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    /* renamed from: m3 */
    public void m73300m3() {
        this.f90621C4 = m73290E3();
        this.f90631M4.m58207n();
        m73287B3();
        this.f90619A4.m27491G();
        m73293H3();
    }
}

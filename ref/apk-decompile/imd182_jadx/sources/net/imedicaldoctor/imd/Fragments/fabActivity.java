package net.imedicaldoctor.imd.Fragments;

import android.os.Bundle;
import android.view.View;
import androidx.appcompat.widget.Toolbar;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.snackbar.Snackbar;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class fabActivity extends iMDActivity {
    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_fab);
        m1129P0((Toolbar) findViewById(C5562R.id.toolbar));
        ((FloatingActionButton) findViewById(C5562R.id.fab)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.fabActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                Snackbar.m40143E0(view, "Replace with your own action", 0).m40152H0("Action", null).mo40115m0();
            }
        });
    }
}

package net.imedicaldoctor.imd.Fragments.EPUB;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.common.C1052C;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.tool.xml.html.HTML;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Random;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class EPUBChaptersActivityFragment extends SearchHelperFragment {

    /* renamed from: G4 */
    private static String f87951G4;

    /* renamed from: A4 */
    private String f87952A4;

    /* renamed from: B4 */
    private Boolean f87953B4;

    /* renamed from: C4 */
    private ArrayList<String> f87954C4;

    /* renamed from: D4 */
    private ArrayList<String> f87955D4;

    /* renamed from: E4 */
    private ArrayList<String> f87956E4;

    /* renamed from: F4 */
    public BroadcastReceiver f87957F4 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.1
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            try {
                if (EPUBChaptersActivityFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("shake", false)) {
                    LocalBroadcastManager.m16410b(EPUBChaptersActivityFragment.this.m15366r()).m16415f(EPUBChaptersActivityFragment.this.f87957F4);
                    EPUBChaptersActivityFragment.this.m72149r3();
                    EPUBChaptersActivityFragment.this.f88797q4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.1.1
                        @Override // java.lang.Runnable
                        public void run() {
                            LocalBroadcastManager.m16410b(EPUBChaptersActivityFragment.this.m15366r()).m16412c(EPUBChaptersActivityFragment.this.f87957F4, new IntentFilter("Shake"));
                        }
                    }, C1052C.f19115c2);
                }
            } catch (Exception unused) {
            }
        }
    };

    public class AccountTextViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f87967I;

        /* renamed from: J */
        private final MaterialRippleLayout f87968J;

        public AccountTextViewHolder(View view) {
            super(view);
            this.f87967I = (TextView) view.findViewById(C5562R.id.text);
            this.f87968J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class ButtonChaptersAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public Context f87970d;

        /* renamed from: e */
        public ArrayList<Bundle> f87971e;

        /* renamed from: f */
        public String f87972f;

        /* renamed from: g */
        public String f87973g;

        /* renamed from: h */
        public String f87974h;

        public ButtonChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2, String str3) {
            this.f87970d = context;
            this.f87971e = arrayList;
            this.f87972f = str;
            this.f87974h = str3;
            this.f87973g = str2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            if (i2 == this.f87971e.size()) {
                return 2;
            }
            return this.f87971e.get(i2).getString("leaf").equals(IcyHeaders.f28171a3) ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            if (i2 != this.f87971e.size()) {
                RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                final Bundle bundle = this.f87971e.get(i2);
                rippleTextViewHolder.f101515I.setText(bundle.getString(this.f87972f));
                rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.ButtonChaptersAdapter.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        ButtonChaptersAdapter.this.mo72152e0(bundle, i2);
                    }
                });
                return;
            }
            AccountTextViewHolder accountTextViewHolder = (AccountTextViewHolder) viewHolder;
            accountTextViewHolder.f87967I.setText(this.f87973g);
            accountTextViewHolder.f87967I.setTextColor(EPUBChaptersActivityFragment.this.m15320b0().getColor(C5562R.color.white));
            accountTextViewHolder.f87968J.setBackgroundColor(Color.parseColor(this.f87974h));
            accountTextViewHolder.f87968J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.ButtonChaptersAdapter.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ButtonChaptersAdapter.this.mo72151d0();
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new RippleTextViewHolder(LayoutInflater.from(this.f87970d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
            }
            if (i2 == 1) {
                return new RippleTextViewHolder(LayoutInflater.from(this.f87970d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
            }
            if (i2 != 2) {
                return null;
            }
            return EPUBChaptersActivityFragment.this.new AccountTextViewHolder(LayoutInflater.from(this.f87970d).inflate(C5562R.layout.list_view_item_account_text, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return this.f87971e.size() + 1;
        }

        /* renamed from: d0 */
        public void mo72151d0() {
        }

        /* renamed from: e0 */
        public void mo72152e0(Bundle bundle, int i2) {
        }
    }

    public class EPUBChaptersAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public Context f87980d;

        /* renamed from: e */
        public ArrayList<Bundle> f87981e;

        /* renamed from: f */
        public String f87982f;

        public EPUBChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
            this.f87980d = context;
            this.f87981e = arrayList;
            this.f87982f = str;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return this.f87981e.get(i2).getString("leaf").equals(IcyHeaders.f28171a3) ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
            final Bundle bundle = this.f87981e.get(i2);
            rippleTextViewHolder.f101515I.setText(bundle.getString(this.f87982f));
            rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.EPUBChaptersAdapter.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    EPUBChaptersAdapter.this.mo72153d0(bundle, i2);
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new RippleTextViewHolder(LayoutInflater.from(this.f87980d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
            }
            if (i2 == 1) {
                return new RippleTextViewHolder(LayoutInflater.from(this.f87980d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return this.f87981e.size();
        }

        /* renamed from: d0 */
        public void mo72153d0(Bundle bundle, int i2) {
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: Q0 */
    public void mo15207Q0(Bundle bundle) {
        super.mo15207Q0(bundle);
        LocalBroadcastManager.m16410b(m15366r()).m16412c(this.f87957F4, new IntentFilter("Shake"));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(C5562R.menu.search, menu);
        this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
        m72462O2();
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        RecyclerView.Adapter adapter;
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        this.f88797q4 = viewInflate;
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        m72462O2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            this.f87952A4 = "0";
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.4
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            this.f87952A4 = m15387y().getString("ParentId");
        }
        this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select id as _id,* from TOC where parentId = " + this.f87952A4);
        Boolean boolValueOf = Boolean.valueOf(this.f88788h4.getString("Name").contains("auntminnie"));
        this.f87953B4 = boolValueOf;
        if (boolValueOf.booleanValue()) {
            adapter = new ButtonChaptersAdapter(m15366r(), this.f88794n4, "name", "Random Case", "#0e4b06") { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.5
                @Override // net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.ButtonChaptersAdapter
                /* renamed from: d0 */
                public void mo72151d0() {
                    EPUBChaptersActivityFragment.this.m72149r3();
                }

                @Override // net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.ButtonChaptersAdapter
                /* renamed from: e0 */
                public void mo72152e0(Bundle bundle2, int i2) {
                    EPUBChaptersActivityFragment.this.m72468V2();
                    String string = bundle2.getString("leaf");
                    String string2 = bundle2.getString("docId");
                    String string3 = bundle2.getString(HTML.Tag.f74369V);
                    if (string.equals("0")) {
                        new CompressHelper(EPUBChaptersActivityFragment.this.m15366r()).m71772A1(EPUBChaptersActivityFragment.this.f88788h4, string2, null, string3);
                        return;
                    }
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("DB", EPUBChaptersActivityFragment.this.f88788h4);
                    bundle3.putString("ParentId", bundle2.getString("id"));
                    new CompressHelper(EPUBChaptersActivityFragment.this.m15366r()).m71798N(EPUBChaptersActivity.class, EPUBChaptersActivityFragment.class, bundle3);
                }
            };
        } else {
            adapter = new EPUBChaptersAdapter(m15366r(), this.f88794n4, "name") { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.6
                @Override // net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.EPUBChaptersAdapter
                /* renamed from: d0 */
                public void mo72153d0(Bundle bundle2, int i2) {
                    EPUBChaptersActivityFragment.this.m72468V2();
                    String string = bundle2.getString("leaf");
                    String string2 = bundle2.getString("docId");
                    String string3 = bundle2.getString(HTML.Tag.f74369V);
                    if (string.equals("0")) {
                        new CompressHelper(EPUBChaptersActivityFragment.this.m15366r()).m71772A1(EPUBChaptersActivityFragment.this.f88788h4, string2, null, string3);
                        return;
                    }
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("DB", EPUBChaptersActivityFragment.this.f88788h4);
                    bundle3.putString("ParentId", bundle2.getString("id"));
                    new CompressHelper(EPUBChaptersActivityFragment.this.m15366r()).m71798N(EPUBChaptersActivity.class, EPUBChaptersActivityFragment.class, bundle3);
                }
            };
        }
        this.f88792l4 = adapter;
        this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.7
            @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
            /* renamed from: e0 */
            public void mo71953e0(Bundle bundle2, int i2) {
                Bundle bundle3;
                CompressHelper compressHelper;
                EPUBChaptersActivityFragment.this.m72468V2();
                String string = bundle2.getString("type");
                String string2 = bundle2.getString("contentId");
                bundle2.getString(HTML.Tag.f74369V);
                if (string.equals(IcyHeaders.f28171a3)) {
                    bundle3 = new Bundle();
                    bundle3.putBundle("DB", EPUBChaptersActivityFragment.this.f88788h4);
                    bundle3.putString("ParentId", string2);
                    compressHelper = new CompressHelper(EPUBChaptersActivityFragment.this.m15366r());
                } else {
                    if (string.equals("5")) {
                        CompressHelper compressHelper2 = new CompressHelper(EPUBChaptersActivityFragment.this.m15366r());
                        EPUBChaptersActivityFragment ePUBChaptersActivityFragment = EPUBChaptersActivityFragment.this;
                        compressHelper2.m71772A1(ePUBChaptersActivityFragment.f88788h4, string2, ePUBChaptersActivityFragment.m72466T2(bundle2.getString("subText")), null);
                        return;
                    }
                    if (!string.equals("0")) {
                        return;
                    }
                    EPUBChaptersActivityFragment ePUBChaptersActivityFragment2 = EPUBChaptersActivityFragment.this;
                    CompressHelper compressHelper3 = ePUBChaptersActivityFragment2.f88791k4;
                    Bundle bundleM71890s1 = compressHelper3.m71890s1(compressHelper3.m71817V(ePUBChaptersActivityFragment2.f88788h4, "Select * from toc where id=" + string2));
                    if (bundleM71890s1 == null) {
                        return;
                    }
                    String string3 = bundleM71890s1.getString("leaf");
                    String string4 = bundleM71890s1.getString("docId");
                    String string5 = bundleM71890s1.getString(HTML.Tag.f74369V);
                    if (string3.equals("0")) {
                        new CompressHelper(EPUBChaptersActivityFragment.this.m15366r()).m71772A1(EPUBChaptersActivityFragment.this.f88788h4, string4, null, string5);
                        return;
                    }
                    bundle3 = new Bundle();
                    bundle3.putBundle("DB", EPUBChaptersActivityFragment.this.f88788h4);
                    bundle3.putString("ParentId", bundleM71890s1.getString("id"));
                    compressHelper = new CompressHelper(EPUBChaptersActivityFragment.this.m15366r());
                }
                compressHelper.m71798N(EPUBChaptersActivity.class, EPUBChaptersActivityFragment.class, bundle3);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return viewInflate;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: V0 */
    public void mo15306V0() {
        super.mo15306V0();
        LocalBroadcastManager.m16410b(m15366r()).m16415f(this.f87957F4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id, Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    /* renamed from: q3 */
    public String m72148q3(ArrayList<String> arrayList) {
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            arrayList2.add("'" + it2.next() + "'");
        }
        return StringUtils.join(arrayList2, ",");
    }

    /* renamed from: r3 */
    public void m72149r3() {
        m72460L2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = EPUBChaptersActivityFragment.this.f87952A4;
                if (EPUBChaptersActivityFragment.this.f87953B4.booleanValue() && str.equals("0")) {
                    str = IcyHeaders.f28171a3;
                }
                if (EPUBChaptersActivityFragment.this.f87954C4 == null) {
                    EPUBChaptersActivityFragment.this.f87954C4 = new ArrayList();
                    EPUBChaptersActivityFragment ePUBChaptersActivityFragment = EPUBChaptersActivityFragment.this;
                    ePUBChaptersActivityFragment.m72150s3(str, ePUBChaptersActivityFragment.f87954C4);
                }
                EPUBChaptersActivityFragment.this.f87955D4 = new ArrayList();
                EPUBChaptersActivityFragment ePUBChaptersActivityFragment2 = EPUBChaptersActivityFragment.this;
                CompressHelper compressHelper = ePUBChaptersActivityFragment2.f88791k4;
                String strM71852h2 = compressHelper.m71852h2();
                StringBuilder sb = new StringBuilder();
                sb.append("Select dbAddress from recent where dbName ='");
                sb.append(EPUBChaptersActivityFragment.this.f88788h4.getString("Name"));
                sb.append("' AND dbAddress in (");
                EPUBChaptersActivityFragment ePUBChaptersActivityFragment3 = EPUBChaptersActivityFragment.this;
                sb.append(ePUBChaptersActivityFragment3.m72148q3(ePUBChaptersActivityFragment3.f87954C4));
                sb.append(")");
                ePUBChaptersActivityFragment2.f87955D4 = compressHelper.m71828Z(strM71852h2, sb.toString(), "dbAddress");
                EPUBChaptersActivityFragment.this.f87956E4 = new ArrayList(EPUBChaptersActivityFragment.this.f87954C4);
                if (EPUBChaptersActivityFragment.this.f87955D4 != null) {
                    EPUBChaptersActivityFragment.this.f87956E4.removeAll(EPUBChaptersActivityFragment.this.f87955D4);
                } else {
                    EPUBChaptersActivityFragment.this.f87955D4 = new ArrayList();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment.3
            @Override // java.lang.Runnable
            public void run() {
                if (EPUBChaptersActivityFragment.this.f87956E4.size() == 0) {
                    CompressHelper.m71767x2(EPUBChaptersActivityFragment.this.m15366r(), "All are reviewed", 1);
                    return;
                }
                CompressHelper.m71767x2(EPUBChaptersActivityFragment.this.m15366r(), EPUBChaptersActivityFragment.this.f87956E4.size() + " Remaining (" + EPUBChaptersActivityFragment.this.f87955D4.size() + " Reviewed)", 1);
                new CompressHelper(EPUBChaptersActivityFragment.this.m15366r()).m71772A1(EPUBChaptersActivityFragment.this.f88788h4, (String) EPUBChaptersActivityFragment.this.f87956E4.get(new Random().nextInt(EPUBChaptersActivityFragment.this.f87956E4.size())), null, null);
            }
        });
    }

    /* renamed from: s3 */
    public void m72150s3(String str, ArrayList<String> arrayList) {
        Log.d("readChildTOCS", "parentId = " + str);
        ArrayList<Bundle> arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "Select * from TOC where parentId = " + str);
        if (arrayListM71817V == null) {
            arrayListM71817V = new ArrayList<>();
        }
        Iterator<Bundle> it2 = arrayListM71817V.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (next.getString("leaf").equals("0")) {
                arrayList.add(next.getString("docId"));
            } else {
                m72150s3(next.getString("id"), arrayList);
            }
        }
    }
}

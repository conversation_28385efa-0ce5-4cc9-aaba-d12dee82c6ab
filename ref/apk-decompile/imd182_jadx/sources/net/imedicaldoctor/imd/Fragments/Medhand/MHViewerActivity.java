package net.imedicaldoctor.imd.Fragments.Medhand;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.JsResult;
import android.webkit.WebView;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import androidx.media3.exoplayer.ExoPlayer;
import com.google.android.material.appbar.AppBarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.CollapsingToolbar.CollapsingToolbarLayout;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.Utils.iMDWebView;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MHViewerActivity extends ViewerHelperActivity {

    public static class MHViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        public ArrayList<Bundle> f88476X4;

        /* renamed from: Y4 */
        public String f88477Y4;

        /* renamed from: Z4 */
        public String f88478Z4;

        /* renamed from: a5 */
        public String f88479a5;

        /* renamed from: b5 */
        public boolean f88480b5;

        /* renamed from: c5 */
        public ArrayList<String> f88481c5;

        /* renamed from: d5 */
        public String f88482d5;

        /* renamed from: e5 */
        public int f88483e5;

        /* renamed from: f5 */
        public boolean f88484f5;

        /* renamed from: g5 */
        private boolean f88485g5;

        /* renamed from: M4 */
        private Bundle m72357M4(String str, String str2) {
            Bundle bundle = new Bundle();
            bundle.putString("url", str);
            bundle.putString(TypedValues.CycleType.f5457R, str2);
            return bundle;
        }

        /* renamed from: T4 */
        private void m72358T4(String str) {
            ArrayList<String> arrayList = this.f88481c5;
            if (arrayList == null || arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
                return;
            }
            ArrayList arrayList2 = new ArrayList();
            Iterator<String> it2 = this.f88481c5.iterator();
            while (it2.hasNext()) {
                String next = it2.next();
                Bundle bundle = new Bundle();
                bundle.putString("ImagePath", next);
                bundle.putString("Description", "");
                bundle.putString("id", next);
                if (new File(next).length() > 5000) {
                    arrayList2.add(bundle);
                }
            }
            int i2 = 0;
            for (int i3 = 0; i3 < arrayList2.size(); i3++) {
                if (((Bundle) arrayList2.get(i3)).getString("id").equals(str)) {
                    i2 = i3;
                }
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList2);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        /* renamed from: I4 */
        public void m72359I4() {
            this.f89598s4.findItem(C5562R.id.action_back).setEnabled(false);
            this.f89598s4.findItem(C5562R.id.action_back).setIcon(C5562R.drawable.ic_action_previous_item_disabled);
        }

        /* renamed from: J4 */
        public void m72360J4() {
            this.f89598s4.findItem(C5562R.id.action_forward).setEnabled(false);
            this.f89598s4.findItem(C5562R.id.action_forward).setIcon(C5562R.drawable.ic_action_next_item_disabled);
        }

        /* renamed from: K4 */
        public void m72361K4() {
            this.f89598s4.findItem(C5562R.id.action_back).setEnabled(true);
            this.f89598s4.findItem(C5562R.id.action_back).setIcon(C5562R.drawable.ic_action_previous_item);
        }

        /* renamed from: L4 */
        public void m72362L4() {
            this.f89598s4.findItem(C5562R.id.action_forward).setEnabled(true);
            this.f89598s4.findItem(C5562R.id.action_forward).setIcon(C5562R.drawable.ic_action_next_item);
        }

        /* renamed from: N4 */
        public void m72363N4() {
            if (this.f88476X4.size() == 0) {
                m72359I4();
                m72360J4();
                return;
            }
            if (this.f88483e5 > 0 || this.f88484f5) {
                m72361K4();
            } else {
                m72359I4();
            }
            if (this.f88483e5 >= this.f88476X4.size() - 1) {
                m72360J4();
            } else {
                m72362L4();
            }
        }

        /* JADX WARN: Removed duplicated region for block: B:18:0x00ca  */
        /* renamed from: O4 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public java.lang.String m72364O4(java.lang.String r7) {
            /*
                Method dump skipped, instructions count: 279
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Medhand.MHViewerActivity.MHViewerFragment.m72364O4(java.lang.String):java.lang.String");
        }

        /* renamed from: P4 */
        public void m72365P4() {
            this.f88485g5 = false;
            if (this.f88483e5 == this.f88476X4.size() - 1 && this.f88484f5) {
                this.f89569G4.m73433g("console.log(\"history,,,,," + this.f88477Y4 + ",,,,,\" + window.pageYOffset);");
                this.f88484f5 = false;
                this.f88485g5 = true;
            }
            if (!this.f88485g5) {
                int i2 = this.f88483e5;
                if (i2 - 1 >= 0) {
                    int i3 = i2 - 1;
                    this.f88483e5 = i3;
                    this.f88482d5 = this.f88476X4.get(i3).getString(TypedValues.CycleType.f5457R);
                    m72367R4(this.f88476X4.get(this.f88483e5).getString("url"), Boolean.TRUE);
                }
            }
            m72363N4();
        }

        /* renamed from: Q4 */
        public void m72366Q4() {
            if (this.f88476X4.size() == 0) {
                return;
            }
            if (this.f88483e5 + 1 <= this.f88476X4.size() - 1) {
                int i2 = this.f88483e5 + 1;
                this.f88483e5 = i2;
                this.f88482d5 = this.f88476X4.get(i2).getString(TypedValues.CycleType.f5457R);
                m72367R4(this.f88476X4.get(this.f88483e5).getString("url"), Boolean.TRUE);
            }
            m72363N4();
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            ArrayList<String> arrayList = this.f88481c5;
            if (arrayList == null || arrayList.size() <= 0) {
                return null;
            }
            return m72840w3(this.f88481c5);
        }

        /* renamed from: R4 */
        public void m72367R4(String str, Boolean bool) {
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "#");
            String str2 = strArrSplitByWholeSeparator[0];
            String str3 = strArrSplitByWholeSeparator.length == 2 ? strArrSplitByWholeSeparator[1] : null;
            this.f88477Y4 = str2;
            ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str2, "/")));
            arrayList.remove(arrayList.size() - 1);
            String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, StringUtils.join(arrayList, "/"));
            this.f88478Z4 = "file://" + new File(strM71753g1).getAbsolutePath() + "/";
            this.f89563A4 = m72364O4(str2.replace("/", "\\"));
            this.f88479a5 = str3;
            if (bool.booleanValue()) {
                m72795O3(this.f89563A4, strM71753g1);
            }
        }

        /* renamed from: S4 */
        public String m72368S4(String str) {
            ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
            arrayList.remove(arrayList.size() - 1);
            return StringUtils.join(arrayList, "/");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_mhviewer, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f88476X4 = bundle.getParcelableArrayList("mHistory");
                this.f88477Y4 = bundle.getString("mCurrentURL");
                this.f88478Z4 = bundle.getString("mLastBasePath");
                this.f88480b5 = bundle.getBoolean("mNewZoom");
                this.f88481c5 = bundle.getStringArrayList("mImages");
                this.f88482d5 = bundle.getString("mGotoOffset");
                this.f88483e5 = bundle.getInt("mHistoryIndex");
                this.f88484f5 = bundle.getBoolean("mRegisterLastPage");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            try {
                CompressHelper compressHelper = new CompressHelper(m15366r());
                String str = this.f89563A4;
                if (str == null || str.length() == 0) {
                    this.f88476X4 = new ArrayList<>();
                    m72367R4(this.f89567E4, Boolean.FALSE);
                }
                compressHelper.m71903x1();
                m72827m4("");
                m72795O3(this.f89563A4, this.f88478Z4);
                m72836s4();
                m72831p4();
                mo72642f3(C5562R.menu.menu_mhviewer);
                m15358o2(false);
                m72786G3();
                ((AppBarLayout) this.f89565C4.findViewById(C5562R.id.appbar)).m35746D(false, true);
                return viewInflate;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                m72779B4(e2);
                return viewInflate;
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: W3 */
        public boolean mo71969W3(ConsoleMessage consoleMessage) {
            String[] strArrSplit = consoleMessage.message().split(",,,,,");
            if (strArrSplit[0].equals("title")) {
                this.f89568F4 = strArrSplit.length < 2 ? "Unnamed" : strArrSplit[1];
                ((CollapsingToolbarLayout) this.f89565C4.findViewById(C5562R.id.collapsing_toolbar)).setTitle(this.f89568F4);
                return true;
            }
            if (strArrSplit[0].equals("images")) {
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
                ArrayList<String> arrayList = new ArrayList<>();
                for (String str : strArrSplitByWholeSeparator) {
                    String strReplace = this.f88478Z4.replace("file://", "");
                    String strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72368S4(strSubstring) : strSubstring + "/" + str2;
                    }
                    if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                        arrayList.add(strSubstring);
                    }
                    iMDLogger.m73554j("MedhandImages", "Imagepath = : " + strSubstring);
                }
                this.f88481c5 = arrayList;
                this.f89598s4.findItem(C5562R.id.action_gallery).setVisible(this.f88481c5.size() > 0);
                mo71972o4();
                this.f89569G4.m73433g("IgnoreSmallImages();");
                this.f89569G4.m73433g("ConvertAllImages();");
                this.f89569G4.m73433g("fixAllImages2();");
            } else if (strArrSplit[0].equals("history")) {
                this.f88476X4.add(m72357M4(strArrSplit[1], strArrSplit[2]));
                int size = this.f88476X4.size();
                this.f88483e5 = size - 1;
                if (this.f88485g5) {
                    if (size - 2 >= 0) {
                        int i2 = size - 2;
                        this.f88483e5 = i2;
                        this.f88482d5 = this.f88476X4.get(i2).getString(TypedValues.CycleType.f5457R);
                        m72367R4(this.f88476X4.get(this.f88483e5).getString("url"), Boolean.TRUE);
                    }
                    this.f88485g5 = false;
                }
                m72363N4();
            }
            return super.mo71969W3(consoleMessage);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: X3 */
        public boolean mo72160X3(WebView webView, String str, String str2, JsResult jsResult) {
            jsResult.confirm();
            return true;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: Z3 */
        public void mo71956Z3(WebView webView, String str) {
            iMDWebView imdwebview;
            String str2;
            super.mo71956Z3(webView, str);
            this.f89569G4.m73433g("console.log(\"title,,,,,\" + document.title);");
            if (this.f88480b5) {
                imdwebview = this.f89569G4;
                str2 = "onBodyLoad();";
            } else {
                imdwebview = this.f89569G4;
                str2 = "console.log(\"images,,,,,\" + getImageList());";
            }
            imdwebview.m73433g(str2);
            if (this.f88482d5 != null) {
                this.f89569G4.m73433g("document.body.scrollTop=" + this.f88482d5);
                this.f88482d5 = null;
            }
            String str3 = this.f88479a5;
            if (str3 != null) {
                mo71967C3(str3);
                this.f88479a5 = null;
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_forward) {
                m72366Q4();
                return true;
            }
            if (itemId == C5562R.id.action_back) {
                m72365P4();
                return true;
            }
            if (itemId != C5562R.id.action_gallery) {
                return super.mo15329e1(menuItem);
            }
            m72358T4("soheilvb");
            return true;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            this.f89598s4 = menu;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws UnsupportedEncodingException {
            if (str2.equals("image")) {
                m72358T4(str3);
                return true;
            }
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            new CompressHelper(m15366r());
            this.f88484f5 = true;
            try {
                str3 = URLDecoder.decode(str3, "UTF-8");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("MHViewerShould", "Error in decoding " + str3 + " : " + e2.getMessage());
            }
            String strSubstring = str3.replace(m72368S4(CompressHelper.m71752f1(this.f89566D4)), "").substring(4);
            if (this.f88483e5 != this.f88476X4.size() - 1 && this.f88476X4.size() > 0) {
                int i2 = this.f88483e5;
                int size = this.f88476X4.size() - this.f88483e5;
                if (size != 0) {
                    for (int i3 = 0; i3 < size; i3++) {
                        this.f88476X4.remove(i2);
                    }
                }
            }
            if (this.f88476X4.size() != 0) {
                ArrayList<Bundle> arrayList = this.f88476X4;
                if (arrayList.get(arrayList.size() - 1).getString("url").equals(this.f88477Y4)) {
                    ArrayList<Bundle> arrayList2 = this.f88476X4;
                    arrayList2.remove(arrayList2.size() - 1);
                }
            }
            this.f89569G4.m73433g("console.log(\"history,,,,," + this.f88477Y4 + ",,,,,\" + window.pageYOffset);");
            m72363N4();
            if (!strSubstring.contains(".html")) {
                return true;
            }
            m72367R4(strSubstring, Boolean.TRUE);
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new MHViewerFragment(), bundle);
    }
}

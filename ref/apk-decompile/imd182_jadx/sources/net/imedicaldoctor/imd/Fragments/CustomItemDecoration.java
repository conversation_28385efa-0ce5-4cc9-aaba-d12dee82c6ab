package net.imedicaldoctor.imd.Fragments;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class CustomItemDecoration extends RecyclerView.ItemDecoration {

    /* renamed from: a */
    private final Drawable f87693a;

    /* renamed from: b */
    private final int f87694b;

    public CustomItemDecoration(Context context) {
        Drawable drawableM6692l = ContextCompat.m6692l(context, C5562R.drawable.recycler_view_divider);
        this.f87693a = drawableM6692l;
        this.f87694b = drawableM6692l != null ? drawableM6692l.getIntrinsicHeight() : 0;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.ItemDecoration
    /* renamed from: k */
    public void mo26977k(Canvas canvas, RecyclerView recyclerView, RecyclerView.State state) {
        int i2;
        int paddingLeft = recyclerView.getPaddingLeft();
        int width = recyclerView.getWidth() - recyclerView.getPaddingRight();
        int childCount = recyclerView.getChildCount();
        for (int i3 = 0; i3 < childCount; i3++) {
            View childAt = recyclerView.getChildAt(i3);
            if (childAt.getLayoutParams().height != 0 && childAt.getVisibility() != 8 && !CompressHelper.m71725C1(childAt) && ((childCount <= (i2 = i3 + 1) || !CompressHelper.m71725C1(recyclerView.getChildAt(i2))) && (childAt.findViewById(C5562R.id.title_item) == null || childCount <= i2 || recyclerView.getChildAt(i2).findViewById(C5562R.id.title_item) == null))) {
                int bottom = childAt.getBottom() + ((ViewGroup.MarginLayoutParams) ((RecyclerView.LayoutParams) childAt.getLayoutParams())).bottomMargin;
                this.f87693a.setBounds(paddingLeft, bottom, width, this.f87694b + bottom);
                this.f87693a.draw(canvas);
            }
        }
    }
}

package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.app.ProgressDialog;
import android.content.res.Configuration;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import androidx.exifinterface.media.ExifInterface;
import androidx.palette.graphics.Palette;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.GridAutoFitLayoutManager;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class VDDxResults extends iMDActivity {

    public static class VDDxResultsFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        public Bundle f89866X4;

        /* renamed from: Y4 */
        public String f89867Y4;

        /* renamed from: Z4 */
        public String f89868Z4;

        /* renamed from: a5 */
        public RecyclerView f89869a5;

        /* renamed from: b5 */
        private Bundle f89870b5;

        /* renamed from: c5 */
        public ArrayList<Bundle> f89871c5;

        public static class HeaderPlaceHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f89894I;

            public HeaderPlaceHolder(View view) {
                super(view);
                this.f89894I = (TextView) view.findViewById(C5562R.id.header_text);
            }
        }

        public static class PhotoCaptionWarningPlaceHolder extends RecyclerView.ViewHolder {

            /* renamed from: I */
            public TextView f89895I;

            /* renamed from: J */
            public ImageView f89896J;

            /* renamed from: K */
            public ImageView f89897K;

            /* renamed from: L */
            public MaterialRippleLayout f89898L;

            public PhotoCaptionWarningPlaceHolder(View view) {
                super(view);
                this.f89895I = (TextView) view.findViewById(C5562R.id.caption);
                this.f89896J = (ImageView) view.findViewById(C5562R.id.image_view);
                this.f89897K = (ImageView) view.findViewById(C5562R.id.warning);
                this.f89898L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            }
        }

        /* renamed from: I4 */
        public Bundle m72926I4(int i2, ArrayList<Bundle> arrayList) {
            Iterator<Bundle> it2 = arrayList.iterator();
            int i3 = 0;
            while (it2.hasNext()) {
                Bundle next = it2.next();
                if (i2 == i3) {
                    Bundle bundle = new Bundle();
                    bundle.putString("Title", next.getString("title"));
                    return bundle;
                }
                int size = i3 + next.getParcelableArrayList("items").size();
                if (i2 <= size) {
                    int size2 = (i2 - (size - next.getParcelableArrayList("items").size())) - 1;
                    Bundle bundle2 = new Bundle();
                    bundle2.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                    return bundle2;
                }
                i3 = size + 1;
            }
            return null;
        }

        /* renamed from: K4 */
        public int m72927K4(ArrayList<Bundle> arrayList) {
            int size = 0;
            if (arrayList == null) {
                return 0;
            }
            Iterator<Bundle> it2 = arrayList.iterator();
            while (it2.hasNext()) {
                size = size + it2.next().getParcelableArrayList("items").size() + 1;
            }
            return size;
        }

        /* renamed from: L4 */
        public int m72928L4(ArrayList<Bundle> arrayList) {
            Iterator<Bundle> it2 = arrayList.iterator();
            int size = 0;
            while (it2.hasNext()) {
                size += it2.next().getParcelableArrayList("items").size();
            }
            return size;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(m72839v3(m72839v3(this.f89871c5).getParcelableArrayList("items")).getString("images"), ",");
            return CompressHelper.m71754h1(this.f89866X4, strArrSplitByWholeSeparator[0] + ".jpg", "Large-Encrypted");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            String str;
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
            this.f89579Q4 = new CompressHelper(m15366r());
            this.f89565C4 = viewInflate;
            this.f89866X4 = m15387y().getBundle("DB");
            this.f89870b5 = new Bundle();
            this.f89867Y4 = m15387y().getString("URL").replace("ddx-", "");
            this.f89868Z4 = m15387y().getString("moduleId");
            final CompressHelper compressHelper = new CompressHelper(m15366r());
            if (bundle != null && bundle.containsKey("items")) {
                this.f89871c5 = bundle.getParcelableArrayList("items");
            }
            final int length = StringUtils.splitByWholeSeparator(this.f89867Y4, ",").length;
            final RecyclerView.Adapter adapter = new RecyclerView.Adapter() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.1
                @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                /* renamed from: C */
                public int mo26845C(int i2) {
                    VDDxResultsFragment vDDxResultsFragment = VDDxResultsFragment.this;
                    return vDDxResultsFragment.m72926I4(i2, vDDxResultsFragment.f89871c5).containsKey("Title") ? 0 : 1;
                }

                @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                /* renamed from: R */
                public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
                    String str2;
                    VDDxResultsFragment vDDxResultsFragment = VDDxResultsFragment.this;
                    Bundle bundleM72926I4 = vDDxResultsFragment.m72926I4(i2, vDDxResultsFragment.f89871c5);
                    if (bundleM72926I4.containsKey("Title")) {
                        boolean zEquals = bundleM72926I4.getString("Title").equals(String.valueOf(length));
                        TextView textView = ((HeaderPlaceHolder) viewHolder).f89894I;
                        if (zEquals) {
                            str2 = "Matched All Findings";
                        } else {
                            str2 = "Matched " + bundleM72926I4.getString("Title") + " Findings";
                        }
                        textView.setText(str2);
                        return;
                    }
                    final PhotoCaptionWarningPlaceHolder photoCaptionWarningPlaceHolder = (PhotoCaptionWarningPlaceHolder) viewHolder;
                    Bundle bundle2 = bundleM72926I4.getBundle("Item");
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(bundle2.getString("images"), ",");
                    final String strM71754h1 = CompressHelper.m71754h1(VDDxResultsFragment.this.f89866X4, strArrSplitByWholeSeparator[0] + ".jpg", "Medium");
                    String string = bundle2.getString("diagnosisTitle");
                    if (VDDxResultsFragment.this.f89870b5.containsKey(strM71754h1)) {
                        photoCaptionWarningPlaceHolder.f89898L.setRippleColor(VDDxResultsFragment.this.f89870b5.getInt(strM71754h1));
                    } else {
                        VDDxResultsFragment.this.m72832q3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.1.1
                            @Override // java.lang.Runnable
                            public void run() {
                                Palette.Swatch swatchM26489C = Palette.m26478b(BitmapFactory.decodeFile(strM71754h1)).m26518g().m26489C();
                                if (swatchM26489C == null) {
                                    return;
                                }
                                int iM26530e = swatchM26489C.m26530e();
                                if (VDDxResultsFragment.this.f89870b5.containsKey(strM71754h1)) {
                                    return;
                                }
                                VDDxResultsFragment.this.f89870b5.putInt(strM71754h1, iM26530e);
                            }
                        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.1.2
                            @Override // java.lang.Runnable
                            public void run() {
                                photoCaptionWarningPlaceHolder.f89898L.setRippleColor(VDDxResultsFragment.this.f89870b5.getInt(strM71754h1));
                            }
                        });
                    }
                    photoCaptionWarningPlaceHolder.f89895I.setText(string);
                    Glide.m30041G(VDDxResultsFragment.this.m15366r()).mo30124i(new File(strM71754h1)).m30165B2(photoCaptionWarningPlaceHolder.f89896J);
                    if (bundle2.getString("severity").equals(ExifInterface.f16317Y4)) {
                        photoCaptionWarningPlaceHolder.f89897K.setVisibility(0);
                    } else {
                        photoCaptionWarningPlaceHolder.f89897K.setVisibility(4);
                    }
                    final String string2 = bundle2.getString("diagnosesModulesId");
                    photoCaptionWarningPlaceHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.1.3
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            String str3 = string2;
                            String strSubstring = str3.substring(0, str3.length() - VDDxResultsFragment.this.f89868Z4.length());
                            if (((iMD) VDDxResultsFragment.this.m15366r().getApplicationContext()).f101678s != null) {
                                new CompressHelper(VDDxResultsFragment.this.m15366r()).m71772A1((Bundle) new ArrayList(Collections2.m42365d(((iMD) VDDxResultsFragment.this.m15366r().getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.1.3.1
                                    @Override // com.google.common.base.Predicate
                                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                                    public boolean apply(Bundle bundle3) {
                                        return bundle3.getString("Type").equals("visualdx");
                                    }
                                })).get(0), strSubstring, null, null);
                            }
                        }
                    });
                }

                @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                /* renamed from: T */
                public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup2, int i2) {
                    return mo26845C(i2) == 0 ? new HeaderPlaceHolder(LayoutInflater.from(VDDxResultsFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup2, false)) : new PhotoCaptionWarningPlaceHolder(LayoutInflater.from(VDDxResultsFragment.this.m15366r()).inflate(C5562R.layout.grid_view_item_image_caption_danger, viewGroup2, false));
                }

                @Override // androidx.recyclerview.widget.RecyclerView.Adapter
                /* renamed from: b */
                public int mo26171b() {
                    VDDxResultsFragment vDDxResultsFragment = VDDxResultsFragment.this;
                    ArrayList<Bundle> arrayList = vDDxResultsFragment.f89871c5;
                    if (arrayList == null) {
                        return 0;
                    }
                    return vDDxResultsFragment.m72927K4(arrayList);
                }
            };
            final RecyclerView recyclerView = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
            this.f89869a5 = recyclerView;
            if (this.f89871c5 == null) {
                if (this.f89867Y4.length() > 0) {
                    str = "SELECT diagnosesModulesId,count(*) c, diagnosisTitle, images, severity, sort FROM diagnosesmodulesfindings Where moduleId=" + this.f89868Z4 + " AND findingId IN(" + this.f89867Y4 + ") GROUP BY diagnosesModulesId having c > " + ((StringUtils.splitByWholeSeparator(this.f89867Y4, ",").length / 2) - 1) + " order by c desc, sort asc, diagnosisTitle asc";
                } else {
                    str = "SELECT diagnosesModulesId,0 c, diagnosisTitle, images, severity, sort FROM diagnosesmodulesfindings Where moduleId=" + this.f89868Z4 + " GROUP BY diagnosesModulesId order by sort asc, diagnosisTitle asc";
                }
                final String str2 = str;
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.2

                    /* renamed from: a */
                    private final ProgressDialog f89882a;

                    {
                        this.f89882a = new ProgressDialog(VDDxResultsFragment.this.m15366r());
                    }

                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        VDDxResultsFragment vDDxResultsFragment = VDDxResultsFragment.this;
                        CompressHelper compressHelper2 = compressHelper;
                        vDDxResultsFragment.f89871c5 = compressHelper2.m71887r2(compressHelper2.m71817V(vDDxResultsFragment.f89866X4, str2), "c");
                        return null;
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        if (this.f89882a.isShowing()) {
                            this.f89882a.dismiss();
                        }
                        VDDxResultsFragment vDDxResultsFragment = VDDxResultsFragment.this;
                        if (vDDxResultsFragment.f89871c5 == null) {
                            vDDxResultsFragment.m15366r().setTitle("Nothing Found !");
                            VDDxResultsFragment vDDxResultsFragment2 = VDDxResultsFragment.this;
                            vDDxResultsFragment2.f89568F4 = "Nothing Found !";
                            vDDxResultsFragment2.m72829n4("Nothing Found !");
                            VDDxResultsFragment.this.mo71972o4();
                            return;
                        }
                        StringBuilder sb = new StringBuilder();
                        VDDxResultsFragment vDDxResultsFragment3 = VDDxResultsFragment.this;
                        sb.append(vDDxResultsFragment3.m72928L4(vDDxResultsFragment3.f89871c5));
                        sb.append(" Diagnosis Found !");
                        vDDxResultsFragment.f89568F4 = sb.toString();
                        VDDxResultsFragment.this.m15366r().setTitle(VDDxResultsFragment.this.f89568F4);
                        VDDxResultsFragment vDDxResultsFragment4 = VDDxResultsFragment.this;
                        vDDxResultsFragment4.m72829n4(vDDxResultsFragment4.f89568F4);
                        VDDxResultsFragment.this.mo71972o4();
                        recyclerView.setAdapter(adapter);
                        VDDxResultsFragment.this.m72792M2();
                    }

                    @Override // android.os.AsyncTask
                    protected void onPreExecute() {
                        this.f89882a.setMessage("Searching");
                        this.f89882a.show();
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
            }
            final GridAutoFitLayoutManager gridAutoFitLayoutManager = new GridAutoFitLayoutManager(m15366r(), (int) (m15320b0().getDisplayMetrics().density * 100.0f));
            gridAutoFitLayoutManager.m27031R3(new GridLayoutManager.SpanSizeLookup() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.3
                @Override // androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
                /* renamed from: f */
                public int mo27056f(int i2) {
                    if (recyclerView.getAdapter().mo26845C(i2) == 0) {
                        return gridAutoFitLayoutManager.f101458b0;
                    }
                    return 1;
                }
            });
            recyclerView.setLayoutManager(gridAutoFitLayoutManager);
            recyclerView.setItemAnimator(new DefaultItemAnimator());
            this.f89565C4 = viewInflate;
            this.f89574L4 = (Toolbar) viewInflate.findViewById(C5562R.id.toolbar);
            mo72642f3(C5562R.menu.empty);
            m15358o2(false);
            m72786G3();
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: o4 */
        public void mo71972o4() {
            new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.5

                /* renamed from: a */
                byte[] f89892a;

                @Override // android.os.AsyncTask
                protected Object doInBackground(Object[] objArr) {
                    try {
                        File file = new File(VDDxResultsFragment.this.mo71955R2());
                        this.f89892a = new CompressHelper(VDDxResultsFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127");
                        return null;
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                        return null;
                    }
                }

                @Override // android.os.AsyncTask
                protected void onPostExecute(Object obj) {
                    super.onPostExecute(obj);
                    Glide.m30041G(VDDxResultsFragment.this.m15366r()).mo30123h(this.f89892a).m30165B2(VDDxResultsFragment.this.f89575M4);
                }
            }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
        }

        @Override // androidx.fragment.app.Fragment, android.content.ComponentCallbacks
        public void onConfigurationChanged(Configuration configuration) {
            super.onConfigurationChanged(configuration);
            this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDxResults.VDDxResultsFragment.4
                @Override // java.lang.Runnable
                public void run() {
                    VDDxResultsFragment.this.f89869a5.getAdapter().m27491G();
                }
            }, 500L);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: v4 */
        public boolean mo71959v4() {
            return false;
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new VDDxResultsFragment());
    }
}

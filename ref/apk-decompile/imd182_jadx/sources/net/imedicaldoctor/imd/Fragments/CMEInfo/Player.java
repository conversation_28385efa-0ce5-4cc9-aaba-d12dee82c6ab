package net.imedicaldoctor.imd.Fragments.CMEInfo;

import android.app.ActionBar;
import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.support.v4.media.session.PlaybackStateCompat;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Toast;
import androidx.annotation.Nullable;
import androidx.annotation.OptIn;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.media3.common.MediaItem;
import androidx.media3.common.PlaybackParameters;
import androidx.media3.common.util.UnstableApi;
import androidx.media3.datasource.AesFlushingCipher;
import androidx.media3.datasource.C1252c;
import androidx.media3.datasource.DataSource;
import androidx.media3.datasource.DataSpec;
import androidx.media3.datasource.TransferListener;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.exoplayer.source.ProgressiveMediaSource;
import androidx.media3.p004ui.PlayerView;
import com.google.common.net.HttpHeaders;
import com.itextpdf.text.pdf.codec.wmf.MetaDo;
import java.io.ByteArrayInputStream;
import java.io.EOFException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.io.SequenceInputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class Player extends AppCompatActivity {

    /* renamed from: I3 */
    public static ExoPlayer f87654I3;

    /* renamed from: A3 */
    long f87655A3;

    /* renamed from: B3 */
    String f87656B3;

    /* renamed from: C3 */
    private float f87657C3;

    /* renamed from: D3 */
    float f87658D3 = 1.0f;

    /* renamed from: E3 */
    float f87659E3 = 1.2f;

    /* renamed from: F3 */
    float f87660F3 = 1.5f;

    /* renamed from: G3 */
    float f87661G3 = 2.0f;

    /* renamed from: H3 */
    float f87662H3 = 3.0f;

    /* renamed from: y3 */
    PlayerView f87663y3;

    /* renamed from: z3 */
    Bundle f87664z3;

    @UnstableApi
    public class InputStreamDataSource implements DataSource {

        /* renamed from: b */
        private final Context f87670b;

        /* renamed from: c */
        private final DataSpec f87671c;

        /* renamed from: d */
        private InputStream f87672d;

        /* renamed from: e */
        private long f87673e;

        /* renamed from: f */
        private boolean f87674f;

        /* renamed from: g */
        private byte[] f87675g;

        /* renamed from: h */
        private byte[] f87676h;

        /* renamed from: i */
        private CompressHelper f87677i;

        public InputStreamDataSource(Context context, DataSpec dataSpec) {
            this.f87670b = context;
            this.f87671c = dataSpec;
        }

        /* renamed from: t */
        private InputStream m72025t(Context context, Uri uri) throws IOException {
            try {
                Log.e("ConvertUri", uri.getPath());
                final FileInputStream fileInputStream = new FileInputStream(new File(uri.getPath()));
                Log.e("ConvertURI", "fileInputStream available : " + fileInputStream.available());
                fileInputStream.available();
                final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.f87676h);
                SequenceInputStream sequenceInputStream = new SequenceInputStream(byteArrayInputStream, fileInputStream) { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.Player.InputStreamDataSource.1
                    @Override // java.io.SequenceInputStream, java.io.InputStream
                    public int available() throws IOException {
                        Log.e("PlayerResult", "Available : " + fileInputStream.available());
                        return fileInputStream.available() - 16;
                    }

                    @Override // java.io.InputStream
                    public long skip(long j2) throws IOException {
                        Log.e("PlayerResult", "Call Skip : " + j2);
                        if (j2 > PlaybackStateCompat.f536p3) {
                            byteArrayInputStream.skip(PlaybackStateCompat.f536p3);
                            fileInputStream.skip(j2 - PlaybackStateCompat.f536p3);
                        } else {
                            byteArrayInputStream.skip(j2);
                        }
                        Log.e("PlayerResult", "Skip : " + j2);
                        return j2;
                    }
                };
                Log.e("ConvertURI", "Combined available : " + sequenceInputStream.available());
                this.f87672d = sequenceInputStream;
                return sequenceInputStream;
            } catch (Exception e2) {
                Log.e("ConvertURI", "Error in creating inputstream");
                e2.printStackTrace();
                return null;
            }
        }

        @Override // androidx.media3.datasource.DataSource
        @OptIn(markerClass = {UnstableApi.class})
        /* renamed from: a */
        public long mo18733a(DataSpec dataSpec) throws IOException {
            this.f87675g = new byte[MetaDo.f72816x];
            this.f87676h = new byte[1024];
            this.f87677i = new CompressHelper(Player.this.getApplicationContext());
            File file = new File(dataSpec.f21142a.getPath());
            RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r");
            randomAccessFile.seek(file.length() - 1040);
            randomAccessFile.read(this.f87675g, 0, MetaDo.f72816x);
            Log.e("Datasource open", "Scratch Length : " + this.f87675g.length);
            this.f87676h = this.f87677i.m71899w(this.f87675g, "kaplan", "127");
            Log.e("Datasource open", "Scratch DE Length : " + this.f87676h.length);
            try {
                InputStream inputStreamM72025t = m72025t(this.f87670b, dataSpec.f21142a);
                this.f87672d = inputStreamM72025t;
                long jSkip = inputStreamM72025t.skip(dataSpec.f21148g);
                Log.e("Player", "Skipped : " + jSkip + " , Dataspec position: " + dataSpec.f21148g + ", DS Length: " + dataSpec.f21149h);
                if (jSkip < dataSpec.f21148g) {
                    Log.e("Player", "Skipped is lower than position");
                    throw new EOFException();
                }
                long j2 = dataSpec.f21149h;
                if (j2 != -1) {
                    this.f87673e = j2;
                } else {
                    long jAvailable = this.f87672d.available();
                    this.f87673e = jAvailable;
                    if (jAvailable == 2147483647L) {
                        this.f87673e = -1L;
                    }
                }
                this.f87673e = this.f87673e;
                Log.e("Player", "Bytes remaining " + this.f87673e);
                this.f87674f = true;
                return this.f87673e;
            } catch (IOException e2) {
                throw new IOException(e2);
            }
        }

        @Override // androidx.media3.datasource.DataSource
        /* renamed from: c */
        public Uri mo18734c() {
            return this.f87671c.f21142a;
        }

        @Override // androidx.media3.datasource.DataSource
        public void close() throws IOException {
            try {
                try {
                    InputStream inputStream = this.f87672d;
                    if (inputStream != null) {
                        inputStream.close();
                    }
                } catch (IOException e2) {
                    throw new IOException(e2);
                }
            } finally {
                this.f87672d = null;
                if (this.f87674f) {
                    this.f87674f = false;
                }
            }
        }

        @Override // androidx.media3.datasource.DataSource
        /* renamed from: e */
        public void mo18735e(TransferListener transferListener) {
        }

        @Override // androidx.media3.datasource.DataSource
        public /* synthetic */ Map getResponseHeaders() {
            return C1252c.m18912a(this);
        }

        @Override // androidx.media3.common.DataReader
        public int read(byte[] bArr, int i2, int i3) throws IOException {
            Log.e("Datasource Read", "offset: " + i2 + " , Readlength : " + i3);
            if (i3 == 0) {
                return 0;
            }
            long j2 = this.f87673e;
            if (j2 == 0) {
                return -1;
            }
            if (j2 != -1) {
                try {
                    i3 = (int) Math.min(j2, i3);
                } catch (IOException e2) {
                    throw new IOException(e2);
                }
            }
            int i4 = this.f87672d.read(bArr, i2, i3);
            if (i4 == -1) {
                if (this.f87673e == -1) {
                    return -1;
                }
                throw new IOException(new EOFException());
            }
            long j3 = this.f87673e;
            if (j3 != -1) {
                this.f87673e = j3 - i4;
            }
            return i4;
        }
    }

    @UnstableApi
    public final class iMDDataSource implements DataSource {

        /* renamed from: b */
        private final DataSource f87682b;

        /* renamed from: c */
        private final byte[] f87683c = new byte[MetaDo.f72816x];

        /* renamed from: d */
        private byte[] f87684d = new byte[1024];

        /* renamed from: e */
        private final String f87685e;

        /* renamed from: f */
        private final CompressHelper f87686f;

        /* renamed from: g */
        @Nullable
        private AesFlushingCipher f87687g;

        public iMDDataSource(DataSource dataSource, String str) {
            this.f87682b = dataSource;
            this.f87686f = new CompressHelper(Player.this.getApplicationContext());
            this.f87685e = str;
        }

        @Override // androidx.media3.datasource.DataSource
        @OptIn(markerClass = {UnstableApi.class})
        /* renamed from: a */
        public long mo18733a(DataSpec dataSpec) throws IOException {
            Log.e("Datasource open", "URI " + this.f87685e);
            File file = new File(this.f87685e);
            RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r");
            randomAccessFile.seek(file.length() - 1040);
            randomAccessFile.read(this.f87683c, 0, MetaDo.f72816x);
            long jMo18733a = this.f87682b.mo18733a(dataSpec);
            Log.e("Datasource open", "DataLength : " + jMo18733a);
            Log.e("Datasource open", "Scratch Length : " + this.f87683c.length);
            this.f87684d = this.f87686f.m71899w(this.f87683c, "kaplan", "127");
            Log.e("Datasource open", "Scratch DE Length : " + this.f87684d.length);
            return jMo18733a - 16;
        }

        @Override // androidx.media3.datasource.DataSource
        /* renamed from: c */
        public Uri mo18734c() {
            return Uri.fromFile(Environment.getExternalStorageDirectory());
        }

        @Override // androidx.media3.datasource.DataSource
        public void close() throws IOException {
            this.f87687g = null;
            this.f87682b.close();
        }

        @Override // androidx.media3.datasource.DataSource
        /* renamed from: e */
        public void mo18735e(TransferListener transferListener) {
            this.f87682b.mo18735e(transferListener);
        }

        @Override // androidx.media3.datasource.DataSource
        public Map<String, List<String>> getResponseHeaders() {
            return this.f87682b.getResponseHeaders();
        }

        @Override // androidx.media3.common.DataReader
        public int read(byte[] bArr, int i2, int i3) throws IOException {
            Log.e("Datasource Read", "offset: " + i2 + " , Readlength : " + i3);
            if (i3 == 0) {
                return 0;
            }
            if (i2 >= 1024) {
                return this.f87682b.read(bArr, i2, 1024);
            }
            int i4 = 1024 - i2;
            System.arraycopy(this.f87684d, i2, bArr, i2, i4);
            return i4;
        }
    }

    @UnstableApi
    public final class iMDDataSourceFactory implements DataSource.Factory {

        /* renamed from: a */
        private final DataSource f87689a;

        /* renamed from: b */
        private final String f87690b;

        public iMDDataSourceFactory(DataSource dataSource, String str) {
            this.f87689a = dataSource;
            this.f87690b = str;
        }

        @Override // androidx.media3.datasource.DataSource.Factory
        /* renamed from: a */
        public DataSource mo18750a() {
            return Player.this.new iMDDataSource(this.f87689a, this.f87690b);
        }
    }

    /* renamed from: b1 */
    private void m72019b1() {
        ExoPlayer exoPlayer = f87654I3;
        if (exoPlayer != null) {
            exoPlayer.stop();
            f87654I3.mo16940a();
            f87654I3 = null;
        }
    }

    /* renamed from: c1 */
    private void m72020c1() {
        Bundle bundle;
        StringBuilder sb;
        Log.e("SaveLocation", "Starting");
        try {
            CompressHelper compressHelper = new CompressHelper(this);
            ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(this.f87664z3, "select * from logs where id = " + this.f87656B3);
            ExoPlayer exoPlayer = f87654I3;
            if (exoPlayer == null) {
                return;
            }
            long jMo16979z2 = exoPlayer.mo16979z2();
            long jMo16931Q = f87654I3.mo16931Q();
            Log.e("SaveLocation", "Position : " + jMo16979z2);
            Log.e("SaveLocation", "Duration : " + jMo16931Q);
            String str = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
                bundle = this.f87664z3;
                sb = new StringBuilder();
                sb.append("Insert into logs values (");
                sb.append(this.f87656B3);
                sb.append(",");
                sb.append(jMo16931Q);
                sb.append(", ");
                sb.append(jMo16979z2);
                sb.append(", '");
                sb.append(str);
                sb.append("')");
            } else {
                bundle = this.f87664z3;
                sb = new StringBuilder();
                sb.append("Update logs set duration=");
                sb.append(jMo16931Q);
                sb.append(", position=");
                sb.append(jMo16979z2);
                sb.append(", vDate='");
                sb.append(str);
                sb.append("' where id = ");
                sb.append(this.f87656B3);
            }
            compressHelper.m71866m(bundle, sb.toString());
        } catch (Exception unused) {
        }
    }

    /* renamed from: Y0 */
    public void m72021Y0() {
        Context applicationContext;
        String str;
        float f2 = this.f87657C3;
        float f3 = this.f87660F3;
        if (f2 == f3) {
            this.f87657C3 = this.f87661G3;
            applicationContext = getApplicationContext();
            str = "2x";
        } else if (f2 == this.f87661G3) {
            this.f87657C3 = this.f87662H3;
            applicationContext = getApplicationContext();
            str = "3x";
        } else if (f2 == this.f87662H3) {
            this.f87657C3 = this.f87658D3;
            applicationContext = getApplicationContext();
            str = "1x";
        } else {
            float f4 = this.f87659E3;
            if (f2 != f4) {
                if (f2 == this.f87658D3) {
                    this.f87657C3 = f4;
                    applicationContext = getApplicationContext();
                    str = "1.2x";
                }
                f87654I3.mo16946f(new PlaybackParameters(this.f87657C3));
            }
            this.f87657C3 = f3;
            applicationContext = getApplicationContext();
            str = "1.5x";
        }
        Toast.makeText(applicationContext, str, 0).show();
        f87654I3.mo16946f(new PlaybackParameters(this.f87657C3));
    }

    /* renamed from: Z0 */
    public float m72022Z0() {
        return this.f87657C3;
    }

    /* renamed from: a1 */
    public boolean m72023a1() {
        return f87654I3.mo16952i() == 3 && f87654I3.mo16961m0();
    }

    /* renamed from: d1 */
    public void m72024d1(float f2) {
        this.f87657C3 = f2;
        f87654I3.mo16946f(new PlaybackParameters(f2));
    }

    @Override // androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    @OptIn(markerClass = {UnstableApi.class})
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        if (getSharedPreferences("default_preferences", 0).getBoolean("dark", false)) {
            AppCompatDelegate.m1154c0(2);
        } else {
            AppCompatDelegate.m1154c0(1);
        }
        if (getSharedPreferences("default_preferences", 0).getBoolean("wakelock", true)) {
            getWindow().addFlags(128);
        }
        setContentView(C5562R.layout.activity_player);
        String stringExtra = getIntent().getStringExtra("Address");
        this.f87664z3 = getIntent().getBundleExtra("DB");
        this.f87655A3 = getIntent().getLongExtra(HttpHeaders.f62970t0, 0L);
        this.f87656B3 = getIntent().getStringExtra("VideoID");
        this.f87663y3 = (PlayerView) findViewById(C5562R.id.exo_player_view);
        try {
            f87654I3 = new ExoPlayer.Builder(this).m19496w();
            DataSpec dataSpec = new DataSpec(Uri.parse(stringExtra));
            final InputStreamDataSource inputStreamDataSource = new InputStreamDataSource(this, dataSpec);
            try {
                inputStreamDataSource.mo18733a(dataSpec);
            } catch (IOException e2) {
                e2.printStackTrace();
            }
            ProgressiveMediaSource progressiveMediaSourceM23133i = new ProgressiveMediaSource.Factory(new DataSource.Factory() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.Player.1
                @Override // androidx.media3.datasource.DataSource.Factory
                /* renamed from: a */
                public DataSource mo18750a() {
                    return inputStreamDataSource;
                }
            }).mo21243c(MediaItem.m17075d(inputStreamDataSource.mo18734c()));
            this.f87663y3.setPlayer(f87654I3);
            f87654I3.mo19357A1(progressiveMediaSourceM23133i);
            f87654I3.mo16957k();
            m72024d1(this.f87658D3);
            f87654I3.mo16953i1(true);
            if (this.f87655A3 != 0) {
                Log.e("Player", "Going to position : " + this.f87655A3);
                f87654I3.mo16733i0(0, this.f87655A3);
            }
            final GestureDetector gestureDetector = new GestureDetector(this, new GestureDetector.SimpleOnGestureListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.Player.2
                @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
                public boolean onDoubleTap(MotionEvent motionEvent) {
                    return true;
                }

                @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
                public void onLongPress(MotionEvent motionEvent) {
                    super.onLongPress(motionEvent);
                }
            });
            this.f87663y3.setOnTouchListener(new View.OnTouchListener() { // from class: net.imedicaldoctor.imd.Fragments.CMEInfo.Player.3
                @Override // android.view.View.OnTouchListener
                public boolean onTouch(View view, MotionEvent motionEvent) {
                    return gestureDetector.onTouchEvent(motionEvent);
                }
            });
            getWindow().getDecorView().setSystemUiVisibility(4);
            ActionBar actionBar = getActionBar();
            if (actionBar != null) {
                actionBar.hide();
            }
        } catch (Exception e3) {
            Log.e("MainAcvtivity", " exoplayer error " + e3);
        }
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onDestroy() {
        super.onDestroy();
        m72020c1();
        m72019b1();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onPause() {
        super.onPause();
        m72020c1();
        m72023a1();
    }
}

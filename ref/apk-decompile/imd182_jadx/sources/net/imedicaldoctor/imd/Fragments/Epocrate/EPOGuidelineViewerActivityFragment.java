package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.view.Menu;
import android.view.MenuItem;
import android.webkit.WebView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;

/* loaded from: classes3.dex */
public class EPOGuidelineViewerActivityFragment extends ViewerHelperFragment {
    /* JADX WARN: Removed duplicated region for block: B:32:0x00f9  */
    /* JADX WARN: Removed duplicated region for block: B:33:0x00fa  */
    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public android.view.View mo15303U0(android.view.LayoutInflater r9, android.view.ViewGroup r10, android.os.Bundle r11) throws android.content.res.Resources.NotFoundException {
        /*
            Method dump skipped, instructions count: 347
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Epocrate.EPOGuidelineViewerActivityFragment.mo15303U0(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
        menu.removeItem(C5562R.id.action_menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        this.f89579Q4.m71800N1(this.f89566D4, str);
        return true;
    }
}

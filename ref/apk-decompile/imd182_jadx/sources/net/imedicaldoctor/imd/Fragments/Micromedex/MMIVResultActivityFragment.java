package net.imedicaldoctor.imd.Fragments.Micromedex;

import android.app.ProgressDialog;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.android.material.tabs.TabLayout;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextImageArrowViewHolder;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MMIVResultActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f88486X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88487Y4;

    /* renamed from: Z4 */
    public Bundle f88488Z4;

    /* renamed from: a5 */
    public ArrayList<String> f88489a5;

    /* renamed from: b5 */
    public NotStickySectionAdapter f88490b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f88491c5;

    /* renamed from: d5 */
    public ArrayList<Bundle> f88492d5;

    /* renamed from: e5 */
    public ArrayList<Bundle> f88493e5;

    /* renamed from: f5 */
    public ArrayList<Bundle> f88494f5;

    /* renamed from: g5 */
    public ArrayList<Bundle> f88495g5;

    /* renamed from: h5 */
    public ArrayList<Bundle> f88496h5;

    /* renamed from: i5 */
    public TabLayout f88497i5;

    /* renamed from: j5 */
    public String f88498j5;

    /* renamed from: k5 */
    public String f88499k5;

    /* renamed from: l5 */
    public NotStickySectionAdapter f88500l5;

    /* renamed from: m5 */
    public ChaptersAdapter f88501m5;

    /* renamed from: n5 */
    public ChaptersAdapter f88502n5;

    /* renamed from: I4 */
    public void m72369I4(String str) {
        TabLayout.Tab tabM40228I = this.f88497i5.m40228I();
        tabM40228I.m40276D(str);
        this.f88497i5.m40248i(tabM40228I);
    }

    /* renamed from: J4 */
    public void m72370J4() {
        this.f88486X4.setItemAnimator(new DefaultItemAnimator());
        this.f88486X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88486X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: K4 */
    public int m72371K4(String str) {
        return str.equals("C") ? C5562R.drawable.iv_compat_compatible : str.equals("I") ? C5562R.drawable.iv_compat_incompatible : str.equals("U") ? C5562R.drawable.iv_compat_uncertain : str.equals("N") ? C5562R.drawable.iv_compat_nottested : str.equals(ExifInterface.f16308X4) ? C5562R.drawable.iv_compat_cautionvariable : C5562R.drawable.placeholder;
    }

    /* renamed from: L4 */
    public void m72372L4() {
        final ProgressDialog progressDialog = new ProgressDialog(m15366r());
        progressDialog.setIndeterminate(true);
        progressDialog.setMessage("Loading");
        progressDialog.show();
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.3
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment.f88495g5 = mMIVResultActivityFragment.f89579Q4.m71817V(mMIVResultActivityFragment.f89566D4, "SELECT * from v_multi_ysite_summary");
                MMIVResultActivityFragment mMIVResultActivityFragment2 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment2.f88496h5 = mMIVResultActivityFragment2.f89579Q4.m71817V(mMIVResultActivityFragment2.f89566D4, "Select * from v_multi_admix_summary");
                observableEmitter.onNext("Completed");
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.4
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                progressDialog.hide();
                MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment.f88501m5.m73465g0(mMIVResultActivityFragment.f88495g5);
                MMIVResultActivityFragment mMIVResultActivityFragment2 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment2.f88502n5.m73465g0(mMIVResultActivityFragment2.f88496h5);
                MMIVResultActivityFragment mMIVResultActivityFragment3 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment3.f88486X4.setAdapter(mMIVResultActivityFragment3.f88501m5);
            }
        });
    }

    /* renamed from: M4 */
    public void m72373M4() {
        final ProgressDialog progressDialog = new ProgressDialog(m15366r());
        progressDialog.setIndeterminate(true);
        progressDialog.setMessage("Loading");
        progressDialog.show();
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.1
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment.f88493e5 = mMIVResultActivityFragment.f89579Q4.m71817V(mMIVResultActivityFragment.f89566D4, "SELECT sol.solution_id solution_id, CASE WHEN commSol.display_name IS NULL THEN sol.title ELSE commSol.display_name END title, res.result, CASE WHEN commSol.display_name IS NULL THEN 1 ELSE 0 END grouper, CASE WHEN commSol.sorter IS NULL THEN UPPER(sol.title) ELSE commSol.sorter END sorter, 1 show_view_button FROM iv_drug_solution_idx sol LEFT JOIN lookup_common_solutions commSol ON sol.solution_id = commSol.id, sv_solution_result res, iv_mono_solution_idx msi, iv_mono_agent_int mai WHERE sol.solution_id = res.solution_id AND msi.iv_id = mai.iv_id AND sol.solution_id = mai.agent_id GROUP BY sol.solution_id UNION ALL SELECT id solution_id, display_name title, 'N' result, 0 grouper, sorter sorter, 0 show_view_button FROM lookup_common_solutions WHERE id NOT IN (SELECT solution_id FROM sv_solution_result) ORDER BY grouper, sorter");
                MMIVResultActivityFragment mMIVResultActivityFragment2 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment2.f88494f5 = mMIVResultActivityFragment2.f89579Q4.m71887r2(mMIVResultActivityFragment2.f88493e5, "grouper");
                MMIVResultActivityFragment mMIVResultActivityFragment3 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment3.f88495g5 = mMIVResultActivityFragment3.f89579Q4.m71817V(mMIVResultActivityFragment3.f89566D4, "Select * from v_ysite_summary");
                MMIVResultActivityFragment mMIVResultActivityFragment4 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment4.f88496h5 = mMIVResultActivityFragment4.f89579Q4.m71817V(mMIVResultActivityFragment4.f89566D4, "Select * from v_admix_summary");
                observableEmitter.onNext("Completed");
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.2
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                progressDialog.hide();
                MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment.f88500l5.m73475h0(mMIVResultActivityFragment.f88494f5);
                MMIVResultActivityFragment mMIVResultActivityFragment2 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment2.f88501m5.m73465g0(mMIVResultActivityFragment2.f88495g5);
                MMIVResultActivityFragment mMIVResultActivityFragment3 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment3.f88502n5.m73465g0(mMIVResultActivityFragment3.f88496h5);
                MMIVResultActivityFragment mMIVResultActivityFragment4 = MMIVResultActivityFragment.this;
                mMIVResultActivityFragment4.f88486X4.setAdapter(mMIVResultActivityFragment4.f88500l5);
            }
        });
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer_tab, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88486X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(this.f89567E4.replace("interactresult-", ""), ";;;;;");
        TabLayout tabLayout = (TabLayout) this.f89565C4.findViewById(C5562R.id.tabs);
        this.f88497i5 = tabLayout;
        tabLayout.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.5
            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: a */
            public void mo40255a(TabLayout.Tab tab) {
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: b */
            public void mo40256b(TabLayout.Tab tab) {
                RecyclerView recyclerView;
                RecyclerView.Adapter adapter;
                String string = tab.m40287n().toString();
                if (string.equals("Solutions")) {
                    MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                    recyclerView = mMIVResultActivityFragment.f88486X4;
                    adapter = mMIVResultActivityFragment.f88500l5;
                } else if (string.equals("Y-Site")) {
                    MMIVResultActivityFragment mMIVResultActivityFragment2 = MMIVResultActivityFragment.this;
                    recyclerView = mMIVResultActivityFragment2.f88486X4;
                    adapter = mMIVResultActivityFragment2.f88501m5;
                } else {
                    if (!string.equals("Admix")) {
                        return;
                    }
                    MMIVResultActivityFragment mMIVResultActivityFragment3 = MMIVResultActivityFragment.this;
                    recyclerView = mMIVResultActivityFragment3.f88486X4;
                    adapter = mMIVResultActivityFragment3.f88502n5;
                }
                recyclerView.setAdapter(adapter);
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: c */
            public void mo40257c(TabLayout.Tab tab) {
            }
        });
        if (strArrSplitByWholeSeparator.length == 1) {
            m72369I4("Solutions");
            m72369I4("Y-Site");
            m72369I4("Admix");
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(strArrSplitByWholeSeparator[0], ",,,,,");
            this.f88498j5 = strArrSplitByWholeSeparator2[0];
            this.f88499k5 = strArrSplitByWholeSeparator2[1];
            this.f89579Q4.m71866m(this.f89566D4, "Update app_state set value=" + strArrSplitByWholeSeparator2[0] + ", title='" + strArrSplitByWholeSeparator2[1] + "' where key='current_agent_id'");
            this.f89568F4 = strArrSplitByWholeSeparator2[1];
            m72373M4();
            int i2 = C5562R.layout.list_view_item_ripple_text_image_arrow;
            NotStickySectionAdapter notStickySectionAdapter = new NotStickySectionAdapter(m15366r(), this.f88494f5, "title", i2) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.6
                @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                /* renamed from: f0 */
                public void mo72200f0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i3) {
                    RippleTextImageArrowViewHolder rippleTextImageArrowViewHolder = (RippleTextImageArrowViewHolder) viewHolder;
                    rippleTextImageArrowViewHolder.f101508I.setText(bundle2.getString("title"));
                    rippleTextImageArrowViewHolder.f101509J.setImageDrawable(MMIVResultActivityFragment.this.m15320b0().getDrawable(MMIVResultActivityFragment.this.m72371K4(bundle2.getString("res.result"))));
                    if (bundle2.getString("res.result").equals("N")) {
                        rippleTextImageArrowViewHolder.f101510K.setVisibility(8);
                        rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.6.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                CompressHelper.m71767x2(MMIVResultActivityFragment.this.m15366r(), "Not Tested", 1);
                            }
                        });
                    } else {
                        rippleTextImageArrowViewHolder.f101510K.setVisibility(0);
                        rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.6.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                String str = "doc-solution,,," + MMIVResultActivityFragment.this.f88498j5 + ",,," + MMIVResultActivityFragment.this.f88499k5 + ",,," + bundle2.getString("solution_id") + ",,," + bundle2.getString("title");
                                MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                                mMIVResultActivityFragment.f89579Q4.m71772A1(mMIVResultActivityFragment.f89566D4, str, null, null);
                            }
                        });
                    }
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                /* renamed from: i0 */
                public String mo72201i0(String str) {
                    return str.equals("0") ? "Common Solutions" : str.equals(IcyHeaders.f28171a3) ? "Other Solutions" : "";
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                /* renamed from: k0 */
                public RecyclerView.ViewHolder mo72202k0(View view2) {
                    return new RippleTextImageArrowViewHolder(view2);
                }
            };
            this.f88500l5 = notStickySectionAdapter;
            notStickySectionAdapter.f101470i = "No Drug-Solution Combination Have Been Tested";
            ChaptersAdapter chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88495g5, "adfs", i2) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.7
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: e0 */
                public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i3) {
                    RippleTextImageArrowViewHolder rippleTextImageArrowViewHolder = (RippleTextImageArrowViewHolder) viewHolder;
                    rippleTextImageArrowViewHolder.f101508I.setText(bundle2.getString("title"));
                    rippleTextImageArrowViewHolder.f101509J.setImageDrawable(MMIVResultActivityFragment.this.m15320b0().getDrawable(MMIVResultActivityFragment.this.m72371K4(bundle2.getString("result"))));
                    if (bundle2.getString("result").equals("N")) {
                        rippleTextImageArrowViewHolder.f101510K.setVisibility(8);
                        rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.7.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                CompressHelper.m71767x2(MMIVResultActivityFragment.this.m15366r(), "Not Tested", 1);
                            }
                        });
                    } else {
                        rippleTextImageArrowViewHolder.f101510K.setVisibility(0);
                        rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.7.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                String str = "doc-ysite,,," + MMIVResultActivityFragment.this.f88498j5 + ",,," + MMIVResultActivityFragment.this.f88499k5 + ",,," + bundle2.getString("value_id") + ",,," + bundle2.getString("title");
                                MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                                mMIVResultActivityFragment.f89579Q4.m71772A1(mMIVResultActivityFragment.f89566D4, str, null, null);
                            }
                        });
                    }
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: h0 */
                public RecyclerView.ViewHolder mo71986h0(View view2) {
                    return new RippleTextImageArrowViewHolder(view2);
                }
            };
            this.f88501m5 = chaptersAdapter;
            chaptersAdapter.f101434h = "No Drug-Drug Combination Have Been Tested";
            ChaptersAdapter chaptersAdapter2 = new ChaptersAdapter(m15366r(), this.f88496h5, "adfs", i2) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.8
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: e0 */
                public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i3) {
                    RippleTextImageArrowViewHolder rippleTextImageArrowViewHolder = (RippleTextImageArrowViewHolder) viewHolder;
                    rippleTextImageArrowViewHolder.f101508I.setText(bundle2.getString("title"));
                    rippleTextImageArrowViewHolder.f101509J.setImageDrawable(MMIVResultActivityFragment.this.m15320b0().getDrawable(MMIVResultActivityFragment.this.m72371K4(bundle2.getString("result"))));
                    if (bundle2.getString("result").equals("N")) {
                        rippleTextImageArrowViewHolder.f101510K.setVisibility(8);
                        rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.8.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                CompressHelper.m71767x2(MMIVResultActivityFragment.this.m15366r(), "Not Tested", 1);
                            }
                        });
                    } else {
                        rippleTextImageArrowViewHolder.f101510K.setVisibility(0);
                        rippleTextImageArrowViewHolder.f101511L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.8.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                String str = "doc-admix,,," + MMIVResultActivityFragment.this.f88498j5 + ",,," + MMIVResultActivityFragment.this.f88499k5 + ",,," + bundle2.getString("value_id") + ",,," + bundle2.getString("title");
                                MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                                mMIVResultActivityFragment.f89579Q4.m71772A1(mMIVResultActivityFragment.f89566D4, str, null, null);
                            }
                        });
                    }
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: h0 */
                public RecyclerView.ViewHolder mo71986h0(View view2) {
                    return new RippleTextImageArrowViewHolder(view2);
                }
            };
            this.f88502n5 = chaptersAdapter2;
            chaptersAdapter2.f101434h = "No Drug-Drug Combination Have Been Tested";
            this.f88486X4.setAdapter(this.f88500l5);
        } else {
            m72369I4("Y-Site");
            m72369I4("Admix");
            this.f89579Q4.m71866m(this.f89566D4, "Delete from selected_agents");
            for (String str : strArrSplitByWholeSeparator) {
                String[] strArrSplitByWholeSeparator3 = StringUtils.splitByWholeSeparator(str, ",,,,,");
                String str2 = strArrSplitByWholeSeparator3[0];
                String str3 = strArrSplitByWholeSeparator3[1];
                String[] strArrSplitByWholeSeparator4 = StringUtils.splitByWholeSeparator(strArrSplitByWholeSeparator3[2], "-");
                this.f89579Q4.m71866m(this.f89566D4, "Insert into selected_agents values (" + str2 + "," + strArrSplitByWholeSeparator4[0] + ", '" + str3 + "', 0, " + strArrSplitByWholeSeparator4[1] + ")");
            }
            this.f89568F4 = "Interaction Result";
            m72372L4();
            int i3 = C5562R.layout.list_view_item_ripple_text_full_interact;
            ChaptersAdapter chaptersAdapter3 = new ChaptersAdapter(m15366r(), this.f88495g5, "title", i3) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.9
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: e0 */
                public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i4) {
                    RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                    final String[] strArrSplitByWholeSeparator5 = StringUtils.splitByWholeSeparator(bundle2.getString("title"), " - ");
                    rippleTextFullViewHolder.f101499I.setText(strArrSplitByWholeSeparator5[0]);
                    rippleTextFullViewHolder.f101500J.setText(strArrSplitByWholeSeparator5[1]);
                    rippleTextFullViewHolder.f101501K.setImageDrawable(MMIVResultActivityFragment.this.m15320b0().getDrawable(MMIVResultActivityFragment.this.m72371K4(bundle2.getString("result"))));
                    rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.9.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            String str4 = "doc-ysite,,," + bundle2.getString("agent_id") + ",,," + strArrSplitByWholeSeparator5[0] + ",,," + bundle2.getString("drug2_id") + ",,," + strArrSplitByWholeSeparator5[1];
                            MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                            mMIVResultActivityFragment.f89579Q4.m71772A1(mMIVResultActivityFragment.f89566D4, str4, null, null);
                        }
                    });
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: h0 */
                public RecyclerView.ViewHolder mo71986h0(View view2) {
                    return new RippleTextFullViewHolder(view2);
                }
            };
            this.f88501m5 = chaptersAdapter3;
            chaptersAdapter3.f101434h = "No Drug-Drug Combination Have Been Tested";
            ChaptersAdapter chaptersAdapter4 = new ChaptersAdapter(m15366r(), this.f88495g5, "title", i3) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.10
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: e0 */
                public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i4) {
                    RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                    final String[] strArrSplitByWholeSeparator5 = StringUtils.splitByWholeSeparator(bundle2.getString("title"), " - ");
                    rippleTextFullViewHolder.f101499I.setText(strArrSplitByWholeSeparator5[0]);
                    rippleTextFullViewHolder.f101500J.setText(strArrSplitByWholeSeparator5[1]);
                    rippleTextFullViewHolder.f101501K.setImageDrawable(MMIVResultActivityFragment.this.m15320b0().getDrawable(MMIVResultActivityFragment.this.m72371K4(bundle2.getString("result"))));
                    rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMIVResultActivityFragment.10.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            String str4 = "doc-admix,,," + bundle2.getString("agent_id") + ",,," + strArrSplitByWholeSeparator5[0] + ",,," + bundle2.getString("drug2_id") + ",,," + strArrSplitByWholeSeparator5[1];
                            MMIVResultActivityFragment mMIVResultActivityFragment = MMIVResultActivityFragment.this;
                            mMIVResultActivityFragment.f89579Q4.m71772A1(mMIVResultActivityFragment.f89566D4, str4, null, null);
                        }
                    });
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: h0 */
                public RecyclerView.ViewHolder mo71986h0(View view2) {
                    return new RippleTextFullViewHolder(view2);
                }
            };
            this.f88502n5 = chaptersAdapter4;
            chaptersAdapter4.f101434h = "No Drug-Drug Combination Have Been Tested";
        }
        m72370J4();
        mo72642f3(C5562R.menu.favorite);
        m15358o2(false);
        m72786G3();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3;
        ArrayList<Bundle> arrayList = this.f88487Y4;
        if (arrayList == null || arrayList.size() == 0 || (bundleM72839v3 = m72839v3(this.f88487Y4)) == null) {
            return;
        }
        Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
    }
}

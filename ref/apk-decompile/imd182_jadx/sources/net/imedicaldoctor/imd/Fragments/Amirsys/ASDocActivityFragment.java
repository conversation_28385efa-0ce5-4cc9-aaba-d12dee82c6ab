package net.imedicaldoctor.imd.Fragments.Amirsys;

import android.content.Intent;
import android.content.res.Resources;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class ASDocActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f87510X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f87511Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f87512Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f87513a5;

    /* renamed from: b5 */
    public String f87514b5;

    /* renamed from: I4 */
    private void m71971I4(String str) {
        if (this.f87512Z4.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
            return;
        }
        ArrayList arrayList = new ArrayList();
        arrayList.addAll(this.f87512Z4);
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            if (((Bundle) arrayList.get(i3)).getString("id").startsWith(str)) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList = this.f87511Y4;
        if (arrayList == null || arrayList.size() <= 0) {
            return null;
        }
        Bundle bundleM72839v3 = m72839v3(this.f87511Y4);
        return CompressHelper.m71754h1(this.f89566D4, bundleM72839v3.getString("id") + ".jpg", "images-E");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASDocActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String str = ASDocActivityFragment.this.f89563A4;
                    if (str == null || str.length() == 0) {
                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(ASDocActivityFragment.this.f89567E4, ",,,");
                        ASDocActivityFragment aSDocActivityFragment = ASDocActivityFragment.this;
                        aSDocActivityFragment.f87514b5 = strArrSplitByWholeSeparator[1];
                        ArrayList<Bundle> arrayListM71817V = aSDocActivityFragment.f89579Q4.m71817V(aSDocActivityFragment.f89566D4, "Select * from topics where id='" + ASDocActivityFragment.this.f87514b5 + "'");
                        if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                            ASDocActivityFragment.this.f87510X4 = arrayListM71817V.get(0);
                            ASDocActivityFragment.this.f89568F4 = ASDocActivityFragment.this.f87510X4.getString("title") + " - " + ASDocActivityFragment.this.f87510X4.getString("category");
                            ASDocActivityFragment aSDocActivityFragment2 = ASDocActivityFragment.this;
                            aSDocActivityFragment2.f87513a5 = aSDocActivityFragment2.f89579Q4.m71817V(aSDocActivityFragment2.f89566D4, "Select * from fields where topicId='" + ASDocActivityFragment.this.f87514b5 + "'");
                            ASDocActivityFragment aSDocActivityFragment3 = ASDocActivityFragment.this;
                            aSDocActivityFragment3.f87511Y4 = aSDocActivityFragment3.f89579Q4.m71817V(aSDocActivityFragment3.f89566D4, "Select * from images where topic_id='" + ASDocActivityFragment.this.f87514b5 + "'");
                            ASDocActivityFragment aSDocActivityFragment4 = ASDocActivityFragment.this;
                            if (aSDocActivityFragment4.f87511Y4 == null) {
                                aSDocActivityFragment4.f87511Y4 = new ArrayList<>();
                            }
                            ASDocActivityFragment.this.f87512Z4 = new ArrayList<>();
                            Iterator<Bundle> it2 = ASDocActivityFragment.this.f87511Y4.iterator();
                            while (it2.hasNext()) {
                                Bundle next = it2.next();
                                Bundle bundle2 = new Bundle();
                                bundle2.putString("ImagePath", CompressHelper.m71754h1(ASDocActivityFragment.this.f89566D4, next.getString("id") + ".jpg", "images-E"));
                                bundle2.putString("id", next.getString("id"));
                                bundle2.putString("Encrypted", IcyHeaders.f28171a3);
                                bundle2.putString("DescriptionHTML2", ASDocActivityFragment.this.f89579Q4.m71773B(next.getString(HTML.Tag.f74389g), next.getString("id"), "127"));
                                bundle2.putBundle("db", ASDocActivityFragment.this.f89566D4);
                                ASDocActivityFragment.this.f87512Z4.add(bundle2);
                            }
                            ASDocActivityFragment aSDocActivityFragment5 = ASDocActivityFragment.this;
                            String strM72817d4 = aSDocActivityFragment5.m72817d4(aSDocActivityFragment5.m15366r(), "ASHeader.css");
                            ASDocActivityFragment aSDocActivityFragment6 = ASDocActivityFragment.this;
                            String strM72817d42 = aSDocActivityFragment6.m72817d4(aSDocActivityFragment6.m15366r(), "ASFooter.css");
                            String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", ASDocActivityFragment.this.f89568F4).replace("[include]", "");
                            ASDocActivityFragment aSDocActivityFragment7 = ASDocActivityFragment.this;
                            String strM71773B = aSDocActivityFragment7.f89579Q4.m71773B(aSDocActivityFragment7.f87510X4.getString(Annotation.f68283i3), ASDocActivityFragment.this.f87510X4.getString("id"), "127");
                            ASDocActivityFragment.this.f89563A4 = strReplace + strM71773B + strM72817d42;
                        }
                        ASDocActivityFragment.this.f89595p4 = "Document doesn't exist";
                        return;
                    }
                    ASDocActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    ASDocActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASDocActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = ASDocActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    ASDocActivityFragment aSDocActivityFragment = ASDocActivityFragment.this;
                    aSDocActivityFragment.m72780C4(aSDocActivityFragment.f89595p4);
                    return;
                }
                String strM71753g1 = CompressHelper.m71753g1(ASDocActivityFragment.this.f89566D4, "base");
                ASDocActivityFragment aSDocActivityFragment2 = ASDocActivityFragment.this;
                aSDocActivityFragment2.m72795O3(aSDocActivityFragment2.f89563A4, strM71753g1);
                ASDocActivityFragment.this.m72836s4();
                ASDocActivityFragment.this.m72831p4();
                ASDocActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                ASDocActivityFragment.this.m15358o2(false);
                ASDocActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            m71971I4("soheilvb");
        }
        if (itemId == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f87513a5);
            bundle.putString("TitleProperty", "fieldTitle");
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.Amirsys.ASDocActivityFragment.3

            /* renamed from: a */
            byte[] f87517a;

            @Override // android.os.AsyncTask
            protected Object doInBackground(Object[] objArr) {
                try {
                    File file = new File(ASDocActivityFragment.this.mo71955R2());
                    this.f87517a = new CompressHelper(ASDocActivityFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127");
                    return null;
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                    return null;
                }
            }

            @Override // android.os.AsyncTask
            protected void onPostExecute(Object obj) {
                super.onPostExecute(obj);
                Glide.m30041G(ASDocActivityFragment.this.m15366r()).mo30123h(this.f87517a).m30165B2(ASDocActivityFragment.this.f89575M4);
            }
        }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

package net.imedicaldoctor.imd.Fragments.TOL;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.exifinterface.media.ExifInterface;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class IGTFragment extends Fragment {

    /* renamed from: A4 */
    public int[] f89064A4;

    /* renamed from: e4 */
    public long f89065e4;

    /* renamed from: f4 */
    public TextView f89066f4;

    /* renamed from: g4 */
    public TextView f89067g4;

    /* renamed from: h4 */
    public TextView f89068h4;

    /* renamed from: i4 */
    public TextView f89069i4;

    /* renamed from: j4 */
    public LinearLayout f89070j4;

    /* renamed from: k4 */
    public LinearLayout f89071k4;

    /* renamed from: l4 */
    public LinearLayout f89072l4;

    /* renamed from: m4 */
    public LinearLayout f89073m4;

    /* renamed from: n4 */
    public RelativeLayout f89074n4;

    /* renamed from: o4 */
    public TextView f89075o4;

    /* renamed from: p4 */
    public TextView f89076p4;

    /* renamed from: q4 */
    public int f89077q4;

    /* renamed from: r4 */
    public int f89078r4;

    /* renamed from: s4 */
    public int f89079s4;

    /* renamed from: t4 */
    public int f89080t4;

    /* renamed from: u4 */
    public ImageView f89081u4;

    /* renamed from: v4 */
    public ImageView f89082v4;

    /* renamed from: w4 */
    public ImageView f89083w4;

    /* renamed from: x4 */
    public ImageView f89084x4;

    /* renamed from: y4 */
    public Long f89085y4;

    /* renamed from: z4 */
    public Random f89086z4;

    /* renamed from: J2 */
    public static IGTFragment m72518J2() {
        return new IGTFragment();
    }

    /* renamed from: K2 */
    public String m72519K2(long j2) {
        return (j2 + "").replace(IcyHeaders.f28171a3, "۱").replace(ExifInterface.f16317Y4, "۲").replace(ExifInterface.f16326Z4, "۳").replace("4", "۴").replace("5", "۵").replace("6", "۶").replace("7", "۷").replace("8", "۸").replace("9", "۹").replace("0", "۰");
    }

    /* renamed from: L2 */
    public void m72520L2() {
        final String str = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        final CompressHelper compressHelper = new CompressHelper(m15366r());
        final EditText editText = new EditText(m15366r());
        editText.setTextColor(m15320b0().getColor(C5562R.color.black));
        new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("لطفا نام و نام خانوادگی را وارد کنید").setView(editText).mo1115y("ذخیره", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.8
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
                FragmentActivity fragmentActivityM15366r;
                String str2;
                String strM71833a1 = compressHelper.m71833a1(editText.getText().toString());
                if (strM71833a1.length() == 0) {
                    fragmentActivityM15366r = IGTFragment.this.m15366r();
                    str2 = "لطفا یک نام وارد کنید";
                } else {
                    CompressHelper compressHelper2 = compressHelper;
                    compressHelper2.m71881q(compressHelper2.m71806R(), "Insert into igt values (null, '" + strM71833a1 + "', " + IGTFragment.this.f89078r4 + ", " + IGTFragment.this.f89085y4 + ", " + IGTFragment.this.f89064A4[0] + ", " + IGTFragment.this.f89064A4[1] + ", " + IGTFragment.this.f89064A4[2] + ", " + IGTFragment.this.f89064A4[3] + ", " + IGTFragment.this.f89080t4 + ", " + IGTFragment.this.f89079s4 + ", '" + str + "')");
                    fragmentActivityM15366r = IGTFragment.this.m15366r();
                    str2 = "با موفقیت ذخیره شد";
                }
                CompressHelper.m71767x2(fragmentActivityM15366r, str2, 1);
            }
        }).mo1106p("بستن", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.7
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
            }
        }).m1090I();
    }

    /* JADX WARN: Removed duplicated region for block: B:48:0x00f8  */
    /* JADX WARN: Removed duplicated region for block: B:49:0x01fa  */
    /* renamed from: M2 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m72521M2(int r13) throws java.lang.IllegalStateException, java.io.IOException, java.lang.IllegalArgumentException {
        /*
            Method dump skipped, instructions count: 536
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.m72521M2(int):void");
    }

    @Override // androidx.fragment.app.Fragment
    @Nullable
    /* renamed from: U0 */
    public View mo15303U0(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        View viewInflate = layoutInflater.inflate(C5562R.layout.igt_fragment, viewGroup, false);
        m15366r().getWindow().setFlags(1024, 1024);
        this.f89066f4 = (TextView) viewInflate.findViewById(C5562R.id.money);
        this.f89067g4 = (TextView) viewInflate.findViewById(C5562R.id.benefit);
        this.f89068h4 = (TextView) viewInflate.findViewById(C5562R.id.zarar);
        this.f89069i4 = (TextView) viewInflate.findViewById(C5562R.id.trials);
        this.f89070j4 = (LinearLayout) viewInflate.findViewById(C5562R.id.stack1);
        this.f89071k4 = (LinearLayout) viewInflate.findViewById(C5562R.id.stack2);
        this.f89072l4 = (LinearLayout) viewInflate.findViewById(C5562R.id.stack3);
        this.f89073m4 = (LinearLayout) viewInflate.findViewById(C5562R.id.stack4);
        this.f89081u4 = (ImageView) viewInflate.findViewById(C5562R.id.image1);
        this.f89082v4 = (ImageView) viewInflate.findViewById(C5562R.id.image2);
        this.f89083w4 = (ImageView) viewInflate.findViewById(C5562R.id.image3);
        this.f89084x4 = (ImageView) viewInflate.findViewById(C5562R.id.image4);
        this.f89075o4 = (TextView) viewInflate.findViewById(C5562R.id.overlay_text_1);
        this.f89076p4 = (TextView) viewInflate.findViewById(C5562R.id.overlay_text_2);
        this.f89074n4 = (RelativeLayout) viewInflate.findViewById(C5562R.id.overlay_layout);
        this.f89077q4 = 100;
        this.f89064A4 = new int[]{0, 0, 0, 0};
        this.f89079s4 = 0;
        this.f89080t4 = 0;
        this.f89067g4.setVisibility(8);
        this.f89068h4.setVisibility(8);
        this.f89074n4.setVisibility(0);
        this.f89078r4 = 2000;
        this.f89066f4.setText("موجودی شما : " + m72519K2(this.f89078r4) + " دلار");
        this.f89066f4.setVisibility(0);
        this.f89075o4.setText("به آزمون تصمیم گیری آیوا خوش آمدید");
        this.f89076p4.setText(((("در این آزمون شما یکی از ۴ دسته موجود را انتخاب می کنید\nبا انتخاب هر دسته مبلغی را برنده می شوید و گاهی مقداری جریمه می شوید که ممکن است از میزان برد شما بیشتر باشد.") + "\nشما در شروع ۲۰۰۰ دلار پول دارید و ۱۰۰ بار می توانید از بین ۴ دسته انتخاب کنید") + "\nهدف آزمون به دست آوردن بیشترین سود می باشد") + "\n\nبرای شروع کلیک کنید");
        this.f89074n4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                IGTFragment iGTFragment = IGTFragment.this;
                if (iGTFragment.f89077q4 > 0) {
                    iGTFragment.f89074n4.setVisibility(8);
                    IGTFragment.this.f89065e4 = System.currentTimeMillis();
                }
            }
        });
        this.f89074n4.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.2
            @Override // android.view.View.OnLongClickListener
            public boolean onLongClick(View view) {
                IGTFragment iGTFragment = IGTFragment.this;
                if (iGTFragment.f89077q4 != 0) {
                    return false;
                }
                iGTFragment.m72520L2();
                return true;
            }
        });
        this.f89086z4 = new Random();
        this.f89070j4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                IGTFragment.this.m72521M2(1);
            }
        });
        this.f89071k4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                IGTFragment.this.m72521M2(2);
            }
        });
        this.f89072l4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                IGTFragment.this.m72521M2(3);
            }
        });
        this.f89073m4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.TOL.IGTFragment.6
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws IllegalStateException, IOException, IllegalArgumentException {
                IGTFragment.this.m72521M2(4);
            }
        });
        return viewInflate;
    }
}

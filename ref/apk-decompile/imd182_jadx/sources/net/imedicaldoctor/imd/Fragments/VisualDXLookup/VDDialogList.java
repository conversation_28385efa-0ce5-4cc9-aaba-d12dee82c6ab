package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class VDDialogList extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f89840F4;

    /* renamed from: G4 */
    private ArrayList<Bundle> f89841G4;

    /* renamed from: H4 */
    private String f89842H4;

    /* renamed from: I4 */
    private String f89843I4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f89840F4 = m15387y().getBundle("db");
        this.f89841G4 = m15387y().getParcelableArrayList("items");
        this.f89842H4 = m15387y().getString("titleProperty");
        this.f89843I4 = m15387y().getString("type");
        new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new ArrayAdapter<Bundle>(m15366r(), C5562R.layout.list_view_item_simple_text, C5562R.id.text, this.f89841G4) { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDialogList.1
            @Override // android.widget.ArrayAdapter, android.widget.Adapter
            public View getView(int i2, View view, ViewGroup viewGroup) {
                if (view == null) {
                    view = LayoutInflater.from(VDDialogList.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text, viewGroup, false);
                    view.setTag(view.findViewById(C5562R.id.text));
                }
                ((TextView) view.getTag()).setText(((Bundle) VDDialogList.this.f89841G4.get(i2)).getString(VDDialogList.this.f89842H4));
                return view;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDDialogList.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                ((VDDialogListInterface) VDDialogList.this.m15351l0()).mo72891h((Bundle) VDDialogList.this.f89841G4.get(i2), VDDialogList.this.f89843I4);
                VDDialogList.this.mo15203M2();
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

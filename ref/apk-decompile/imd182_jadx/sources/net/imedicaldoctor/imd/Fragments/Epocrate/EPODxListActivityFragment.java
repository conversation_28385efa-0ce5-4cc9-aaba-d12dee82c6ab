package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class EPODxListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88015A4;

    /* renamed from: B4 */
    public String f88016B4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String string;
        CompressHelper compressHelper;
        Bundle bundle2;
        String str;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            string = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxListActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            string = m15387y().getString("ParentId");
        }
        this.f88016B4 = string;
        if (this.f88016B4 == null) {
            compressHelper = this.f88791k4;
            bundle2 = this.f88788h4;
            str = "Select * from dx_cats";
        } else {
            compressHelper = this.f88791k4;
            bundle2 = this.f88788h4;
            str = "SELECT * FROM dx_topics where id in ( select topicid from dx_cats_topics where catId=" + this.f88016B4 + ")";
        }
        this.f88794n4 = compressHelper.m71817V(bundle2, str);
        this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxListActivityFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: f0 */
            public void mo71975f0(Bundle bundle3, int i2) {
                EPODxListActivityFragment.this.m72468V2();
                EPODxListActivityFragment ePODxListActivityFragment = EPODxListActivityFragment.this;
                if (ePODxListActivityFragment.f88016B4 == null) {
                    Bundle bundle4 = new Bundle();
                    bundle4.putBundle("DB", EPODxListActivityFragment.this.f88788h4);
                    bundle4.putString("ParentId", bundle3.getString("id"));
                    EPODxListActivityFragment.this.f88791k4.m71798N(EPODxListActivity.class, EPODxListActivityFragment.class, bundle4);
                    return;
                }
                ePODxListActivityFragment.f88791k4.m71772A1(ePODxListActivityFragment.f88788h4, "dx-" + bundle3.getString("mId"), null, null);
            }
        };
        this.f88015A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPODxListActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: g0 */
            public void mo71976g0(Bundle bundle3, int i2) {
                EPODxListActivityFragment.this.m72468V2();
                EPODxListActivityFragment ePODxListActivityFragment = EPODxListActivityFragment.this;
                ePODxListActivityFragment.f88791k4.m71772A1(ePODxListActivityFragment.f88788h4, "dx-" + bundle3.getString("contentId"), null, null);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle3) {
                EPODxListActivityFragment.this.m72468V2();
                EPODxListActivityFragment.this.f88799s4.m2508k0(bundle3.getString("word"), true);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88015A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88015A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match 'text:" + str + "* AND typeText:Dx AND type:1'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: c3 */
    public void mo72171c3() {
        this.f88800t4.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.id_tx_icon));
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "Diseases";
    }
}

package net.imedicaldoctor.imd.Fragments.LWW;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;

/* loaded from: classes3.dex */
public class LWWChaptersFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    private CursorAdapter f88313A4;

    /* renamed from: B4 */
    private CursorAdapter f88314B4;

    /* renamed from: C4 */
    private String f88315C4;

    public class AMChaptersAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public Context f88320d;

        /* renamed from: e */
        public ArrayList<Bundle> f88321e;

        /* renamed from: f */
        public String f88322f;

        public AMChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
            this.f88320d = context;
            this.f88321e = arrayList;
            this.f88322f = str;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return this.f88321e.get(i2).getString("leaf").equals(IcyHeaders.f28171a3) ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
            final Bundle bundle = this.f88321e.get(i2);
            rippleTextViewHolder.f101515I.setText(bundle.getString(this.f88322f));
            rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LWW.LWWChaptersFragment.AMChaptersAdapter.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    AMChaptersAdapter.this.mo72294d0(bundle, i2);
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new RippleTextViewHolder(LayoutInflater.from(this.f88320d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
            }
            if (i2 == 1) {
                return new RippleTextViewHolder(LayoutInflater.from(this.f88320d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return this.f88321e.size();
        }

        /* renamed from: d0 */
        public void mo72294d0(Bundle bundle, int i2) {
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(C5562R.menu.search, menu);
        this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
        m72462O2();
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String string;
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        this.f88797q4 = viewInflate;
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        m72462O2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            string = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.LWW.LWWChaptersFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            string = m15387y().getString("ParentId");
        }
        this.f88315C4 = string;
        ArrayList<Bundle> arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "Select id as _id,* from toc where parentId = " + this.f88315C4);
        this.f88794n4 = arrayListM71817V;
        if (arrayListM71817V == null) {
            this.f88794n4 = new ArrayList<>();
        }
        this.f88792l4 = new AMChaptersAdapter(m15366r(), this.f88794n4, "name") { // from class: net.imedicaldoctor.imd.Fragments.LWW.LWWChaptersFragment.2
            @Override // net.imedicaldoctor.imd.Fragments.LWW.LWWChaptersFragment.AMChaptersAdapter
            /* renamed from: d0 */
            public void mo72294d0(Bundle bundle2, int i2) {
                LWWChaptersFragment.this.m72468V2();
                String string2 = bundle2.getString("leaf");
                String string3 = bundle2.getString("docId");
                if (string2.equals(IcyHeaders.f28171a3)) {
                    LWWChaptersFragment lWWChaptersFragment = LWWChaptersFragment.this;
                    lWWChaptersFragment.f88791k4.m71772A1(lWWChaptersFragment.f88788h4, string3, null, null);
                } else {
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("DB", LWWChaptersFragment.this.f88788h4);
                    bundle3.putString("ParentId", bundle2.getString("id"));
                    LWWChaptersFragment.this.f88791k4.m71798N(LWWChapters.class, LWWChaptersFragment.class, bundle3);
                }
            }
        };
        this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.LWW.LWWChaptersFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
            /* renamed from: e0 */
            public void mo71953e0(Bundle bundle2, int i2) {
                LWWChaptersFragment.this.m72468V2();
                String string2 = bundle2.getString("type");
                String string3 = bundle2.getString("contentId");
                if (string2.equals("0")) {
                    Bundle bundle3 = new Bundle();
                    bundle3.putBundle("DB", LWWChaptersFragment.this.f88788h4);
                    bundle3.putString("ParentId", string3);
                    LWWChaptersFragment.this.f88791k4.m71798N(LWWChapters.class, LWWChaptersFragment.class, bundle3);
                    return;
                }
                if (string2.equals(IcyHeaders.f28171a3) || string2.equals(ExifInterface.f16317Y4) || string2.equals(ExifInterface.f16326Z4) || string2.equals("4")) {
                    LWWChaptersFragment lWWChaptersFragment = LWWChaptersFragment.this;
                    lWWChaptersFragment.f88791k4.m71772A1(lWWChaptersFragment.f88788h4, string3, null, null);
                } else if (string2.equals("5")) {
                    LWWChaptersFragment lWWChaptersFragment2 = LWWChaptersFragment.this;
                    lWWChaptersFragment2.f88791k4.m71772A1(lWWChaptersFragment2.f88788h4, string3, lWWChaptersFragment2.m72466T2(bundle2.getString("subText")), null);
                }
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return viewInflate;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id, Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.database.SQLException;
import android.graphics.Bitmap;
import android.media.MediaPlayer;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.print.PrintAttributes;
import android.print.PrintManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.webkit.ConsoleMessage;
import android.webkit.JsResult;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.ProgressBar;
import android.widget.TabHost;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.widget.NestedScrollView;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentResultListener;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.common.C1052C;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.slidingpanelayout.widget.SlidingPaneLayout;
import at.grabner.circleprogress.CircleProgressView;
import com.basusingh.beautifulprogressdialog.BeautifulProgressDialog;
import com.google.android.material.appbar.AppBarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import com.itextpdf.tool.xml.html.HTML;
import fi.iki.elonen.NanoHTTPD;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.requery.android.database.sqlite.SQLiteDatabase;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.Random;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.CollapsingToolbar.CollapsingToolbarLayout;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Data.HistoryAdapter;
import net.imedicaldoctor.imd.Data.UnzipCompleted;
import net.imedicaldoctor.imd.Decompress;
import net.imedicaldoctor.imd.Fragments.AccessMedicine.AMHTMLViewerFragment;
import net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity;
import net.imedicaldoctor.imd.GeneralDialogFragment;
import net.imedicaldoctor.imd.LocalServer;
import net.imedicaldoctor.imd.Utils.ActionModeResponse;
import net.imedicaldoctor.imd.Utils.iMDWebView;
import net.imedicaldoctor.imd.Views.BlueHighlightButton;
import net.imedicaldoctor.imd.Views.GreenHighlightButton;
import net.imedicaldoctor.imd.Views.RedHighlightButton;
import net.imedicaldoctor.imd.Views.WhiteHighlightButton;
import net.imedicaldoctor.imd.Views.YellowHighlightButton;
import net.imedicaldoctor.imd.extractingFragment;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import okio.BufferedSink;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;
import org.ccil.cowan.tagsoup.Parser;
import org.ccil.cowan.tagsoup.XMLWriter;
import org.json.JSONArray;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import org.xml.sax.InputSource;

/* loaded from: classes3.dex */
public class ViewerHelperFragment extends Fragment implements ActionBar.TabListener {

    /* renamed from: V4 */
    private static final String f89561V4 = "ViewerHelperFragment";

    /* renamed from: W4 */
    private static extractingFragment f89562W4;

    /* renamed from: A4 */
    public String f89563A4;

    /* renamed from: B4 */
    public Activity f89564B4;

    /* renamed from: C4 */
    public View f89565C4;

    /* renamed from: D4 */
    public Bundle f89566D4;

    /* renamed from: E4 */
    public String f89567E4;

    /* renamed from: F4 */
    public String f89568F4;

    /* renamed from: G4 */
    public iMDWebView f89569G4;

    /* renamed from: H4 */
    public JSONArray f89570H4;

    /* renamed from: I4 */
    public BeautifulProgressDialog f89571I4;

    /* renamed from: J4 */
    public MenuItem f89572J4;

    /* renamed from: K4 */
    public String f89573K4;

    /* renamed from: L4 */
    public Toolbar f89574L4;

    /* renamed from: M4 */
    public ImageView f89575M4;

    /* renamed from: N4 */
    public TextView f89576N4;

    /* renamed from: O4 */
    public PopupWindow f89577O4;

    /* renamed from: P4 */
    public NestedScrollView f89578P4;

    /* renamed from: Q4 */
    public CompressHelper f89579Q4;

    /* renamed from: R4 */
    public long f89580R4;

    /* renamed from: S4 */
    public String f89581S4;

    /* renamed from: T4 */
    public Runnable f89582T4;

    /* renamed from: U4 */
    public Runnable f89583U4;

    /* renamed from: e4 */
    public boolean f89584e4;

    /* renamed from: f4 */
    public String f89585f4;

    /* renamed from: g4 */
    public String f89586g4;

    /* renamed from: h4 */
    public boolean f89587h4;

    /* renamed from: i4 */
    private DrawerLayout f89588i4;

    /* renamed from: j4 */
    public RecyclerView f89589j4;

    /* renamed from: k4 */
    public boolean f89590k4;

    /* renamed from: l4 */
    public GeneralDialogFragment f89591l4;

    /* renamed from: m4 */
    public Bundle f89592m4;

    /* renamed from: n4 */
    public int f89593n4;

    /* renamed from: o4 */
    public int f89594o4;

    /* renamed from: p4 */
    public String f89595p4;

    /* renamed from: q4 */
    private ProgressBar f89596q4;

    /* renamed from: r4 */
    public SearchView f89597r4;

    /* renamed from: s4 */
    public Menu f89598s4;

    /* renamed from: t4 */
    public ImageButton f89599t4;

    /* renamed from: u4 */
    public ImageButton f89600u4;

    /* renamed from: v4 */
    public TextView f89601v4;

    /* renamed from: w4 */
    public MenuItem f89602w4;

    /* renamed from: x4 */
    public TabHost f89603x4;

    /* renamed from: y4 */
    public String[] f89604y4;

    /* renamed from: z4 */
    public String f89605z4;

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: L3 */
    public /* synthetic */ void m72766L3(String str) {
        try {
            this.f89569G4.evaluateJavascript("document.documentElement.innerHTML = '" + str + "';", new ValueCallback<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.35
                @Override // android.webkit.ValueCallback
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void onReceiveValue(String str2) {
                    ViewerHelperFragment.this.f89571I4.m30009a();
                }
            });
        } catch (Exception e2) {
            Log.e("WebView", "Activity is finishing or has finished", e2);
        }
    }

    /* renamed from: O2 */
    public static String m72768O2(String str, String str2) {
        int iIndexOf;
        if (str2.equalsIgnoreCase("charis")) {
            return str;
        }
        while (true) {
            int iIndexOf2 = str.indexOf("@font-face");
            if (iIndexOf2 <= -1 || (iIndexOf = str.indexOf("}", iIndexOf2)) < iIndexOf2) {
                break;
            }
            int i2 = iIndexOf + 1;
            str = str.substring(0, iIndexOf2) + "" + (i2 < str.length() ? str.substring(i2) : "");
        }
        return m72776j4(str, "\"Charis\"", "\"" + str2 + "\"");
    }

    /* renamed from: h4 */
    public static String m72773h4(String str) {
        int iIndexOf;
        while (true) {
            int iIndexOf2 = str.indexOf("line-height:");
            if (iIndexOf2 <= -1 || (iIndexOf = str.indexOf(";", iIndexOf2)) < iIndexOf2) {
                break;
            }
            int i2 = iIndexOf + 1;
            str = str.substring(0, iIndexOf2) + "" + (i2 < str.length() ? str.substring(i2) : "");
        }
        return str;
    }

    /* renamed from: i3 */
    public static void m72774i3(File file, File file2) throws IOException {
        if (file.isDirectory()) {
            if (!file2.exists()) {
                file2.mkdir();
            }
            String[] list = file.list();
            for (int i2 = 0; i2 < file.listFiles().length; i2++) {
                m72774i3(new File(file, list[i2]), new File(file2, list[i2]));
            }
            return;
        }
        FileInputStream fileInputStream = new FileInputStream(file);
        FileOutputStream fileOutputStream = new FileOutputStream(file2);
        byte[] bArr = new byte[102400];
        while (true) {
            int i3 = fileInputStream.read(bArr);
            if (i3 <= 0) {
                fileInputStream.close();
                fileOutputStream.close();
                return;
            }
            fileOutputStream.write(bArr, 0, i3);
        }
    }

    /* renamed from: i4 */
    public static String m72775i4(String str) {
        int iIndexOf;
        while (true) {
            int iIndexOf2 = str.indexOf("text-align:");
            if (iIndexOf2 <= -1 || (iIndexOf = str.indexOf(";", iIndexOf2)) < iIndexOf2) {
                break;
            }
            int i2 = iIndexOf + 1;
            str = str.substring(0, iIndexOf2) + "" + (i2 < str.length() ? str.substring(i2) : "");
        }
        return str;
    }

    /* renamed from: j4 */
    public static String m72776j4(String str, String str2, String str3) {
        StringBuilder sb = new StringBuilder(str);
        int iIndexOf = sb.indexOf(str2);
        while (iIndexOf != -1) {
            sb.replace(iIndexOf, str2.length() + iIndexOf, str3);
            iIndexOf = sb.indexOf(str2, iIndexOf + str3.length());
        }
        return sb.toString();
    }

    /* renamed from: A3 */
    public void mo72743A3(int i2) {
        if (this.f89570H4.length() > 0) {
            try {
                this.f89569G4.m73433g("setHighlightClass(\"" + this.f89570H4.getString(this.f89593n4) + "\")");
                this.f89569G4.m73433g("goToSelectedItem(\"" + this.f89570H4.getString(i2) + "\")");
                this.f89593n4 = i2;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }
        m72805U2();
    }

    /* renamed from: A4 */
    public boolean m72777A4() {
        return m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("showpopup", true);
    }

    /* renamed from: B3 */
    public void m72778B3() {
        if (m15387y().containsKey("gotoHighlight")) {
            String string = m15387y().getString("gotoHighlight");
            this.f89569G4.m73433g("gotoHighlight('" + string + "');");
            m15387y().remove("gotoHighlight");
        }
    }

    /* renamed from: B4 */
    public void m72779B4(Exception exc) {
        exc.printStackTrace();
        FirebaseCrashlytics.m48010d().m48016g(exc);
        new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("Error occured in loading document. : " + exc).mo1106p("Go Back", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.1
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
                ViewerHelperFragment.this.f89579Q4.m71821W1(false);
            }
        }).m1090I();
    }

    @Override // androidx.appcompat.app.ActionBar.TabListener
    /* renamed from: C */
    public void mo1004C(ActionBar.Tab tab, FragmentTransaction fragmentTransaction) {
    }

    /* renamed from: C3 */
    public void mo71967C3(String str) {
        iMDLogger.m73554j("Viewer activity , Gotosection", str);
        this.f89569G4.m73433g("document.getElementById(\"" + str + "\").scrollIntoView(true);");
    }

    /* renamed from: C4 */
    public void m72780C4(String str) {
        new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("Error occured in loading document. : " + str).mo1106p("Go Back", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.2
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
                ViewerHelperFragment.this.f89579Q4.m71821W1(false);
            }
        }).m1090I();
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: D2 */
    public void mo15256D2(Intent intent) {
        super.mo15256D2(intent);
        m15366r().overridePendingTransition(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out);
    }

    /* renamed from: D3 */
    public boolean mo72169D3() {
        return true;
    }

    /* renamed from: D4 */
    public void m72781D4(String str) {
        String str2 = "select rowid as _id,* from highlight where save match '" + this.f89579Q4.m71833a1(str) + "' AND rowid in (select rowid from highlight where highlight match 'dbName:" + this.f89566D4.getString("Name").replace("'", "''") + " AND dbAddress:" + this.f89579Q4.m71833a1(mo72688s3()) + "')";
        iMDLogger.m73554j("Url", "sql : " + str2);
        ArrayList<Bundle> arrayListM71825Y = this.f89579Q4.m71825Y(m72790I3(), str2);
        if (arrayListM71825Y == null || arrayListM71825Y.size() == 0) {
            iMDLogger.m73554j("Url", "note size is zero");
            CompressHelper.m71767x2(m15366r(), "Note Not Found", 1);
            return;
        }
        final Bundle bundle = arrayListM71825Y.get(0);
        final EditText editText = new EditText(m15366r());
        editText.setTextColor(m15320b0().getColor(C5562R.color.black));
        editText.setText(bundle.getString("note"));
        new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("Note").setView(editText).mo1115y("Update", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.37
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) throws SQLException {
                String string = editText.getText().toString();
                ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                viewerHelperFragment.f89579Q4.m71881q(viewerHelperFragment.m72790I3(), "Update highlight set note = '" + string.replace("'", "''") + "' where rowid=" + bundle.getString("_id"));
            }
        }).mo1106p("Delete", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.36
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) throws NumberFormatException, SQLException {
                ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                viewerHelperFragment.f89579Q4.m71881q(viewerHelperFragment.m72790I3(), "delete from highlight where rowid=" + bundle.getString("_id"));
                ViewerHelperFragment.this.m72802S3();
            }
        }).m1090I();
    }

    /* renamed from: E3 */
    public Boolean m72782E3(String str) {
        return Boolean.FALSE;
    }

    /* renamed from: E4 */
    public void m72783E4(String str, String str2) {
        if (!m72777A4()) {
            this.f89579Q4.m71772A1(this.f89566D4, "html-" + str2 + ",,,,," + str, null, null);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putBundle("DB", this.f89566D4);
        bundle.putString("URL", this.f89579Q4.m71783F2(this.f89566D4, "html-" + str2 + ",,,,," + str));
        bundle.putString("Dialog", IcyHeaders.f28171a3);
        AMHTMLViewerFragment aMHTMLViewerFragment = new AMHTMLViewerFragment();
        aMHTMLViewerFragment.m15342i2(bundle);
        GeneralDialogFragment generalDialogFragment = new GeneralDialogFragment(aMHTMLViewerFragment);
        this.f89591l4 = generalDialogFragment;
        generalDialogFragment.mo15218Z2(true);
        this.f89591l4.mo15222e3(m15307V1().m15416k0(), "AMSectionsViewer");
        m15307V1().m15416k0().mo15626b("AMResult", this, new FragmentResultListener() { // from class: net.imedicaldoctor.imd.Fragments.c
            @Override // androidx.fragment.app.FragmentResultListener
            /* renamed from: a */
            public final void mo15697a(String str3, Bundle bundle2) {
                bundle2.getString("result_key");
            }
        });
    }

    /* renamed from: F3 */
    public void m72784F3() {
        CircleProgressView circleProgressView = (CircleProgressView) this.f89565C4.findViewById(C5562R.id.progress_view);
        if (circleProgressView != null) {
            circleProgressView.setVisibility(8);
        }
    }

    /* renamed from: F4 */
    public String m72785F4() {
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "tempp.db");
        if (!new File(strM71753g1).exists()) {
            try {
                SQLiteDatabase.openOrCreateDatabase(strM71753g1, (SQLiteDatabase.CursorFactory) null).execSQL("CREATE TABLE tempp (id varchar(255) PRIMARY KEY NOT NULL  UNIQUE , content Text, date Text);");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                new File(strM71753g1).delete();
                try {
                    SQLiteDatabase.openOrCreateDatabase(strM71753g1, (SQLiteDatabase.CursorFactory) null).execSQL("CREATE TABLE tempp (id varchar(255) PRIMARY KEY NOT NULL  UNIQUE , content Text, date Text);");
                } catch (Exception unused) {
                }
            }
        }
        return strM71753g1;
    }

    /* renamed from: G3 */
    public void m72786G3() {
        try {
            InputMethodManager inputMethodManager = (InputMethodManager) m15366r().getSystemService("input_method");
            if (m15366r().getCurrentFocus() != null) {
                inputMethodManager.hideSoftInputFromWindow(m15366r().getCurrentFocus().getWindowToken(), 0);
            }
            m15366r().getCurrentFocus().clearFocus();
        } catch (Exception unused) {
        }
    }

    /* renamed from: G4 */
    public void m72787G4() {
        this.f89569G4.m73434i();
    }

    /* renamed from: H3 */
    public void m72788H3(Boolean bool) {
        try {
            LinearLayout linearLayout = (LinearLayout) m15366r().findViewById(C5562R.id.find_layout);
            if (bool.booleanValue()) {
                this.f89602w4.setVisible(false);
                linearLayout.setVisibility(8);
            } else {
                linearLayout.setVisibility(0);
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: H4 */
    public void m72789H4() {
        this.f89569G4.m73435j();
    }

    @Override // androidx.appcompat.app.ActionBar.TabListener
    /* renamed from: I */
    public void mo1005I(ActionBar.Tab tab, FragmentTransaction fragmentTransaction) {
    }

    /* renamed from: I3 */
    public String m72790I3() throws SQLException {
        String str = this.f89579Q4.m71797M1() + "/highlights.db";
        if (!new File(str).exists()) {
            SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("create virtual table highlight using fts4 (dbName, dbTitle, dbAddress, dbDate, dbDocName, type, text, note, save)");
        }
        return str;
    }

    /* renamed from: J3 */
    public boolean mo72158J3(Context context) {
        return m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("dark", false);
    }

    /* renamed from: K3 */
    public Boolean mo72668K3(String str) {
        CompressHelper compressHelper = this.f89579Q4;
        String strM71823X0 = compressHelper.m71823X0();
        StringBuilder sb = new StringBuilder();
        sb.append("select * from favorites where dbName='");
        sb.append(this.f89579Q4.m71833a1(this.f89566D4.getString("Name")));
        sb.append("' AND dbAddress='");
        sb.append(this.f89579Q4.m71833a1(str));
        sb.append("'");
        return Boolean.valueOf(compressHelper.m71890s1(compressHelper.m71825Y(strM71823X0, sb.toString())) != null);
    }

    /* renamed from: L2 */
    public void m72791L2(String str, String str2) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String strM71833a1 = this.f89579Q4.m71833a1(str);
        String strM71833a12 = this.f89579Q4.m71833a1(str2);
        String str3 = simpleDateFormat.format(new Date());
        String strM71833a13 = this.f89579Q4.m71833a1(this.f89566D4.getString("Title"));
        String strM71833a14 = this.f89579Q4.m71833a1(this.f89566D4.getString("Name"));
        CompressHelper compressHelper = this.f89579Q4;
        compressHelper.m71881q(compressHelper.m71823X0(), "Insert into favorites values (null, '" + strM71833a14 + "', '" + strM71833a13 + "', '" + strM71833a12 + "', '" + str3 + "', '" + strM71833a1 + "')");
        m72825l4();
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: M0 */
    public void mo15284M0(Activity activity) {
        super.mo15284M0(activity);
        this.f89564B4 = activity;
    }

    /* renamed from: M2 */
    public void m72792M2() {
        Bundle bundle = this.f89566D4;
        if (bundle == null) {
            return;
        }
        this.f89579Q4.m71771A0(bundle.getString("Name"), this.f89566D4.getString("Title"), mo72676e4(), mo72677f4());
    }

    /* renamed from: N2 */
    public void m72793N2() {
        this.f89569G4.m73433g("removeAllHighlights(\"aa\");");
        m72788H3(Boolean.TRUE);
    }

    /* renamed from: N3 */
    public void m72794N3(String str, String str2) {
        this.f89569G4.loadDataWithBaseURL(str, "<html><body>Loading Content</body></html>", NanoHTTPD.f77082p, "utf-8", null);
        final String strReplace = str2.replace("'", "\\'").replace(StringUtils.f103471LF, "\\n").replace(StringUtils.f103470CR, "\\r");
        BeautifulProgressDialog beautifulProgressDialog = new BeautifulProgressDialog(m15366r(), BeautifulProgressDialog.f37991q, null);
        this.f89571I4 = beautifulProgressDialog;
        beautifulProgressDialog.m30023p("loading-1.json");
        this.f89571I4.m30024q(true);
        this.f89571I4.m30028w();
        this.f89569G4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.b
            @Override // java.lang.Runnable
            public final void run() {
                this.f90196s.m72766L3(strReplace);
            }
        }, 100L);
    }

    /* renamed from: O3 */
    public void m72795O3(String str, String str2) {
        if (str2 != null && str2.startsWith("file://")) {
            str2 = str2.replace("file://", "").substring(0, r5.length() - 1);
        }
        if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("newdocument", false)) {
            m72800R3(str, str2);
        } else {
            m72797P3(str, str2);
        }
    }

    /* renamed from: P2 */
    public void m72796P2() {
        ((AppBarLayout) this.f89565C4.findViewById(C5562R.id.appbar)).m35746D(false, false);
    }

    /* renamed from: P3 */
    public void m72797P3(String str, String str2) {
        String str3;
        if (str2 != null) {
            str3 = "file://" + new File(str2).getAbsolutePath() + "/";
        } else {
            str3 = null;
        }
        this.f89569G4.loadDataWithBaseURL(str3, str, NanoHTTPD.f77082p, "utf-8", null);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: Q0 */
    public void mo15207Q0(Bundle bundle) {
        super.mo15207Q0(bundle);
        this.f89579Q4 = new CompressHelper(m15366r());
    }

    /* renamed from: Q2 */
    public void m72798Q2(File file) {
        if (file.isDirectory()) {
            for (File file2 : file.listFiles()) {
                m72798Q2(file2);
            }
        }
        file.delete();
    }

    /* renamed from: Q3 */
    public void m72799Q3(String str, String str2) {
        if (str2 == null) {
            m72797P3(str, str2);
            return;
        }
        File file = new File(new File(str2), "t.html");
        try {
            CompressHelper.m71729E2(file, str);
        } catch (Exception unused) {
        }
        this.f89569G4.loadUrl("file://" + file.getAbsolutePath());
    }

    /* renamed from: R2 */
    public String mo71955R2() {
        return null;
    }

    /* renamed from: R3 */
    public void m72800R3(String str, String str2) {
        this.f89569G4.getSettings().setCacheMode(2);
        if (((iMD) m15366r().getApplicationContext()).f101667X != null) {
            try {
                ((iMD) m15366r().getApplicationContext()).f101667X.mo58321O();
            } catch (Exception unused) {
            }
        }
        ((iMD) m15366r().getApplicationContext()).f101667X = new LocalServer(8585, str, str2);
        try {
            ((iMD) m15366r().getApplicationContext()).f101667X.mo58318L();
        } catch (IOException e2) {
            e2.printStackTrace();
        }
        this.f89569G4.loadUrl("about:blank");
        this.f89569G4.loadUrl("http://localhost:8585/content");
    }

    /* renamed from: S2 */
    public Observable<String> m72801S2() {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.8
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                String strMo71955R2 = ViewerHelperFragment.this.mo71955R2();
                if (strMo71955R2 == null) {
                    strMo71955R2 = "";
                }
                observableEmitter.onNext(strMo71955R2);
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: S3 */
    public void m72802S3() throws NumberFormatException {
        try {
            ArrayList<Bundle> arrayListM71825Y = this.f89579Q4.m71825Y(m72790I3(), "select rowid as _id, dbName, dbTitle, dbAddress, dbDate, dbDocName, type, text, note, save from highlight where dbName='" + this.f89566D4.getString("Name").replace("'", "''") + "' AND dbAddress='" + this.f89579Q4.m71833a1(mo72688s3()) + "'");
            if (arrayListM71825Y == null) {
                arrayListM71825Y = new ArrayList<>();
            }
            this.f89569G4.m73433g("highlighter.removeAllHighlights();");
            ArrayList arrayList = new ArrayList();
            arrayList.add("type:textContent");
            ArrayList arrayList2 = new ArrayList();
            Iterator<Bundle> it2 = arrayListM71825Y.iterator();
            while (it2.hasNext()) {
                Bundle next = it2.next();
                if (next.getString("save").length() > 0) {
                    String string = next.getString("save");
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(string, "$");
                    String string2 = next.getString("type");
                    String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(string2, ",");
                    if (string2.equals("0") || strArrSplitByWholeSeparator2.length < 2) {
                        arrayList2.add("Update highlight set type='base," + this.f89580R4 + "' where rowid=" + next.getString("_id"));
                    } else {
                        Long lValueOf = Long.valueOf(strArrSplitByWholeSeparator2[1]);
                        if (this.f89580R4 != lValueOf.longValue()) {
                            long jLongValue = this.f89580R4 - lValueOf.longValue();
                            try {
                                string = (Integer.valueOf(strArrSplitByWholeSeparator[0]).intValue() + jLongValue) + "$" + (Integer.valueOf(strArrSplitByWholeSeparator[1]).intValue() + jLongValue) + "$" + strArrSplitByWholeSeparator[2] + "$" + strArrSplitByWholeSeparator[3] + "$" + strArrSplitByWholeSeparator[4];
                            } catch (Exception unused) {
                                Log.e("Highlighter", "Error in loading " + strArrSplitByWholeSeparator);
                                string = "";
                            }
                        }
                    }
                    next.getString("text");
                    Log.e("HighlightFixer", string + " --- " + next.getString("text"));
                    if (!string.isEmpty()) {
                        arrayList.add(string);
                    }
                }
            }
            if (!arrayList2.isEmpty()) {
                this.f89579Q4.m71884r(m72790I3(), (String[]) arrayList2.toArray(new String[0]), 0);
            }
            String strJoin = TextUtils.join("|", arrayList);
            this.f89569G4.m73433g("highlighter.deserialize('" + strJoin + "');");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            iMDLogger.m73550f("ViewerActivity", "Error in loading highlights " + e2);
        }
    }

    /* renamed from: T2 */
    public void m72803T2(final Runnable runnable, final Runnable runnable2) {
        final BeautifulProgressDialog beautifulProgressDialog = new BeautifulProgressDialog(m15366r(), BeautifulProgressDialog.f37991q, null);
        beautifulProgressDialog.m30023p("loading-1.json");
        beautifulProgressDialog.m30024q(true);
        new Thread(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.24
            @Override // java.lang.Runnable
            public void run() throws InterruptedException {
                try {
                    Thread.sleep(500L);
                    ViewerHelperFragment.this.f89565C4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.24.1
                        @Override // java.lang.Runnable
                        public void run() {
                            RunnableC519924 runnableC519924 = RunnableC519924.this;
                            if (ViewerHelperFragment.this.f89587h4) {
                                return;
                            }
                            beautifulProgressDialog.m30028w();
                        }
                    });
                } catch (InterruptedException unused) {
                }
            }
        }).start();
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.25
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    runnable.run();
                    observableEmitter.onNext("asdfadf");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                    ViewerHelperFragment.this.f89587h4 = true;
                    beautifulProgressDialog.m30009a();
                }
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.26
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                ViewerHelperFragment.this.f89587h4 = true;
                beautifulProgressDialog.m30009a();
                try {
                    runnable2.run();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.27
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                ViewerHelperFragment.this.f89587h4 = true;
                beautifulProgressDialog.m30009a();
                th.printStackTrace();
                FirebaseCrashlytics.m48010d().m48016g(th);
                runnable2.run();
            }
        });
    }

    /* renamed from: T3 */
    public void m72804T3(String str, String str2) {
        String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, str + ".jpg", str2);
        if (new File(strM71754h1).exists()) {
            return;
        }
        String str3 = str2.equals("small") ? "small.db" : "01234567".contains(str.substring(0, 1)) ? "images-1.db" : "images-2.db";
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, str2);
        if (!new File(strM71753g1).exists()) {
            new File(strM71753g1).mkdirs();
        }
        CompressHelper compressHelper = this.f89579Q4;
        Bundle bundleM71871n1 = compressHelper.m71871n1(compressHelper.m71819W(this.f89566D4, "Select image from thumbs where id='" + str + "'", str3));
        if (bundleM71871n1 != null) {
            try {
                CompressHelper.m71728D2(new File(strM71754h1), bundleM71871n1.getByteArray("image"));
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("LoadImageIfnecessary", "Failed at writing to " + strM71754h1 + " loading image with id" + str + " in folder " + str2);
            }
        }
    }

    /* renamed from: U2 */
    public void m72805U2() {
        JSONArray jSONArray = this.f89570H4;
        if (jSONArray == null || jSONArray.length() == 0) {
            this.f89601v4.setText("Nothing Found");
            this.f89599t4.setEnabled(false);
            this.f89600u4.setEnabled(false);
            return;
        }
        this.f89599t4.setEnabled(true);
        this.f89600u4.setEnabled(true);
        this.f89601v4.setText((this.f89593n4 + 1) + " Of " + this.f89570H4.length());
    }

    /* renamed from: U3 */
    public void m72806U3() {
        try {
            PopupWindow popupWindow = this.f89577O4;
            if (popupWindow != null) {
                popupWindow.dismiss();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
        }
    }

    /* renamed from: V3 */
    public void m72807V3() {
        this.f89569G4.m73433g("getRect(" + this.f89569G4.getWidth() + "," + this.f89569G4.getHeight() + ")");
    }

    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) throws NumberFormatException, SQLException {
        iMDLogger.m73550f("Javascript Console Message", consoleMessage.message() + " - " + consoleMessage.sourceId() + " - " + consoleMessage.lineNumber());
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        if (strArrSplit[0].equals("baserange")) {
            this.f89580R4 = Long.valueOf(strArrSplit[1]).longValue();
            m72802S3();
            if (m15387y() == null || !m15387y().containsKey("SEARCH")) {
                mo71970z3();
            }
        }
        if (strArrSplit[0].equals("highlightAction")) {
            String str = strArrSplit[1];
            String str2 = strArrSplit[2];
            m72810a3(mo72688s3(), this.f89568F4, "base," + this.f89580R4, str, "", str2);
        } else if (strArrSplit[0].equals("defineAction")) {
            if (strArrSplit.length == 1) {
                iMDLogger.m73550f("HelperFragment", "Dont have a word to define");
                return true;
            }
            m72830o3(strArrSplit[1]);
        } else if (strArrSplit[0].equals("dehighlightAction")) {
            m72819g4(strArrSplit[1], this.f89567E4);
        } else if (strArrSplit[0].equals("copyAction")) {
            ((ClipboardManager) m15366r().getSystemService("clipboard")).setPrimaryClip(ClipData.newPlainText("label", strArrSplit[1]));
        } else if (strArrSplit[0].equals("shareAction")) {
            Intent intent = new Intent("android.intent.action.SEND");
            intent.setType("text/plain");
            String str3 = strArrSplit[1];
            intent.putExtra("android.intent.extra.SUBJECT", this.f89568F4);
            intent.putExtra("android.intent.extra.TEXT", str3);
            mo15256D2(Intent.createChooser(intent, "Share via"));
        } else if (strArrSplit[0].equals("saveAction")) {
            this.f89569G4.m73433g("window.getSelection().removeAllRanges();");
            String str4 = strArrSplit[1];
            this.f89586g4 = str4;
            this.f89592m4.putString("mLastPosition", str4);
        } else if (strArrSplit[0].equals(HTML.Tag.f74425y)) {
            final String str5 = strArrSplit[1];
            new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.43

                /* renamed from: a */
                boolean f89692a;

                @Override // android.os.AsyncTask
                protected Object doInBackground(Object[] objArr) {
                    this.f89692a = false;
                    Document document = new Document();
                    String strM71797M1 = ViewerHelperFragment.this.f89579Q4.m71797M1();
                    try {
                        Parser parser = new Parser();
                        StringWriter stringWriter = new StringWriter();
                        parser.setContentHandler(new XMLWriter(stringWriter));
                        parser.parse(new InputSource(new StringReader(Jsoup.m76520c(str5, Whitelist.m77216m()))));
                        String strReplaceAll = ViewerHelperFragment.this.f89568F4.replaceAll("[\\W]", "_");
                        PdfWriter pdfWriterM54672p1 = PdfWriter.m54672p1(document, new FileOutputStream(strM71797M1 + "/" + strReplaceAll + ".pdf"));
                        document.open();
                        XMLWorkerHelper.m56677e().m56690o(pdfWriterM54672p1, document, new StringReader(stringWriter.toString()));
                        document.close();
                        MediaScannerConnection.scanFile(ViewerHelperFragment.this.m15366r(), new String[]{strM71797M1 + "/" + strReplaceAll + ".pdf"}, null, null);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        this.f89692a = true;
                        iMDLogger.m73550f(getClass().toString(), "Error in converting to pdf :" + e2);
                    }
                    return null;
                }

                @Override // android.os.AsyncTask
                protected void onPostExecute(Object obj) {
                    super.onPostExecute(obj);
                    ViewerHelperFragment.f89562W4.mo15203M2();
                    extractingFragment unused = ViewerHelperFragment.f89562W4 = null;
                    String strM71797M1 = ViewerHelperFragment.this.f89579Q4.m71797M1();
                    String strReplaceAll = ViewerHelperFragment.this.f89568F4.replaceAll("[\\W]", "_");
                    Intent intent2 = new Intent("android.intent.action.VIEW");
                    File file = new File(strM71797M1 + "/" + strReplaceAll + ".pdf");
                    if (this.f89692a) {
                        CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Error occured in making pdf document", 1);
                        if (file.exists()) {
                            file.delete();
                        }
                    }
                    if (file.exists()) {
                        intent2.setDataAndType(Uri.fromFile(file), "application/pdf");
                        ViewerHelperFragment.this.mo15256D2(intent2);
                    }
                }

                @Override // android.os.AsyncTask
                protected void onPreExecute() {
                    extractingFragment unused = ViewerHelperFragment.f89562W4 = new extractingFragment();
                    ViewerHelperFragment.f89562W4.m15382v2(true);
                    Bundle bundle = new Bundle();
                    bundle.putString("MESSAGE", "Generating PDF");
                    ViewerHelperFragment.f89562W4.m15342i2(bundle);
                    ViewerHelperFragment.f89562W4.mo15218Z2(false);
                    ViewerHelperFragment.f89562W4.mo15222e3(ViewerHelperFragment.this.m15283M(), "extracting");
                }
            }.execute(new Object[0]);
        } else if (strArrSplit[0].equals("findAction")) {
            String str6 = strArrSplit[1];
            this.f89570H4 = new JSONArray();
            try {
                this.f89570H4 = new JSONArray(str6);
                m72788H3(Boolean.valueOf(this.f89597r4.getQuery().length() <= 0));
                this.f89570H4.length();
                int i2 = this.f89593n4;
                int i3 = this.f89594o4;
                if (i2 < i3) {
                    this.f89593n4 = i3;
                } else {
                    this.f89593n4 = 0;
                }
                mo72743A3(this.f89593n4);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("Error in reading findAction Json", e2.toString());
                return false;
            }
        } else if (strArrSplit[0].equals("coordinates")) {
            float f2 = m15320b0().getDisplayMetrics().density;
            int iIntValue = Float.valueOf(strArrSplit[1]).intValue();
            int iIntValue2 = Float.valueOf(strArrSplit[2]).intValue();
            Float.valueOf(strArrSplit[3]).intValue();
            Float.valueOf(strArrSplit[4]).intValue();
            int[] iArr = new int[2];
            this.f89569G4.getLocationInWindow(iArr);
            int i4 = ((int) (iIntValue * f2)) + iArr[0];
            int i5 = ((int) (iIntValue2 * f2)) + iArr[1];
            int i6 = (int) (100 * f2);
            m15366r();
            View viewInflate = ((LayoutInflater) m15366r().getSystemService("layout_inflater")).inflate(C5562R.layout.popup_test, (ViewGroup) null, false);
            PopupWindow popupWindow = new PopupWindow(viewInflate, (int) (220 * f2), i6, false);
            this.f89577O4 = popupWindow;
            popupWindow.setWindowLayoutMode(-2, -2);
            this.f89577O4.setHeight(1);
            this.f89577O4.setWidth(1);
            this.f89577O4.showAtLocation(this.f89569G4, 0, i4, i5 - i6);
            WhiteHighlightButton whiteHighlightButton = (WhiteHighlightButton) viewInflate.findViewById(C5562R.id.highlight_clear);
            YellowHighlightButton yellowHighlightButton = (YellowHighlightButton) viewInflate.findViewById(C5562R.id.highlight_yellow);
            BlueHighlightButton blueHighlightButton = (BlueHighlightButton) viewInflate.findViewById(C5562R.id.highlight_blue);
            GreenHighlightButton greenHighlightButton = (GreenHighlightButton) viewInflate.findViewById(C5562R.id.highlight_green);
            RedHighlightButton redHighlightButton = (RedHighlightButton) viewInflate.findViewById(C5562R.id.highlight_red);
            TextView textView = (TextView) viewInflate.findViewById(C5562R.id.copyTv);
            TextView textView2 = (TextView) viewInflate.findViewById(C5562R.id.noteTv);
            TextView textView3 = (TextView) viewInflate.findViewById(C5562R.id.dictTv);
            whiteHighlightButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.44
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ViewerHelperFragment.this.f89569G4.m73433g("h=removeHighlightFromSelectedText()");
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('dehighlightAction,,,,,' + h.characterRange.start+'$'+h.characterRange.end+'$'+h.id+'$'+h.classApplier.cssClass+'$' );");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
            yellowHighlightButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.45
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ViewerHelperFragment.this.f89569G4.m73433g("h=highlightSelectedTextWithClass(\"highlightYellow\")");
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('highlightAction,,,,,' + h.getText() + ',,,,,' + h.characterRange.start+'$'+h.characterRange.end+'$'+h.id+'$'+h.classApplier.cssClass+'$' );");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
            greenHighlightButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.46
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ViewerHelperFragment.this.f89569G4.m73433g("h=highlightSelectedTextWithClass(\"highlightGreen\")");
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('highlightAction,,,,,' + h.getText() + ',,,,,' + h.characterRange.start+'$'+h.characterRange.end+'$'+h.id+'$'+h.classApplier.cssClass+'$' );");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
            blueHighlightButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.47
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ViewerHelperFragment.this.f89569G4.m73433g("h=highlightSelectedTextWithClass(\"highlightBlue\")");
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('highlightAction,,,,,' + h.getText() + ',,,,,' + h.characterRange.start+'$'+h.characterRange.end+'$'+h.id+'$'+h.classApplier.cssClass+'$' );");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
            redHighlightButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.48
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ViewerHelperFragment.this.f89569G4.m73433g("h=highlightSelectedTextWithClass(\"highlightRed\")");
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('highlightAction,,,,,' + h.getText() + ',,,,,' + h.characterRange.start+'$'+h.characterRange.end+'$'+h.id+'$'+h.classApplier.cssClass+'$' );");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
            textView3.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.49
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    iMDLogger.m73554j(getClass().getName(), "Define Clicked");
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('defineAction,,,,,' + window.getSelection().toString())");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
            textView.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.50
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('copyAction,,,,,' + window.getSelection().toString())");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
            textView2.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.51
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ViewerHelperFragment.this.f89569G4.m73433g("h=highlightSelectedTextWithClass(\"highlightNote\")");
                    ViewerHelperFragment.this.f89569G4.m73433g("console.log('highlightAction,,,,,' + h.getText() + ',,,,,' + h.characterRange.start+'$'+h.characterRange.end+'$'+h.id+'$'+h.classApplier.cssClass+'$' );");
                    ViewerHelperFragment.this.m72838u3();
                }
            });
        } else if (strArrSplit[0].equals("finisham")) {
            m72806U3();
        }
        return true;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: X0 */
    public void mo15214X0() {
        super.mo15214X0();
    }

    /* renamed from: X3 */
    public boolean mo72160X3(WebView webView, String str, String str2, JsResult jsResult) {
        return false;
    }

    /* renamed from: Y3 */
    public void mo72808Y3(WebView webView, String str) {
    }

    /* renamed from: Z2 */
    public void m72809Z2(final String str, final String str2) {
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str2, "/");
        final String str3 = strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1];
        final String str4 = this.f89566D4.getString("Name") + " - " + this.f89567E4;
        final downloadFragment downloadfragment = ((iMD) m15366r().getApplicationContext()).f101675c3;
        final CircleProgressView circleProgressView = (CircleProgressView) this.f89565C4.findViewById(C5562R.id.progress_view);
        Bundle bundleM73238x3 = downloadfragment.m73238x3(str4);
        if (bundleM73238x3 == null) {
            new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("You don't have this video on your device, do you wish to download it ?").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.10
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                    CompressHelper compressHelper = ViewerHelperFragment.this.f89579Q4;
                    StringBuilder sb = new StringBuilder();
                    sb.append("fileSizes|||||");
                    sb.append(str.replace(ViewerHelperFragment.this.f89579Q4.m71790J() + "/", ""));
                    compressHelper.m71874o0(sb.toString()).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.10.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(String str5) throws Throwable {
                            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str5, ";;;");
                            if (strArrSplitByWholeSeparator2.length != 11) {
                                CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Can't find file, please try again later", 1);
                                return;
                            }
                            circleProgressView.setVisibility(0);
                            DialogInterfaceOnClickListenerC518410 dialogInterfaceOnClickListenerC518410 = DialogInterfaceOnClickListenerC518410.this;
                            ViewerHelperFragment.this.f89581S4 = str4;
                            ArrayList arrayList = new ArrayList();
                            arrayList.addAll(Arrays.asList(strArrSplitByWholeSeparator2).subList(1, 11));
                            String strJoin = StringUtils.join(arrayList, ",,,");
                            downloadFragment downloadfragment2 = downloadfragment;
                            String str6 = ViewerHelperFragment.this.f89566D4.getString("Title") + " - " + str3;
                            DialogInterfaceOnClickListenerC518410 dialogInterfaceOnClickListenerC5184102 = DialogInterfaceOnClickListenerC518410.this;
                            downloadfragment2.m73235u3(str6, str, str2, strArrSplitByWholeSeparator2[0], str3, str4, strJoin);
                            DialogInterfaceOnClickListenerC518410 dialogInterfaceOnClickListenerC5184103 = DialogInterfaceOnClickListenerC518410.this;
                            downloadfragment.m73223U3(str4, circleProgressView);
                            DialogInterfaceOnClickListenerC518410 dialogInterfaceOnClickListenerC5184104 = DialogInterfaceOnClickListenerC518410.this;
                            downloadfragment.m73218R3(str4, ViewerHelperFragment.this.f89582T4);
                            DialogInterfaceOnClickListenerC518410 dialogInterfaceOnClickListenerC5184105 = DialogInterfaceOnClickListenerC518410.this;
                            downloadfragment.m73220S3(str4, ViewerHelperFragment.this.f89583U4);
                            DialogInterfaceOnClickListenerC518410 dialogInterfaceOnClickListenerC5184106 = DialogInterfaceOnClickListenerC518410.this;
                            downloadfragment.m73232Z3(str4);
                            CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Download Started", 0);
                        }
                    }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.10.2
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(Throwable th) throws Throwable {
                            try {
                                th.printStackTrace();
                                CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Error occured ", 0);
                            } catch (Exception e2) {
                                FirebaseCrashlytics.m48010d().m48016g(e2);
                            }
                        }
                    }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.10.3
                        @Override // io.reactivex.rxjava3.functions.Action
                        public void run() throws Throwable {
                        }
                    });
                }
            }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.9
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                }
            }).m1090I();
            return;
        }
        if (bundleM73238x3.containsKey("downloader")) {
            CompressHelper.m71767x2(m15366r(), "We are downloading a file for this document, please be patient", 1);
            return;
        }
        circleProgressView.setVisibility(0);
        this.f89581S4 = str4;
        downloadfragment.m73223U3(str4, circleProgressView);
        downloadfragment.m73218R3(str4, this.f89582T4);
        downloadfragment.m73220S3(str4, this.f89583U4);
        downloadfragment.m73232Z3(str4);
    }

    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        SearchView searchView;
        if (this.f89590k4) {
            this.f89590k4 = false;
            String string = m15307V1().getSharedPreferences("default_preferences", 0).getString("background_color", "#ffffff");
            if (!string.equals("#ffffff")) {
                this.f89569G4.m73433g("(function() { document.body.style.backgroundColor = '" + string + "'; })()");
            }
            String string2 = m15307V1().getSharedPreferences("default_preferences", 0).getString("line_height", "Default");
            if (!string2.equals("Default")) {
                this.f89569G4.m73433g("(function() { document.body.style.lineHeight = '" + string2 + "'; })()");
            }
            if (m15387y() != null && m15387y().containsKey("SECTION")) {
                String string3 = m15387y().getString("SECTION");
                if (string3.length() > 0) {
                    mo71967C3(string3);
                }
            }
            if (m15387y() != null && m15387y().containsKey("SEARCH")) {
                this.f89597r4.m2508k0(TextUtils.join(" OR ", m15387y().getStringArray("SEARCH")), true);
                this.f89602w4.setVisible(true);
                m72786G3();
                m15387y().remove("SEARCH");
            }
            if (this.f89586g4 != null) {
                iMDLogger.m73554j("viewhelper", "Restoring " + this.f89586g4);
                String str2 = "type:textContent|" + this.f89586g4;
                this.f89569G4.m73433g("highlighter.removeAllHighlights();");
                this.f89569G4.m73433g("highlighter.deserialize('" + str2 + "');");
                this.f89569G4.m73433g("gotoHighlight('" + this.f89586g4 + "');");
                this.f89569G4.m73433g("highlighter.removeAllHighlights();");
                this.f89569G4.m73433g("element=document.getElementById('orientation');element.parentNode.removeChild(element);");
                this.f89586g4 = null;
            }
            String str3 = this.f89585f4;
            if (str3 != null && str3.length() > 0 && (searchView = this.f89597r4) != null) {
                searchView.m2508k0(this.f89585f4, true);
                this.f89585f4 = null;
                this.f89597r4.setIconified(false);
                this.f89597r4.clearFocus();
            }
            this.f89569G4.m73433g("divs = document.getElementsByTagName('div');div=divs[0];range = document.createRange();range.setStart(div,0);range.setEnd(div,0);console.log('baserange,,,,,' + (new rangy.WrappedRange(range)).getBookmark(document.body).start);");
        }
    }

    /* renamed from: a3 */
    public void m72810a3(String str, String str2, String str3, String str4, String str5, String str6) throws SQLException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String strM71833a1 = this.f89579Q4.m71833a1(this.f89566D4.getString("Name"));
        String strM71833a12 = this.f89579Q4.m71833a1(this.f89566D4.getString("Title"));
        String strM71833a13 = this.f89579Q4.m71833a1(str);
        String strM71833a14 = this.f89579Q4.m71833a1(str2);
        String strM71833a15 = this.f89579Q4.m71833a1(str3);
        String strM71833a16 = this.f89579Q4.m71833a1(str4);
        String strM71833a17 = this.f89579Q4.m71833a1(str5);
        String strM71833a18 = this.f89579Q4.m71833a1(str6);
        String str7 = simpleDateFormat.format(new Date());
        this.f89579Q4.m71881q(m72790I3(), "Insert into highlight (dbName, dbTitle, dbAddress, dbDate, dbDocName, type, text, note, save) values ('" + strM71833a1 + "', '" + strM71833a12 + "', '" + strM71833a13 + "', '" + str7 + "', '" + strM71833a14 + "' , '" + strM71833a15 + "', '" + strM71833a16 + "', '" + strM71833a17 + "', '" + strM71833a18 + "')");
        if (strM71833a18.contains("highlightNote")) {
            m72781D4(strM71833a18);
        }
    }

    /* renamed from: a4 */
    public void m72811a4(String str) {
        mo15256D2(new Intent("android.intent.action.VIEW", Uri.parse(str)));
    }

    /* renamed from: b3 */
    public void m72812b3(String str, String str2) {
    }

    /* renamed from: b4 */
    public void m72813b4(String str) throws IllegalStateException {
        MediaPlayer.create(m15366r(), Uri.fromFile(new File(str))).start();
    }

    /* renamed from: c3 */
    public void m72814c3() {
        try {
            String str = this.f89566D4.getString("Name") + " - " + this.f89567E4;
            downloadFragment downloadfragment = ((iMD) m15366r().getApplicationContext()).f101675c3;
            CircleProgressView circleProgressView = (CircleProgressView) this.f89565C4.findViewById(C5562R.id.progress_view);
            Bundle bundleM73238x3 = downloadfragment.m73238x3(str);
            if (bundleM73238x3 == null || !bundleM73238x3.containsKey("downloader")) {
                return;
            }
            circleProgressView.setVisibility(0);
            downloadfragment.m73223U3(str, circleProgressView);
            downloadfragment.m73220S3(str, this.f89583U4);
            downloadfragment.m73218R3(str, this.f89582T4);
        } catch (Exception unused) {
        }
    }

    /* renamed from: c4 */
    public void m72815c4(String str, String str2, String str3, final String str4) throws IllegalStateException {
        Observable<byte[]> observableM71939e = Decompress.m71939e(str, str2);
        final File file = new File(str3);
        if (!file.exists()) {
            observableM71939e.m59701h6(Schedulers.m61580f()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<byte[]>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.58
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(byte[] bArr) throws Throwable {
                    byte[] bArrM71899w = ViewerHelperFragment.this.f89579Q4.m71899w(bArr, str4, "127");
                    try {
                        if (file.getParentFile().exists()) {
                            file.getParentFile().mkdirs();
                        }
                        CompressHelper.m71728D2(file, bArrM71899w);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("getSound", "Error in writing sound to " + file.getAbsolutePath() + " : " + e2);
                    }
                    file.deleteOnExit();
                    ViewerHelperFragment.this.m72813b4(file.getAbsolutePath());
                }
            });
        } else {
            m72813b4(file.getAbsolutePath());
            file.deleteOnExit();
        }
    }

    /* renamed from: d3 */
    public void m72816d3() {
        try {
            this.f89603x4.clearAllTabs();
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* renamed from: d4 */
    public String m72817d4(Context context, String str) throws IOException {
        if (context == null) {
            try {
                context = m15366r();
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
                return "";
            }
        }
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(context.getAssets().open(str), StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        while (true) {
            String line = bufferedReader.readLine();
            if (line == null) {
                break;
            }
            sb.append(line + StringUtils.f103471LF);
        }
        bufferedReader.close();
        String string = sb.toString();
        if (context.getSharedPreferences("default_preferences", 0).getBoolean("defaultfont", false)) {
            string = m72768O2(string, C1052C.f19172o);
        }
        if (!context.getSharedPreferences("default_preferences", 0).getBoolean("justify", true)) {
            string = m72775i4(string);
        }
        String strM72773h4 = m72773h4(string);
        return (mo72158J3(context) && strM72773h4.contains("</body>")) ? m72776j4(strM72773h4, "</body>", "<script src=\"file:///android_asset/jquery.js\" ></script><script src=\"file:///android_asset/reverse1.js\" ></script><script src=\"file:///android_asset/reverse2.js\" ></script></body>") : strM72773h4;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int i2;
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.fix_highlight) {
            final EditText editText = new EditText(m15366r());
            editText.setTextColor(m15320b0().getColor(C5562R.color.black));
            new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("Enter Offset for highlights").setView(editText).mo1115y("Only This Chapter", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.13
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                    final long jLongValue = Long.valueOf(editText.getText().toString()).longValue();
                    ViewerHelperFragment.this.m72834r3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.13.1
                        @Override // java.lang.Runnable
                        public void run() {
                            Iterator<Bundle> it2;
                            String[] strArrSplitByWholeSeparator;
                            StringBuilder sb;
                            try {
                                ArrayList arrayList = new ArrayList();
                                StringBuilder sb2 = new StringBuilder();
                                sb2.append("select rowid as _id, dbName, dbTitle, dbAddress, dbDate, dbDocName, type, text, note, save from highlight where dbName='");
                                sb2.append(ViewerHelperFragment.this.f89566D4.getString("Name").replace("'", "''"));
                                sb2.append("' AND dbAddress='");
                                ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                                sb2.append(viewerHelperFragment.f89579Q4.m71833a1(viewerHelperFragment.mo72688s3()));
                                sb2.append("'");
                                String string = sb2.toString();
                                ViewerHelperFragment viewerHelperFragment2 = ViewerHelperFragment.this;
                                ArrayList<Bundle> arrayListM71825Y = viewerHelperFragment2.f89579Q4.m71825Y(viewerHelperFragment2.m72790I3(), string);
                                if (arrayListM71825Y == null) {
                                    ViewerHelperFragment.this.f89579Q4.m71898v2("There is no highlights in this document");
                                    return;
                                }
                                Iterator<Bundle> it3 = arrayListM71825Y.iterator();
                                while (it3.hasNext()) {
                                    Bundle next = it3.next();
                                    if (next.getString("save").length() > 0) {
                                        try {
                                            String string2 = next.getString("save");
                                            if (string2.startsWith(",")) {
                                                string2 = string2.substring(1);
                                            }
                                            strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(string2, "$");
                                            sb = new StringBuilder();
                                            it2 = it3;
                                        } catch (Exception e2) {
                                            e = e2;
                                            it2 = it3;
                                        }
                                        try {
                                            sb.append(Integer.valueOf(strArrSplitByWholeSeparator[0]).intValue() + jLongValue);
                                            sb.append("$");
                                            sb.append(Integer.valueOf(strArrSplitByWholeSeparator[1]).intValue() + jLongValue);
                                            sb.append("$");
                                            sb.append(strArrSplitByWholeSeparator[2]);
                                            sb.append("$");
                                            sb.append(strArrSplitByWholeSeparator[3]);
                                            sb.append("$");
                                            sb.append(strArrSplitByWholeSeparator[4]);
                                            arrayList.add("Update highlight set save='" + sb.toString().replace("'", "''") + "' where rowid=" + next.getString("_id"));
                                        } catch (Exception e3) {
                                            e = e3;
                                            e.printStackTrace();
                                            iMDLogger.m73550f("ViewerActivity", "Error in changing highlights " + e);
                                            it3 = it2;
                                        }
                                    } else {
                                        it2 = it3;
                                    }
                                    it3 = it2;
                                }
                                ViewerHelperFragment viewerHelperFragment3 = ViewerHelperFragment.this;
                                viewerHelperFragment3.f89579Q4.m71884r(viewerHelperFragment3.m72790I3(), (String[]) arrayList.toArray(new String[0]), 0);
                            } catch (Exception e4) {
                                FirebaseCrashlytics.m48010d().m48016g(e4);
                                e4.printStackTrace();
                                iMDLogger.m73550f("ViewerActivity", "Error in changing highlights " + e4);
                            }
                        }
                    }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.13.2
                        @Override // java.lang.Runnable
                        public void run() throws NumberFormatException {
                            ViewerHelperFragment.this.m72802S3();
                            Toast.makeText(ViewerHelperFragment.this.m15366r(), "Highlights Updated", 1);
                        }
                    });
                }
            }).mo1106p("All Documents of Database", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.12
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                    ViewerHelperFragment.this.m72834r3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.12.1
                        @Override // java.lang.Runnable
                        public void run() {
                            Iterator<Bundle> it2;
                            try {
                                long jLongValue = Long.valueOf(editText.getText().toString()).longValue();
                                ArrayList arrayList = new ArrayList();
                                String str = "select rowid as _id, dbName, dbTitle, dbAddress, dbDate, dbDocName, type, text, note, save from highlight where dbName='" + ViewerHelperFragment.this.f89566D4.getString("Name").replace("'", "''") + "'";
                                ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                                ArrayList<Bundle> arrayListM71825Y = viewerHelperFragment.f89579Q4.m71825Y(viewerHelperFragment.m72790I3(), str);
                                if (arrayListM71825Y == null) {
                                    ViewerHelperFragment.this.f89579Q4.m71898v2("There is no highlights in this database");
                                    return;
                                }
                                Iterator<Bundle> it3 = arrayListM71825Y.iterator();
                                while (it3.hasNext()) {
                                    Bundle next = it3.next();
                                    if (next.getString("save").length() > 0) {
                                        try {
                                            String string = next.getString("save");
                                            if (string.startsWith(",")) {
                                                string = string.substring(1);
                                            }
                                            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(string, "$");
                                            StringBuilder sb = new StringBuilder();
                                            it2 = it3;
                                            try {
                                                sb.append(Integer.valueOf(strArrSplitByWholeSeparator[0]).intValue() + jLongValue);
                                                sb.append("$");
                                                sb.append(Integer.valueOf(strArrSplitByWholeSeparator[1]).intValue() + jLongValue);
                                                sb.append("$");
                                                sb.append(strArrSplitByWholeSeparator[2]);
                                                sb.append("$");
                                                sb.append(strArrSplitByWholeSeparator[3]);
                                                sb.append("$");
                                                sb.append(strArrSplitByWholeSeparator[4]);
                                                arrayList.add("Update highlight set save='" + sb.toString().replace("'", "''") + "' where rowid=" + next.getString("_id"));
                                            } catch (Exception e2) {
                                                e = e2;
                                                e.printStackTrace();
                                                iMDLogger.m73550f("ViewerActivity", "Error in changing highlights " + e);
                                                it3 = it2;
                                            }
                                        } catch (Exception e3) {
                                            e = e3;
                                            it2 = it3;
                                        }
                                    } else {
                                        it2 = it3;
                                    }
                                    it3 = it2;
                                }
                                ViewerHelperFragment viewerHelperFragment2 = ViewerHelperFragment.this;
                                viewerHelperFragment2.f89579Q4.m71884r(viewerHelperFragment2.m72790I3(), (String[]) arrayList.toArray(new String[0]), 0);
                            } catch (Exception e4) {
                                e4.printStackTrace();
                                iMDLogger.m73550f("ViewerActivity", "Error in changing highlights " + e4);
                            }
                        }
                    }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.12.2
                        @Override // java.lang.Runnable
                        public void run() throws NumberFormatException {
                            ViewerHelperFragment.this.m72802S3();
                            Toast.makeText(ViewerHelperFragment.this.m15366r(), "Highlights Updated", 1);
                        }
                    });
                }
            }).mo1109s("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.11
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                }
            }).m1090I();
        }
        if (itemId == C5562R.id.remove_highlight) {
            new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("this will delete all the highlights.").mo1115y("From this document", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.16
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) throws NumberFormatException {
                    StringBuilder sb = new StringBuilder();
                    sb.append("delete from highlight where dbName='");
                    ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                    sb.append(viewerHelperFragment.f89579Q4.m71833a1(viewerHelperFragment.f89566D4.getString("Name")));
                    sb.append("' AND dbAddress='");
                    ViewerHelperFragment viewerHelperFragment2 = ViewerHelperFragment.this;
                    sb.append(viewerHelperFragment2.f89579Q4.m71833a1(viewerHelperFragment2.mo72688s3()));
                    sb.append("'");
                    String string = sb.toString();
                    ViewerHelperFragment viewerHelperFragment3 = ViewerHelperFragment.this;
                    viewerHelperFragment3.f89579Q4.m71881q(viewerHelperFragment3.m72790I3(), string);
                    ViewerHelperFragment.this.m72802S3();
                }
            }).mo1106p("From all documents of this database", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.15
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) throws NumberFormatException {
                    StringBuilder sb = new StringBuilder();
                    sb.append("delete from highlight where dbName='");
                    ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                    sb.append(viewerHelperFragment.f89579Q4.m71833a1(viewerHelperFragment.f89566D4.getString("Name")));
                    sb.append("'");
                    String string = sb.toString();
                    ViewerHelperFragment viewerHelperFragment2 = ViewerHelperFragment.this;
                    viewerHelperFragment2.f89579Q4.m71881q(viewerHelperFragment2.m72790I3(), string);
                    ViewerHelperFragment.this.m72802S3();
                }
            }).mo1109s("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.14
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                }
            }).m1090I();
        }
        if (itemId == C5562R.id.action_home) {
            if (m15387y().containsKey("Dialog")) {
                try {
                    ((DialogFragment) m15302U()).mo15205N2();
                } catch (Exception unused) {
                    try {
                        m15391z().m15664u().m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out).mo15144B(this).mo15164r();
                    } catch (Exception e2) {
                        e2.printStackTrace();
                        try {
                            m15305V().m15664u().m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out).mo15144B(this).mo15164r();
                        } catch (Exception e3) {
                            e3.printStackTrace();
                        }
                    }
                }
            } else {
                this.f89579Q4.m71830Z1(false);
            }
        }
        if (itemId == C5562R.id.action_favorites) {
            new Bundle();
            if (menuItem.getTitle().equals("Add Favorite")) {
                m72791L2(this.f89568F4, this.f89567E4);
                menuItem.setTitle("Remove Favorite");
                i2 = C5562R.drawable.ic_action_favorite_yellow;
            } else {
                mo72686p3(this.f89567E4);
                menuItem.setTitle("Add Favorite");
                i2 = C5562R.drawable.ic_action_favorite;
            }
            menuItem.setIcon(i2);
            return true;
        }
        if (itemId == C5562R.id.action_reload) {
            String str = "file://" + new File(CompressHelper.m71753g1(this.f89566D4, "base")).getAbsolutePath() + "/";
            this.f89569G4.clearCache(true);
            this.f89569G4.loadDataWithBaseURL(str, this.f89563A4, NanoHTTPD.f77082p, "utf-8", null);
            return true;
        }
        if (itemId == C5562R.id.action_close) {
            this.f89597r4.m2508k0("", false);
            m72793N2();
            if (this.f89579Q4.m71903x1()) {
                m72816d3();
                TabHost tabHost = this.f89603x4;
                if (tabHost != null) {
                    tabHost.setVisibility(8);
                }
            } else {
                ActionBar actionBarM1122F0 = ((AppCompatActivity) m15366r()).m1122F0();
                if (actionBarM1122F0 != null) {
                    actionBarM1122F0.mo926N();
                    actionBarM1122F0.mo970s0(0);
                }
            }
            this.f89602w4.setVisible(false);
        }
        if (itemId == C5562R.id.zoom_in) {
            m72787G4();
        }
        if (itemId == C5562R.id.zoom_out) {
            m72789H4();
        }
        if (itemId == C5562R.id.action_pdf) {
            if (this.f89566D4.containsKey("Demo")) {
                Toast.makeText(m15366r(), "Can't make pdf in demo mode", 1).show();
                return true;
            }
            try {
                ((PrintManager) m15366r().getSystemService("print")).print("iMD - " + this.f89566D4.getString("Title") + " - " + this.f89568F4, this.f89569G4.createPrintDocumentAdapter("iMD - " + this.f89566D4.getString("Title") + " - " + this.f89568F4), new PrintAttributes.Builder().build());
                return true;
            } catch (Exception e4) {
                FirebaseCrashlytics.m48010d().m48016g(e4);
                if (f89562W4 == null) {
                    new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.17

                        /* renamed from: a */
                        boolean f89630a;

                        @Override // android.os.AsyncTask
                        protected Object doInBackground(Object[] objArr) {
                            this.f89630a = false;
                            Document document = new Document();
                            String strM71797M1 = ViewerHelperFragment.this.f89579Q4.m71797M1();
                            try {
                                Parser parser = new Parser();
                                StringWriter stringWriter = new StringWriter();
                                parser.setContentHandler(new XMLWriter(stringWriter));
                                parser.parse(new InputSource(new StringReader(Jsoup.m76520c(ViewerHelperFragment.this.f89563A4, Whitelist.m77216m()))));
                                String strReplaceAll = ViewerHelperFragment.this.f89568F4.replaceAll("[\\W]", "_");
                                PdfWriter pdfWriterM54672p1 = PdfWriter.m54672p1(document, new FileOutputStream(strM71797M1 + "/" + strReplaceAll + ".pdf"));
                                document.open();
                                XMLWorkerHelper.m56677e().m56690o(pdfWriterM54672p1, document, new StringReader(stringWriter.toString()));
                                document.close();
                                MediaScannerConnection.scanFile(ViewerHelperFragment.this.m15366r(), new String[]{strM71797M1 + "/" + strReplaceAll + ".pdf"}, null, null);
                            } catch (Exception e5) {
                                FirebaseCrashlytics.m48010d().m48016g(e5);
                                this.f89630a = true;
                                iMDLogger.m73550f(getClass().toString(), "Error in converting to pdf :" + e5);
                            }
                            return null;
                        }

                        @Override // android.os.AsyncTask
                        protected void onPostExecute(Object obj) {
                            super.onPostExecute(obj);
                            ViewerHelperFragment.f89562W4.mo15203M2();
                            extractingFragment unused2 = ViewerHelperFragment.f89562W4 = null;
                            ViewerHelperFragment.this.f89579Q4.m71896u2(ViewerHelperFragment.this.f89579Q4.m71797M1() + "/" + ViewerHelperFragment.this.f89568F4.replaceAll("[\\W]", "_") + ".pdf", "application/pdf");
                        }

                        @Override // android.os.AsyncTask
                        protected void onPreExecute() {
                            extractingFragment unused2 = ViewerHelperFragment.f89562W4 = new extractingFragment();
                            ViewerHelperFragment.f89562W4.m15382v2(true);
                            Bundle bundle = new Bundle();
                            bundle.putString("MESSAGE", "Generating PDF");
                            ViewerHelperFragment.f89562W4.m15342i2(bundle);
                            ViewerHelperFragment.f89562W4.mo15218Z2(false);
                            ViewerHelperFragment.f89562W4.mo15222e3(ViewerHelperFragment.this.m15283M(), "extracting");
                        }
                    }.execute(new Object[0]);
                }
            }
        }
        return super.mo15329e1(menuItem);
    }

    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
    }

    /* renamed from: e4 */
    public String mo72676e4() {
        return this.f89567E4;
    }

    /* renamed from: f3 */
    public void mo72642f3(int i2) {
        this.f89574L4.setNavigationIcon(C5562R.drawable.back_icon_small);
        this.f89574L4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                if (!ViewerHelperFragment.this.m15387y().containsKey("Dialog")) {
                    ViewerHelperFragment.this.f89579Q4.m71821W1(false);
                    return;
                }
                try {
                    ViewerHelperFragment.this.m15391z().m15664u().m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out).mo15144B(ViewerHelperFragment.this).mo15164r();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                    try {
                        ViewerHelperFragment.this.m15305V().m15664u().m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out).mo15144B(ViewerHelperFragment.this).mo15164r();
                    } catch (Exception e3) {
                        e3.printStackTrace();
                    }
                }
            }
        });
        ImageButton imageButton = (ImageButton) this.f89565C4.findViewById(C5562R.id.menu_button);
        if (imageButton != null) {
            if (this.f89579Q4.m71903x1()) {
                imageButton.setVisibility(0);
                imageButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.4
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        SlidingPaneLayout slidingPaneLayout = (SlidingPaneLayout) ViewerHelperFragment.this.m15366r().findViewById(C5562R.id.sliding_layout);
                        if (slidingPaneLayout != null) {
                            if (slidingPaneLayout.m28126l()) {
                                slidingPaneLayout.m28119c();
                            } else {
                                slidingPaneLayout.m28129o();
                            }
                        }
                    }
                });
            } else {
                imageButton.setVisibility(8);
            }
        }
        if (this.f89568F4 == null) {
            this.f89568F4 = "Unknown";
        }
        this.f89574L4.setTitle(this.f89568F4);
        if (mo71959v4()) {
            m72792M2();
        }
        try {
            this.f89579Q4.m71786H(this.f89567E4, this.f89568F4, this.f89566D4.getString("Name") + " --- " + this.f89566D4.getString("Title"));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        this.f89575M4 = (ImageView) this.f89565C4.findViewById(C5562R.id.toolbar_image_view);
        this.f89574L4.getMenu().clear();
        this.f89574L4.mo2678z(i2);
        final Menu menu = this.f89574L4.getMenu();
        m72833q4(menu);
        mo71957e3(menu);
        this.f89574L4.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.5
            @Override // androidx.appcompat.widget.Toolbar.OnMenuItemClickListener
            public boolean onMenuItemClick(MenuItem menuItem) {
                ViewerHelperFragment.this.mo15329e1(menuItem);
                return true;
            }
        });
        mo71972o4();
        this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.6
            @Override // java.lang.Runnable
            public void run() {
                MenuItem menuItemFindItem;
                String str;
                if (menu.findItem(C5562R.id.action_favorites) != null) {
                    ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                    if (viewerHelperFragment.mo72668K3(viewerHelperFragment.mo72688s3()).booleanValue()) {
                        menu.findItem(C5562R.id.action_favorites).setIcon(C5562R.drawable.ic_action_favorite_yellow);
                        menuItemFindItem = menu.findItem(C5562R.id.action_favorites);
                        str = "Remove Favorite";
                    } else {
                        menu.findItem(C5562R.id.action_favorites).setIcon(C5562R.drawable.ic_action_favorite);
                        menuItemFindItem = menu.findItem(C5562R.id.action_favorites);
                        str = "Add Favorite";
                    }
                    menuItemFindItem.setTitle(str);
                }
            }
        }, 1000L);
    }

    /* renamed from: f4 */
    public String mo72677f4() {
        return this.f89568F4;
    }

    /* renamed from: g3 */
    public void m72818g3() throws Resources.NotFoundException {
        mo72759u4();
        DrawerLayout drawerLayout = (DrawerLayout) this.f89565C4.findViewById(C5562R.id.drawer_layout);
        this.f89588i4 = drawerLayout;
        if (drawerLayout != null) {
            drawerLayout.m14272a(new DrawerLayout.DrawerListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.56
                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: a */
                public void mo1008a(View view) {
                    ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                    viewerHelperFragment.f89589j4.setAdapter(new HistoryAdapter(viewerHelperFragment.m15366r(), ViewerHelperFragment.this.f89588i4));
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: b */
                public void mo1009b(View view) {
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: c */
                public void mo1010c(int i2) {
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: d */
                public void mo1011d(View view, float f2) {
                }
            });
            RecyclerView recyclerView = (RecyclerView) this.f89565C4.findViewById(C5562R.id.drawer_view);
            this.f89589j4 = recyclerView;
            if (recyclerView != null) {
                recyclerView.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
                this.f89589j4.m27459p(new CustomItemDecoration(m15366r()));
            }
        }
    }

    /* renamed from: g4 */
    public void m72819g4(String str, String str2) {
        try {
            this.f89579Q4.m71881q(m72790I3(), "delete from highlight where save match '" + this.f89579Q4.m71833a1(str) + "' AND rowid in (select rowid from highlight where dbName='" + this.f89579Q4.m71833a1(this.f89566D4.getString("Name")) + "' AND dbAddress='" + this.f89579Q4.m71833a1(mo72688s3()) + "')");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            iMDLogger.m73550f("RemoveHighlight", "Error occured  " + str2);
        }
    }

    /* renamed from: h3 */
    public void m72820h3(String str) throws IOException {
        ArrayList arrayList = new ArrayList();
        arrayList.add("CharisSILB.ttf");
        arrayList.add("CharisSILBI.ttf");
        arrayList.add("CharisSILI.ttf");
        arrayList.add("CharisSILR.ttf");
        for (int i2 = 0; i2 < arrayList.size(); i2++) {
            String str2 = (String) arrayList.get(i2);
            String str3 = str + "/" + str2;
            if (!new File(str3).exists()) {
                try {
                    InputStream inputStreamOpen = m15366r().getAssets().open(str2);
                    BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(str3)));
                    try {
                        bufferedSinkM75768d.mo75508y1(Okio.m75785u(inputStreamOpen));
                        bufferedSinkM75768d.close();
                    } catch (Throwable th) {
                        if (bufferedSinkM75768d != null) {
                            try {
                                bufferedSinkM75768d.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        }
                        throw th;
                    }
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }
    }

    /* renamed from: j3 */
    public void m72821j3(String str) throws IOException {
        m72822k3(str, "base");
    }

    /* renamed from: k3 */
    public void m72822k3(String str, String str2) throws IOException {
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, str2);
        if (!new File(strM71753g1).exists()) {
            new File(strM71753g1).mkdirs();
        }
        File file = new File(strM71753g1 + '/' + str);
        if (file.exists()) {
            file.delete();
        }
        if (file.exists()) {
            return;
        }
        try {
            InputStream inputStreamOpen = m15307V1().getAssets().open(str);
            BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
            try {
                bufferedSinkM75768d.mo75508y1(Okio.m75785u(inputStreamOpen));
                bufferedSinkM75768d.close();
            } finally {
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("CopyJavascript", "Error in Copying " + str + " to " + strM71753g1 + "/" + str);
        }
    }

    /* renamed from: k4 */
    public void m72823k4(String str) {
        try {
            String strM72817d4 = m72817d4(m15366r(), str);
            this.f89569G4.m73433g("" + strM72817d4);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("GetJavascript", "Can't read " + str);
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: l1 */
    public void mo15352l1() {
        super.mo15352l1();
        m72786G3();
        iMDWebView imdwebview = this.f89569G4;
        if (imdwebview == null) {
            return;
        }
        imdwebview.m73433g("element=document.getElementById('orientation');element.parentNode.removeChild(element);");
    }

    /* renamed from: l3 */
    public void m72824l3(final String str) {
        m72832q3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.30
            @Override // java.lang.Runnable
            public void run() throws IOException {
                String strM71753g1 = CompressHelper.m71753g1(ViewerHelperFragment.this.f89566D4, "base");
                if (!new File(strM71753g1).exists()) {
                    new File(strM71753g1).mkdirs();
                }
                File file = new File(strM71753g1 + '/' + str);
                if (file.exists()) {
                    file.delete();
                }
                if (file.exists()) {
                    return;
                }
                try {
                    InputStream inputStreamOpen = ViewerHelperFragment.this.m15366r().getAssets().open(str);
                    try {
                        BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
                        try {
                            bufferedSinkM75768d.mo75508y1(Okio.m75785u(inputStreamOpen));
                            bufferedSinkM75768d.close();
                            if (inputStreamOpen != null) {
                                inputStreamOpen.close();
                            }
                        } finally {
                        }
                    } finally {
                    }
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("CopyJavascript", "Error in Copying " + str + " to " + strM71753g1 + "/" + str);
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.31
            @Override // java.lang.Runnable
            public void run() {
            }
        });
    }

    /* renamed from: l4 */
    public void m72825l4() {
        iMDLogger.m73548d("sendFavorite", "Sending FavoriteChanged message");
        Intent intent = new Intent("net.imedicaldoctor.imd.favorite");
        intent.putExtra("Test", "Random data for test");
        LocalBroadcastManager.m16410b(m15366r()).m16413d(intent);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    /* renamed from: m3 */
    public void m72826m3() throws IOException {
        m72828n3("base");
    }

    /* renamed from: m4 */
    public void m72827m4(String str) {
        try {
            ((AppCompatActivity) m15366r()).m1122F0().mo910A0(str);
        } catch (Exception unused) {
        }
    }

    /* renamed from: n3 */
    public void m72828n3(String str) throws IOException {
        m72822k3("log4javascript.js", str);
        m72822k3("core.js", str);
        m72822k3("dom.js", str);
        m72822k3("domrange.js", str);
        m72822k3("wrappedrange.js", str);
        m72822k3("wrappedselection.js", str);
        m72822k3("rangy-cssclassapplier.js", str);
        m72822k3("rangy-highlighter.js", str);
        m72822k3("hightlight.js", str);
        m72822k3("find.js", str);
    }

    /* renamed from: n4 */
    public void m72829n4(String str) {
        ((CollapsingToolbarLayout) this.f89565C4.findViewById(C5562R.id.collapsing_toolbar)).setTitle(str);
    }

    /* renamed from: o3 */
    public void m72830o3(String str) {
        Bundle bundleM71855i1 = this.f89579Q4.m71855i1("Dictionary");
        if (bundleM71855i1 == null) {
            new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("Dictionary is not installed . you can download it from downloads page.").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.42
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                }
            }).mo1106p("Download Now", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.41
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                    ViewerHelperFragment.this.f89579Q4.m71830Z1(true);
                    ((iMD) ViewerHelperFragment.this.m15366r().getApplicationContext()).f101668X2 = "\"Dictionary\"";
                }
            }).m1090I();
            return;
        }
        CDicSearchActivity.CDicSearchFragment cDicSearchFragment = new CDicSearchActivity.CDicSearchFragment();
        Bundle bundle = new Bundle();
        bundle.putBundle("DB", bundleM71855i1);
        bundle.putString("Dialog", str);
        cDicSearchFragment.m15342i2(bundle);
        cDicSearchFragment.mo15218Z2(true);
        cDicSearchFragment.mo15222e3(m15305V(), "dictionaryDialog");
    }

    /* renamed from: o4 */
    public void mo71972o4() {
        m72801S2().m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.7
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                ImageView imageView = ViewerHelperFragment.this.f89575M4;
                if (imageView == null) {
                    return;
                }
                imageView.setImageDrawable(null);
                if (str == null || str.length() <= 0) {
                    return;
                }
                ViewerHelperFragment.this.f89575M4.setVisibility(0);
                File file = new File(str);
                if (file.exists()) {
                    ViewerHelperFragment.this.f89575M4.setImageURI(Uri.fromFile(file));
                }
            }
        });
    }

    /* renamed from: p3 */
    public void mo72686p3(String str) {
        CompressHelper compressHelper = this.f89579Q4;
        compressHelper.m71881q(compressHelper.m71823X0(), "delete from favorites where dbName='" + this.f89579Q4.m71833a1(this.f89566D4.getString("Name")) + "' AND dbAddress='" + this.f89579Q4.m71833a1(str) + "'");
        m72825l4();
    }

    /* renamed from: p4 */
    public void m72831p4() {
        ImageButton imageButton = (ImageButton) this.f89565C4.findViewById(C5562R.id.previous_button);
        ImageButton imageButton2 = (ImageButton) this.f89565C4.findViewById(C5562R.id.next_button);
        this.f89600u4 = imageButton2;
        this.f89599t4 = imageButton;
        this.f89601v4 = (TextView) this.f89565C4.findViewById(C5562R.id.find_status_label);
        imageButton.setEnabled(false);
        imageButton2.setEnabled(false);
        imageButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.52
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                int i2 = ViewerHelperFragment.this.f89593n4;
                ViewerHelperFragment.this.mo72743A3(i2 == 0 ? r2.f89570H4.length() - 1 : i2 - 1);
            }
        });
        imageButton2.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.53
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                ViewerHelperFragment.this.mo72743A3(viewerHelperFragment.f89593n4 == viewerHelperFragment.f89570H4.length() + (-1) ? 0 : ViewerHelperFragment.this.f89593n4 + 1);
            }
        });
        ActionBar actionBarM1122F0 = ((AppCompatActivity) m15366r()).m1122F0();
        if (actionBarM1122F0 != null) {
            actionBarM1122F0.mo958m0(false);
        }
        if (!this.f89579Q4.m71903x1()) {
            if (actionBarM1122F0 != null) {
                actionBarM1122F0.mo970s0(0);
            }
        } else {
            TabHost tabHost = this.f89603x4;
            if (tabHost != null) {
                tabHost.setVisibility(8);
            }
        }
    }

    /* renamed from: q3 */
    public void m72832q3(final Runnable runnable, final Runnable runnable2) {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.21
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    runnable.run();
                    observableEmitter.onNext("asdfadf");
                } catch (Exception unused) {
                }
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.22
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                try {
                    runnable2.run();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.23
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                try {
                    iMDLogger.m73550f("Error occured", th.getMessage());
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        });
    }

    /* renamed from: q4 */
    public void m72833q4(Menu menu) {
        MenuItem menuItemFindItem;
        String str;
        this.f89598s4 = menu;
        if (menu.findItem(C5562R.id.action_favorites) != null) {
            if (mo72668K3(mo72688s3()).booleanValue()) {
                menu.findItem(C5562R.id.action_favorites).setIcon(C5562R.drawable.ic_action_favorite_yellow);
                menuItemFindItem = menu.findItem(C5562R.id.action_favorites);
                str = "Remove Favorite";
            } else {
                menu.findItem(C5562R.id.action_favorites).setIcon(C5562R.drawable.ic_action_favorite);
                menuItemFindItem = menu.findItem(C5562R.id.action_favorites);
                str = "Add Favorite";
            }
            menuItemFindItem.setTitle(str);
        }
        menu.findItem(C5562R.id.progress_menu);
        if (menu.findItem(C5562R.id.action_find) == null) {
            return;
        }
        this.f89597r4 = (SearchView) menu.findItem(C5562R.id.action_find).getActionView();
        this.f89602w4 = menu.findItem(C5562R.id.action_close);
        this.f89597r4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.28

            /* renamed from: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment$28$1, reason: invalid class name */
            class AnonymousClass1 implements TabHost.TabContentFactory {
                AnonymousClass1() {
                }

                @Override // android.widget.TabHost.TabContentFactory
                public View createTabContent(String str) {
                    TextView textView = new TextView(ViewerHelperFragment.this.m15366r());
                    textView.setText(str);
                    return textView;
                }
            }

            /* renamed from: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment$28$2, reason: invalid class name */
            class AnonymousClass2 implements TabHost.OnTabChangeListener {
                AnonymousClass2() {
                }

                @Override // android.widget.TabHost.OnTabChangeListener
                public void onTabChanged(String str) {
                    ViewerHelperFragment.this.mo71958t3(str);
                }
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(String str2) {
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str2) {
                str2.split(" OR ");
                ViewerHelperFragment.this.mo71958t3(str2);
                ViewerHelperFragment.this.m72786G3();
                return true;
            }
        });
        this.f89597r4.setOnCloseListener(new SearchView.OnCloseListener() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.29
            @Override // androidx.appcompat.widget.SearchView.OnCloseListener
            /* renamed from: a */
            public boolean mo2513a() {
                ViewerHelperFragment.this.m72793N2();
                if (ViewerHelperFragment.this.f89579Q4.m71903x1()) {
                    ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                    if (viewerHelperFragment.f89603x4 != null) {
                        viewerHelperFragment.m72816d3();
                        ViewerHelperFragment.this.f89603x4.setVisibility(8);
                    }
                } else if (((AppCompatActivity) ViewerHelperFragment.this.m15366r()).m1122F0() != null) {
                    ((AppCompatActivity) ViewerHelperFragment.this.m15366r()).m1122F0().mo926N();
                    ((AppCompatActivity) ViewerHelperFragment.this.m15366r()).m1122F0().mo970s0(0);
                }
                return false;
            }
        });
        this.f89598s4 = menu;
        this.f89579Q4.m71903x1();
    }

    /* renamed from: r3 */
    public void m72834r3(final Runnable runnable, final Runnable runnable2) {
        final BeautifulProgressDialog beautifulProgressDialog = new BeautifulProgressDialog(m15366r(), BeautifulProgressDialog.f37991q, null);
        beautifulProgressDialog.m30023p("loading-1.json");
        beautifulProgressDialog.m30024q(true);
        beautifulProgressDialog.m30028w();
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.18
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    runnable.run();
                    observableEmitter.onNext("asdfadf");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.19
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                beautifulProgressDialog.m30009a();
                try {
                    runnable2.run();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.20
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                beautifulProgressDialog.m30009a();
            }
        });
    }

    /* renamed from: r4 */
    public void m72835r4(View view, Bundle bundle) throws Resources.NotFoundException {
        if (bundle != null && bundle.containsKey("Restoring")) {
            this.f89584e4 = true;
            if (bundle.containsKey("Find")) {
                this.f89585f4 = bundle.getString("Find");
                this.f89594o4 = bundle.getInt("FindIndex");
            }
            if (bundle.containsKey("mFinalHTML")) {
                this.f89563A4 = bundle.getString("mFinalHTML");
            }
            if (bundle.containsKey("mTitle")) {
                this.f89568F4 = bundle.getString("mTitle");
            }
        }
        if (bundle != null && bundle.containsKey("mLastPosition")) {
            this.f89586g4 = bundle.getString("mLastPosition");
        }
        this.f89565C4 = view;
        this.f89569G4 = (iMDWebView) view.findViewById(C5562R.id.webView);
        this.f89566D4 = m15387y().getBundle("DB");
        this.f89567E4 = m15387y().getString("URL");
        TabHost tabHost = (TabHost) view.findViewById(C5562R.id.findtabhost);
        this.f89603x4 = tabHost;
        if (tabHost != null) {
            tabHost.setup();
        }
        this.f89574L4 = (Toolbar) this.f89565C4.findViewById(C5562R.id.toolbar);
        this.f89578P4 = (NestedScrollView) this.f89565C4.findViewById(C5562R.id.webview_scrollview);
        m72818g3();
        this.f89582T4 = new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.54
            @Override // java.lang.Runnable
            public void run() {
                CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Download Completed", 1);
                ViewerHelperFragment.this.m72784F3();
            }
        };
        this.f89583U4 = new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.55
            @Override // java.lang.Runnable
            public void run() {
                CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Download failed", 1);
                ViewerHelperFragment.this.m72784F3();
            }
        };
        if (mo71961z4()) {
            return;
        }
        ((AppBarLayout) this.f89565C4.findViewById(C5562R.id.appbar)).setExpanded(false);
    }

    @Override // androidx.appcompat.app.ActionBar.TabListener
    /* renamed from: s */
    public void mo1006s(ActionBar.Tab tab, FragmentTransaction fragmentTransaction) {
        mo71958t3((String) tab.mo992f());
    }

    /* renamed from: s3 */
    public String mo72688s3() {
        return this.f89567E4;
    }

    /* renamed from: s4 */
    public void m72836s4() {
        iMDWebView imdwebview = this.f89569G4;
        imdwebview.f101416j3 = this.f89566D4.getString("type");
        imdwebview.getSettings().setAllowFileAccess(true);
        imdwebview.getSettings().setDomStorageEnabled(true);
        imdwebview.getSettings().setJavaScriptEnabled(true);
        imdwebview.getSettings().setAllowFileAccessFromFileURLs(true);
        imdwebview.getSettings().setAllowUniversalAccessFromFileURLs(true);
        imdwebview.setLayerType(2, null);
        imdwebview.getSettings().setLoadWithOverviewMode(true);
        SharedPreferences sharedPreferences = m15307V1().getSharedPreferences("default_preferences", 0);
        imdwebview.getSettings().setTextZoom(sharedPreferences.getInt(this.f89566D4.getString("type") + "zoom", 100));
        imdwebview.setScrollbarFadingEnabled(true);
        imdwebview.setScrollBarStyle(16777216);
        imdwebview.setWebChromeClient(new WebChromeClient() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.38
            @Override // android.webkit.WebChromeClient
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                return ViewerHelperFragment.this.mo71969W3(consoleMessage);
            }

            @Override // android.webkit.WebChromeClient
            public boolean onJsAlert(WebView webView, String str, String str2, JsResult jsResult) {
                return ViewerHelperFragment.this.mo72160X3(webView, str, str2, jsResult);
            }
        });
        final View view = this.f89565C4;
        imdwebview.setWebViewClient(new WebViewClient() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.39
            @Override // android.webkit.WebViewClient
            public void onLoadResource(WebView webView, String str) {
                iMDLogger.m73554j("URL Requested", str);
                super.onLoadResource(webView, str);
            }

            @Override // android.webkit.WebViewClient
            public void onPageFinished(final WebView webView, final String str) {
                MenuItem menuItem = ViewerHelperFragment.this.f89572J4;
                if (menuItem != null) {
                    menuItem.setVisible(false);
                }
                view.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.39.1
                    @Override // java.lang.Runnable
                    public void run() {
                        try {
                            ViewerHelperFragment.this.mo71956Z3(webView, str);
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                        }
                    }
                }, 1000L);
            }

            @Override // android.webkit.WebViewClient
            public void onPageStarted(WebView webView, final String str, Bitmap bitmap) {
                MenuItem menuItem = ViewerHelperFragment.this.f89572J4;
                if (menuItem != null) {
                    menuItem.setVisible(true);
                    ViewerHelperFragment.this.f89596q4.setIndeterminate(true);
                }
                ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                viewerHelperFragment.f89590k4 = true;
                viewerHelperFragment.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.39.2
                    @Override // java.lang.Runnable
                    public void run() {
                        ViewerHelperFragment viewerHelperFragment2 = ViewerHelperFragment.this;
                        if (viewerHelperFragment2.f89590k4) {
                            try {
                                viewerHelperFragment2.mo71956Z3(viewerHelperFragment2.f89569G4, str);
                            } catch (Exception e2) {
                                FirebaseCrashlytics.m48010d().m48016g(e2);
                                e2.printStackTrace();
                            }
                        }
                    }
                }, ExoPlayer.f21773a1);
                iMDLogger.m73554j("URL Requested", str);
                super.onPageStarted(webView, str, bitmap);
            }

            @Override // android.webkit.WebViewClient
            public void onReceivedError(WebView webView, int i2, String str, String str2) {
                MenuItem menuItem = ViewerHelperFragment.this.f89572J4;
                if (menuItem != null) {
                    menuItem.setVisible(false);
                }
                CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Oh no! " + str, 0);
            }

            @Override // android.webkit.WebViewClient
            public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest webResourceRequest) {
                WebResourceResponse webResourceResponseMo72841w4 = ViewerHelperFragment.this.mo72841w4(webView, webResourceRequest);
                return webResourceResponseMo72841w4 != null ? webResourceResponseMo72841w4 : super.shouldInterceptRequest(webView, webResourceRequest);
            }

            @Override // android.webkit.WebViewClient
            public boolean shouldOverrideUrlLoading(WebView webView, String str) {
                iMDLogger.m73554j("URL Requested", str);
                Uri uri = Uri.parse(str);
                String scheme = uri.getScheme();
                String schemeSpecificPart = uri.getSchemeSpecificPart();
                if (!scheme.equals("note")) {
                    return ViewerHelperFragment.this.mo71960y4(webView, str, scheme, schemeSpecificPart);
                }
                ViewerHelperFragment.this.m72781D4(schemeSpecificPart.replace("//", "").replace("soheilvbsoheilvbsoheilvb", "$"));
                return true;
            }

            @Override // android.webkit.WebViewClient
            public void onReceivedError(WebView webView, WebResourceRequest webResourceRequest, WebResourceError webResourceError) {
                super.onReceivedError(webView, webResourceRequest, webResourceError);
            }

            @Override // android.webkit.WebViewClient
            public WebResourceResponse shouldInterceptRequest(WebView webView, String str) {
                WebResourceResponse webResourceResponseMo72843x4 = ViewerHelperFragment.this.mo72843x4(webView, str);
                return webResourceResponseMo72843x4 != null ? webResourceResponseMo72843x4 : super.shouldInterceptRequest(webView, str);
            }
        });
        this.f89569G4.f101413g3 = new ActionModeResponse() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.40
            @Override // net.imedicaldoctor.imd.Utils.ActionModeResponse
            /* renamed from: a */
            public void mo72853a() {
                ViewerHelperFragment.this.m72806U3();
            }

            @Override // net.imedicaldoctor.imd.Utils.ActionModeResponse
            /* renamed from: b */
            public void mo72854b() {
                ViewerHelperFragment.this.m72807V3();
            }
        };
    }

    /* renamed from: t3 */
    public void mo71958t3(String str) {
        String strReplace = str.replace(" OR ", "\",\"");
        this.f89569G4.m73433g("removeAllHighlights(\"aa\");");
        this.f89569G4.m73433g("console.log('findAction,,,,,' + highlightAllOccurencesOfString([\"" + strReplace + "\"],\"aa\"))");
    }

    /* renamed from: t4 */
    public void m72837t4() {
        iMDWebView imdwebview = this.f89569G4;
        imdwebview.f101416j3 = this.f89566D4.getString("type");
        imdwebview.getSettings().setAllowFileAccess(true);
        imdwebview.getSettings().setDomStorageEnabled(true);
        imdwebview.getSettings().setJavaScriptEnabled(true);
        SharedPreferences sharedPreferences = m15307V1().getSharedPreferences("default_preferences", 0);
        imdwebview.getSettings().setTextZoom(sharedPreferences.getInt(this.f89566D4.getString("type") + "zoom", 100));
        imdwebview.getSettings().setSupportZoom(true);
        imdwebview.getSettings().setBuiltInZoomControls(true);
        imdwebview.getSettings().setDisplayZoomControls(false);
        imdwebview.setWebChromeClient(new WebChromeClient() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.32
            @Override // android.webkit.WebChromeClient
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                return ViewerHelperFragment.this.mo71969W3(consoleMessage);
            }

            @Override // android.webkit.WebChromeClient
            public boolean onJsAlert(WebView webView, String str, String str2, JsResult jsResult) {
                return ViewerHelperFragment.this.mo72160X3(webView, str, str2, jsResult);
            }
        });
        final View view = this.f89565C4;
        imdwebview.setWebViewClient(new WebViewClient() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.33
            @Override // android.webkit.WebViewClient
            public void onLoadResource(WebView webView, String str) {
                iMDLogger.m73554j("URL Requested", str);
                ViewerHelperFragment.this.mo72808Y3(webView, str);
                super.onLoadResource(webView, str);
            }

            @Override // android.webkit.WebViewClient
            public void onPageFinished(final WebView webView, final String str) {
                MenuItem menuItem = ViewerHelperFragment.this.f89572J4;
                if (menuItem != null) {
                    menuItem.setVisible(false);
                }
                view.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.33.1
                    @Override // java.lang.Runnable
                    public void run() {
                        ViewerHelperFragment.this.mo71956Z3(webView, str);
                    }
                }, 1000L);
            }

            @Override // android.webkit.WebViewClient
            public void onPageStarted(WebView webView, final String str, Bitmap bitmap) {
                MenuItem menuItem = ViewerHelperFragment.this.f89572J4;
                if (menuItem != null) {
                    menuItem.setVisible(true);
                    ViewerHelperFragment.this.f89596q4.setIndeterminate(true);
                }
                ViewerHelperFragment viewerHelperFragment = ViewerHelperFragment.this;
                viewerHelperFragment.f89590k4 = true;
                viewerHelperFragment.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.33.2
                    @Override // java.lang.Runnable
                    public void run() {
                        ViewerHelperFragment viewerHelperFragment2 = ViewerHelperFragment.this;
                        if (viewerHelperFragment2.f89590k4) {
                            try {
                                viewerHelperFragment2.mo71956Z3(viewerHelperFragment2.f89569G4, str);
                            } catch (Exception e2) {
                                FirebaseCrashlytics.m48010d().m48016g(e2);
                                e2.printStackTrace();
                            }
                        }
                    }
                }, ExoPlayer.f21773a1);
                iMDLogger.m73554j("URL Requested", str);
                super.onPageStarted(webView, str, bitmap);
            }

            @Override // android.webkit.WebViewClient
            public void onReceivedError(WebView webView, int i2, String str, String str2) {
                MenuItem menuItem = ViewerHelperFragment.this.f89572J4;
                if (menuItem != null) {
                    menuItem.setVisible(false);
                }
                CompressHelper.m71767x2(ViewerHelperFragment.this.m15366r(), "Oh no! " + str, 0);
            }

            @Override // android.webkit.WebViewClient
            public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest webResourceRequest) {
                WebResourceResponse webResourceResponseMo72841w4 = ViewerHelperFragment.this.mo72841w4(webView, webResourceRequest);
                return webResourceResponseMo72841w4 != null ? webResourceResponseMo72841w4 : super.shouldInterceptRequest(webView, webResourceRequest);
            }

            @Override // android.webkit.WebViewClient
            public boolean shouldOverrideUrlLoading(WebView webView, String str) {
                iMDLogger.m73554j("URL Requested", str);
                Uri uri = Uri.parse(str);
                String scheme = uri.getScheme();
                String schemeSpecificPart = uri.getSchemeSpecificPart();
                if (!scheme.equals("note")) {
                    return ViewerHelperFragment.this.mo71960y4(webView, str, scheme, schemeSpecificPart);
                }
                ViewerHelperFragment.this.m72781D4(schemeSpecificPart.replace("//", "").replace("soheilvbsoheilvbsoheilvb", "$"));
                return true;
            }

            @Override // android.webkit.WebViewClient
            public WebResourceResponse shouldInterceptRequest(WebView webView, String str) {
                return super.shouldInterceptRequest(webView, str);
            }
        });
        this.f89569G4.f101413g3 = new ActionModeResponse() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.34
            @Override // net.imedicaldoctor.imd.Utils.ActionModeResponse
            /* renamed from: a */
            public void mo72853a() {
                ViewerHelperFragment.this.m72806U3();
            }

            @Override // net.imedicaldoctor.imd.Utils.ActionModeResponse
            /* renamed from: b */
            public void mo72854b() {
                ViewerHelperFragment.this.m72807V3();
            }
        };
    }

    /* renamed from: u3 */
    public void m72838u3() {
        this.f89569G4.m73436k();
        m15366r().onActionModeFinished(null);
    }

    /* renamed from: u4 */
    public void mo72759u4() throws Resources.NotFoundException {
        if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
            float dimension = m15320b0().getDimension(C5562R.dimen.toolbar_padding);
            Toolbar toolbar = this.f89574L4;
            if (toolbar != null) {
                toolbar.setPadding(0, (int) dimension, 0, 0);
            }
        }
    }

    /* renamed from: v3 */
    public Bundle m72839v3(ArrayList<Bundle> arrayList) {
        try {
            return arrayList.get(new Random().nextInt(arrayList.size()));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return null;
        }
    }

    /* renamed from: v4 */
    public boolean mo71959v4() {
        return !m15387y().containsKey("Dialog");
    }

    /* renamed from: w3 */
    public String m72840w3(ArrayList<String> arrayList) {
        if (arrayList != null) {
            try {
                if (arrayList.size() != 0) {
                    return arrayList.get(new Random().nextInt(arrayList.size()));
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }
        return null;
    }

    /* renamed from: w4 */
    public WebResourceResponse mo72841w4(WebView webView, WebResourceRequest webResourceRequest) {
        return null;
    }

    /* renamed from: x3 */
    public void m72842x3(String str, String str2, String str3, final String str4) throws IllegalStateException {
        final File file = new File(str3);
        if (!file.exists()) {
            Decompress.m71940f(str, str2, new UnzipCompleted() { // from class: net.imedicaldoctor.imd.Fragments.ViewerHelperFragment.57
                @Override // net.imedicaldoctor.imd.Data.UnzipCompleted
                /* renamed from: a */
                public void mo71929a(String str5) {
                    super.mo71929a(str5);
                }

                @Override // net.imedicaldoctor.imd.Data.UnzipCompleted
                /* renamed from: b */
                public void mo71930b(byte[] bArr) throws IllegalStateException {
                    try {
                        CompressHelper.m71728D2(file, ViewerHelperFragment.this.f89579Q4.m71899w(bArr, str4, "127"));
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("getSound", "Error in writing sound to " + file.getAbsolutePath() + " : " + e2);
                    }
                    file.deleteOnExit();
                    ViewerHelperFragment.this.m72813b4(file.getAbsolutePath());
                }
            });
        } else {
            m72813b4(file.getAbsolutePath());
            file.deleteOnExit();
        }
    }

    /* renamed from: x4 */
    public WebResourceResponse mo72843x4(WebView webView, String str) {
        return null;
    }

    /* renamed from: y3 */
    public String m72844y3(String str) {
        String strM72785F4 = m72785F4();
        ArrayList<Bundle> arrayListM71825Y = this.f89579Q4.m71825Y(strM72785F4, "Select id,content from temp where id ='" + str + "'");
        if (arrayListM71825Y == null || arrayListM71825Y.size() == 0) {
            return null;
        }
        return arrayListM71825Y.get(0).getString(Annotation.f68283i3);
    }

    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        Log.e("Overriding", "Overriding");
        return true;
    }

    /* renamed from: z3 */
    public void mo71970z3() {
        try {
            if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("lastred", false)) {
                String str = "select save from highlight where dbName='" + this.f89566D4.getString("Name").replace("'", "''") + "' AND dbAddress='" + this.f89579Q4.m71833a1(mo72688s3()) + "' AND save like '%$highlightRed$%'";
                CompressHelper compressHelper = this.f89579Q4;
                Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71825Y(m72790I3(), str));
                if (bundleM71890s1 == null) {
                    return;
                }
                this.f89569G4.loadUrl("javascript:gotoHighlight('" + bundleM71890s1.getString("save") + "');");
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: z4 */
    public boolean mo71961z4() {
        return m15246B().getSharedPreferences("default_preferences", 0).getBoolean("NestedScroll", true);
    }
}

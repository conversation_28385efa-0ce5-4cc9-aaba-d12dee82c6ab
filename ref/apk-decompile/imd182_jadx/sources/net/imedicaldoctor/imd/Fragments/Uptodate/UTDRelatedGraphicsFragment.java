package net.imedicaldoctor.imd.Fragments.Uptodate;

import android.app.Dialog;
import android.database.DataSetObserver;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ExpandableListAdapter;
import android.widget.ExpandableListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.json.JSONArray;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class UTDRelatedGraphicsFragment extends DialogFragment {
    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_utdrelated_graphics, (ViewGroup) null);
        ExpandableListView expandableListView = (ExpandableListView) viewInflate.findViewById(C5562R.id.list_view);
        try {
            final JSONArray jSONArray = new JSONArray(m15387y().getString("RELATED"));
            expandableListView.setAdapter(new ExpandableListAdapter() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDRelatedGraphicsFragment.1
                @Override // android.widget.ExpandableListAdapter
                public boolean areAllItemsEnabled() {
                    return true;
                }

                @Override // android.widget.ExpandableListAdapter
                public Object getChild(int i2, int i3) {
                    try {
                        return jSONArray.getJSONObject(i2).getJSONArray("graphics").getJSONObject(i3);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f(getClass().toString(), "Error in getChild : " + e2);
                        return null;
                    }
                }

                @Override // android.widget.ExpandableListAdapter
                public long getChildId(int i2, int i3) {
                    return (i2 * 1000) + i3;
                }

                @Override // android.widget.ExpandableListAdapter
                public View getChildView(int i2, int i3, boolean z, View view, ViewGroup viewGroup) {
                    View viewInflate2 = LayoutInflater.from(UTDRelatedGraphicsFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_related_graphic, viewGroup, false);
                    try {
                        ((TextView) viewInflate2.findViewById(C5562R.id.title_text)).setText(((JSONObject) getChild(i2, i3)).getJSONObject("graphicInfo").getString("displayName"));
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f(getClass().toString(), "Error in getChildView Title : " + e2);
                    }
                    return viewInflate2;
                }

                @Override // android.widget.ExpandableListAdapter
                public int getChildrenCount(int i2) {
                    try {
                        return jSONArray.getJSONObject(i2).getJSONArray("graphics").length();
                    } catch (Exception e2) {
                        iMDLogger.m73550f(getClass().toString(), "Error in getting ChildrenCount from json : " + e2);
                        return 0;
                    }
                }

                @Override // android.widget.ExpandableListAdapter
                public long getCombinedChildId(long j2, long j3) {
                    return 0L;
                }

                @Override // android.widget.ExpandableListAdapter
                public long getCombinedGroupId(long j2) {
                    return 0L;
                }

                @Override // android.widget.ExpandableListAdapter
                public Object getGroup(int i2) {
                    try {
                        return jSONArray.getJSONObject(i2);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f(getClass().toString(), "Error in getGroup : " + e2);
                        return null;
                    }
                }

                @Override // android.widget.ExpandableListAdapter
                public int getGroupCount() {
                    return jSONArray.length();
                }

                @Override // android.widget.ExpandableListAdapter
                public long getGroupId(int i2) {
                    return i2;
                }

                @Override // android.widget.ExpandableListAdapter
                public View getGroupView(int i2, boolean z, View view, ViewGroup viewGroup) {
                    View viewInflate2 = LayoutInflater.from(UTDRelatedGraphicsFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_related_graphic_header, viewGroup, false);
                    try {
                        ((TextView) viewInflate2.findViewById(C5562R.id.title_text)).setText(((JSONObject) getGroup(i2)).getString("headingTitle"));
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f(getClass().toString(), "Error in getGroupView Title : " + e2);
                    }
                    return viewInflate2;
                }

                @Override // android.widget.ExpandableListAdapter
                public boolean hasStableIds() {
                    return true;
                }

                @Override // android.widget.ExpandableListAdapter
                public boolean isChildSelectable(int i2, int i3) {
                    return true;
                }

                @Override // android.widget.ExpandableListAdapter
                public boolean isEmpty() {
                    return false;
                }

                @Override // android.widget.ExpandableListAdapter
                public void onGroupCollapsed(int i2) {
                }

                @Override // android.widget.ExpandableListAdapter
                public void onGroupExpanded(int i2) {
                }

                @Override // android.widget.ExpandableListAdapter
                public void registerDataSetObserver(DataSetObserver dataSetObserver) {
                }

                @Override // android.widget.ExpandableListAdapter
                public void unregisterDataSetObserver(DataSetObserver dataSetObserver) {
                }
            });
            expandableListView.setOnChildClickListener(new ExpandableListView.OnChildClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDRelatedGraphicsFragment.2
                @Override // android.widget.ExpandableListView.OnChildClickListener
                public boolean onChildClick(ExpandableListView expandableListView2, View view, int i2, int i3, long j2) {
                    try {
                        JSONObject jSONObject = (JSONObject) expandableListView2.getExpandableListAdapter().getChild(i2, i3);
                        iMDLogger.m73554j(getClass().toString(), "graph clicked " + jSONObject);
                        ((UTDViewerActivity.UTDViewerFragment) UTDRelatedGraphicsFragment.this.m15351l0()).m72744R4(jSONObject.getJSONObject("graphicInfo").getString("id"));
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f(getClass().toString(), "Error in onChildClick : " + e2);
                    }
                    UTDRelatedGraphicsFragment.this.mo15203M2();
                    return true;
                }
            });
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f(getClass().toString(), "Error in parsing related Graphics " + e2);
        }
        for (int groupCount = expandableListView.getExpandableListAdapter().getGroupCount(); groupCount >= 0; groupCount--) {
            expandableListView.expandGroup(groupCount, true);
        }
        builder.setView(viewInflate);
        return builder.create();
    }
}

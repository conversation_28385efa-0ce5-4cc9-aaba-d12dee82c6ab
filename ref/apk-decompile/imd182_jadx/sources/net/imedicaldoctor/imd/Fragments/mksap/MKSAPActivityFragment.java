package net.imedicaldoctor.imd.Fragments.mksap;

import android.os.Bundle;
import android.view.Menu;
import android.webkit.ConsoleMessage;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import androidx.media3.common.C1052C;
import com.itextpdf.text.Annotation;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MKSAPActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public boolean f90790X4;

    /* renamed from: Y4 */
    public String f90791Y4;

    /* renamed from: J4 */
    public Boolean m73341J4() {
        return Boolean.valueOf(!this.f89566D4.getString("Name").toLowerCase().contains("mksap17"));
    }

    /* JADX WARN: Removed duplicated region for block: B:34:0x011d  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x017c A[Catch: Exception -> 0x0057, TryCatch #5 {Exception -> 0x0057, blocks: (B:9:0x0030, B:12:0x0054, B:15:0x005a, B:17:0x0062, B:18:0x007a, B:20:0x00a2, B:32:0x00ef, B:35:0x011e, B:37:0x017c, B:38:0x0180, B:31:0x00d1, B:39:0x01b0, B:41:0x01c5, B:42:0x01cd, B:44:0x01e0, B:58:0x01fe, B:57:0x01f4, B:56:0x01f3, B:55:0x01f0), top: B:72:0x0030, inners: #3 }] */
    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public android.view.View mo15303U0(android.view.LayoutInflater r17, android.view.ViewGroup r18, android.os.Bundle r19) throws android.content.res.Resources.NotFoundException {
        /*
            Method dump skipped, instructions count: 553
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.mksap.MKSAPActivityFragment.mo15303U0(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        iMDLogger.m73550f("Javascript Console Message", consoleMessage.message() + " - " + consoleMessage.sourceId() + " - " + consoleMessage.lineNumber());
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        if (strArrSplit[0].equals("title") && strArrSplit.length == 2) {
            String str = strArrSplit[1];
            this.f89568F4 = str;
            this.f89574L4.setTitle(str);
            this.f89579Q4.m71771A0(this.f89566D4.getString("Name"), this.f89566D4.getString("Title"), this.f89567E4, this.f89568F4);
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Y3 */
    public void mo72808Y3(WebView webView, String str) {
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(final WebView webView, final String str) {
        iMDLogger.m73550f(" Finished", str);
        this.f89567E4 = str;
        this.f89569G4.m73433g("console.log('title,,,,,' + document.title )");
        this.f89590k4 = true;
        this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.mksap.MKSAPActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                MKSAPActivityFragment.super.mo71956Z3(webView, str);
            }
        }, C1052C.f19115c2);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
        menu.removeItem(C5562R.id.action_menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: v4 */
    public boolean mo71959v4() {
        return false;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: w4 */
    public WebResourceResponse mo72841w4(WebView webView, WebResourceRequest webResourceRequest) {
        String string = webResourceRequest.getUrl().toString();
        String str = StringUtils.splitByWholeSeparator(string, "/")[r7.length - 1];
        if (!str.endsWith(".json")) {
            return null;
        }
        String strReplace = str.replace(".json", "");
        CompressHelper compressHelper = this.f89579Q4;
        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from docs where docName='" + strReplace + "'"));
        if (bundleM71890s1 != null) {
            return new WebResourceResponse("application/json", "utf-8", new ByteArrayInputStream(this.f89579Q4.m71773B(bundleM71890s1.getString(Annotation.f68283i3), bundleM71890s1.getString("docName"), "127").getBytes(StandardCharsets.UTF_8)));
        }
        iMDLogger.m73550f("MKSAP", "can't find json of " + string + StringUtils.SPACE + bundleM71890s1);
        return null;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: x4 */
    public WebResourceResponse mo72843x4(WebView webView, String str) {
        String str2 = StringUtils.splitByWholeSeparator(str, "/")[r6.length - 1];
        if (!str2.endsWith(".json")) {
            return null;
        }
        String strReplace = str2.replace(".json", "");
        CompressHelper compressHelper = this.f89579Q4;
        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from docs where docName='" + strReplace + "'"));
        if (bundleM71890s1 != null) {
            return new WebResourceResponse("application/json", "utf-8", new ByteArrayInputStream(this.f89579Q4.m71773B(bundleM71890s1.getString(Annotation.f68283i3), bundleM71890s1.getString("docName"), "127").getBytes(StandardCharsets.UTF_8)));
        }
        iMDLogger.m73550f("MKSAP", "can't find json of " + str + StringUtils.SPACE + bundleM71890s1);
        return null;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

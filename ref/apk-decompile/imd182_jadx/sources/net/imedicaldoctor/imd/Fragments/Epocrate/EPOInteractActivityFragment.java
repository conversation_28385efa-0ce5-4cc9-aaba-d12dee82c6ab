package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.p003ts.TsExtractor;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.Annotation;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullDeleteViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.helper.StringUtil;

/* loaded from: classes3.dex */
public class EPOInteractActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88067A4;

    /* renamed from: B4 */
    public ArrayList<Bundle> f88068B4;

    /* renamed from: C4 */
    public Button f88069C4;

    /* renamed from: D4 */
    public String f88070D4;

    /* renamed from: E4 */
    public ArrayList<String> f88071E4;

    /* renamed from: F4 */
    public ArrayList<Bundle> f88072F4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_epointeract, viewGroup, false);
        this.f88068B4 = new ArrayList<>();
        this.f88071E4 = new ArrayList<>();
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f88070D4 = "RX.sqlite";
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(false, false);
        appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                relativeLayout.setVisibility(0);
            }
        }, 800L);
        Button button = (Button) this.f88797q4.findViewById(C5562R.id.result_button);
        this.f88069C4 = button;
        button.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                if (EPOInteractActivityFragment.this.f88068B4.size() > 0) {
                    new Bundle().putParcelableArrayList("Items", EPOInteractActivityFragment.this.f88068B4);
                    ArrayList arrayList = new ArrayList();
                    Iterator<Bundle> it2 = EPOInteractActivityFragment.this.f88068B4.iterator();
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        String string = next.getString("text");
                        arrayList.add(EPOInteractActivityFragment.this.m72194j3(next.getString("contentId")) + ",,,,," + string);
                    }
                    EPOInteractActivityFragment ePOInteractActivityFragment = EPOInteractActivityFragment.this;
                    ePOInteractActivityFragment.f88791k4.m71772A1(ePOInteractActivityFragment.f88788h4, "interactresult-" + StringUtil.m76600g(arrayList, ";;;;;"), null, null);
                }
            }
        });
        ChaptersAdapter chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88068B4, "title", C5562R.layout.list_view_item_ripple_text_full_delete) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullDeleteViewHolder rippleTextFullDeleteViewHolder = (RippleTextFullDeleteViewHolder) viewHolder;
                rippleTextFullDeleteViewHolder.f101493I.setText(bundle2.getString("text"));
                rippleTextFullDeleteViewHolder.f101494J.setText(bundle2.getString(Annotation.f68283i3));
                rippleTextFullDeleteViewHolder.f101498N.setVisibility(0);
                rippleTextFullDeleteViewHolder.f101496L.setVisibility(0);
                if (bundle2.getString(Annotation.f68283i3).length() == 0) {
                    rippleTextFullDeleteViewHolder.f101494J.setVisibility(8);
                } else {
                    rippleTextFullDeleteViewHolder.f101494J.setVisibility(0);
                }
                String string = bundle2.getString("type");
                rippleTextFullDeleteViewHolder.f101495K.setImageDrawable(EPOInteractActivityFragment.this.m15366r().getResources().getDrawable(string.equals("7") ? C5562R.drawable.plus_alt : string.equals("6") ? C5562R.drawable.plus_otc : C5562R.drawable.plus_rx));
                final String strM72194j3 = EPOInteractActivityFragment.this.m72194j3(bundle2.getString("contentId"));
                rippleTextFullDeleteViewHolder.f101497M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.3.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        EPOInteractActivityFragment.this.m72468V2();
                        EPOInteractActivityFragment ePOInteractActivityFragment = EPOInteractActivityFragment.this;
                        ePOInteractActivityFragment.f88791k4.m71772A1(ePOInteractActivityFragment.f88788h4, "interact-" + strM72194j3, null, null);
                    }
                });
                rippleTextFullDeleteViewHolder.f101498N.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.3.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        EPOInteractActivityFragment.this.f88068B4.remove(bundle2);
                        EPOInteractActivityFragment.this.f88071E4.remove(strM72194j3);
                        EPOInteractActivityFragment ePOInteractActivityFragment = EPOInteractActivityFragment.this;
                        ((ChaptersAdapter) ePOInteractActivityFragment.f88792l4).m73465g0(ePOInteractActivityFragment.f88068B4);
                        EPOInteractActivityFragment.this.m72193i3();
                        EPOInteractActivityFragment.this.f88792l4.m27491G();
                        EPOInteractActivityFragment ePOInteractActivityFragment2 = EPOInteractActivityFragment.this;
                        ePOInteractActivityFragment2.f88803w4.setAdapter(ePOInteractActivityFragment2.f88792l4);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                return new RippleTextFullDeleteViewHolder(view);
            }
        };
        this.f88792l4 = chaptersAdapter;
        chaptersAdapter.f101434h = "Search To Add Drug";
        this.f88067A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.4
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                rippleTextFullViewHolder.f101502L.setVisibility(8);
                if (bundle2.getString(Annotation.f68283i3).length() == 0) {
                    rippleTextFullViewHolder.f101500J.setVisibility(8);
                } else {
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                }
                String string = bundle2.getString("type");
                rippleTextFullViewHolder.f101501K.setImageDrawable(EPOInteractActivityFragment.this.m15366r().getResources().getDrawable(string.equals("7") ? C5562R.drawable.plus_alt : string.equals("6") ? C5562R.drawable.plus_otc : C5562R.drawable.plus_rx));
                final String strM72194j3 = EPOInteractActivityFragment.this.m72194j3(bundle2.getString("contentId"));
                if (EPOInteractActivityFragment.this.f88071E4.contains(strM72194j3)) {
                    rippleTextFullViewHolder.f101499I.setTextColor(Color.rgb(TsExtractor.f30466L, TsExtractor.f30466L, TsExtractor.f30466L));
                } else {
                    rippleTextFullViewHolder.f101499I.setTextColor(Color.rgb(0, 0, 0));
                    rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.4.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            EPOInteractActivityFragment.this.m72468V2();
                            EPOInteractActivityFragment.this.f88068B4.add(bundle2);
                            EPOInteractActivityFragment.this.f88071E4.add(strM72194j3);
                            EPOInteractActivityFragment.this.m72193i3();
                            EPOInteractActivityFragment.this.f88799s4.m2508k0("", false);
                            EPOInteractActivityFragment ePOInteractActivityFragment = EPOInteractActivityFragment.this;
                            ((ChaptersAdapter) ePOInteractActivityFragment.f88792l4).m73465g0(ePOInteractActivityFragment.f88068B4);
                            EPOInteractActivityFragment.this.f88792l4.m27491G();
                            EPOInteractActivityFragment ePOInteractActivityFragment2 = EPOInteractActivityFragment.this;
                            ePOInteractActivityFragment2.f88803w4.setAdapter(ePOInteractActivityFragment2.f88792l4);
                        }
                    });
                }
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                EPOInteractActivityFragment.this.m72468V2();
                EPOInteractActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                return new RippleTextFullViewHolder(view);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88067A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88067A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match '(text:" + str + "* OR content:" + str + "*) AND typeText:RX NOT (type:5)'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: c3 */
    public void mo72171c3() {
        this.f88800t4.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.interaction_check_icon));
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "Drug Interactions";
    }

    /* renamed from: i3 */
    public void m72193i3() {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.5
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                EPOInteractActivityFragment.this.f88072F4 = new ArrayList<>();
                ArrayList<String> arrayList = EPOInteractActivityFragment.this.f88071E4;
                if (arrayList != null && arrayList.size() > 0) {
                    String strJoin = StringUtils.join(EPOInteractActivityFragment.this.f88071E4, ",");
                    EPOInteractActivityFragment ePOInteractActivityFragment = EPOInteractActivityFragment.this;
                    ePOInteractActivityFragment.f88072F4 = ePOInteractActivityFragment.f88791k4.m71822X(ePOInteractActivityFragment.f88788h4, "SELECT                     ID,                     DRUG_ID AS DRUG_0_ID,                     INTERACTING_DRUG_ID AS DRUG_1_ID,                     DDI_ID,                     GROUP_0_ID,                     GROUP_1_ID                     FROM (                     SELECT DISTINCT                     tDID.ID,                     MIN(d1.ID, d2.ID) AS DRUG_ID,                     MAX(d1.ID, d2.ID) AS INTERACTING_DRUG_ID,                     tDID.DDI_ID,                     DDI.GROUP_0_ID,                     DDI.GROUP_1_ID                     FROM                     DRUG_TO_INTERACTING_DRUG tDID                     JOIN DDI ON tDID.DDI_ID = DDI.ID                     JOIN DRUG d1 ON d1.ID = tDID.DRUG_0_ID OR d1.GENERIC_ID = tDID.DRUG_0_ID OR d1.ID = tDID.DRUG_1_ID OR d1.GENERIC_ID = tDID.DRUG_1_ID                     JOIN DRUG d2 ON                     CASE WHEN d1.ID = tDID.DRUG_0_ID OR d1.GENERIC_ID = tDID.DRUG_0_ID                     THEN d2.ID = tDID.DRUG_1_ID OR d2.GENERIC_ID = tDID.DRUG_1_ID                     ELSE d2.ID = tDID.DRUG_0_ID OR d2.GENERIC_ID = tDID.DRUG_0_ID                     END                     WHERE                     tDID.DRUG_0_ID IN (" + strJoin + ")                     AND                     tDID.DRUG_1_ID IN (" + strJoin + ")                     AND                     DRUG_0_ID <> DRUG_1_ID                     AND                     d1.ID IN (" + strJoin + ")                     AND                     d2.ID IN (" + strJoin + ")                     ORDER BY CATEGORY_ID, d1.name, d2.name                     ) ", EPOInteractActivityFragment.this.f88070D4, true);
                }
                observableEmitter.onNext("asdf");
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.6
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                if (EPOInteractActivityFragment.this.f88072F4.size() == 0) {
                    EPOInteractActivityFragment.this.f88069C4.setEnabled(false);
                    EPOInteractActivityFragment.this.f88069C4.setBackgroundColor(Color.rgb(100, 100, 100));
                    EPOInteractActivityFragment.this.f88069C4.setText("Nothing Found");
                    return;
                }
                EPOInteractActivityFragment.this.f88069C4.setText(EPOInteractActivityFragment.this.f88072F4.size() + " Interactions Found");
                EPOInteractActivityFragment.this.f88069C4.setEnabled(true);
                EPOInteractActivityFragment.this.f88069C4.setBackgroundColor(Color.rgb(64, 140, 83));
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOInteractActivityFragment.7
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
            }
        });
    }

    /* renamed from: j3 */
    public String m72194j3(String str) {
        return str.contains("-") ? str.split("-")[1] : str;
    }
}

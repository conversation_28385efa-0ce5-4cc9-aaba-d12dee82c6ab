package net.imedicaldoctor.imd.Fragments.DRE;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import com.itextpdf.tool.xml.html.HTML;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.HeaderCellViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import saman.zamani.persiandate.PersianDate;
import saman.zamani.persiandate.PersianDateFormat;

/* loaded from: classes3.dex */
public class DRETestResultActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f87775X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f87776Y4;

    /* renamed from: Z4 */
    public Bundle f87777Z4;

    /* renamed from: a5 */
    public ArrayList<String> f87778a5;

    /* renamed from: b5 */
    public UWTestResultAdapter f87779b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f87780c5;

    /* renamed from: d5 */
    public Bundle f87781d5;

    /* renamed from: e5 */
    public int f87782e5 = 0;

    /* renamed from: f5 */
    public int f87783f5 = 0;

    /* renamed from: g5 */
    public int f87784g5 = 0;

    /* renamed from: h5 */
    public final int f87785h5 = 0;

    /* renamed from: i5 */
    public final int f87786i5 = 1;

    /* renamed from: j5 */
    public final int f87787j5 = 2;

    /* renamed from: k5 */
    public final int f87788k5 = 3;

    /* renamed from: l5 */
    public boolean f87789l5;

    public class QuestionViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f87790I;

        /* renamed from: J */
        public TextView f87791J;

        /* renamed from: K */
        public TextView f87792K;

        /* renamed from: L */
        public TextView f87793L;

        /* renamed from: M */
        public ImageView f87794M;

        /* renamed from: N */
        public MaterialRippleLayout f87795N;

        public QuestionViewHolder(View view) {
            super(view);
            this.f87790I = (TextView) view.findViewById(C5562R.id.text_number);
            this.f87791J = (TextView) view.findViewById(C5562R.id.text_title);
            this.f87792K = (TextView) view.findViewById(C5562R.id.text_correct);
            this.f87795N = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f87793L = (TextView) view.findViewById(C5562R.id.text_time);
            this.f87794M = (ImageView) view.findViewById(C5562R.id.image_view);
        }
    }

    public class TestScoreViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f87797I;

        /* renamed from: J */
        public TextView f87798J;

        /* renamed from: K */
        public TextView f87799K;

        /* renamed from: L */
        public TextView f87800L;

        /* renamed from: M */
        public ImageView f87801M;

        /* renamed from: N */
        public TextView f87802N;

        /* renamed from: O */
        public MaterialRippleLayout f87803O;

        public TestScoreViewHolder(View view) {
            super(view);
            this.f87797I = (TextView) view.findViewById(C5562R.id.text_date);
            this.f87798J = (TextView) view.findViewById(C5562R.id.text_info1);
            this.f87799K = (TextView) view.findViewById(C5562R.id.text_info2);
            this.f87803O = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f87800L = (TextView) view.findViewById(C5562R.id.text_score);
            this.f87801M = (ImageView) view.findViewById(C5562R.id.image_view);
            this.f87802N = (TextView) view.findViewById(C5562R.id.text_resume);
        }
    }

    public class UWTestResultAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        public String f87805d;

        public UWTestResultAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            if (i2 == 0) {
                return 0;
            }
            if (i2 == 1) {
                return 1;
            }
            return i2 == DRETestResultActivityFragment.this.f87780c5.size() + 2 ? 3 : 2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            TextView textView;
            StringBuilder sb;
            TextView textView2;
            String str;
            ImageView imageView;
            Resources resourcesM15320b0;
            int i3;
            String string;
            int iM27811F = viewHolder.m27811F();
            if (iM27811F == 3) {
                HeaderCellViewHolder headerCellViewHolder = (HeaderCellViewHolder) viewHolder;
                headerCellViewHolder.f101460I.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                textView = headerCellViewHolder.f101460I;
                sb = new StringBuilder();
                sb.append(DRETestResultActivityFragment.this.f87782e5);
                sb.append(" درست . ");
                sb.append(DRETestResultActivityFragment.this.f87783f5);
                sb.append(" نادرست . ");
                sb.append(DRETestResultActivityFragment.this.f87784g5);
                sb.append(" نزده");
            } else {
                if (iM27811F == 1) {
                    HeaderCellViewHolder headerCellViewHolder2 = (HeaderCellViewHolder) viewHolder;
                    headerCellViewHolder2.f101460I.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                    textView = headerCellViewHolder2.f101460I;
                    string = "سوالات";
                    textView.setText(string);
                }
                if (iM27811F == 2) {
                    final int i4 = i2 - 2;
                    Bundle bundle = DRETestResultActivityFragment.this.f87780c5.get(i4);
                    QuestionViewHolder questionViewHolder = (QuestionViewHolder) viewHolder;
                    questionViewHolder.f87790I.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                    questionViewHolder.f87791J.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                    questionViewHolder.f87793L.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                    questionViewHolder.f87792K.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                    questionViewHolder.f87790I.setText(String.valueOf(i2 - 1));
                    questionViewHolder.f87791J.setText(bundle.getString("title"));
                    if (DRETestResultActivityFragment.this.f87789l5) {
                        textView2 = questionViewHolder.f87792K;
                        str = "-";
                    } else {
                        textView2 = questionViewHolder.f87792K;
                        str = bundle.getString("CorrPerc") + CSS.Value.f74136n0;
                    }
                    textView2.setText(str);
                    questionViewHolder.f87793L.setText(bundle.getString(HTML.Tag.f74358P0) + " ثانیه");
                    String string2 = bundle.getString("selectedAnswer");
                    String string3 = bundle.getString("corrAnswer");
                    if (string2.length() == 0) {
                        imageView = questionViewHolder.f87794M;
                        resourcesM15320b0 = DRETestResultActivityFragment.this.m15320b0();
                        i3 = C5562R.drawable.omitted_icon;
                    } else if (string2.equals(string3)) {
                        imageView = questionViewHolder.f87794M;
                        resourcesM15320b0 = DRETestResultActivityFragment.this.m15320b0();
                        i3 = C5562R.drawable.correct_icon;
                    } else {
                        imageView = questionViewHolder.f87794M;
                        resourcesM15320b0 = DRETestResultActivityFragment.this.m15320b0();
                        i3 = C5562R.drawable.incorrect_icon;
                    }
                    imageView.setImageDrawable(resourcesM15320b0.getDrawable(i3));
                    questionViewHolder.f87795N.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestResultActivityFragment.UWTestResultAdapter.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            Bundle bundle2 = new Bundle();
                            bundle2.putInt("gotoQIndex", i4);
                            DRETestResultActivityFragment dRETestResultActivityFragment = DRETestResultActivityFragment.this;
                            dRETestResultActivityFragment.f89579Q4.m71775B1(dRETestResultActivityFragment.f89566D4, "test-" + DRETestResultActivityFragment.this.f87781d5.getString("id"), null, null, bundle2);
                        }
                    });
                    return;
                }
                if (iM27811F != 0) {
                    return;
                }
                TestScoreViewHolder testScoreViewHolder = (TestScoreViewHolder) viewHolder;
                testScoreViewHolder.f87797I.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87798J.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87799K.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87802N.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                testScoreViewHolder.f87800L.setTypeface(ResourcesCompat.m7158j(DRETestResultActivityFragment.this.m15366r(), C5562R.font.iransans));
                TextView textView3 = testScoreViewHolder.f87797I;
                DRETestResultActivityFragment dRETestResultActivityFragment = DRETestResultActivityFragment.this;
                textView3.setText(dRETestResultActivityFragment.m72044J4(dRETestResultActivityFragment.f87781d5.getString("createdDate")));
                String str2 = DRETestResultActivityFragment.this.f87781d5.getString("mode").equals("Testing") ? "امتحان" : "مطالعه";
                testScoreViewHolder.f87798J.setText(DRETestResultActivityFragment.this.f87780c5.size() + " سوال . حالت مطالعه: " + str2);
                DRETestResultActivityFragment dRETestResultActivityFragment2 = DRETestResultActivityFragment.this;
                if (dRETestResultActivityFragment2.f87789l5) {
                    testScoreViewHolder.f87799K.setText(dRETestResultActivityFragment2.f87781d5.getString("subject"));
                } else {
                    testScoreViewHolder.f87799K.setText(DRETestResultActivityFragment.this.f87781d5.getString("subject") + " , " + DRETestResultActivityFragment.this.f87781d5.getString("system"));
                }
                testScoreViewHolder.f87801M.setImageDrawable(DRETestResultActivityFragment.this.m15320b0().getDrawable(C5562R.drawable.circle_green));
                testScoreViewHolder.f87802N.setText("نمره");
                testScoreViewHolder.f87800L.setVisibility(0);
                textView = testScoreViewHolder.f87800L;
                sb = new StringBuilder();
                sb.append(DRETestResultActivityFragment.this.f87781d5.getString("score"));
                sb.append(CSS.Value.f74136n0);
            }
            string = sb.toString();
            textView.setText(string);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 3) {
                return new HeaderCellViewHolder(LayoutInflater.from(DRETestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_footer, viewGroup, false));
            }
            if (i2 == 1) {
                return new HeaderCellViewHolder(LayoutInflater.from(DRETestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 2) {
                return DRETestResultActivityFragment.this.new QuestionViewHolder(LayoutInflater.from(DRETestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_dre_quetion, viewGroup, false));
            }
            if (i2 != 0) {
                return null;
            }
            return DRETestResultActivityFragment.this.new TestScoreViewHolder(LayoutInflater.from(DRETestResultActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_dre_test, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return DRETestResultActivityFragment.this.f87780c5.size() + 3;
        }

        /* renamed from: d0 */
        public String m72045d0(String str) {
            return str;
        }

        /* renamed from: e0 */
        public void m72046e0(RecyclerView.ViewHolder viewHolder, Bundle bundle, int i2) {
        }

        /* renamed from: f0 */
        public void m72047f0(Bundle bundle, int i2) {
        }

        /* renamed from: g0 */
        public RecyclerView.ViewHolder m72048g0(View view) {
            return new RippleTextViewHolder(view);
        }
    }

    /* renamed from: I4 */
    public void m72043I4() {
        this.f87775X4.setItemAnimator(new DefaultItemAnimator());
        this.f87775X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f87775X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: J4 */
    public String m72044J4(String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ");
        new SimpleDateFormat("MM dd,yyyy HH:mm:ss");
        try {
            return new PersianDateFormat().m78973b(new PersianDate(Long.valueOf(simpleDateFormat.parse(str).getTime())));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return str;
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        CompressHelper compressHelper;
        Bundle bundle2;
        StringBuilder sb;
        String str;
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f87775X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        String str2 = this.f89567E4.split("-")[1];
        this.f87789l5 = this.f89579Q4.m71817V(this.f89566D4, "Select CorrPerc from Questions limit 1") == null;
        CompressHelper compressHelper2 = this.f89579Q4;
        this.f87781d5 = compressHelper2.m71890s1(compressHelper2.m71817V(this.f89566D4, "select * from tests where id=" + str2));
        if (this.f87789l5) {
            compressHelper = this.f89579Q4;
            bundle2 = this.f89566D4;
            sb = new StringBuilder();
            str = "Select questions.id,'سوال شماره '|| questions.id as title,selectedAnswer,corrAnswer,time  from Questions left outer join (select * from logs where testId=";
        } else {
            compressHelper = this.f89579Q4;
            bundle2 = this.f89566D4;
            sb = new StringBuilder();
            str = "Select questions.id,CorrPerc,'سوال شماره '|| questions.id as title,selectedAnswer,corrAnswer,time  from Questions left outer join (select * from logs where testId=";
        }
        sb.append(str);
        sb.append(str2);
        sb.append(") as logs2 on questions.id=logs2.qid where questions.id in (");
        sb.append(this.f87781d5.getString("qIds"));
        sb.append(")");
        this.f87780c5 = compressHelper.m71817V(bundle2, sb.toString());
        this.f89568F4 = "نتیجه آزمون";
        Iterator<Bundle> it2 = this.f87780c5.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (next.getString("selectedAnswer").length() == 0) {
                this.f87784g5++;
            } else if (next.getString("selectedAnswer").equals(next.getString("corrAnswer"))) {
                this.f87782e5++;
            } else {
                this.f87783f5++;
            }
        }
        UWTestResultAdapter uWTestResultAdapter = new UWTestResultAdapter();
        this.f87779b5 = uWTestResultAdapter;
        this.f87775X4.setAdapter(uWTestResultAdapter);
        m72043I4();
        mo72642f3(C5562R.menu.empty);
        m15358o2(false);
        m72786G3();
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        Bundle bundleM72839v3;
        ArrayList<Bundle> arrayList = this.f87776Y4;
        if (arrayList == null || arrayList.size() == 0 || (bundleM72839v3 = m72839v3(this.f87776Y4)) == null) {
            return;
        }
        Glide.m30041G(m15366r()).mo30129t("http://www.epocrates.com/pillimages/" + (bundleM72839v3.getString("FILENAME") + ".jpg")).m30165B2(this.f89575M4);
    }
}

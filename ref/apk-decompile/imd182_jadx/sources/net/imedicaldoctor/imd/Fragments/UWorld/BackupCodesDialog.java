package net.imedicaldoctor.imd.Fragments.UWorld;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import com.itextpdf.tool.xml.html.HTML;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class BackupCodesDialog {

    public interface BackupCodeSelectedListener {
        /* renamed from: a */
        void mo72543a(String str);
    }

    /* renamed from: a */
    public static void m72542a(Context context, final ArrayList<Bundle> arrayList, final BackupCodeSelectedListener backupCodeSelectedListener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        View viewInflate = ((LayoutInflater) context.getSystemService("layout_inflater")).inflate(C5562R.layout.dialog_backup_codes, (ViewGroup) null);
        builder.setView(viewInflate);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.listViewBackupCodes);
        listView.setAdapter((ListAdapter) new BackupCodesAdapter(context, arrayList));
        final String[] strArr = {null};
        final AlertDialog alertDialogCreate = builder.create();
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.BackupCodesDialog.1
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                BackupCodeSelectedListener backupCodeSelectedListener2;
                strArr[0] = ((Bundle) arrayList.get(i2)).getString(HTML.Tag.f74390g0);
                String str = strArr[0];
                if (str != null && (backupCodeSelectedListener2 = backupCodeSelectedListener) != null) {
                    backupCodeSelectedListener2.mo72543a(str);
                }
                alertDialogCreate.dismiss();
            }
        });
        alertDialogCreate.show();
    }
}

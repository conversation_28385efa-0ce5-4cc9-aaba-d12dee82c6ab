package net.imedicaldoctor.imd.Fragments.Martindale;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import com.google.android.material.tabs.TabLayout;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MDViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88462X4;

    /* renamed from: Y4 */
    public ArrayList<String> f88463Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88464Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f88465a5;

    /* renamed from: b5 */
    public String f88466b5;

    /* renamed from: c5 */
    public TabLayout f88467c5;

    /* renamed from: L4 */
    private void m72353L4(String str) {
        ArrayList<String> arrayList = this.f88463Y4;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f88463Y4.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", next);
            bundle.putString("Description", "");
            bundle.putString("id", next);
            if (new File(next).length() > 5000) {
                arrayList2.add(bundle);
            }
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public void m72354I4(String str) {
        TabLayout.Tab tabM40228I = this.f88467c5.m40228I();
        tabM40228I.m40276D(str);
        this.f88467c5.m40248i(tabM40228I);
    }

    /* JADX WARN: Can't wrap try/catch for region: R(8:0|2|(1:4)(2:6|(1:8)(2:9|(1:11)(6:12|(1:14)(1:15)|22|16|20|21)))|5|22|16|20|21) */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0110, code lost:
    
        r7 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x0111, code lost:
    
        com.google.firebase.crashlytics.FirebaseCrashlytics.m48010d().m48016g(r7);
        r7.printStackTrace();
     */
    /* renamed from: J4 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m72355J4(java.lang.String r7) {
        /*
            Method dump skipped, instructions count: 297
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Martindale.MDViewerActivityFragment.m72355J4(java.lang.String):void");
    }

    /* renamed from: K4 */
    public String m72356K4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return m72840w3(this.f88463Y4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer_tab, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88467c5 = (TabLayout) this.f89565C4.findViewById(C5562R.id.tabs);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Martindale.MDViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String str = MDViewerActivityFragment.this.f89563A4;
                    if (str == null || str.length() == 0) {
                        MDViewerActivityFragment mDViewerActivityFragment = MDViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71817V = mDViewerActivityFragment.f89579Q4.m71817V(mDViewerActivityFragment.f89566D4, "Select * from docs where id=" + MDViewerActivityFragment.this.f89567E4);
                        if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                            MDViewerActivityFragment.this.f88462X4 = arrayListM71817V.get(0);
                            MDViewerActivityFragment mDViewerActivityFragment2 = MDViewerActivityFragment.this;
                            mDViewerActivityFragment2.f89568F4 = mDViewerActivityFragment2.f88462X4.getString("title");
                            MDViewerActivityFragment.this.m72354I4("Monograph");
                            if (MDViewerActivityFragment.this.f88462X4.getString("adult_ed").length() > 0) {
                                MDViewerActivityFragment.this.m72354I4("Adult Pt Ed");
                            }
                            if (MDViewerActivityFragment.this.f88462X4.getString("ped_ed").length() > 0) {
                                MDViewerActivityFragment.this.m72354I4("Ped Pt Ed");
                            }
                            if (MDViewerActivityFragment.this.f88462X4.getString("product_list").length() > 0) {
                                MDViewerActivityFragment.this.m72354I4("Products");
                            }
                            MDViewerActivityFragment.this.f88467c5.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.Martindale.MDViewerActivityFragment.1.1
                                @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
                                /* renamed from: a */
                                public void mo40255a(TabLayout.Tab tab) {
                                }

                                @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
                                /* renamed from: b */
                                public void mo40256b(TabLayout.Tab tab) {
                                    MDViewerActivityFragment.this.m72355J4(tab.m40287n().toString());
                                }

                                @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
                                /* renamed from: c */
                                public void mo40257c(TabLayout.Tab tab) {
                                }
                            });
                        }
                        MDViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                        return;
                    }
                    MDViewerActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    MDViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Martindale.MDViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = MDViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    MDViewerActivityFragment mDViewerActivityFragment = MDViewerActivityFragment.this;
                    mDViewerActivityFragment.m72780C4(mDViewerActivityFragment.f89595p4);
                    return;
                }
                MDViewerActivityFragment.this.m72355J4("Monograph");
                MDViewerActivityFragment.this.m72836s4();
                MDViewerActivityFragment.this.m72831p4();
                MDViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                MDViewerActivityFragment.this.m15358o2(false);
                MDViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = strM71753g1.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72356K4(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = strM71753g1 + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f88463Y4 = arrayList;
            mo71972o4();
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        super.mo71956Z3(webView, str);
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        this.f89569G4.m73433g("onBodyLoad();");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            m72353L4("asdfafdsaf");
            return true;
        }
        if (itemId == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f88465a5);
            bundle.putString("TitleProperty", "title");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        String strM71893t1;
        if (str2.equals("image")) {
            m72353L4(str3);
            return true;
        }
        if (str2.equals(Annotation.f68285k3) || (str2.equals("http") && str3.contains("localhost:"))) {
            String strM71893t12 = this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(str3, "/"));
            if (strM71893t12 != null && strM71893t12.length() != 0) {
                if (strM71893t12.contains("?")) {
                    strM71893t12 = StringUtils.splitByWholeSeparator(strM71893t12, "?")[0];
                }
                if (str3.contains("/pated_f/")) {
                    Bundle bundleM71826Y0 = this.f89579Q4.m71826Y0("Name", "259_pated_f.sqlite");
                    if (bundleM71826Y0 == null) {
                        CompressHelper.m71767x2(m15366r(), "You Must Install Adult Patient Education Database", 1);
                        return true;
                    }
                    this.f89579Q4.m71772A1(bundleM71826Y0, strM71893t12, null, null);
                }
                if (str3.contains("/pedip_f/")) {
                    Bundle bundleM71826Y02 = this.f89579Q4.m71826Y0("Name", "261_pedip_f.sqlite");
                    if (bundleM71826Y02 == null) {
                        CompressHelper.m71767x2(m15366r(), "You Must Install Pediatric Patient Education Database", 1);
                        return true;
                    }
                    this.f89579Q4.m71772A1(bundleM71826Y02, strM71893t12, null, null);
                }
                if (strM71893t12.contains("#")) {
                    strM71893t1 = this.f89579Q4.m71893t1(StringUtils.splitByWholeSeparator(strM71893t12, "#"));
                    strM71893t12 = StringUtils.splitByWholeSeparator(strM71893t12, "#")[0];
                } else {
                    strM71893t1 = "";
                }
                if (strM71893t12.equals(this.f89567E4)) {
                    mo71967C3(strM71893t1);
                    return true;
                }
                CompressHelper compressHelper = this.f89579Q4;
                if (compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "Select * from docs where id = '" + strM71893t12 + "'")) != null) {
                    this.f89579Q4.m71772A1(this.f89566D4, strM71893t12, null, strM71893t1);
                }
                iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            }
        } else {
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        }
        return true;
    }
}

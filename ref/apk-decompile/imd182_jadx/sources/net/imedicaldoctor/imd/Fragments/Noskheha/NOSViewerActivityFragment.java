package net.imedicaldoctor.imd.Fragments.Noskheha;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class NOSViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f88663X4;

    /* renamed from: Y4 */
    public ArrayList<String> f88664Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88665Z4;

    /* renamed from: a5 */
    public boolean f88666a5;

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: J4 */
    public String m72424J4(String str, String str2, String str3, String str4, String str5) {
        return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + str5 + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        this.f88666a5 = true;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Noskheha.NOSViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                NOSViewerActivityFragment nOSViewerActivityFragment;
                String str;
                String str2;
                String str3;
                String str4;
                try {
                    String str5 = NOSViewerActivityFragment.this.f89563A4;
                    if (str5 == null || str5.length() == 0) {
                        iMDLogger.m73550f("Loading Document", NOSViewerActivityFragment.this.f89567E4);
                        NOSViewerActivityFragment nOSViewerActivityFragment2 = NOSViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71817V = nOSViewerActivityFragment2.f89579Q4.m71817V(nOSViewerActivityFragment2.f89566D4, "Select * from docs where id=" + NOSViewerActivityFragment.this.f89567E4);
                        if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                            NOSViewerActivityFragment.this.f88663X4 = arrayListM71817V.get(0);
                            NOSViewerActivityFragment nOSViewerActivityFragment3 = NOSViewerActivityFragment.this;
                            nOSViewerActivityFragment3.f89568F4 = nOSViewerActivityFragment3.f88663X4.getString("name");
                            NOSViewerActivityFragment nOSViewerActivityFragment4 = NOSViewerActivityFragment.this;
                            nOSViewerActivityFragment4.f89568F4 = nOSViewerActivityFragment4.f89568F4.replace("\\n", StringUtils.SPACE);
                            String strReplace = NOSViewerActivityFragment.this.f88663X4.getString(Annotation.f68283i3).replace("\\n", "<br />");
                            if (NOSViewerActivityFragment.this.f89566D4.getString("Name").equals("orders.db")) {
                                nOSViewerActivityFragment = NOSViewerActivityFragment.this;
                                str = "";
                                str2 = "";
                                str3 = "LTR";
                                str4 = "0";
                            } else {
                                nOSViewerActivityFragment = NOSViewerActivityFragment.this;
                                str = "X Traffic";
                                str2 = "";
                                str3 = "RTL";
                                str4 = "0";
                            }
                            String strM72424J4 = nOSViewerActivityFragment.m72424J4(strReplace, str, str2, str3, str4);
                            NOSViewerActivityFragment nOSViewerActivityFragment5 = NOSViewerActivityFragment.this;
                            String strM72817d4 = nOSViewerActivityFragment5.m72817d4(nOSViewerActivityFragment5.m15366r(), "IDHeader.css");
                            NOSViewerActivityFragment nOSViewerActivityFragment6 = NOSViewerActivityFragment.this;
                            String strM72817d42 = nOSViewerActivityFragment6.m72817d4(nOSViewerActivityFragment6.m15366r(), "IDFooter.css");
                            String strReplace2 = strM72817d4.replace("[size]", "200").replace("[title]", NOSViewerActivityFragment.this.f89568F4).replace("[include]", "");
                            NOSViewerActivityFragment.this.f89563A4 = strReplace2 + strM72424J4 + strM72817d42;
                        }
                        NOSViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                        return;
                    }
                    NOSViewerActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    NOSViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Noskheha.NOSViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = NOSViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    NOSViewerActivityFragment nOSViewerActivityFragment = NOSViewerActivityFragment.this;
                    nOSViewerActivityFragment.m72780C4(nOSViewerActivityFragment.f89595p4);
                    return;
                }
                String strM71753g1 = CompressHelper.m71753g1(NOSViewerActivityFragment.this.f89566D4, "base");
                NOSViewerActivityFragment.this.m72836s4();
                NOSViewerActivityFragment nOSViewerActivityFragment2 = NOSViewerActivityFragment.this;
                nOSViewerActivityFragment2.m72795O3(nOSViewerActivityFragment2.f89563A4, strM71753g1);
                NOSViewerActivityFragment.this.m72831p4();
                NOSViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                NOSViewerActivityFragment.this.m15358o2(false);
                NOSViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
        menu.removeItem(C5562R.id.action_menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

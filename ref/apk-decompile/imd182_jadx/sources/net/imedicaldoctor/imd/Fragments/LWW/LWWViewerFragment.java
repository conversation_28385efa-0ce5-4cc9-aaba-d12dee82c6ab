package net.imedicaldoctor.imd.Fragments.LWW;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.GeneralDialogFragment;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class LWWViewerFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    private GeneralDialogFragment f88327X4;

    /* renamed from: Y4 */
    private String f88328Y4;

    /* renamed from: Z4 */
    private MenuItem f88329Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f88330a5;

    /* renamed from: b5 */
    public ArrayList<Bundle> f88331b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f88332c5;

    /* renamed from: d5 */
    public ArrayList<Bundle> f88333d5;

    /* renamed from: e5 */
    public String f88334e5;

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Removed duplicated region for block: B:35:0x0171 A[PHI: r13
      0x0171: PHI (r13v4 java.lang.String) = (r13v3 java.lang.String), (r13v6 java.lang.String) binds: [B:31:0x0150, B:33:0x0169] A[DONT_GENERATE, DONT_INLINE]] */
    /* renamed from: I4 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m72295I4() {
        /*
            Method dump skipped, instructions count: 512
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.LWW.LWWViewerFragment.m72295I4():void");
    }

    /* renamed from: M4 */
    private void m72299M4(String str) {
        if (this.f88330a5.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
            return;
        }
        ArrayList arrayList = new ArrayList();
        arrayList.addAll(this.f88330a5);
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            if (((Bundle) arrayList.get(i3)).getString("id").startsWith(str)) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: C3 */
    public void mo71967C3(String str) {
        this.f89569G4.m73433g("showSection('" + str + "');");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList;
        Bundle bundleM72839v3;
        if (this.f88330a5.size() <= 0 || (arrayList = this.f88330a5) == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f88330a5)) == null) {
            return null;
        }
        return bundleM72839v3.getString("ImagePath");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(C5562R.menu.menu_amviewer, menu);
        m72833q4(menu);
        mo71957e3(menu);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        m72835r4(viewInflate, bundle);
        if (bundle != null) {
            this.f88328Y4 = bundle.getString("mResources");
            this.f88330a5 = bundle.getParcelableArrayList("mImages");
            this.f88331b5 = bundle.getParcelableArrayList("mOtherImages");
            this.f88332c5 = bundle.getParcelableArrayList("mSections");
        }
        if (m15387y() == null) {
            return viewInflate;
        }
        iMDLogger.m73554j("AMViewer", "Loading LWW Document with mDocAddress = " + this.f89567E4);
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.LWW.LWWViewerFragment.1
            @Override // java.lang.Runnable
            public void run() {
                ArrayList<Bundle> arrayListM71817V;
                try {
                    CompressHelper compressHelper = new CompressHelper(LWWViewerFragment.this.m15366r());
                    String str = LWWViewerFragment.this.f89563A4;
                    if (str == null || str.length() == 0) {
                        Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(LWWViewerFragment.this.f89566D4, "Select * from Docs where id=" + LWWViewerFragment.this.f89567E4));
                        if (bundleM71907z == null) {
                            Bundle bundleM71907z2 = compressHelper.m71907z(compressHelper.m71817V(LWWViewerFragment.this.f89566D4, "Select * from AllSections where id='" + LWWViewerFragment.this.f89567E4 + "' collate nocase"));
                            if (bundleM71907z2 == null) {
                                Bundle bundleM71907z3 = compressHelper.m71907z(compressHelper.m71817V(LWWViewerFragment.this.f89566D4, "Select * from AllSections where id like '" + LWWViewerFragment.this.f89567E4 + "%' collate nocase"));
                                if (bundleM71907z3 == null) {
                                    LWWViewerFragment.this.f89595p4 = "Document doesn't exist";
                                    return;
                                }
                                LWWViewerFragment.this.f89567E4 = bundleM71907z3.getString("docId");
                                arrayListM71817V = compressHelper.m71817V(LWWViewerFragment.this.f89566D4, "Select * from Docs where id=" + LWWViewerFragment.this.f89567E4);
                            } else {
                                LWWViewerFragment.this.f89567E4 = bundleM71907z2.getString("docId");
                                LWWViewerFragment.this.f88334e5 = bundleM71907z2.getString("id");
                                arrayListM71817V = compressHelper.m71817V(LWWViewerFragment.this.f89566D4, "Select * from Docs where id=" + LWWViewerFragment.this.f89567E4);
                            }
                            bundleM71907z = compressHelper.m71907z(arrayListM71817V);
                        }
                        String string = bundleM71907z.getString("mainContent");
                        LWWViewerFragment.this.f88328Y4 = bundleM71907z.getString("resources");
                        LWWViewerFragment.this.f89568F4 = bundleM71907z.getString("name");
                        String str2 = new String(compressHelper.m71897v(string, bundleM71907z.getString("id"), "127"));
                        LWWViewerFragment lWWViewerFragment = LWWViewerFragment.this;
                        String strM72817d4 = lWWViewerFragment.m72817d4(lWWViewerFragment.m15366r(), "LWWHeader.css");
                        LWWViewerFragment lWWViewerFragment2 = LWWViewerFragment.this;
                        String strM72817d42 = lWWViewerFragment2.m72817d4(lWWViewerFragment2.m15366r(), "LWWFooter.css");
                        String str3 = strM72817d4.replace("[size]", "200").replace("[title]", LWWViewerFragment.this.f89568F4) + str2 + strM72817d42;
                        LWWViewerFragment.this.m72826m3();
                        LWWViewerFragment lWWViewerFragment3 = LWWViewerFragment.this;
                        lWWViewerFragment3.f89563A4 = str3;
                        lWWViewerFragment3.m72295I4();
                        if (LWWViewerFragment.this.f88329Z4 != null) {
                            LWWViewerFragment.this.f88329Z4.setVisible(LWWViewerFragment.this.f88330a5.size() != 0);
                        }
                    }
                    LWWViewerFragment lWWViewerFragment4 = LWWViewerFragment.this;
                    lWWViewerFragment4.f88333d5 = compressHelper.m71817V(lWWViewerFragment4.f89566D4, "Select * from sections where docId=" + LWWViewerFragment.this.f89567E4);
                    if (compressHelper.m71903x1()) {
                        return;
                    }
                    LWWViewerFragment.this.m72827m4("Chapter");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                    LWWViewerFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.LWW.LWWViewerFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = LWWViewerFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    LWWViewerFragment lWWViewerFragment = LWWViewerFragment.this;
                    lWWViewerFragment.m72780C4(lWWViewerFragment.f89595p4);
                    return;
                }
                String strM71753g1 = CompressHelper.m71753g1(LWWViewerFragment.this.f89566D4, "base");
                LWWViewerFragment lWWViewerFragment2 = LWWViewerFragment.this;
                lWWViewerFragment2.m72795O3(lWWViewerFragment2.f89563A4, strM71753g1);
                LWWViewerFragment.this.m72836s4();
                LWWViewerFragment.this.m72831p4();
                LWWViewerFragment.this.mo72642f3(C5562R.menu.menu_amviewer);
                LWWViewerFragment.this.m15358o2(false);
                LWWViewerFragment.this.m72786G3();
            }
        });
        return viewInflate;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        iMDLogger.m73550f("Javascript Console Message", consoleMessage.message());
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        super.mo71956Z3(webView, str);
        String str2 = this.f88334e5;
        if (str2 != null) {
            mo71967C3(str2);
            this.f88334e5 = null;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            m72299M4("soheilvb");
        }
        if (itemId == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f88333d5);
            bundle.putString("TitleProperty", "name");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "lwwsections");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        this.f88329Z4 = menu.findItem(C5562R.id.action_gallery);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: t3 */
    public void mo71958t3(String str) {
        super.mo71958t3(str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        if (str2.equals("image")) {
            m72299M4(str3.replace("//", ""));
            return true;
        }
        String str4 = str3 + "&";
        if ((str2.equals(Annotation.f68285k3) || (str2.equals("http") & str4.contains("localhost:"))) && str4.contains("content.aspx")) {
            if (!str4.toLowerCase().contains("sectionid=")) {
                return false;
            }
            new CompressHelper(m15366r()).m71772A1(this.f89566D4, CompressHelper.m71751f(str4.toLowerCase() + "&", "sectionid=", "&"), null, null);
        }
        return true;
    }
}

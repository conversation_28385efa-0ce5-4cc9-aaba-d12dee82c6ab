package net.imedicaldoctor.imd.Fragments;

import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Typeface;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.basusingh.beautifulprogressdialog.BeautifulProgressDialog;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.appbar.AppBarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Data.HistoryAdapter;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.Views.ButtonSmall;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class SearchHelperFragment extends Fragment {

    /* renamed from: e4 */
    public int f88785e4;

    /* renamed from: f4 */
    public String f88786f4;

    /* renamed from: g4 */
    public ListView f88787g4;

    /* renamed from: h4 */
    public Bundle f88788h4;

    /* renamed from: i4 */
    public boolean f88789i4;

    /* renamed from: j4 */
    public boolean f88790j4;

    /* renamed from: k4 */
    public CompressHelper f88791k4;

    /* renamed from: l4 */
    public RecyclerView.Adapter f88792l4;

    /* renamed from: m4 */
    public ContentSearchAdapter f88793m4;

    /* renamed from: n4 */
    public ArrayList<Bundle> f88794n4;

    /* renamed from: o4 */
    public ArrayList<Bundle> f88795o4;

    /* renamed from: p4 */
    public ArrayList<Bundle> f88796p4;

    /* renamed from: q4 */
    public View f88797q4;

    /* renamed from: r4 */
    public Toolbar f88798r4;

    /* renamed from: s4 */
    public SearchView f88799s4;

    /* renamed from: t4 */
    public ImageView f88800t4;

    /* renamed from: u4 */
    public TextView f88801u4;

    /* renamed from: v4 */
    public ButtonSmall f88802v4;

    /* renamed from: w4 */
    public RecyclerView f88803w4;

    /* renamed from: x4 */
    public ImageButton f88804x4;

    /* renamed from: y4 */
    private DrawerLayout f88805y4;

    /* renamed from: z4 */
    public RecyclerView f88806z4;

    public class ContentSearchViewHolder {

        /* renamed from: a */
        public final TextView f88853a;

        /* renamed from: b */
        public final TextView f88854b;

        public ContentSearchViewHolder(View view) {
            this.f88853a = (TextView) view.findViewById(C5562R.id.title_text);
            this.f88854b = (TextView) view.findViewById(C5562R.id.subtitle_text);
        }
    }

    /* renamed from: J2 */
    public void m72458J2() {
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(false, false);
        appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.1
            @Override // java.lang.Runnable
            public void run() {
                relativeLayout.setVisibility(0);
            }
        }, 800L);
    }

    /* renamed from: K2 */
    public void m72459K2() {
        SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        this.f88799s4 = searchView;
        searchView.setIconifiedByDefault(false);
        this.f88799s4.setQueryHint("Can't Search in Demo");
        this.f88799s4.setEnabled(false);
    }

    /* renamed from: L2 */
    public void m72460L2(final Runnable runnable, final Runnable runnable2) {
        final BeautifulProgressDialog beautifulProgressDialog = new BeautifulProgressDialog(m15366r(), BeautifulProgressDialog.f37991q, null);
        beautifulProgressDialog.m30023p("loading-1.json");
        beautifulProgressDialog.m30024q(true);
        new Thread(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.8
            @Override // java.lang.Runnable
            public void run() throws InterruptedException {
                try {
                    Thread.sleep(500L);
                    SearchHelperFragment.this.f88797q4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.8.1
                        @Override // java.lang.Runnable
                        public void run() {
                            RunnableC50218 runnableC50218 = RunnableC50218.this;
                            if (SearchHelperFragment.this.f88790j4) {
                                return;
                            }
                            beautifulProgressDialog.m30028w();
                        }
                    });
                } catch (InterruptedException unused) {
                }
            }
        }).start();
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.9
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    runnable.run();
                    observableEmitter.onNext("asdfadf");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                    SearchHelperFragment.this.f88790j4 = true;
                    beautifulProgressDialog.m30009a();
                }
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.10
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                SearchHelperFragment.this.f88790j4 = true;
                beautifulProgressDialog.m30009a();
                try {
                    runnable2.run();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.11
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                SearchHelperFragment.this.f88790j4 = true;
                beautifulProgressDialog.m30009a();
                th.printStackTrace();
                FirebaseCrashlytics.m48010d().m48016g(th);
                runnable2.run();
            }
        });
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: N0 */
    public void mo15204N0(Context context) {
        super.mo15204N0(context);
    }

    /* renamed from: N2 */
    public void m72461N2() {
        this.f88803w4.setItemAnimator(new DefaultItemAnimator());
        this.f88803w4.m27459p(new CustomItemDecoration(m15246B()));
        this.f88803w4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: O2 */
    public void m72462O2() {
        if (this.f88788h4.containsKey("Demo")) {
            m72459K2();
            return;
        }
        SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88799s4.setIconifiedByDefault(false);
        this.f88799s4.setQueryHint("Search All");
        m72470Y2();
        this.f88799s4.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.20
            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: a */
            public boolean mo2516a(int i2) {
                Cursor cursorMo10512c = SearchHelperFragment.this.f88799s4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (SearchHelperFragment.this.f88799s4.getTag(1) != null && ((String) SearchHelperFragment.this.f88799s4.getTag(1)).length() > 0) {
                    string = SearchHelperFragment.this.f88799s4.getTag() + StringUtils.SPACE + string;
                }
                SearchHelperFragment.this.f88799s4.m2508k0(string, true);
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: b */
            public boolean mo2517b(int i2) {
                Cursor cursorMo10512c = SearchHelperFragment.this.f88799s4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (SearchHelperFragment.this.f88799s4.getTag() != null && ((String) SearchHelperFragment.this.f88799s4.getTag()).length() > 0) {
                    string = SearchHelperFragment.this.f88799s4.getTag() + StringUtils.SPACE + string;
                }
                SearchHelperFragment.this.f88799s4.m2508k0(string, true);
                return false;
            }
        });
        ((ImageView) this.f88799s4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.21
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchHelperFragment.this.f88799s4.m2508k0("", false);
                SearchHelperFragment.this.f88799s4.clearFocus();
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                searchHelperFragment.f88803w4.setAdapter(searchHelperFragment.f88792l4);
                SearchHelperFragment.this.m72468V2();
                SearchHelperFragment.this.mo72211Z2();
            }
        });
        ((SearchView.SearchAutoComplete) this.f88799s4.findViewById(C5562R.id.search_src_text)).setDropDownAnchor(C5562R.id.search_view);
        this.f88799s4.setSuggestionsAdapter(new CursorAdapter(m15366r(), null, 0) { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.22
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("word")));
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell, viewGroup, false);
                viewInflate.setTag(viewInflate.findViewById(C5562R.id.text));
                return viewInflate;
            }
        });
        this.f88799s4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.23
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                if (!searchHelperFragment.f88789i4) {
                    return true;
                }
                searchHelperFragment.f88786f4 = str;
                if (str.length() <= 1) {
                    return false;
                }
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.23.2
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        String[] strArrSplit = str.trim().split(StringUtils.SPACE);
                        String str2 = strArrSplit[strArrSplit.length - 1];
                        String str3 = "";
                        for (int i2 = 0; i2 < strArrSplit.length - 1; i2++) {
                            str3 = str3 + StringUtils.SPACE + strArrSplit[i2];
                        }
                        SearchHelperFragment.this.f88799s4.setTag(str3.trim());
                        return SearchHelperFragment.this.mo71951g3(str2);
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchHelperFragment.this.f88799s4.getSuggestionsAdapter().mo10519m(SearchHelperFragment.this.f88791k4.m71850h((ArrayList) obj));
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(final String str) {
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.23.1
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                        searchHelperFragment.f88795o4 = searchHelperFragment.mo71950a3(str);
                        return null;
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchHelperFragment.this.mo71973X2();
                    }

                    @Override // android.os.AsyncTask
                    protected void onPreExecute() {
                        try {
                            SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                            searchHelperFragment.f88791k4.m71788I(searchHelperFragment.f88786f4, SearchHelperFragment.this.f88788h4.getString("Name") + " --- " + SearchHelperFragment.this.f88788h4.getString("Title"));
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                        }
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return false;
            }
        });
    }

    /* renamed from: P2 */
    public void m72463P2() {
        if (this.f88788h4.containsKey("Demo")) {
            m72459K2();
            return;
        }
        SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88799s4.setIconifiedByDefault(false);
        this.f88799s4.setQueryHint("Search");
        m72470Y2();
        ((ImageView) this.f88799s4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.14
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchHelperFragment.this.f88799s4.m2508k0("", false);
                SearchHelperFragment.this.f88799s4.clearFocus();
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                searchHelperFragment.f88803w4.setAdapter(searchHelperFragment.f88792l4);
                SearchHelperFragment.this.m72468V2();
                SearchHelperFragment.this.mo72211Z2();
            }
        });
        this.f88799s4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.15
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                if (!searchHelperFragment.f88789i4) {
                    return true;
                }
                searchHelperFragment.f88786f4 = str;
                if (str.length() <= 1) {
                    return false;
                }
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.15.1
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        SearchHelperFragment searchHelperFragment2 = SearchHelperFragment.this;
                        searchHelperFragment2.f88795o4 = searchHelperFragment2.mo71950a3(str);
                        return null;
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchHelperFragment.this.mo71973X2();
                    }

                    @Override // android.os.AsyncTask
                    protected void onPreExecute() {
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str) {
                return false;
            }
        });
    }

    /* renamed from: Q2 */
    public void mo71990Q2() {
        if (this.f88788h4.containsKey("Demo")) {
            m72459K2();
            return;
        }
        SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88799s4.setIconifiedByDefault(false);
        this.f88799s4.setQueryHint("Search");
        this.f88789i4 = true;
        ((ImageView) this.f88799s4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.12
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchHelperFragment.this.f88799s4.m2508k0("", false);
                SearchHelperFragment.this.f88799s4.clearFocus();
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                searchHelperFragment.f88803w4.setAdapter(searchHelperFragment.f88792l4);
                SearchHelperFragment.this.m72468V2();
                SearchHelperFragment.this.mo72211Z2();
            }
        });
        this.f88799s4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.13
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                if (!searchHelperFragment.f88789i4) {
                    return true;
                }
                searchHelperFragment.f88786f4 = str;
                if (str.length() > 1) {
                    new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.13.1
                        @Override // android.os.AsyncTask
                        protected Object doInBackground(Object[] objArr) {
                            SearchHelperFragment searchHelperFragment2 = SearchHelperFragment.this;
                            searchHelperFragment2.f88795o4 = searchHelperFragment2.mo71950a3(str);
                            SearchHelperFragment searchHelperFragment3 = SearchHelperFragment.this;
                            searchHelperFragment3.f88796p4 = searchHelperFragment3.mo71951g3(str);
                            return null;
                        }

                        @Override // android.os.AsyncTask
                        protected void onPostExecute(Object obj) {
                            SearchHelperFragment.this.mo71973X2();
                        }

                        @Override // android.os.AsyncTask
                        protected void onPreExecute() {
                        }
                    }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                    return true;
                }
                SearchHelperFragment searchHelperFragment2 = SearchHelperFragment.this;
                searchHelperFragment2.f88803w4.setAdapter(searchHelperFragment2.f88792l4);
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str) {
                return false;
            }
        });
    }

    /* renamed from: R2 */
    public void m72464R2() {
        if (this.f88788h4.containsKey("Demo")) {
            m72459K2();
            return;
        }
        SearchView searchView = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        this.f88799s4.setIconifiedByDefault(false);
        this.f88799s4.setQueryHint("Search All");
        m72470Y2();
        this.f88799s4.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.16
            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: a */
            public boolean mo2516a(int i2) {
                Cursor cursorMo10512c = SearchHelperFragment.this.f88799s4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (SearchHelperFragment.this.f88799s4.getTag(1) != null && ((String) SearchHelperFragment.this.f88799s4.getTag(1)).length() > 0) {
                    string = SearchHelperFragment.this.f88799s4.getTag() + StringUtils.SPACE + string;
                }
                SearchHelperFragment.this.f88799s4.m2508k0(string, true);
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: b */
            public boolean mo2517b(int i2) {
                Cursor cursorMo10512c = SearchHelperFragment.this.f88799s4.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (SearchHelperFragment.this.f88799s4.getTag() != null && ((String) SearchHelperFragment.this.f88799s4.getTag()).length() > 0) {
                    string = SearchHelperFragment.this.f88799s4.getTag() + StringUtils.SPACE + string;
                }
                SearchHelperFragment.this.f88799s4.m2508k0(string, true);
                return false;
            }
        });
        ((ImageView) this.f88799s4.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.17
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchHelperFragment.this.f88799s4.m2508k0("", false);
                SearchHelperFragment.this.f88799s4.clearFocus();
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                searchHelperFragment.f88803w4.setAdapter(searchHelperFragment.f88792l4);
                SearchHelperFragment.this.m72468V2();
                SearchHelperFragment.this.mo72211Z2();
            }
        });
        ((SearchView.SearchAutoComplete) this.f88799s4.findViewById(C5562R.id.search_src_text)).setDropDownAnchor(C5562R.id.search_view);
        this.f88799s4.setSuggestionsAdapter(new CursorAdapter(m15366r(), null, 0) { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.18
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("word")));
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell, viewGroup, false);
                viewInflate.setTag(viewInflate.findViewById(C5562R.id.text));
                return viewInflate;
            }
        });
        this.f88799s4.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.19
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                if (!searchHelperFragment.f88789i4) {
                    return true;
                }
                searchHelperFragment.f88786f4 = str;
                if (str.length() <= 1) {
                    return false;
                }
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.19.2
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        return SearchHelperFragment.this.mo71951g3(str);
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchHelperFragment.this.f88799s4.getSuggestionsAdapter().mo10519m(SearchHelperFragment.this.f88791k4.m71850h((ArrayList) obj));
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(final String str) {
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.19.1
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                        searchHelperFragment.f88795o4 = searchHelperFragment.mo71950a3(str);
                        return null;
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        SearchHelperFragment.this.mo71973X2();
                    }

                    @Override // android.os.AsyncTask
                    protected void onPreExecute() {
                        try {
                            SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                            searchHelperFragment.f88791k4.m71788I(searchHelperFragment.f88786f4, SearchHelperFragment.this.f88788h4.getString("Name") + " --- " + SearchHelperFragment.this.f88788h4.getString("Title"));
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                        }
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return false;
            }
        });
    }

    /* renamed from: S2 */
    public void m72465S2() throws Resources.NotFoundException {
        Toolbar toolbar = (Toolbar) this.f88797q4.findViewById(C5562R.id.toolbar);
        this.f88798r4 = toolbar;
        if (toolbar == null) {
            return;
        }
        this.f88802v4 = (ButtonSmall) this.f88797q4.findViewById(C5562R.id.back_button);
        this.f88798r4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SearchHelperFragment.this.f88791k4.m71821W1(true);
            }
        });
        if (this.f88802v4 != null) {
            this.f88802v4.setDrawableIcon(m15366r().getResources().getDrawable(C5562R.drawable.back_icon_white));
            this.f88802v4.setRippleColor(m15366r().getResources().getColor(C5562R.color.toolbar_item_ripple_color));
            this.f88802v4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    SearchHelperFragment.this.f88791k4.m71821W1(true);
                }
            });
            this.f88802v4.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.4
                @Override // android.view.View.OnLongClickListener
                public boolean onLongClick(View view) {
                    SearchHelperFragment.this.f88791k4.m71830Z1(true);
                    return true;
                }
            });
        }
        ImageButton imageButton = (ImageButton) this.f88797q4.findViewById(C5562R.id.action_home);
        this.f88804x4 = imageButton;
        if (imageButton != null) {
            imageButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.5
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    SearchHelperFragment.this.f88791k4.m71830Z1(true);
                }
            });
        }
        this.f88800t4 = (ImageView) this.f88797q4.findViewById(C5562R.id.toolbar_image_view);
        TextView textView = (TextView) this.f88797q4.findViewById(C5562R.id.toolbar_text_view);
        this.f88801u4 = textView;
        if (textView != null) {
            this.f88801u4.setTypeface(Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf"));
            this.f88801u4.setText(mo72066h3());
        }
        if (this.f88800t4 != null) {
            mo72171c3();
        }
        mo72338d3();
        DrawerLayout drawerLayout = (DrawerLayout) this.f88797q4.findViewById(C5562R.id.drawer_layout);
        this.f88805y4 = drawerLayout;
        if (drawerLayout != null) {
            drawerLayout.m14272a(new DrawerLayout.DrawerListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.6
                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: a */
                public void mo1008a(View view) {
                    SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                    searchHelperFragment.f88806z4.setAdapter(new HistoryAdapter(searchHelperFragment.m15366r(), SearchHelperFragment.this.f88805y4));
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: b */
                public void mo1009b(View view) {
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: c */
                public void mo1010c(int i2) {
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: d */
                public void mo1011d(View view, float f2) {
                }
            });
            RecyclerView recyclerView = (RecyclerView) this.f88797q4.findViewById(C5562R.id.drawer_view);
            this.f88806z4 = recyclerView;
            if (recyclerView != null) {
                recyclerView.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
                this.f88806z4.m27459p(new CustomItemDecoration(m15366r()));
            }
        }
        this.f88797q4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.7
            @Override // java.lang.Runnable
            public void run() {
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                if (searchHelperFragment.f88800t4 != null) {
                    searchHelperFragment.mo72171c3();
                }
            }
        }, 1000L);
    }

    /* renamed from: T2 */
    public String[] m72466T2(String str) {
        ArrayList arrayList = new ArrayList();
        for (String str2 : str.split("</b>")) {
            String[] strArrSplit = str2.split("<b>");
            if (strArrSplit.length == 2 && !arrayList.contains(strArrSplit[1].toLowerCase()) && !strArrSplit[1].equals("...")) {
                arrayList.add(strArrSplit[1].toLowerCase());
            }
        }
        if (str.contains("</b> <b>")) {
            for (String str3 : str.replace("</b> <b>", StringUtils.SPACE).split("</b>")) {
                String[] strArrSplit2 = str3.split("<b>");
                if (strArrSplit2.length == 2 && !arrayList.contains(strArrSplit2[1].toLowerCase()) && !strArrSplit2[1].equals("...") && !arrayList.contains(strArrSplit2[1].toLowerCase())) {
                    arrayList.add(0, strArrSplit2[1]);
                }
            }
        }
        return (String[]) arrayList.toArray(new String[arrayList.size()]);
    }

    @Override // androidx.fragment.app.Fragment
    @Nullable
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        this.f88791k4 = new CompressHelper(m15366r());
        if (bundle != null && bundle.containsKey("Position")) {
            this.f88785e4 = bundle.getInt("Position");
        }
        if (bundle != null && bundle.containsKey("Query")) {
            this.f88786f4 = bundle.getString("Query");
        }
        this.f88787g4 = (ListView) this.f88797q4.findViewById(C5562R.id.list_view);
        this.f88788h4 = (m15387y() == null || !m15387y().containsKey("DB")) ? null : m15387y().getBundle("DB");
        try {
            m72471b3("");
        } catch (Exception unused) {
        }
        LinearLayout linearLayout = (LinearLayout) this.f88797q4.findViewById(C5562R.id.status_layout);
        if (linearLayout != null) {
            linearLayout.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.24
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    SearchHelperFragment.this.m72468V2();
                }
            });
        }
        return super.mo15303U0(layoutInflater, viewGroup, bundle);
    }

    /* renamed from: U2 */
    public int m72467U2() {
        int identifier = m15320b0().getIdentifier("status_bar_height", "dimen", "android");
        if (identifier > 0) {
            return m15320b0().getDimensionPixelSize(identifier);
        }
        return 0;
    }

    /* renamed from: V2 */
    public void m72468V2() {
        try {
            InputMethodManager inputMethodManager = (InputMethodManager) m15366r().getSystemService("input_method");
            if (m15366r().getCurrentFocus() != null) {
                inputMethodManager.hideSoftInputFromWindow(m15366r().getCurrentFocus().getWindowToken(), 0);
            }
            SearchView searchView = this.f88799s4;
            if (searchView != null) {
                searchView.clearFocus();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* renamed from: W2 */
    public void m72469W2(Bundle bundle) {
        this.f88791k4 = new CompressHelper(m15366r());
        if (bundle != null && bundle.containsKey("Position")) {
            this.f88785e4 = bundle.getInt("Position");
        }
        if (bundle != null && bundle.containsKey("Query")) {
            this.f88786f4 = bundle.getString("Query");
        }
        this.f88788h4 = (m15387y() == null || !m15387y().containsKey("DB")) ? null : m15387y().getBundle("DB");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: X0 */
    public void mo15214X0() {
        super.mo15214X0();
    }

    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88793m4.m73469f0(this.f88795o4);
        this.f88803w4.setAdapter(this.f88793m4);
    }

    /* renamed from: Y2 */
    public void m72470Y2() {
        this.f88799s4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.SearchHelperFragment.25
            @Override // java.lang.Runnable
            public void run() {
                SearchHelperFragment searchHelperFragment = SearchHelperFragment.this;
                searchHelperFragment.f88789i4 = true;
                searchHelperFragment.f88799s4.m2508k0(searchHelperFragment.f88786f4, false);
                SearchHelperFragment.this.m72468V2();
            }
        }, 10L);
        this.f88789i4 = false;
    }

    /* renamed from: Z2 */
    public void mo72211Z2() {
    }

    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return null;
    }

    /* renamed from: b3 */
    public void m72471b3(String str) {
        try {
            ((AppCompatActivity) m15366r()).m1122F0().mo910A0(str);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* renamed from: c3 */
    public void mo72171c3() {
        try {
            Glide.m30040F(this).mo30129t(CompressHelper.m71724C(this.f88788h4)).mo30182a(new RequestOptions().m31361u()).m30165B2(this.f88800t4);
        } catch (Exception unused) {
        }
    }

    /* renamed from: d3 */
    public void mo72338d3() throws Resources.NotFoundException {
        if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
            float dimension = m15320b0().getDimension(C5562R.dimen.toolbar_padding);
            Toolbar toolbar = this.f88798r4;
            if (toolbar != null) {
                toolbar.setPadding(0, (int) dimension, 0, 0);
            }
        }
    }

    /* renamed from: e3 */
    public void mo72472e3() {
        ListView listView = (ListView) this.f88797q4.findViewById(C5562R.id.list_view);
        TextView textView = (TextView) this.f88797q4.findViewById(C5562R.id.status_label);
        LinearLayout linearLayout = (LinearLayout) this.f88797q4.findViewById(C5562R.id.status_layout);
        listView.setVisibility(0);
        textView.setVisibility(8);
        linearLayout.setVisibility(8);
    }

    /* renamed from: f3 */
    public void mo72473f3(String str) {
        ListView listView = (ListView) this.f88797q4.findViewById(C5562R.id.list_view);
        TextView textView = (TextView) this.f88797q4.findViewById(C5562R.id.status_label);
        LinearLayout linearLayout = (LinearLayout) this.f88797q4.findViewById(C5562R.id.status_layout);
        listView.setVisibility(8);
        textView.setVisibility(0);
        linearLayout.setVisibility(0);
        textView.setText(str);
    }

    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return null;
    }

    /* renamed from: h3 */
    public String mo72066h3() {
        return this.f88791k4.m71836b1(this.f88788h4.getString("Title"));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }
}

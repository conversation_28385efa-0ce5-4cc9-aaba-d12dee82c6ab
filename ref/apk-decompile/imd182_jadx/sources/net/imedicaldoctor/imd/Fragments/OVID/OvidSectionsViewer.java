package net.imedicaldoctor.imd.Fragments.OVID;

import android.app.Dialog;
import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.itextpdf.tool.xml.html.HTML;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.OVID.OvidViewerActivity;

/* loaded from: classes3.dex */
public class OvidSectionsViewer extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f88710F4;

    /* renamed from: G4 */
    private String f88711G4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f88710F4 = m15387y().getBundle("db");
        this.f88711G4 = m15387y().getString("parentId");
        final CompressHelper compressHelper = new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new CursorAdapter(m15366r(), compressHelper.m71850h(compressHelper.m71817V(this.f88710F4, "Select id as _id,* from toc where parentId = " + this.f88711G4)), 0) { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidSectionsViewer.1
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                TextView textView = (TextView) view.getTag();
                final Bundle bundleM71868m2 = compressHelper.m71868m2(cursor);
                if (!cursor.getString(cursor.getColumnIndex("leaf")).equals(IcyHeaders.f28171a3) && (cursor.getString(cursor.getColumnIndex("xpath")).length() > 0 || cursor.getString(cursor.getColumnIndex(HTML.Tag.f74369V)).length() > 0)) {
                    ((ImageView) view.findViewById(C5562R.id.info_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidSectionsViewer.1.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            ((OvidViewerActivity.OvidViewerFragment) OvidSectionsViewer.this.m15351l0()).m72436M4(bundleM71868m2);
                            OvidSectionsViewer.this.mo15203M2();
                        }
                    });
                }
                textView.setText(cursor.getString(cursor.getColumnIndex("name")));
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getItemViewType(int i2) {
                Cursor cursor = (Cursor) getItem(i2);
                if (cursor.getString(cursor.getColumnIndex("leaf")).equals(IcyHeaders.f28171a3)) {
                    return 0;
                }
                return (cursor.getString(cursor.getColumnIndex("xpath")).length() > 0 || cursor.getString(cursor.getColumnIndex(HTML.Tag.f74369V)).length() > 0) ? 1 : 2;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getViewTypeCount() {
                return 3;
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate2 = LayoutInflater.from(context).inflate(cursor.getString(cursor.getColumnIndex("leaf")).equals(IcyHeaders.f28171a3) ? C5562R.layout.list_view_item_simple_text : (cursor.getString(cursor.getColumnIndex("xpath")).length() > 0 || cursor.getString(cursor.getColumnIndex(HTML.Tag.f74369V)).length() > 0) ? C5562R.layout.list_view_item_simple_text_goto_arrow : C5562R.layout.list_view_item_simple_text_arrow, viewGroup, false);
                viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                return viewInflate2;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.OVID.OvidSectionsViewer.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                if (cursorMo10512c.moveToPosition(i2)) {
                    String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("leaf"));
                    cursorMo10512c.getString(cursorMo10512c.getColumnIndex("bookId"));
                    if (string.equals(IcyHeaders.f28171a3)) {
                        ((OvidViewerActivity.OvidViewerFragment) OvidSectionsViewer.this.m15351l0()).m72436M4(compressHelper.m71868m2(cursorMo10512c));
                    } else {
                        OvidSectionsViewer ovidSectionsViewer = new OvidSectionsViewer();
                        Bundle bundle2 = new Bundle();
                        bundle2.putBundle("db", OvidSectionsViewer.this.f88710F4);
                        bundle2.putString("parentId", cursorMo10512c.getString(cursorMo10512c.getColumnIndex("id")));
                        ovidSectionsViewer.m15342i2(bundle2);
                        ovidSectionsViewer.mo15218Z2(true);
                        ovidSectionsViewer.m15245A2(OvidSectionsViewer.this.m15351l0(), 0);
                        ovidSectionsViewer.mo15222e3(OvidSectionsViewer.this.m15283M(), "OvidSectionsViewer");
                    }
                    OvidSectionsViewer.this.mo15203M2();
                }
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

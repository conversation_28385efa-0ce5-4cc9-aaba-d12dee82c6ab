package net.imedicaldoctor.imd.Fragments.Micromedex;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class MMNeoListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88601A4;

    /* renamed from: B4 */
    public String f88602B4;

    /* renamed from: C4 */
    public TabLayout f88603C4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        String string;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list_tab, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f88603C4 = (TabLayout) this.f88797q4.findViewById(C5562R.id.tabs);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            string = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            string = m15387y().getString("ParentId");
        }
        this.f88602B4 = string;
        if (this.f88602B4 == null) {
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "SELECT * FROM drug_class_idx order by title collate nocase asc");
            this.f88603C4.setVisibility(0);
        } else {
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "select drug_class_int.name as title, drug_class_int.drug_Id as drug_id ,genericTitle as subtitle from drug_class_int inner join (select drug_id as generic_id, name as genericTitle from drug_idx where has_generic=0) on drug_class_int.drug_id =generic_id where class_id=" + this.f88602B4 + " order by drug_class_int.name collate nocase asc");
            this.f88603C4.setVisibility(8);
            CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) this.f88803w4.getLayoutParams();
            layoutParams.setMargins(0, 0, 0, 0);
            this.f88803w4.setLayoutParams(layoutParams);
        }
        String[] strArr = {"Drugs", "Enteral Formulas"};
        for (int i2 = 0; i2 < 2; i2++) {
            TabLayout.Tab tabM40228I = this.f88603C4.m40228I();
            tabM40228I.m40276D(strArr[i2]);
            this.f88603C4.m40248i(tabM40228I);
        }
        this.f88603C4.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivityFragment.2
            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: a */
            public void mo40255a(TabLayout.Tab tab) {
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: b */
            public void mo40256b(TabLayout.Tab tab) {
                MMNeoListActivityFragment mMNeoListActivityFragment;
                CompressHelper compressHelper;
                Bundle bundle2;
                String str;
                if (tab.m40284k() == 0) {
                    mMNeoListActivityFragment = MMNeoListActivityFragment.this;
                    compressHelper = mMNeoListActivityFragment.f88791k4;
                    bundle2 = mMNeoListActivityFragment.f88788h4;
                    str = "SELECT * FROM drug_class_idx order by title collate nocase asc";
                } else {
                    mMNeoListActivityFragment = MMNeoListActivityFragment.this;
                    compressHelper = mMNeoListActivityFragment.f88791k4;
                    bundle2 = mMNeoListActivityFragment.f88788h4;
                    str = "SELECT * FROM formula_idx order by title collate nocase asc";
                }
                mMNeoListActivityFragment.f88794n4 = compressHelper.m71817V(bundle2, str);
                if (MMNeoListActivityFragment.this.f88799s4.getQuery().toString().length() > 0) {
                    MMNeoListActivityFragment mMNeoListActivityFragment2 = MMNeoListActivityFragment.this;
                    mMNeoListActivityFragment2.f88795o4 = mMNeoListActivityFragment2.mo71950a3(mMNeoListActivityFragment2.f88799s4.getQuery().toString());
                    MMNeoListActivityFragment.this.mo71973X2();
                } else {
                    MMNeoListActivityFragment mMNeoListActivityFragment3 = MMNeoListActivityFragment.this;
                    ((ChaptersAdapter) mMNeoListActivityFragment3.f88792l4).m73465g0(mMNeoListActivityFragment3.f88794n4);
                    MMNeoListActivityFragment mMNeoListActivityFragment4 = MMNeoListActivityFragment.this;
                    mMNeoListActivityFragment4.f88803w4.setAdapter(mMNeoListActivityFragment4.f88792l4);
                }
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: c */
            public void mo40257c(TabLayout.Tab tab) {
            }
        });
        this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, final int i3) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("title"));
                String string2 = bundle2.getString("subtitle");
                if (bundle2.getString("title").equals(string2) || string2 == null || string2.length() == 0) {
                    rippleTextFullViewHolder.f101500J.setVisibility(8);
                } else {
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    rippleTextFullViewHolder.f101500J.setText(string2);
                }
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivityFragment.3.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        MMNeoListActivityFragment.this.m72397i3(bundle2, i3);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88601A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivityFragment.4
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i3) {
                TextView textView;
                int i4;
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                if (bundle2.getString(Annotation.f68283i3).equals(bundle2.getString("text")) || bundle2.getString(Annotation.f68283i3).length() == 0) {
                    textView = rippleTextFullViewHolder.f101500J;
                    i4 = 8;
                } else {
                    textView = rippleTextFullViewHolder.f101500J;
                    i4 = 0;
                }
                textView.setVisibility(i4);
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivityFragment.4.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CompressHelper compressHelper;
                        Bundle bundle3;
                        StringBuilder sb;
                        String str;
                        MMNeoListActivityFragment.this.m72468V2();
                        if (bundle2.getString("typeText").equals("Drug")) {
                            MMNeoListActivityFragment mMNeoListActivityFragment = MMNeoListActivityFragment.this;
                            compressHelper = mMNeoListActivityFragment.f88791k4;
                            bundle3 = mMNeoListActivityFragment.f88788h4;
                            sb = new StringBuilder();
                            str = "drug-";
                        } else {
                            MMNeoListActivityFragment mMNeoListActivityFragment2 = MMNeoListActivityFragment.this;
                            compressHelper = mMNeoListActivityFragment2.f88791k4;
                            bundle3 = mMNeoListActivityFragment2.f88788h4;
                            sb = new StringBuilder();
                            str = "formula-";
                        }
                        sb.append(str);
                        sb.append(bundle2.getString("contentId"));
                        compressHelper.m71772A1(bundle3, sb.toString(), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                MMNeoListActivityFragment.this.m72468V2();
                MMNeoListActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88601A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88601A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        CompressHelper compressHelper;
        Bundle bundle;
        StringBuilder sb;
        String str2;
        if (this.f88603C4.getSelectedTabPosition() == 0) {
            compressHelper = this.f88791k4;
            bundle = this.f88788h4;
            sb = new StringBuilder();
            sb.append("Select * from search where search match '(text:");
            sb.append(str);
            sb.append("* OR content:");
            sb.append(str);
            str2 = "*) AND type:1 AND typeText:Drug'";
        } else {
            compressHelper = this.f88791k4;
            bundle = this.f88788h4;
            sb = new StringBuilder();
            sb.append("Select * from search where search match '(text:");
            sb.append(str);
            sb.append("* OR content:");
            sb.append(str);
            str2 = "*) AND type:1 AND typeText:Formula'";
        }
        sb.append(str2);
        return compressHelper.m71819W(bundle, sb.toString(), "fsearch.sqlite");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71819W(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'", "fsearch.sqlite");
    }

    /* renamed from: i3 */
    public void m72397i3(Bundle bundle, int i2) {
        CompressHelper compressHelper;
        Bundle bundle2;
        StringBuilder sb;
        String str;
        m72468V2();
        if (this.f88602B4 != null) {
            compressHelper = this.f88791k4;
            bundle2 = this.f88788h4;
            sb = new StringBuilder();
            sb.append("drug-");
            str = "drug_id";
        } else {
            if (this.f88603C4.getSelectedTabPosition() == 0) {
                Bundle bundle3 = new Bundle();
                bundle3.putBundle("DB", this.f88788h4);
                bundle3.putString("ParentId", bundle.getString("class_id"));
                this.f88791k4.m71798N(MMNeoListActivity.class, MMNeoListActivityFragment.class, bundle3);
                return;
            }
            compressHelper = this.f88791k4;
            bundle2 = this.f88788h4;
            sb = new StringBuilder();
            sb.append("formula-");
            str = "formula_id";
        }
        sb.append(bundle.getString(str));
        compressHelper.m71772A1(bundle2, sb.toString(), null, null);
    }
}

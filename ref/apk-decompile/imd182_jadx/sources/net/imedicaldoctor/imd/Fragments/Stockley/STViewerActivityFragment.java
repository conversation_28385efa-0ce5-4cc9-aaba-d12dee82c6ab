package net.imedicaldoctor.imd.Fragments.Stockley;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class STViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f89059X4;

    /* renamed from: Y4 */
    public ArrayList<String> f89060Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f89061Z4;

    /* renamed from: J4 */
    private void m72516J4(String str) {
        ArrayList<String> arrayList = this.f89060Y4;
        if (arrayList == null || arrayList.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f89060Y4.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", next);
            bundle.putString("Description", "");
            bundle.putString("id", next);
            if (new File(next).length() > 5000) {
                arrayList2.add(bundle);
            }
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public String m72517I4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return m72840w3(this.f89060Y4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String str = STViewerActivityFragment.this.f89563A4;
                    if (str == null || str.length() == 0) {
                        iMDLogger.m73550f("Loading Document", STViewerActivityFragment.this.f89567E4);
                        STViewerActivityFragment sTViewerActivityFragment = STViewerActivityFragment.this;
                        ArrayList<Bundle> arrayListM71817V = sTViewerActivityFragment.f89579Q4.m71817V(sTViewerActivityFragment.f89566D4, "Select * from docs where docName='" + STViewerActivityFragment.this.f89567E4 + "'");
                        if (arrayListM71817V != null && arrayListM71817V.size() != 0) {
                            STViewerActivityFragment.this.f89059X4 = arrayListM71817V.get(0);
                            STViewerActivityFragment sTViewerActivityFragment2 = STViewerActivityFragment.this;
                            sTViewerActivityFragment2.f89568F4 = sTViewerActivityFragment2.f89059X4.getString("title");
                            STViewerActivityFragment sTViewerActivityFragment3 = STViewerActivityFragment.this;
                            String strM71773B = sTViewerActivityFragment3.f89579Q4.m71773B(sTViewerActivityFragment3.f89059X4.getString(Annotation.f68283i3), STViewerActivityFragment.this.f89567E4, "127");
                            STViewerActivityFragment sTViewerActivityFragment4 = STViewerActivityFragment.this;
                            String strM72817d4 = sTViewerActivityFragment4.m72817d4(sTViewerActivityFragment4.m15366r(), "STHeader.css");
                            STViewerActivityFragment sTViewerActivityFragment5 = STViewerActivityFragment.this;
                            String strM72817d42 = sTViewerActivityFragment5.m72817d4(sTViewerActivityFragment5.m15366r(), "STFooter.css");
                            String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", STViewerActivityFragment.this.f89568F4).replace("[include]", "");
                            STViewerActivityFragment.this.f89563A4 = strReplace + strM71773B + strM72817d42;
                        }
                        STViewerActivityFragment.this.f89595p4 = "Document doesn't exist";
                        return;
                    }
                    STViewerActivityFragment.this.m72826m3();
                } catch (Exception e2) {
                    e2.printStackTrace();
                    STViewerActivityFragment.this.f89595p4 = e2.getLocalizedMessage();
                }
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Stockley.STViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = STViewerActivityFragment.this.f89595p4;
                if (str != null && str.length() > 0) {
                    STViewerActivityFragment sTViewerActivityFragment = STViewerActivityFragment.this;
                    sTViewerActivityFragment.m72780C4(sTViewerActivityFragment.f89595p4);
                    return;
                }
                String str2 = STViewerActivityFragment.this.f89566D4.getString("Path") + "/base";
                STViewerActivityFragment sTViewerActivityFragment2 = STViewerActivityFragment.this;
                sTViewerActivityFragment2.m72795O3(sTViewerActivityFragment2.f89563A4, str2);
                STViewerActivityFragment.this.m72836s4();
                STViewerActivityFragment.this.m72831p4();
                STViewerActivityFragment.this.mo72642f3(C5562R.menu.elsviewer2);
                STViewerActivityFragment.this.m15358o2(false);
                STViewerActivityFragment.this.m72786G3();
            }
        });
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = strM71753g1.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72517I4(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = strM71753g1 + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f89060Y4 = arrayList;
            mo71972o4();
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        this.f89569G4.m73433g("onBodyLoad();");
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() != C5562R.id.action_gallery) {
            return super.mo15329e1(menuItem);
        }
        m72516J4("asdfafdsaf");
        return true;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        String str4;
        if (str2.equals("image")) {
            m72516J4(str3);
            return true;
        }
        if (str2.equals(Annotation.f68285k3) || (str2.equals("http") & str3.contains("localhost:"))) {
            String str5 = "//" + CompressHelper.m71753g1(this.f89566D4, "base") + "/";
            if (str3.contains("#")) {
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, "#");
                String str6 = strArrSplitByWholeSeparator[0];
                String str7 = strArrSplitByWholeSeparator[1];
                String strReplace = str6.replace(str5, "").replace(".html", "");
                if (this.f89059X4.getString("docName").equalsIgnoreCase(strReplace)) {
                    this.f89569G4.m73433g("window.location.href = \"#" + str7 + "\"");
                    return true;
                }
                if (strReplace.length() == 0) {
                    this.f89569G4.m73433g("window.location.href = \"#" + str7 + "\"");
                    return true;
                }
                if (strReplace.endsWith("/")) {
                    this.f89569G4.m73433g("window.location.href = \"#" + str7 + "\"");
                    return true;
                }
                str4 = str7;
                str3 = strReplace;
            } else {
                str4 = "";
            }
            String strReplace2 = str3.replace(str5, "");
            if (strReplace2.length() == 0) {
                return true;
            }
            str3 = strReplace2.toLowerCase();
            CompressHelper compressHelper = this.f89579Q4;
            if (compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "select * from docs where docName='" + str3 + "'")) == null) {
                CompressHelper.m71767x2(m15366r(), "Sorry, not in this book", 1);
            } else {
                this.f89579Q4.m71772A1(this.f89566D4, str3, null, str4);
            }
        }
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.R;
import android.annotation.SuppressLint;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.NinePatchDrawable;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.view.result.ActivityResultCallback;
import android.view.result.ActivityResultLauncher;
import android.view.result.contract.ActivityResultContracts;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.SearchView;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.exifinterface.media.ExifInterface;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.common.util.C1201k;
import androidx.media3.exoplayer.audio.SilenceSkippingAudioProcessor;
import androidx.media3.extractor.AacUtil;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.palette.graphics.Palette;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter;
import com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemViewHolder;
import com.h6ah4i.android.widget.advrecyclerview.draggable.ItemDraggableRange;
import com.h6ah4i.android.widget.advrecyclerview.draggable.RecyclerViewDragDropManager;
import com.h6ah4i.android.widget.advrecyclerview.utils.AbstractDraggableItemViewHolder;
import com.itextpdf.text.pdf.PdfBoolean;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.observers.DisposableObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.File;
import java.io.IOException;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import okio.BufferedSource;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;

/* loaded from: classes3.dex */
public class databasesFragment extends Fragment {

    /* renamed from: D4 */
    public static ArrayList<Bundle> f90250D4;

    /* renamed from: E4 */
    public static ArrayList<Bundle> f90251E4;

    /* renamed from: F4 */
    public static ArrayList<Bundle> f90252F4;

    /* renamed from: A4 */
    HashMap<String, Bundle> f90253A4 = new HashMap<>();

    /* renamed from: B4 */
    private final ActivityResultLauncher<String> f90254B4 = mo640G(new ActivityResultContracts.RequestPermission(), new ActivityResultCallback() { // from class: net.imedicaldoctor.imd.Fragments.j
        @Override // android.view.result.ActivityResultCallback
        /* renamed from: a */
        public final void mo796a(Object obj) {
            ((Boolean) obj).booleanValue();
        }
    });

    /* renamed from: C4 */
    public BroadcastReceiver f90255C4 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.15
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            databasesFragment.this.f90276y4.m71807R0(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.15.1
                @Override // java.lang.Runnable
                public void run() {
                    databasesFragment.this.f90276y4.m71861k0();
                    databasesFragment.f90251E4 = databasesFragment.this.f90276y4.m71880p2();
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.15.2
                @Override // java.lang.Runnable
                public void run() {
                    RecyclerView recyclerView;
                    RecyclerView.Adapter adapter;
                    databasesFragment.this.m73139I3();
                    databasesFragment.f90251E4.size();
                    if (databasesFragment.this.f90259h4 != null) {
                        databasesFragment.this.f90259h4.m27491G();
                    }
                    if (databasesFragment.this.f90268q4 != null) {
                        databasesFragment.this.f90268q4.m27491G();
                    }
                    databasesFragment.this.m73149Q2();
                    if (databasesFragment.this.f90267p4 == null) {
                        return;
                    }
                    databasesFragment.this.f90267p4.m2508k0("", false);
                    databasesFragment.this.f90267p4.clearFocus();
                    if (databasesFragment.this.f90261j4) {
                        recyclerView = databasesFragment.this.f90258g4;
                        adapter = databasesFragment.this.f90268q4;
                    } else {
                        recyclerView = databasesFragment.this.f90258g4;
                        adapter = databasesFragment.this.f90259h4;
                    }
                    recyclerView.setAdapter(adapter);
                    LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("reloadDownloads"));
                    LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("referesh.account"));
                    LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("reloadaccountdownloads"));
                }
            });
        }
    };

    /* renamed from: e4 */
    private String f90256e4;

    /* renamed from: f4 */
    private View f90257f4;

    /* renamed from: g4 */
    private RecyclerView f90258g4;

    /* renamed from: h4 */
    private DatabasesAdapter f90259h4;

    /* renamed from: i4 */
    private long f90260i4;

    /* renamed from: j4 */
    private boolean f90261j4;

    /* renamed from: k4 */
    public VBHelper f90262k4;

    /* renamed from: l4 */
    private boolean f90263l4;

    /* renamed from: m4 */
    private String f90264m4;

    /* renamed from: n4 */
    private MenuItem f90265n4;

    /* renamed from: o4 */
    private MenuItem f90266o4;

    /* renamed from: p4 */
    private SearchView f90267p4;

    /* renamed from: q4 */
    private CollectionAdapter f90268q4;

    /* renamed from: r4 */
    private RecyclerViewDragDropManager f90269r4;

    /* renamed from: s4 */
    private long f90270s4;

    /* renamed from: t4 */
    private CustomItemDecoration f90271t4;

    /* renamed from: u4 */
    private GridLayoutManager f90272u4;

    /* renamed from: v4 */
    private FloatingActionButton f90273v4;

    /* renamed from: w4 */
    private boolean f90274w4;

    /* renamed from: x4 */
    Typeface f90275x4;

    /* renamed from: y4 */
    private CompressHelper f90276y4;

    /* renamed from: z4 */
    private Bundle f90277z4;

    /* renamed from: net.imedicaldoctor.imd.Fragments.databasesFragment$22 */
    class C539622 extends DisposableObserver<String> {

        /* renamed from: X */
        final /* synthetic */ Bundle f90316X;

        /* renamed from: net.imedicaldoctor.imd.Fragments.databasesFragment$22$2, reason: invalid class name */
        class AnonymousClass2 implements DialogInterface.OnClickListener {
            AnonymousClass2() {
            }

            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
                DisposableObserver<String> disposableObserver = new DisposableObserver<String>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.22.2.1
                    @Override // io.reactivex.rxjava3.core.Observer
                    @SuppressLint({"NotifyDataSetChanged"})
                    /* renamed from: c, reason: merged with bridge method [inline-methods] */
                    public void onNext(@NonNull String str) throws JSONException {
                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "|||||");
                        if (!strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                            if (strArrSplitByWholeSeparator.length > 1) {
                                if (strArrSplitByWholeSeparator[1].contains("Not Enough Money")) {
                                    new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("You don't have enough credit in your account. what do you want to do ?").mo1115y("Buy Database", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.22.2.1.2
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface2, int i3) {
                                            StringBuilder sb = new StringBuilder();
                                            sb.append("http://imedicaldoctor.net/buydb.php?user=");
                                            databasesFragment databasesfragment = databasesFragment.this;
                                            sb.append(databasesfragment.f90262k4.m73452n(databasesfragment.f90276y4.m71905y1(), "127"));
                                            sb.append("&db=");
                                            C539622 c539622 = C539622.this;
                                            sb.append(databasesFragment.this.f90262k4.m73452n(c539622.f90316X.getString("Name"), "127"));
                                            databasesFragment.this.f90276y4.m71803P(sb.toString());
                                        }
                                    }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.22.2.1.1
                                        @Override // android.content.DialogInterface.OnClickListener
                                        public void onClick(DialogInterface dialogInterface2, int i3) {
                                        }
                                    }).m1090I();
                                    return;
                                } else {
                                    CompressHelper.m71767x2(databasesFragment.this.m15366r(), strArrSplitByWholeSeparator[1], 1);
                                    return;
                                }
                            }
                            return;
                        }
                        databasesFragment.this.f90276y4.m71894t2(strArrSplitByWholeSeparator[1]);
                        databasesFragment.this.f90276y4.m71861k0();
                        LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("referesh.account"));
                        LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("reloadDownloads"));
                        C539622.this.f90316X.remove("Inactive");
                        C539622.this.f90316X.remove("Demo");
                        databasesFragment.this.f90258g4.getAdapter().m27491G();
                        databasesFragment.this.f90276y4.m71795L1();
                        Toast.makeText(databasesFragment.this.m15366r(), "Purchase was successful", 1).show();
                    }

                    @Override // io.reactivex.rxjava3.core.Observer
                    public void onComplete() {
                    }

                    @Override // io.reactivex.rxjava3.core.Observer
                    public void onError(@NonNull Throwable th) {
                        CompressHelper.m71767x2(databasesFragment.this.m15366r(), "Error in contacting server, try again later", 1);
                    }
                };
                CompressHelper compressHelper = databasesFragment.this.f90276y4;
                databasesFragment databasesfragment = databasesFragment.this;
                compressHelper.m71789I0(databasesfragment, databasesfragment.f90276y4.m71874o0("BuyItem|||||" + new VBHelper(databasesFragment.this.m15366r()).m73451m() + "|||||" + C539622.this.f90316X.getString("Name"))).mo59651a(disposableObserver);
            }
        }

        C539622(Bundle bundle) {
            this.f90316X = bundle;
        }

        @Override // io.reactivex.rxjava3.core.Observer
        /* renamed from: c, reason: merged with bridge method [inline-methods] */
        public void onNext(@NonNull String str) {
            String str2;
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "|||||");
            if (!strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                CompressHelper.m71767x2(databasesFragment.this.m15366r(), strArrSplitByWholeSeparator[1], 1);
                return;
            }
            String str3 = strArrSplitByWholeSeparator[1];
            if (str3.equals("0")) {
                str2 = "Are You Sure You Want To Buy " + this.f90316X.getString("Title") + " For Free ?";
            } else {
                str2 = "Are You Sure You Want To Buy " + this.f90316X.getString("Title") + " For " + str3 + " Toman ?";
            }
            new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l(str2).mo1115y("Yes", new AnonymousClass2()).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.22.1
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                }
            }).m1090I();
        }

        @Override // io.reactivex.rxjava3.core.Observer
        public void onComplete() {
        }

        @Override // io.reactivex.rxjava3.core.Observer
        public void onError(@NonNull Throwable th) {
            CompressHelper.m71767x2(databasesFragment.this.m15366r(), "Error in contacting server, try again later", 1);
        }
    }

    public static class AddCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90359I;

        public AddCellViewHolder(View view) {
            super(view);
            this.f90359I = (TextView) view.findViewById(C5562R.id.text);
        }
    }

    public static class CardViewPlaceHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90360I;

        /* renamed from: J */
        private final TextView f90361J;

        /* renamed from: K */
        private final ImageView f90362K;

        /* renamed from: L */
        private final AppCompatButton f90363L;

        /* renamed from: M */
        private final MaterialRippleLayout f90364M;

        public CardViewPlaceHolder(View view) {
            super(view);
            this.f90360I = (TextView) view.findViewById(C5562R.id.text_view);
            this.f90361J = (TextView) view.findViewById(C5562R.id.subtext_view);
            this.f90362K = (ImageView) view.findViewById(C5562R.id.image_view);
            this.f90363L = (AppCompatButton) view.findViewById(C5562R.id.buy_button);
            this.f90364M = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class CollectionAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        HashMap<String, Integer> f90365d = new HashMap<>();

        public CollectionAdapter() {
            Iterator<Bundle> it2 = databasesFragment.f90251E4.iterator();
            int i2 = 0;
            while (it2.hasNext()) {
                Bundle next = it2.next();
                this.f90365d.put("Section" + next.getString("title"), Integer.valueOf(i2));
                i2++;
                for (int i3 = 0; i3 < next.getParcelableArrayList("items").size(); i3++) {
                    Bundle bundle = (Bundle) next.getParcelableArrayList("items").get(i3);
                    this.f90365d.put("Database" + bundle.getString("Name"), Integer.valueOf(i2));
                    i2++;
                }
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            return i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return databasesFragment.this.m73147O2(i2, databasesFragment.f90251E4).containsKey("Title") ? 1 : 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            Bundle bundleM73147O2 = databasesFragment.this.m73147O2(i2, databasesFragment.f90251E4);
            if (!bundleM73147O2.containsKey("Item")) {
                ((HeaderCellViewHolder) viewHolder).f90402I.setText(bundleM73147O2.getString("Title"));
                return;
            }
            CardViewPlaceHolder cardViewPlaceHolder = (CardViewPlaceHolder) viewHolder;
            databasesFragment.this.m73143L2(bundleM73147O2.getBundle("Item"), cardViewPlaceHolder.f90363L, cardViewPlaceHolder.f90360I, cardViewPlaceHolder.f90361J, cardViewPlaceHolder.f90362K, cardViewPlaceHolder.f90364M, cardViewPlaceHolder.f33076a);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                CardViewPlaceHolder cardViewPlaceHolder = new CardViewPlaceHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_card_view_database, viewGroup, false));
                cardViewPlaceHolder.f90360I.setTypeface(databasesFragment.this.f90275x4);
                return cardViewPlaceHolder;
            }
            if (i2 == 1) {
                return new HeaderCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_card_header, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return databasesFragment.this.m73144L3(databasesFragment.f90251E4);
        }
    }

    public static class DatabaseCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90367I;

        /* renamed from: J */
        public TextView f90368J;

        /* renamed from: K */
        public ImageView f90369K;

        /* renamed from: L */
        public MaterialRippleLayout f90370L;

        /* renamed from: M */
        public AppCompatButton f90371M;

        public DatabaseCellViewHolder(View view) {
            super(view);
            this.f90367I = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90369K = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90370L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f90371M = (AppCompatButton) view.findViewById(C5562R.id.buy_button);
            this.f90368J = (TextView) view.findViewById(C5562R.id.database_subtitle);
        }
    }

    public class DatabasesAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        HashMap<String, Integer> f90372d = new HashMap<>();

        public DatabasesAdapter() {
            Iterator<Bundle> it2 = databasesFragment.f90251E4.iterator();
            int i2 = 0;
            while (it2.hasNext()) {
                Bundle next = it2.next();
                this.f90372d.put("Section" + next.getString("title"), Integer.valueOf(i2));
                i2++;
                for (int i3 = 0; i3 < next.getParcelableArrayList("items").size(); i3++) {
                    Bundle bundle = (Bundle) next.getParcelableArrayList("items").get(i3);
                    this.f90372d.put("Database" + bundle.getString("Name"), Integer.valueOf(i2));
                    i2++;
                }
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            return i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return databasesFragment.this.m73147O2(i2, databasesFragment.f90251E4).containsKey("Title") ? 1 : 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            Bundle bundleM73147O2 = databasesFragment.this.m73147O2(i2, databasesFragment.f90251E4);
            if (!bundleM73147O2.containsKey("Item")) {
                ((HeaderCellViewHolder) viewHolder).f90402I.setText(bundleM73147O2.getString("Title"));
                return;
            }
            DatabaseCellViewHolder databaseCellViewHolder = (DatabaseCellViewHolder) viewHolder;
            databasesFragment.this.m73143L2(bundleM73147O2.getBundle("Item"), databaseCellViewHolder.f90371M, databaseCellViewHolder.f90367I, databaseCellViewHolder.f90368J, databaseCellViewHolder.f90369K, databaseCellViewHolder.f90370L, databaseCellViewHolder.f33076a);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new DatabaseCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_ripple, viewGroup, false));
            }
            if (i2 == 1) {
                return new HeaderCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return databasesFragment.this.m73144L3(databasesFragment.f90251E4);
        }
    }

    public static class EditDatabaseCellViewHolder extends AbstractDraggableItemViewHolder implements DraggableItemViewHolder {

        /* renamed from: J */
        public TextView f90374J;

        /* renamed from: K */
        public ImageView f90375K;

        /* renamed from: L */
        public ImageView f90376L;

        /* renamed from: M */
        public LinearLayout f90377M;

        /* renamed from: N */
        public ImageView f90378N;

        public EditDatabaseCellViewHolder(View view) {
            super(view);
            this.f90374J = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90375K = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90376L = (ImageView) view.findViewById(C5562R.id.drag_indicator);
            this.f90377M = (LinearLayout) view.findViewById(C5562R.id.container_view);
            this.f90378N = (ImageView) view.findViewById(C5562R.id.remove_icon);
        }
    }

    public class EditDatabasesAdapter extends RecyclerView.Adapter implements DraggableItemAdapter {

        /* renamed from: d */
        HashMap<String, Integer> f90379d;

        /* renamed from: e */
        int f90380e;

        public EditDatabasesAdapter() {
            HashMap<String, Integer> map = new HashMap<>();
            this.f90379d = map;
            this.f90380e = 0;
            map.put("AddSEction", Integer.valueOf(AacUtil.f27532f));
            this.f90379d.put("EditSectionas", Integer.valueOf(this.f90380e));
            this.f90380e++;
            Iterator<Bundle> it2 = databasesFragment.f90251E4.iterator();
            while (it2.hasNext()) {
                Bundle next = it2.next();
                this.f90379d.put("EditSection" + next.getString("title"), Integer.valueOf(this.f90380e));
                this.f90380e = this.f90380e + 1;
            }
            Iterator<Bundle> it3 = databasesFragment.f90251E4.iterator();
            while (it3.hasNext()) {
                Bundle next2 = it3.next();
                this.f90379d.put("Section" + next2.getString("title"), Integer.valueOf(this.f90380e));
                this.f90380e = this.f90380e + 1;
                for (int i2 = 0; i2 < next2.getParcelableArrayList("items").size(); i2++) {
                    Bundle bundle = (Bundle) next2.getParcelableArrayList("items").get(i2);
                    this.f90379d.put("Database" + bundle.getString("Name"), Integer.valueOf(this.f90380e));
                    this.f90380e = this.f90380e + 1;
                }
            }
            mo26852a0(true);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            StringBuilder sb;
            String str;
            String string;
            Integer num;
            if (i2 < databasesFragment.f90251E4.size() + 1) {
                if (i2 == 0) {
                    num = this.f90379d.get("EditSectionas");
                    return num.intValue();
                }
                string = "EditSection" + databasesFragment.f90251E4.get(i2 - 1).getString("title");
            } else {
                if (i2 == databasesFragment.f90251E4.size() + 1) {
                    return SilenceSkippingAudioProcessor.f23265A;
                }
                Bundle bundleM73147O2 = databasesFragment.this.m73147O2((i2 - databasesFragment.f90251E4.size()) - 2, databasesFragment.f90251E4);
                if (bundleM73147O2.containsKey("Item")) {
                    bundleM73147O2 = bundleM73147O2.getBundle("Item");
                    sb = new StringBuilder();
                    sb.append("Database");
                    str = "Name";
                } else {
                    sb = new StringBuilder();
                    sb.append("Section");
                    str = "Title";
                }
                sb.append(bundleM73147O2.getString(str));
                string = sb.toString();
                if (!this.f90379d.containsKey(string)) {
                    return -1L;
                }
            }
            num = this.f90379d.get(string);
            return num.intValue();
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            if (i2 < databasesFragment.f90251E4.size() + 1) {
                return i2 == 0 ? 1 : 2;
            }
            if (i2 == databasesFragment.f90251E4.size() + 1) {
                return 3;
            }
            return databasesFragment.this.m73147O2((i2 - databasesFragment.f90251E4.size()) - 2, databasesFragment.f90251E4).containsKey("Title") ? 1 : 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            if (viewHolder.m27811F() == 3) {
                AddCellViewHolder addCellViewHolder = (AddCellViewHolder) viewHolder;
                addCellViewHolder.f90359I.setText("Add Section");
                addCellViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        final EditText editText = new EditText(databasesFragment.this.m15366r());
                        editText.setTextColor(databasesFragment.this.m15320b0().getColor(C5562R.color.black));
                        new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Enter Section Name").setView(editText).mo1115y("Add", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.1.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                                String string = editText.getText().toString();
                                if (string.length() == 0) {
                                    CompressHelper.m71767x2(databasesFragment.this.m15366r(), "You must enter a name for the section", 1);
                                    return;
                                }
                                if (EditDatabasesAdapter.this.f90379d.containsKey("EditSection" + string)) {
                                    CompressHelper.m71767x2(databasesFragment.this.m15366r(), "You already have a section with this name", 1);
                                    return;
                                }
                                Bundle bundle = new Bundle();
                                bundle.putString("title", string);
                                bundle.putParcelableArrayList("items", new ArrayList<>());
                                databasesFragment.f90251E4.add(bundle);
                                EditDatabasesAdapter editDatabasesAdapter = EditDatabasesAdapter.this;
                                editDatabasesAdapter.f90380e++;
                                editDatabasesAdapter.f90379d.put("EditSection" + string, Integer.valueOf(EditDatabasesAdapter.this.f90380e));
                            }
                        }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.1.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                            }
                        }).m1090I();
                    }
                });
                return;
            }
            if (i2 < databasesFragment.f90251E4.size() + 1) {
                if (i2 == 0) {
                    ((HeaderCellViewHolder) viewHolder).f90402I.setText("Sections");
                    return;
                }
                final String string = databasesFragment.f90251E4.get(i2 - 1).getString("title");
                EditHeaderCellViewHolder editHeaderCellViewHolder = (EditHeaderCellViewHolder) viewHolder;
                editHeaderCellViewHolder.f90398J.setText(string);
                editHeaderCellViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        final EditText editText = new EditText(databasesFragment.this.m15366r());
                        editText.setText(string);
                        editText.setTextColor(databasesFragment.this.m15320b0().getColor(C5562R.color.black));
                        new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Edit Section Name").setView(editText).mo1115y("Edit", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.2.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                                String string2 = editText.getText().toString();
                                if (string2.length() == 0) {
                                    CompressHelper.m71767x2(databasesFragment.this.m15366r(), "You must enter a name for the section", 1);
                                    return;
                                }
                                if (string2.equals(string)) {
                                    return;
                                }
                                if (EditDatabasesAdapter.this.f90379d.containsKey("EditSection" + string2)) {
                                    CompressHelper.m71767x2(databasesFragment.this.m15366r(), "You already have a section with this name", 1);
                                    return;
                                }
                                Bundle bundle = databasesFragment.f90251E4.get(i2 - 1);
                                bundle.remove("title");
                                bundle.putString("title", string2);
                                EditDatabasesAdapter editDatabasesAdapter = EditDatabasesAdapter.this;
                                editDatabasesAdapter.f90380e++;
                                editDatabasesAdapter.f90379d.put("EditSection" + string2, Integer.valueOf(EditDatabasesAdapter.this.f90380e));
                            }
                        }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.2.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                            }
                        }).m1090I();
                    }
                });
                editHeaderCellViewHolder.f90401M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        ArrayList parcelableArrayList = databasesFragment.f90251E4.get(i2 - 1).getParcelableArrayList("items");
                        if (parcelableArrayList != null && parcelableArrayList.size() > 0) {
                            CompressHelper.m71767x2(databasesFragment.this.m15366r(), "First delete databases inside this section", 1);
                        } else {
                            databasesFragment.f90251E4.remove(i2 - 1);
                            databasesFragment.this.f90258g4.getAdapter().m27491G();
                        }
                    }
                });
                return;
            }
            final Bundle bundleM73147O2 = databasesFragment.this.m73147O2((i2 - databasesFragment.f90251E4.size()) - 2, databasesFragment.f90251E4);
            if (!bundleM73147O2.containsKey("Item")) {
                ((HeaderCellViewHolder) viewHolder).f90402I.setText(bundleM73147O2.getString("Title"));
                return;
            }
            Bundle bundle = bundleM73147O2.getBundle("Item");
            EditDatabaseCellViewHolder editDatabaseCellViewHolder = (EditDatabaseCellViewHolder) viewHolder;
            editDatabaseCellViewHolder.f90374J.setText(databasesFragment.this.f90276y4.m71836b1(bundle.getString("Title")));
            Glide.m30040F(databasesFragment.this).mo30129t(CompressHelper.m71724C(bundle)).m30165B2(editDatabaseCellViewHolder.f90375K);
            editDatabaseCellViewHolder.f90378N.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you really want to delete this database ?").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.4.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i3) throws JSONException {
                            int i4 = bundleM73147O2.getInt("Section");
                            int i5 = bundleM73147O2.getInt("Row");
                            String string2 = bundleM73147O2.getBundle("Item").getString("Path");
                            databasesFragment.this.m73135B3();
                            iMDLogger.m73550f("HideKeyboard", "5");
                            databasesFragment.this.m73146N2(new File(string2));
                            databasesFragment.f90251E4.get(i4).getParcelableArrayList("items").remove(i5);
                            databasesFragment.this.f90258g4.getAdapter().m27491G();
                            databasesFragment.this.f90276y4.m71876o2(databasesFragment.f90251E4);
                        }
                    }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.EditDatabasesAdapter.4.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i3) {
                        }
                    }).m1090I();
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new EditDatabaseCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_edit, viewGroup, false));
            }
            if (i2 == 1) {
                return new HeaderCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 2) {
                return new EditHeaderCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header_edit, viewGroup, false));
            }
            if (i2 == 3) {
                return new AddCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_add, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return databasesFragment.this.m73144L3(databasesFragment.f90251E4) + databasesFragment.f90251E4.size() + 2;
        }

        @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter
        /* renamed from: h */
        public void mo50466h(int i2, int i3) {
            ArrayList parcelableArrayList;
            if (i2 > 0) {
                try {
                    if (i2 < databasesFragment.f90251E4.size() + 1) {
                        int i4 = i2 - 1;
                        Bundle bundle = databasesFragment.f90251E4.get(i4);
                        databasesFragment.f90251E4.remove(i4);
                        databasesFragment.f90251E4.add(i3 - 1, bundle);
                        return;
                    }
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    return;
                }
            }
            int size = (i2 - databasesFragment.f90251E4.size()) - 2;
            int size2 = (i3 - databasesFragment.f90251E4.size()) - 2;
            Bundle bundleM73147O2 = databasesFragment.this.m73147O2(size, databasesFragment.f90251E4);
            int i5 = bundleM73147O2.getInt("Row");
            int i6 = bundleM73147O2.getInt("Section");
            Bundle bundle2 = (Bundle) databasesFragment.f90251E4.get(i6).getParcelableArrayList("items").get(i5);
            Bundle bundleM73147O22 = databasesFragment.this.m73147O2(size2, databasesFragment.f90251E4);
            int i7 = bundleM73147O22.getInt("Row");
            int i8 = bundleM73147O22.getInt("Section");
            iMDLogger.m73550f("Staring Drag", "Section " + i6 + " , Row : " + i5 + ", Section2:" + i8 + ", row2:" + i7);
            databasesFragment.f90251E4.get(i6).getParcelableArrayList("items").remove(i5);
            if (bundleM73147O22.containsKey("Title")) {
                int i9 = bundleM73147O22.getInt("Row2");
                int i10 = bundleM73147O22.getInt("Section2");
                iMDLogger.m73550f("Staring Drag", "Section22:" + i10 + ", row22:" + i9);
                if (i6 >= i8) {
                    if (databasesFragment.f90251E4.get(i10).getParcelableArrayList("items").size() == 0) {
                        i9 = 0;
                    }
                    databasesFragment.f90251E4.get(i10).getParcelableArrayList("items").add(i9, bundle2);
                    return;
                }
                parcelableArrayList = databasesFragment.f90251E4.get(i8).getParcelableArrayList("items");
            } else {
                parcelableArrayList = databasesFragment.f90251E4.get(i8).getParcelableArrayList("items");
            }
            parcelableArrayList.add(i7, bundle2);
        }

        @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter
        /* renamed from: k */
        public boolean mo50467k(RecyclerView.ViewHolder viewHolder, int i2, int i3, int i4) {
            ImageView imageView;
            LinearLayout linearLayout;
            if (viewHolder.m27811F() == 1 || viewHolder.m27811F() == 3) {
                return false;
            }
            if (viewHolder.m27811F() == 2) {
                EditHeaderCellViewHolder editHeaderCellViewHolder = (EditHeaderCellViewHolder) viewHolder;
                imageView = editHeaderCellViewHolder.f90399K;
                linearLayout = editHeaderCellViewHolder.f90400L;
            } else {
                EditDatabaseCellViewHolder editDatabaseCellViewHolder = (EditDatabaseCellViewHolder) viewHolder;
                imageView = editDatabaseCellViewHolder.f90376L;
                linearLayout = editDatabaseCellViewHolder.f90377M;
            }
            int left = linearLayout.getLeft() + ((int) (ViewCompat.m9034B0(linearLayout) + 0.5f));
            linearLayout.getTop();
            ViewCompat.m9038C0(linearLayout);
            boolean zM72761a = ViewUtils.m72761a(imageView, i3 - left, 50);
            iMDLogger.m73550f("CanDrag", zM72761a ? PdfBoolean.f69890l3 : "false");
            return zM72761a;
        }

        @Override // com.h6ah4i.android.widget.advrecyclerview.draggable.DraggableItemAdapter
        /* renamed from: w */
        public ItemDraggableRange mo50468w(RecyclerView.ViewHolder viewHolder, int i2) {
            if (i2 <= 0 || i2 >= databasesFragment.f90251E4.size() + 1) {
                return new ItemDraggableRange(databasesFragment.f90251E4.size() + 3, mo26171b() - 1);
            }
            iMDLogger.m73554j("RequestingRange", "Range requested");
            return new ItemDraggableRange(1, databasesFragment.f90251E4.size());
        }
    }

    public static class EditHeaderCellViewHolder extends AbstractDraggableItemViewHolder implements DraggableItemViewHolder {

        /* renamed from: J */
        public TextView f90398J;

        /* renamed from: K */
        public ImageView f90399K;

        /* renamed from: L */
        public LinearLayout f90400L;

        /* renamed from: M */
        public ImageView f90401M;

        public EditHeaderCellViewHolder(View view) {
            super(view);
            this.f90398J = (TextView) view.findViewById(C5562R.id.header_text);
            this.f90399K = (ImageView) view.findViewById(C5562R.id.drag_indicator);
            this.f90400L = (LinearLayout) view.findViewById(C5562R.id.container_view);
            this.f90401M = (ImageView) view.findViewById(C5562R.id.remove_icon);
        }
    }

    public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90402I;

        public HeaderCellViewHolder(View view) {
            super(view);
            this.f90402I = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    public class SearchCollectionDatabasesAdapter extends RecyclerView.Adapter {
        public SearchCollectionDatabasesAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            return i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            CardViewPlaceHolder cardViewPlaceHolder = (CardViewPlaceHolder) viewHolder;
            databasesFragment.this.m73143L2(databasesFragment.f90252F4.get(i2), cardViewPlaceHolder.f90363L, cardViewPlaceHolder.f90360I, cardViewPlaceHolder.f90361J, cardViewPlaceHolder.f90362K, cardViewPlaceHolder.f90364M, cardViewPlaceHolder.f33076a);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            return new CardViewPlaceHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_card_view_database, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return databasesFragment.f90252F4.size();
        }
    }

    public class SearchDatabasesAdapter extends RecyclerView.Adapter {
        public SearchDatabasesAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            return i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            DatabaseCellViewHolder databaseCellViewHolder = (DatabaseCellViewHolder) viewHolder;
            databasesFragment.this.m73143L2(databasesFragment.f90252F4.get(i2), databaseCellViewHolder.f90371M, databaseCellViewHolder.f90367I, databaseCellViewHolder.f90368J, databaseCellViewHolder.f90369K, databaseCellViewHolder.f90370L, databaseCellViewHolder.f33076a);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            return new DatabaseCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_ripple, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return databasesFragment.f90252F4.size();
        }
    }

    public class TestCollectionAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        HashMap<String, Integer> f90405d = new HashMap<>();

        public TestCollectionAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: B */
        public long mo26183B(int i2) {
            return i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return databasesFragment.this.m73147O2(1, databasesFragment.f90251E4).containsKey("Title") ? 1 : 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            Bundle bundleM73147O2 = databasesFragment.this.m73147O2(1, databasesFragment.f90251E4);
            if (!bundleM73147O2.containsKey("Item")) {
                ((HeaderCellViewHolder) viewHolder).f90402I.setText(bundleM73147O2.getString("Title"));
                return;
            }
            Bundle bundle = bundleM73147O2.getBundle("Item");
            final CardViewPlaceHolder cardViewPlaceHolder = (CardViewPlaceHolder) viewHolder;
            cardViewPlaceHolder.f90360I.setText(databasesFragment.this.f90276y4.m71836b1(bundle.getString("Title")));
            final String strM71724C = CompressHelper.m71724C(bundle);
            if (databasesFragment.this.f90277z4.containsKey(strM71724C)) {
                cardViewPlaceHolder.f90364M.setRippleColor(databasesFragment.this.f90277z4.getInt(strM71724C));
            } else {
                databasesFragment.this.m73150y3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.TestCollectionAdapter.1
                    @Override // java.lang.Runnable
                    public void run() {
                        Bitmap bitmapDecodeStream;
                        if (strM71724C.contains("file:///android_asset/")) {
                            try {
                                bitmapDecodeStream = BitmapFactory.decodeStream(databasesFragment.this.m15366r().getAssets().open(strM71724C.replace("file:///android_asset/", "")));
                            } catch (Exception e2) {
                                FirebaseCrashlytics.m48010d().m48016g(e2);
                                e2.printStackTrace();
                                return;
                            }
                        } else {
                            bitmapDecodeStream = BitmapFactory.decodeFile(strM71724C);
                        }
                        Palette.Swatch swatchM26489C = Palette.m26478b(bitmapDecodeStream).m26518g().m26489C();
                        if (swatchM26489C == null) {
                            return;
                        }
                        int iM26530e = swatchM26489C.m26530e();
                        if (databasesFragment.this.f90277z4.containsKey(strM71724C)) {
                            return;
                        }
                        databasesFragment.this.f90277z4.putInt(strM71724C, iM26530e);
                    }
                }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.TestCollectionAdapter.2
                    @Override // java.lang.Runnable
                    public void run() {
                        cardViewPlaceHolder.f90364M.setRippleColor(databasesFragment.this.f90277z4.getInt(strM71724C));
                    }
                });
            }
            Glide.m30040F(databasesFragment.this).mo30129t(strM71724C).mo30182a(new RequestOptions().m31361u()).m30165B2(cardViewPlaceHolder.f90362K);
            cardViewPlaceHolder.f90364M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.TestCollectionAdapter.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    Bundle bundleM73147O22 = databasesFragment.this.m73147O2(i2, databasesFragment.f90251E4);
                    if (bundleM73147O22.containsKey("Title")) {
                        return;
                    }
                    databasesFragment.this.f90276y4.m71909z1(bundleM73147O22.getBundle("Item"));
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                CardViewPlaceHolder cardViewPlaceHolder = new CardViewPlaceHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_card_view_database, viewGroup, false));
                cardViewPlaceHolder.f90360I.setTypeface(databasesFragment.this.f90275x4);
                return cardViewPlaceHolder;
            }
            if (i2 == 1) {
                return new HeaderCellViewHolder(LayoutInflater.from(databasesFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_card_header, viewGroup, false));
            }
            return null;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            return 300;
        }
    }

    public abstract class UIActionClass extends ItemTouchHelper.Callback {

        /* renamed from: i */
        Context f90414i;

        /* renamed from: j */
        private final Paint f90415j;

        /* renamed from: k */
        private final ColorDrawable f90416k = new ColorDrawable();

        /* renamed from: l */
        private final int f90417l = Color.parseColor("#b80f0a");

        /* renamed from: m */
        private final Drawable f90418m;

        /* renamed from: n */
        private final int f90419n;

        /* renamed from: o */
        private final int f90420o;

        UIActionClass(Context context) {
            this.f90414i = context;
            Paint paint = new Paint();
            this.f90415j = paint;
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
            Drawable drawableM6692l = ContextCompat.m6692l(this.f90414i, R.drawable.ic_menu_delete);
            this.f90418m = drawableM6692l;
            this.f90419n = drawableM6692l.getIntrinsicWidth();
            this.f90420o = drawableM6692l.getIntrinsicHeight();
        }

        /* renamed from: E */
        private void m73168E(Canvas canvas, Float f2, Float f3, Float f4, Float f5) {
            canvas.drawRect(f2.floatValue(), f3.floatValue(), f4.floatValue(), f5.floatValue(), this.f90415j);
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: A */
        public boolean mo27105A(@androidx.annotation.NonNull RecyclerView recyclerView, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder2) {
            return false;
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: l */
        public int mo27117l(@androidx.annotation.NonNull RecyclerView recyclerView, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder) {
            if (databasesFragment.this.m73136C3()) {
                return ItemTouchHelper.Callback.m27104v(0, 0);
            }
            viewHolder.m27811F();
            return ItemTouchHelper.Callback.m27104v(0, 4);
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: n */
        public float mo27119n(@androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder) {
            return 0.7f;
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: w */
        public void mo27126w(@androidx.annotation.NonNull Canvas canvas, @androidx.annotation.NonNull RecyclerView recyclerView, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder, float f2, float f3, int i2, boolean z) {
            super.mo27126w(canvas, recyclerView, viewHolder, f2, f3, i2, z);
            View view = viewHolder.f33076a;
            int height = view.getHeight();
            if (f2 != 0.0f || z) {
                this.f90416k.setColor(this.f90417l);
                this.f90416k.setBounds(view.getRight() + ((int) f2), view.getTop(), view.getRight(), view.getBottom());
                this.f90416k.draw(canvas);
                int top = view.getTop();
                int i3 = this.f90420o;
                int i4 = top + ((height - i3) / 2);
                int i5 = (height - i3) / 2;
                this.f90418m.setBounds((view.getRight() - i5) - this.f90419n, i4, view.getRight() - i5, this.f90420o + i4);
                this.f90418m.draw(canvas);
            } else {
                m73168E(canvas, Float.valueOf(view.getRight() + f2), Float.valueOf(view.getTop()), Float.valueOf(view.getRight()), Float.valueOf(view.getBottom()));
            }
            super.mo27126w(canvas, recyclerView, viewHolder, f2, f3, i2, z);
        }
    }

    public abstract class UIActionClassRight extends ItemTouchHelper.Callback {

        /* renamed from: i */
        Context f90422i;

        /* renamed from: j */
        private final Paint f90423j;

        /* renamed from: k */
        private final ColorDrawable f90424k = new ColorDrawable();

        /* renamed from: l */
        private final int f90425l = Color.parseColor("#0ab867");

        /* renamed from: m */
        private final Drawable f90426m;

        /* renamed from: n */
        private final int f90427n;

        /* renamed from: o */
        private final int f90428o;

        UIActionClassRight(Context context) {
            this.f90422i = context;
            Paint paint = new Paint();
            this.f90423j = paint;
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
            Drawable drawableM6692l = ContextCompat.m6692l(this.f90422i, R.drawable.ic_menu_edit);
            this.f90426m = drawableM6692l;
            this.f90427n = drawableM6692l.getIntrinsicWidth();
            this.f90428o = drawableM6692l.getIntrinsicHeight();
        }

        /* renamed from: E */
        private void m73169E(Canvas canvas, Float f2, Float f3, Float f4, Float f5) {
            canvas.drawRect(f2.floatValue(), f3.floatValue(), f4.floatValue(), f5.floatValue(), this.f90423j);
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: A */
        public boolean mo27105A(@androidx.annotation.NonNull RecyclerView recyclerView, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder2) {
            return false;
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: l */
        public int mo27117l(@androidx.annotation.NonNull RecyclerView recyclerView, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder) {
            if (!databasesFragment.this.m73136C3() && viewHolder.m27811F() == 1) {
                return ItemTouchHelper.Callback.m27104v(0, 8);
            }
            return ItemTouchHelper.Callback.m27104v(0, 0);
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: n */
        public float mo27119n(@androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder) {
            return 0.7f;
        }

        @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
        /* renamed from: w */
        public void mo27126w(@androidx.annotation.NonNull Canvas canvas, @androidx.annotation.NonNull RecyclerView recyclerView, @androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder, float f2, float f3, int i2, boolean z) {
            super.mo27126w(canvas, recyclerView, viewHolder, f2, f3, i2, z);
            View view = viewHolder.f33076a;
            int height = view.getHeight();
            if (f2 != 0.0f || z) {
                this.f90424k.setColor(this.f90425l);
                this.f90424k.setBounds(view.getLeft(), view.getTop(), view.getLeft() + ((int) f2), view.getBottom());
                this.f90424k.draw(canvas);
                int top = view.getTop();
                int i3 = this.f90428o;
                int i4 = top + ((height - i3) / 2);
                int i5 = (height - i3) / 2;
                this.f90426m.setBounds(i5, i4, this.f90427n + i5, i3 + i4);
                this.f90426m.draw(canvas);
            } else {
                m73169E(canvas, Float.valueOf(view.getRight() + f2), Float.valueOf(view.getTop()), Float.valueOf(view.getRight()), Float.valueOf(view.getBottom()));
            }
            super.mo27126w(canvas, recyclerView, viewHolder, f2, f3, i2, z);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: F3 */
    public void m73097F3(@androidx.annotation.NonNull File file, @androidx.annotation.NonNull File file2, Bundle bundle, ObservableEmitter<Bundle> observableEmitter) throws IOException {
        if (file.isDirectory()) {
            m73098G3(file, file2, bundle, observableEmitter);
            return;
        }
        this.f90260i4++;
        if (System.currentTimeMillis() - this.f90270s4 > 1000) {
            if (bundle.containsKey("counter")) {
                bundle.remove("counter");
            }
            bundle.putLong("counter", this.f90260i4);
            this.f90270s4 = System.currentTimeMillis();
            observableEmitter.onNext(bundle);
        }
        if (Build.VERSION.SDK_INT < 26) {
            if (file2.exists() && !((file2.getName().contains("favorites.db") || file2.getName().contains("favorites.json") || file2.getName().contains("databases.json") || file2.getName().contains("highlights.db") || file2.getName().contains("recent.db")) && file2.delete())) {
                return;
            }
            CompressHelper.m71742Q1(file, file2);
            return;
        }
        Path path = Paths.get(file.toURI());
        Path path2 = Paths.get(file2.toURI());
        if (!file2.exists()) {
            Files.move(path, path2, new CopyOption[0]);
            return;
        }
        if ((file2.getName().contains("favorites.db") || file2.getName().contains("favorites.json") || file2.getName().contains("databases.json") || file2.getName().contains("highlights.db") || file2.getName().contains("recent.db")) && file2.delete()) {
            Files.move(path, path2, new CopyOption[0]);
        }
    }

    /* renamed from: G3 */
    private void m73098G3(@androidx.annotation.NonNull File file, @androidx.annotation.NonNull File file2, Bundle bundle, ObservableEmitter<Bundle> observableEmitter) throws IOException {
        if (file2.exists() || file2.mkdir()) {
            for (String str : file.list()) {
                m73097F3(new File(file, str), new File(file2, str), bundle, observableEmitter);
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: w3 */
    public void m73131w3() {
        m73132x3();
        if (Build.VERSION.SDK_INT < 33 || ContextCompat.m6681a(m15366r(), "android.permission.POST_NOTIFICATIONS") == 0) {
            return;
        }
        m15252C2("android.permission.POST_NOTIFICATIONS");
        this.f90254B4.m798b("android.permission.POST_NOTIFICATIONS");
    }

    /* renamed from: x3 */
    private void m73132x3() {
        if (Build.VERSION.SDK_INT >= 26) {
            NotificationChannel notificationChannelM18672a = C1201k.m18672a("12121", "iMDChannel", 4);
            notificationChannelM18672a.setDescription("iMDChannel");
            ((NotificationManager) m15366r().getSystemService(NotificationManager.class)).createNotificationChannel(notificationChannelM18672a);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: z3 */
    public void m73133z3() {
        UIActionClass uIActionClass = new UIActionClass(m15366r()) { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.1
            @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
            /* renamed from: D */
            public void mo27108D(@androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder, int i2) {
                try {
                    final int iM27807B = viewHolder.m27807B();
                    final Bundle bundleM73147O2 = (databasesFragment.this.f90258g4.getAdapter().getClass() == SearchDatabasesAdapter.class || databasesFragment.this.f90258g4.getAdapter().getClass() == SearchCollectionDatabasesAdapter.class) ? databasesFragment.f90252F4.get(iM27807B) : databasesFragment.this.m73147O2(iM27807B, databasesFragment.f90251E4);
                    if (viewHolder.m27811F() == 0) {
                        new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you really want to delete this database ?").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.1.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) throws JSONException {
                                if (databasesFragment.this.f90258g4.getAdapter().getClass() == SearchDatabasesAdapter.class || databasesFragment.this.f90258g4.getAdapter().getClass() == SearchCollectionDatabasesAdapter.class) {
                                    String string = bundleM73147O2.getString("Path");
                                    databasesFragment.this.m73135B3();
                                    databasesFragment.this.m73146N2(new File(string));
                                    databasesFragment.this.f90276y4.m71876o2(databasesFragment.f90251E4);
                                    databasesFragment.this.f90258g4.getAdapter().m27500P(iM27807B);
                                    return;
                                }
                                int i4 = bundleM73147O2.getInt("Section");
                                int i5 = bundleM73147O2.getInt("Row");
                                String string2 = bundleM73147O2.getBundle("Item").getString("Path");
                                databasesFragment.this.m73135B3();
                                databasesFragment.this.m73146N2(new File(string2));
                                databasesFragment.f90251E4.get(i4).getParcelableArrayList("items").remove(i5);
                                databasesFragment.this.f90276y4.m71876o2(databasesFragment.f90251E4);
                                databasesFragment.this.f90258g4.getAdapter().m27491G();
                            }
                        }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.1.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i3) {
                                databasesFragment.this.f90258g4.getAdapter().m27492H(iM27807B);
                            }
                        }).m1090I();
                        return;
                    }
                    int i3 = bundleM73147O2.getInt("Section");
                    ArrayList parcelableArrayList = databasesFragment.f90251E4.get(i3).getParcelableArrayList("items");
                    if (parcelableArrayList != null && parcelableArrayList.size() > 0) {
                        CompressHelper.m71767x2(databasesFragment.this.m15366r(), "First delete databases inside this section", 1);
                        databasesFragment.this.f90258g4.getAdapter().m27492H(iM27807B);
                    } else {
                        databasesFragment.f90251E4.remove(i3);
                        databasesFragment.this.f90276y4.m71876o2(databasesFragment.f90251E4);
                        databasesFragment.this.f90258g4.getAdapter().m27500P(iM27807B);
                    }
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        };
        UIActionClassRight uIActionClassRight = new UIActionClassRight(m15366r()) { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.2
            @Override // androidx.recyclerview.widget.ItemTouchHelper.Callback
            /* renamed from: D */
            public void mo27108D(@androidx.annotation.NonNull RecyclerView.ViewHolder viewHolder, int i2) {
                final ArrayList arrayList = new ArrayList();
                final int iM27807B = viewHolder.m27807B();
                final int i3 = databasesFragment.this.m73147O2(iM27807B, databasesFragment.f90251E4).getInt("Section");
                final String string = databasesFragment.f90251E4.get(i3).getString("title");
                for (int i4 = 0; i4 < databasesFragment.f90251E4.size(); i4++) {
                    if (i4 != i3) {
                        arrayList.add(databasesFragment.f90251E4.get(i4).getString("title"));
                    }
                }
                final EditText editText = new EditText(databasesFragment.this.m15366r());
                editText.setText(string);
                editText.setTextColor(databasesFragment.this.m15320b0().getColor(C5562R.color.black));
                new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Edit Section Name").setView(editText).mo1115y("Edit", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.2.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i5) throws JSONException {
                        String string2 = editText.getText().toString();
                        if (string2.length() == 0) {
                            CompressHelper.m71767x2(databasesFragment.this.m15366r(), "You must enter a name for the section", 1);
                            databasesFragment.this.f90258g4.getAdapter().m27492H(iM27807B);
                            return;
                        }
                        if (string2.equals(string)) {
                            databasesFragment.this.f90258g4.getAdapter().m27492H(iM27807B);
                            return;
                        }
                        if (arrayList.contains(string2)) {
                            CompressHelper.m71767x2(databasesFragment.this.m15366r(), "You already have a section with this name", 1);
                            databasesFragment.this.f90258g4.getAdapter().m27492H(iM27807B);
                            return;
                        }
                        Bundle bundle = databasesFragment.f90251E4.get(i3);
                        bundle.remove("title");
                        bundle.putString("title", string2);
                        databasesFragment.this.f90258g4.getAdapter().m27492H(iM27807B);
                        databasesFragment.this.f90276y4.m71876o2(databasesFragment.f90251E4);
                    }
                }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.2.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i5) {
                        databasesFragment.this.f90258g4.getAdapter().m27492H(iM27807B);
                    }
                }).m1090I();
            }
        };
        new ItemTouchHelper(uIActionClass).m27092m(this.f90258g4);
        new ItemTouchHelper(uIActionClassRight).m27092m(this.f90258g4);
    }

    /* renamed from: A3 */
    public long m73134A3(File file) {
        if (!file.isDirectory()) {
            return 1L;
        }
        int iM73134A3 = 0;
        for (File file2 : file.listFiles()) {
            iM73134A3 = file2.isDirectory() ? (int) (iM73134A3 + m73134A3(file2)) : iM73134A3 + 1;
        }
        return iM73134A3;
    }

    /* renamed from: B3 */
    public void m73135B3() {
        iMDLogger.m73550f("HideKeyboard", "6");
        try {
            InputMethodManager inputMethodManager = (InputMethodManager) m15366r().getSystemService("input_method");
            if (m15366r().getCurrentFocus() != null) {
                inputMethodManager.hideSoftInputFromWindow(m15366r().getCurrentFocus().getWindowToken(), 0);
            }
            if (m15366r().getCurrentFocus() != null) {
                m15366r().getCurrentFocus().clearFocus();
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: C3 */
    public boolean m73136C3() {
        ArrayList<Bundle> arrayList;
        RecyclerView recyclerView = this.f90258g4;
        return (recyclerView == null || recyclerView.getAdapter().getClass() == SearchDatabasesAdapter.class || this.f90258g4.getAdapter().getClass() == SearchCollectionDatabasesAdapter.class || this.f90258g4.getAdapter().getClass() == DatabasesAdapter.class || this.f90258g4.getAdapter().getClass() == CollectionAdapter.class || (arrayList = f90251E4) == null || arrayList.size() == 0) ? false : true;
    }

    /* renamed from: E3 */
    public void m73137E3() {
        if ((Build.VERSION.SDK_INT < 23 || ContextCompat.m6681a(m15366r(), "android.permission.WRITE_EXTERNAL_STORAGE") == 0) && new File(this.f90276y4.m71794L()).exists()) {
            m73148P2(new File(this.f90276y4.m71794L()), new File(this.f90276y4.m71797M1()));
        }
    }

    /* renamed from: H3 */
    public void m73138H3(final String str) {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.11
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                databasesFragment.f90252F4 = new ArrayList<>(Collections2.m42365d(((iMD) databasesFragment.this.m15366r().getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.11.1
                    @Override // com.google.common.base.Predicate
                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                    public boolean apply(Bundle bundle) {
                        try {
                            return bundle.getString("Title").toLowerCase().contains(str.toLowerCase());
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                            iMDLogger.m73550f("Error in filtering", e2.getLocalizedMessage());
                            e2.printStackTrace();
                            return false;
                        }
                    }
                }));
                observableEmitter.onComplete();
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.12
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str2) throws Throwable {
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.13
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                try {
                    iMDLogger.m73550f("DatabasesFragmentSearch", "Error occured in filtering databases");
                    th.printStackTrace();
                } catch (Exception unused) {
                }
            }
        }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.14
            @Override // io.reactivex.rxjava3.functions.Action
            public void run() throws Throwable {
                RecyclerView recyclerView;
                RecyclerView.Adapter searchCollectionDatabasesAdapter;
                ArrayList<Bundle> arrayList = databasesFragment.f90252F4;
                if (arrayList == null || arrayList.size() == 0) {
                    databasesFragment.this.m73142K3("Nothing Found");
                    return;
                }
                databasesFragment.this.m73140J3();
                if (databasesFragment.this.f90261j4) {
                    recyclerView = databasesFragment.this.f90258g4;
                    searchCollectionDatabasesAdapter = databasesFragment.this.new SearchCollectionDatabasesAdapter();
                } else {
                    recyclerView = databasesFragment.this.f90258g4;
                    searchCollectionDatabasesAdapter = databasesFragment.this.new SearchDatabasesAdapter();
                }
                recyclerView.setAdapter(searchCollectionDatabasesAdapter);
            }
        });
    }

    /* renamed from: I3 */
    public final void m73139I3() {
        iMDLogger.m73548d("sendFavorite", "Sending FavoriteChanged message");
        Intent intent = new Intent("net.imedicaldoctor.imd.favorite");
        intent.putExtra("Test", "Random data for test");
        LocalBroadcastManager.m16410b(m15366r()).m16413d(intent);
    }

    /* renamed from: J3 */
    public void m73140J3() {
        RecyclerView recyclerView;
        RecyclerView.Adapter adapter;
        if (this.f90261j4) {
            recyclerView = this.f90258g4;
            adapter = this.f90268q4;
        } else {
            recyclerView = this.f90258g4;
            adapter = this.f90259h4;
        }
        recyclerView.setAdapter(adapter);
    }

    /* renamed from: K2 */
    public void m73141K2(Bundle bundle) {
        C539622 c539622 = new C539622(bundle);
        CompressHelper compressHelper = this.f90276y4;
        compressHelper.m71789I0(this, compressHelper.m71874o0("QueryPrice|||||" + new VBHelper(m15366r()).m73451m() + "|||||" + bundle.getString("Name"))).mo59651a(c539622);
    }

    /* renamed from: K3 */
    public void m73142K3(String str) {
        RecyclerView recyclerView;
        StatusAdapter statusAdapter;
        if (str.toLowerCase().contains("go to".toLowerCase())) {
            recyclerView = this.f90258g4;
            statusAdapter = new StatusAdapter(m15366r(), str) { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.26
                @Override // net.imedicaldoctor.imd.ViewHolders.StatusAdapter
                /* renamed from: d0 */
                public void mo73158d0() throws Resources.NotFoundException {
                    ((mainActivity) databasesFragment.this.m15366r()).f90750y3.setCurrentItem(4);
                }
            };
        } else {
            recyclerView = this.f90258g4;
            statusAdapter = new StatusAdapter(m15366r(), str);
        }
        recyclerView.setAdapter(statusAdapter);
    }

    /* renamed from: L2 */
    public void m73143L2(final Bundle bundle, AppCompatButton appCompatButton, TextView textView, TextView textView2, ImageView imageView, final MaterialRippleLayout materialRippleLayout, View view) {
        String str;
        View.OnClickListener onClickListener;
        textView.setText(this.f90276y4.m71836b1(bundle.getString("Title")));
        final String strM71724C = CompressHelper.m71724C(bundle);
        if (strM71724C.contains("file:///android_asset/") || new File(strM71724C).exists()) {
            if (strM71724C.contains("irandarou")) {
                iMDLogger.m73550f("debug", "de");
            }
            Glide.m30040F(this).mo30129t(strM71724C).m30165B2(imageView);
            if (this.f90274w4) {
                if (this.f90277z4.containsKey(strM71724C)) {
                    materialRippleLayout.setRippleColor(this.f90277z4.getInt(strM71724C));
                } else {
                    m73150y3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.16
                        @Override // java.lang.Runnable
                        public void run() {
                            Bitmap bitmapDecodeStream;
                            if (strM71724C.contains("file:///android_asset/")) {
                                try {
                                    bitmapDecodeStream = BitmapFactory.decodeStream(databasesFragment.this.m15366r().getAssets().open(strM71724C.replace("file:///android_asset/", "")));
                                } catch (Exception e2) {
                                    FirebaseCrashlytics.m48010d().m48016g(e2);
                                    e2.printStackTrace();
                                    return;
                                }
                            } else {
                                bitmapDecodeStream = BitmapFactory.decodeFile(strM71724C);
                            }
                            Palette.Swatch swatchM26489C = Palette.m26478b(bitmapDecodeStream).m26518g().m26489C();
                            if (swatchM26489C == null) {
                                return;
                            }
                            int iM26530e = swatchM26489C.m26530e();
                            if (databasesFragment.this.f90277z4.containsKey(strM71724C)) {
                                return;
                            }
                            databasesFragment.this.f90277z4.putInt(strM71724C, iM26530e);
                        }
                    }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.17
                        @Override // java.lang.Runnable
                        public void run() {
                            materialRippleLayout.setRippleColor(databasesFragment.this.f90277z4.getInt(strM71724C));
                        }
                    });
                }
            }
        } else {
            imageView.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.placeholder));
        }
        if (!bundle.containsKey("Inactive")) {
            appCompatButton.setVisibility(8);
            if (bundle.containsKey("ExpDate")) {
                textView2.setVisibility(0);
                textView2.setText(CompressHelper.m71746c1(bundle.getString("Version")));
            } else {
                textView2.setVisibility(8);
            }
            materialRippleLayout.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.18
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    databasesFragment.this.f90276y4.m71909z1(bundle);
                }
            });
            return;
        }
        appCompatButton.setVisibility(0);
        textView2.setVisibility(0);
        if (bundle.containsKey("ExpDate")) {
            str = "Expired at " + CompressHelper.m71746c1(bundle.getString("ExpDate"));
        } else {
            str = "Click Buy for activation";
        }
        textView2.setText(str);
        if (bundle.containsKey("Demo")) {
            textView2.setText(((Object) textView2.getText()) + " - Demo Available");
            textView2.setVisibility(8);
            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.19
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    new AlertDialog.Builder(databasesFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("You are using demo version of this database, in this mode you can see three random chapters and can't search the database").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.19.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            databasesFragment.this.f90276y4.m71909z1(bundle);
                        }
                    }).m1090I();
                }
            };
        } else {
            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.20
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    Toast.makeText(databasesFragment.this.m15366r(), "You don't own this database, please click Buy for purchase.", 1).show();
                }
            };
        }
        materialRippleLayout.setOnClickListener(onClickListener);
        appCompatButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.21
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                databasesFragment.this.m73141K2(bundle);
            }
        });
    }

    /* renamed from: L3 */
    public int m73144L3(ArrayList<Bundle> arrayList) {
        int size = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            size = size + it2.next().getParcelableArrayList("items").size() + 1;
        }
        return size;
    }

    /* renamed from: M2 */
    public void m73145M2(File file) {
        if (file.isDirectory()) {
            for (File file2 : file.listFiles()) {
                m73145M2(file2);
            }
        }
        file.delete();
    }

    /* renamed from: N2 */
    public void m73146N2(final File file) {
        Observable observableM59451w1 = Observable.m59451w1(new ObservableOnSubscribe<Long>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.27
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<Long> observableEmitter) throws Throwable {
                databasesFragment.this.m73145M2(file);
                observableEmitter.onComplete();
            }
        });
        final ProgressDialog progressDialogShow = ProgressDialog.show(m15366r(), "Deleting", "Please wait...", true);
        observableM59451w1.m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<Long>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.28
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Long l2) throws Throwable {
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.29
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                progressDialogShow.dismiss();
                databasesFragment.this.f90276y4.m71861k0();
                LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("reloadDownloads"));
            }
        }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.30
            @Override // io.reactivex.rxjava3.functions.Action
            public void run() throws Throwable {
                progressDialogShow.dismiss();
                databasesFragment.this.f90276y4.m71861k0();
                LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("reloadDownloads"));
            }
        });
    }

    /* renamed from: O2 */
    public Bundle m73147O2(int i2, ArrayList<Bundle> arrayList) {
        Iterator<Bundle> it2 = arrayList.iterator();
        int i3 = 0;
        int i4 = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Title", next.getString("title"));
                bundle.putInt("Row", 0);
                bundle.putInt("Section", i4);
                bundle.putInt("Row2", 1);
                bundle.putInt("Section2", i4 - 1);
                return bundle;
            }
            int size = i3 + next.getParcelableArrayList("items").size();
            if (i2 <= size) {
                int size2 = (i2 - (size - next.getParcelableArrayList("items").size())) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                bundle2.putInt("Row", size2);
                bundle2.putInt("Section", i4);
                return bundle2;
            }
            i3 = size + 1;
            i4++;
        }
        return null;
    }

    /* renamed from: P2 */
    public void m73148P2(final File file, final File file2) {
        Observable observableM59451w1 = Observable.m59451w1(new ObservableOnSubscribe<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.31
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<Bundle> observableEmitter) throws Throwable {
                BufferedSource bufferedSourceM75769e;
                String[] list = file.list();
                int length = list.length;
                int i2 = 0;
                int i3 = 0;
                while (i3 < length) {
                    String string = list[i3];
                    int i4 = i2 + 1;
                    databasesFragment.this.f90260i4 = 0L;
                    File file3 = new File(file, string);
                    File file4 = new File(file2, string);
                    if (file3.isDirectory()) {
                        File file5 = new File(file3, "info.vbe");
                        if (file5.exists()) {
                            try {
                                bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file5));
                                try {
                                    Bundle bundleM73443e = databasesFragment.this.f90262k4.m73443e(bufferedSourceM75769e.mo75462b0(), file5);
                                    if (bundleM73443e != null) {
                                        string = bundleM73443e.getString("Title");
                                    }
                                    bufferedSourceM75769e.close();
                                } finally {
                                }
                            } catch (IOException e2) {
                                Log.e("Error", "Error reading vbe file", e2);
                            }
                        } else {
                            File file6 = new File(file4, "info.vbe");
                            if (file6.exists()) {
                                bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file6));
                                try {
                                    Bundle bundleM73443e2 = databasesFragment.this.f90262k4.m73443e(bufferedSourceM75769e.mo75462b0(), file6);
                                    if (bundleM73443e2 != null) {
                                        string = bundleM73443e2.getString("Title");
                                    }
                                    bufferedSourceM75769e.close();
                                } finally {
                                }
                            }
                        }
                    }
                    Bundle bundle = new Bundle();
                    bundle.putLong("counter", 0L);
                    bundle.putLong("total", databasesFragment.this.m73134A3(file3));
                    bundle.putString("title", "Moving " + i4 + " of " + list.length + " : " + string);
                    observableEmitter.onNext(bundle);
                    try {
                        databasesFragment.this.m73097F3(file3, file4, bundle, observableEmitter);
                    } catch (Exception e3) {
                        Log.e("IOError", e3.toString());
                    }
                    i3++;
                    i2 = i4;
                }
                try {
                    CompressHelper.m71740P0(file);
                } catch (Exception unused) {
                }
                observableEmitter.onComplete();
            }
        });
        final ProgressDialog progressDialogShow = ProgressDialog.show(m15366r(), "Migrating Databases", "Please wait...", true);
        observableM59451w1.m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.32
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Bundle bundle) throws Throwable {
                progressDialogShow.setMessage(bundle.getString("title") + " (" + bundle.getLong("counter") + " of " + bundle.getLong("total") + " files)");
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.33
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                th.printStackTrace();
                iMDLogger.m73550f("Moving", th.getMessage());
            }
        }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.34
            @Override // io.reactivex.rxjava3.functions.Action
            public void run() throws Throwable {
                progressDialogShow.dismiss();
                LocalBroadcastManager.m16410b(databasesFragment.this.m15366r()).m16413d(new Intent("reload"));
                databasesFragment.this.m73139I3();
            }
        });
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: Q0 */
    public void mo15207Q0(Bundle bundle) {
        super.mo15207Q0(bundle);
        LocalBroadcastManager.m16410b(m15366r()).m16412c(this.f90255C4, new IntentFilter("reload"));
    }

    /* renamed from: Q2 */
    public void m73149Q2() {
        ArrayList<Bundle> arrayList = f90251E4;
        if (arrayList == null || arrayList.size() == 0) {
            m73142K3("No databases is installed. Tap to go to store tab to buy and download databases");
        } else {
            m73140J3();
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(C5562R.menu.menu_databases, menu);
        final MenuItem menuItemFindItem = menu.findItem(C5562R.id.action_edit);
        final MenuItem menuItemFindItem2 = menu.findItem(C5562R.id.action_done);
        this.f90265n4 = menuItemFindItem;
        this.f90266o4 = menuItemFindItem2;
        menuItemFindItem.setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.5
            @Override // android.view.MenuItem.OnMenuItemClickListener
            public boolean onMenuItemClick(MenuItem menuItem) {
                RecyclerView recyclerView;
                RecyclerView.Adapter adapter;
                ArrayList<Bundle> arrayList = databasesFragment.f90251E4;
                if (arrayList == null || arrayList.size() == 0) {
                    return true;
                }
                databasesFragment.this.f90267p4.clearFocus();
                if (databasesFragment.this.f90258g4.getAdapter().getClass() == SearchDatabasesAdapter.class || databasesFragment.this.f90258g4.getAdapter().getClass() == SearchCollectionDatabasesAdapter.class) {
                    databasesFragment.this.f90267p4.m2508k0("", false);
                    databasesFragment.this.f90267p4.clearFocus();
                    if (databasesFragment.this.f90261j4) {
                        recyclerView = databasesFragment.this.f90258g4;
                        adapter = databasesFragment.this.f90268q4;
                    } else {
                        recyclerView = databasesFragment.this.f90258g4;
                        adapter = databasesFragment.this.f90259h4;
                    }
                    recyclerView.setAdapter(adapter);
                    databasesFragment.this.m73135B3();
                    iMDLogger.m73550f("HideKeyboard", "4");
                }
                menuItemFindItem.setVisible(false);
                menuItemFindItem2.setVisible(true);
                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(databasesFragment.this.m15366r());
                databasesFragment.this.f90269r4 = new RecyclerViewDragDropManager();
                databasesFragment.this.f90269r4.m50571R((NinePatchDrawable) databasesFragment.this.m15320b0().getDrawable(C5562R.drawable.material_shadow_z3_9));
                RecyclerView.Adapter adapterM50582o = databasesFragment.this.f90269r4.m50582o(databasesFragment.this.new EditDatabasesAdapter());
                databasesFragment.this.f90258g4.setLayoutManager(linearLayoutManager);
                databasesFragment.this.f90258g4.setAdapter(adapterM50582o);
                databasesFragment.this.f90269r4.m50579h(databasesFragment.this.f90258g4);
                return false;
            }
        });
        menuItemFindItem2.setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.6
            @Override // android.view.MenuItem.OnMenuItemClickListener
            public boolean onMenuItemClick(MenuItem menuItem) throws JSONException {
                menuItemFindItem.setVisible(true);
                menuItemFindItem2.setVisible(false);
                databasesFragment.this.f90269r4.m50570N();
                if (databasesFragment.this.f90261j4) {
                    databasesFragment databasesfragment = databasesFragment.this;
                    databasesfragment.f90268q4 = databasesfragment.new CollectionAdapter();
                    databasesFragment.this.f90258g4.setAdapter(databasesFragment.this.f90268q4);
                    databasesFragment.this.f90258g4.setLayoutManager(databasesFragment.this.f90272u4);
                } else {
                    databasesFragment databasesfragment2 = databasesFragment.this;
                    databasesfragment2.f90259h4 = databasesfragment2.new DatabasesAdapter();
                    databasesFragment.this.f90258g4.setAdapter(databasesFragment.this.f90259h4);
                }
                databasesFragment.this.f90276y4.m71876o2(databasesFragment.f90251E4);
                databasesFragment.this.f90276y4.m71880p2();
                databasesFragment.this.m73139I3();
                return false;
            }
        });
        if (m73136C3()) {
            this.f90265n4.setVisible(false);
            this.f90266o4.setVisible(true);
        } else {
            this.f90265n4.setVisible(true);
            this.f90266o4.setVisible(false);
        }
        menu.findItem(C5562R.id.action_navigation).setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.7
            @Override // android.view.MenuItem.OnMenuItemClickListener
            public boolean onMenuItemClick(MenuItem menuItem) {
                ((mainActivity) databasesFragment.this.m15366r()).m73332w1();
                return true;
            }
        });
        SearchView searchView = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
        searchView.setIconified(true);
        searchView.mo1567d();
        this.f90267p4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        searchView.setIconifiedByDefault(false);
        searchView.setQueryHint("Search Databases");
        final String str = this.f90264m4;
        this.f90267p4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.8
            @Override // java.lang.Runnable
            public void run() {
                databasesFragment.this.f90263l4 = true;
                databasesFragment.this.f90267p4.m2508k0(str, false);
                if (databasesFragment.this.f90256e4 == null || databasesFragment.this.f90256e4.length() <= 0) {
                    return;
                }
                ArrayList<Bundle> arrayList = databasesFragment.f90252F4;
                if (arrayList == null || arrayList.size() == 0) {
                    databasesFragment.this.f90267p4.m2508k0(databasesFragment.this.f90256e4, true);
                } else {
                    databasesFragment.this.f90267p4.m2508k0(databasesFragment.this.f90256e4, false);
                    databasesFragment.this.f90258g4.getAdapter().m27491G();
                    databasesFragment.this.m73140J3();
                }
                databasesFragment.this.m73135B3();
                iMDLogger.m73550f("HideKeyboard", IcyHeaders.f28171a3);
            }
        }, 10L);
        this.f90263l4 = false;
        ImageView imageView = (ImageView) searchView.findViewById(C5562R.id.search_close_btn);
        ((TextView) searchView.findViewById(C5562R.id.search_src_text)).setText("");
        imageView.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.9
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                RecyclerView recyclerView;
                RecyclerView.Adapter adapter;
                databasesFragment.this.f90267p4.m2508k0("", false);
                databasesFragment.this.f90267p4.clearFocus();
                databasesFragment.this.m73149Q2();
                if (databasesFragment.this.f90261j4) {
                    recyclerView = databasesFragment.this.f90258g4;
                    adapter = databasesFragment.this.f90268q4;
                } else {
                    recyclerView = databasesFragment.this.f90258g4;
                    adapter = databasesFragment.this.f90259h4;
                }
                recyclerView.setAdapter(adapter);
                databasesFragment.this.m73135B3();
                iMDLogger.m73550f("HideKeyboard", ExifInterface.f16317Y4);
            }
        });
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.10
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(String str2) {
                RecyclerView recyclerView;
                RecyclerView.Adapter adapter;
                if (!databasesFragment.this.f90263l4) {
                    return true;
                }
                databasesFragment.this.f90264m4 = str2;
                databasesFragment.this.f90256e4 = str2;
                if (str2.length() == 0) {
                    databasesFragment.this.m73149Q2();
                    if (databasesFragment.this.f90261j4) {
                        recyclerView = databasesFragment.this.f90258g4;
                        adapter = databasesFragment.this.f90268q4;
                    } else {
                        recyclerView = databasesFragment.this.f90258g4;
                        adapter = databasesFragment.this.f90259h4;
                    }
                    recyclerView.setAdapter(adapter);
                } else {
                    databasesFragment.this.m73138H3(str2);
                }
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str2) {
                return false;
            }
        });
        try {
            m15366r().setTitle("");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, final Bundle bundle) {
        View view = this.f90257f4;
        if (view != null) {
            return view;
        }
        this.f90275x4 = Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf");
        this.f90274w4 = m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("ripple", true);
        this.f90276y4 = new CompressHelper(m15366r());
        this.f90277z4 = new Bundle();
        this.f90261j4 = m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("GridView", true);
        this.f90262k4 = new VBHelper(m15366r());
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_databases, viewGroup, false);
        this.f90257f4 = viewInflate;
        m73135B3();
        iMDLogger.m73550f("HideKeyboard", ExifInterface.f16326Z4);
        this.f90264m4 = "";
        this.f90276y4.m71807R0(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.3
            @Override // java.lang.Runnable
            public void run() {
                databasesFragment.f90251E4 = databasesFragment.this.f90276y4.m71880p2();
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.4
            @Override // java.lang.Runnable
            public void run() {
                FloatingActionButton floatingActionButton;
                Resources resources;
                int i2;
                databasesFragment.this.m73139I3();
                databasesFragment.this.m15358o2(true);
                Bundle bundle2 = bundle;
                if (bundle2 != null && bundle2.containsKey("Query")) {
                    databasesFragment.this.f90256e4 = bundle.getString("Query");
                }
                databasesFragment databasesfragment = databasesFragment.this;
                databasesfragment.f90258g4 = (RecyclerView) databasesfragment.f90257f4.findViewById(C5562R.id.recycler_view);
                databasesFragment.this.f90258g4.setVisibility(0);
                ((TextView) databasesFragment.this.f90257f4.findViewById(C5562R.id.loading_text)).setVisibility(8);
                databasesFragment databasesfragment2 = databasesFragment.this;
                databasesfragment2.f90271t4 = new CustomItemDecoration(databasesfragment2.m15366r());
                databasesFragment databasesfragment3 = databasesFragment.this;
                databasesfragment3.f90272u4 = new GridLayoutManager(databasesfragment3.m15366r(), 2);
                databasesFragment.this.f90272u4.m27031R3(new GridLayoutManager.SpanSizeLookup() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.4.1
                    @Override // androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
                    /* renamed from: f */
                    public int mo27056f(int i3) {
                        Bundle bundleM73147O2;
                        return (databasesFragment.this.f90258g4.getAdapter().getClass() == SearchCollectionDatabasesAdapter.class || (bundleM73147O2 = databasesFragment.this.m73147O2(i3, databasesFragment.f90251E4)) == null || bundleM73147O2.containsKey("Title")) ? 2 : 1;
                    }
                });
                if (databasesFragment.this.f90261j4) {
                    databasesFragment.this.f90258g4.m27380A1(databasesFragment.this.f90271t4);
                    databasesFragment databasesfragment4 = databasesFragment.this;
                    databasesfragment4.f90268q4 = databasesfragment4.new CollectionAdapter();
                    databasesFragment.this.f90258g4.setAdapter(databasesFragment.this.f90268q4);
                    databasesFragment.this.f90258g4.setLayoutManager(databasesFragment.this.f90272u4);
                } else {
                    databasesFragment.this.f90258g4.m27459p(databasesFragment.this.f90271t4);
                    databasesFragment.this.f90258g4.setLayoutManager(new LinearLayoutManager(databasesFragment.this.m15366r(), 1, false));
                    databasesFragment.this.f90258g4.setItemAnimator(new DefaultItemAnimator());
                    databasesFragment databasesfragment5 = databasesFragment.this;
                    databasesfragment5.f90259h4 = databasesfragment5.new DatabasesAdapter();
                    databasesFragment.this.f90258g4.setAdapter(databasesFragment.this.f90259h4);
                }
                databasesFragment.this.m73149Q2();
                databasesFragment databasesfragment6 = databasesFragment.this;
                databasesfragment6.f90273v4 = (FloatingActionButton) databasesfragment6.f90257f4.findViewById(C5562R.id.fab);
                if (databasesFragment.this.f90261j4) {
                    floatingActionButton = databasesFragment.this.f90273v4;
                    resources = databasesFragment.this.m15366r().getResources();
                    i2 = C5562R.drawable.listview_icon;
                } else {
                    floatingActionButton = databasesFragment.this.f90273v4;
                    resources = databasesFragment.this.m15366r().getResources();
                    i2 = C5562R.drawable.gridview_icon;
                }
                floatingActionButton.setImageDrawable(resources.getDrawable(i2));
                databasesFragment.this.f90273v4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.4.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        SharedPreferences.Editor editorPutBoolean;
                        databasesFragment.this.f90265n4.setVisible(true);
                        databasesFragment.this.f90266o4.setVisible(false);
                        if (databasesFragment.this.f90261j4) {
                            databasesFragment.this.f90258g4.setLayoutManager(new LinearLayoutManager(databasesFragment.this.m15366r()));
                            databasesFragment.this.f90258g4.m27459p(databasesFragment.this.f90271t4);
                            databasesFragment.this.f90261j4 = false;
                            databasesFragment databasesfragment7 = databasesFragment.this;
                            databasesfragment7.f90259h4 = databasesfragment7.new DatabasesAdapter();
                            databasesFragment.this.f90258g4.setAdapter(databasesFragment.this.f90259h4);
                            databasesFragment.this.f90273v4.setImageDrawable(databasesFragment.this.m15366r().getResources().getDrawable(C5562R.drawable.gridview_icon));
                            editorPutBoolean = databasesFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("GridView", false);
                        } else {
                            databasesFragment.this.f90258g4.setLayoutManager(databasesFragment.this.f90272u4);
                            databasesFragment.this.f90258g4.m27380A1(databasesFragment.this.f90271t4);
                            databasesFragment.this.f90261j4 = true;
                            databasesFragment databasesfragment8 = databasesFragment.this;
                            databasesfragment8.f90268q4 = databasesfragment8.new CollectionAdapter();
                            databasesFragment.this.f90258g4.setAdapter(databasesFragment.this.f90268q4);
                            databasesFragment.this.f90273v4.setImageDrawable(databasesFragment.this.m15366r().getResources().getDrawable(C5562R.drawable.listview_icon));
                            editorPutBoolean = databasesFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putBoolean("GridView", true);
                        }
                        editorPutBoolean.commit();
                        databasesFragment.this.m73149Q2();
                    }
                });
                if (databasesFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("swipedelete", true)) {
                    databasesFragment.this.m73133z3();
                }
                databasesFragment.this.m73131w3();
            }
        });
        return viewInflate;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: V0 */
    public void mo15306V0() {
        super.mo15306V0();
        LocalBroadcastManager.m16410b(m15366r()).m16415f(this.f90255C4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    /* renamed from: y3 */
    public void m73150y3(final Runnable runnable, final Runnable runnable2) {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.23
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    runnable.run();
                    observableEmitter.onNext("asdfadf");
                } catch (Exception unused) {
                }
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.24
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                try {
                    runnable2.run();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.databasesFragment.25
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                try {
                    iMDLogger.m73550f("Error occured", th.getMessage());
                } catch (Exception unused) {
                }
            }
        });
    }
}

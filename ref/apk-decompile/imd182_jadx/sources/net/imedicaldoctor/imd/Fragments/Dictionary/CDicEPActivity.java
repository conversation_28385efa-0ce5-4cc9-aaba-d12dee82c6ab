package net.imedicaldoctor.imd.Fragments.Dictionary;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class CDicEPActivity extends ViewerHelperActivity {

    public static class CDicEPFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        public String f87875X4;

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: I4 */
        public String m72075I4(String str) {
            return str.replace(StringUtils.f103471LF, "<br>");
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: O4 */
        public String m72081O4(int i2, String str, String str2) {
            return "<a name=\"f" + i2 + "\"><div id=\"h" + i2 + "\" class=\"headerExpanded\" onclick=\"collapse(f" + i2 + ");toggleHeaderExpanded(h" + i2 + ");\"><span class=\"fieldname\" style=\"font-family:X Traffic;\">" + str + "</span></div></a><div class=\"content\"  DIR=\"RTL\" id=\"f" + i2 + "\" style=\"font-family:X Traffic;\">" + str2 + "</div>";
        }

        /* renamed from: P4 */
        private String m72082P4(String str, String str2) {
            return "<div class=\"content\"  DIR=\"" + str2 + "\" style=\"font-family:\"X Traffic\";\">" + str + "</div>";
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: Q4 */
        public String m72083Q4(int i2, String str, String str2) {
            return "<a name=\"f" + i2 + "\"><div id=\"h" + i2 + "\" class=\"headerExpanded\" onclick=\"collapse(f" + i2 + ");toggleHeaderExpanded(h" + i2 + ");\"><span class=\"fieldname\" style=\"font-family:X Traffic;\">" + str + "</span></div></a><div class=\"content\"  DIR=\"LTR\" id=\"f" + i2 + "\">" + str2 + "</div>";
        }

        /* renamed from: R4 */
        private String m72084R4(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8) {
            return "<a name=\"f" + str8 + "\"><div id=\"h" + str8 + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + str8 + ");toggleHeaderExpanded(h" + str8 + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + str8 + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: S4 */
        public String m72085S4(Bundle bundle) {
            return "<div class=\"content\"  DIR=\"LTR\" >" + new CompressHelper(m15366r()).m71773B(bundle.getString("TLine"), bundle.getString("id"), "127") + " <a href=\"Sample:" + bundle.getString("id") + "\"><img src=\"file:///android_asset/VideoPlayer_Play.png\" height=10 width=10></a></div>";
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: T4 */
        public String m72086T4(int i2, String str, String str2) {
            return "<a name=\"f" + i2 + "\"><div id=\"h" + i2 + "\" class=\"headerExpanded\" onclick=\"collapse(f" + i2 + ");toggleHeaderExpanded(h" + i2 + ");\"><span class=\"fieldname\" style=\"font-family:X Traffic;\">" + str + "</span></div></a><div class=\"content\"  DIR=\"LTR\" id=\"f" + i2 + "\">" + str2 + "</div>";
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_cdic_e, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f87875X4 = bundle.getString("mSoundNumber");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicEPActivity.CDicEPFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    String str;
                    String str2;
                    String string;
                    Iterator<Bundle> it2;
                    String str3 = "";
                    String str4 = "Phonetic";
                    try {
                        CompressHelper compressHelper = new CompressHelper(CDicEPFragment.this.m15366r());
                        String str5 = CDicEPFragment.this.f89563A4;
                        if (str5 == null || str5.length() == 0) {
                            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(CDicEPFragment.this.f89567E4.split("-")[1], ",,,,,");
                            String str6 = strArrSplitByWholeSeparator[0];
                            String str7 = strArrSplitByWholeSeparator[1];
                            CDicEPFragment cDicEPFragment = CDicEPFragment.this;
                            cDicEPFragment.f89568F4 = str7;
                            ArrayList<Bundle> arrayListM71819W = compressHelper.m71819W(cDicEPFragment.f89566D4, "Select english.id as id,Word,Mean,Grammer, Phonetic, USVoice, UKVoice, TCode, type, Type.id as typeId  from English Inner Join Type On English.TCode = Type.ID where english.id in (" + str6 + ")", "HData.db");
                            ArrayList<Bundle> arrayListM71819W2 = compressHelper.m71819W(CDicEPFragment.this.f89566D4, "Select * from Sample where word = '" + str7 + "'", "HData.db");
                            ArrayList<Bundle> arrayListM71819W3 = compressHelper.m71819W(CDicEPFragment.this.f89566D4, "Select * from Verb where verb = '" + str7 + "'", "HData.db");
                            Iterator<Bundle> it3 = arrayListM71819W.iterator();
                            String string2 = null;
                            String string3 = null;
                            String string4 = null;
                            while (it3.hasNext()) {
                                Bundle next = it3.next();
                                if (next.getString("Grammer").length() > 0) {
                                    string3 = next.getString("Grammer");
                                    string4 = next.getString("id");
                                }
                                if (next.getString(str4).length() > 0) {
                                    next.getString(str4);
                                    next.getString("id");
                                }
                                String str8 = str4;
                                if (next.getString("USVoice").equals(IcyHeaders.f28171a3)) {
                                    string2 = next.getString("id");
                                }
                                str4 = str8;
                            }
                            CDicEPFragment cDicEPFragment2 = CDicEPFragment.this;
                            cDicEPFragment2.f87875X4 = string2;
                            String strM72817d4 = cDicEPFragment2.m72817d4(cDicEPFragment2.m15366r(), "EPHeader.css");
                            CDicEPFragment cDicEPFragment3 = CDicEPFragment.this;
                            String strM72817d42 = cDicEPFragment3.m72817d4(cDicEPFragment3.m15366r(), "EPFooter.css");
                            String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", CDicEPFragment.this.f89568F4);
                            String string5 = "</head><body><a name=\"f0\"><div class=\"title\">" + str7 + "</div></a>";
                            ArrayList arrayList = new ArrayList();
                            ArrayList arrayList2 = new ArrayList();
                            Iterator<Bundle> it4 = arrayListM71819W.iterator();
                            while (it4.hasNext()) {
                                Bundle next2 = it4.next();
                                String str9 = str3;
                                String str10 = string5;
                                String strM71773B = compressHelper.m71773B(next2.getString("type"), next2.getString("typeId"), "127");
                                String strM71773B2 = compressHelper.m71773B(next2.getString("Mean"), next2.getString("id"), "127");
                                if (arrayList.contains(strM71773B)) {
                                    int iIndexOf = arrayList.indexOf(strM71773B);
                                    StringBuilder sb = new StringBuilder();
                                    it2 = it4;
                                    sb.append((String) arrayList2.get(iIndexOf));
                                    sb.append("<br>");
                                    sb.append(strM71773B2);
                                    strM71773B2 = sb.toString();
                                    arrayList2.remove(iIndexOf);
                                    arrayList.remove(iIndexOf);
                                } else {
                                    it2 = it4;
                                }
                                arrayList.add(strM71773B);
                                arrayList2.add(strM71773B2);
                                str3 = str9;
                                string5 = str10;
                                it4 = it2;
                            }
                            String str11 = str3;
                            int i2 = 0;
                            int i3 = 0;
                            while (i2 < arrayList.size()) {
                                String str12 = strM72817d42;
                                if (((String) arrayList.get(i2)).equals("معنی عمومی")) {
                                    StringBuilder sb2 = new StringBuilder();
                                    sb2.append(string5);
                                    str = strReplace;
                                    str2 = string4;
                                    sb2.append(CDicEPFragment.this.m72081O4(i3, (String) arrayList.get(i2), CDicEPFragment.this.m72075I4((String) arrayList2.get(i2))));
                                    string = sb2.toString();
                                } else {
                                    str = strReplace;
                                    str2 = string4;
                                    string = string5 + CDicEPFragment.this.m72081O4(i3, (String) arrayList.get(i2), (String) arrayList2.get(i2));
                                }
                                string5 = string;
                                i3++;
                                i2++;
                                strM72817d42 = str12;
                                strReplace = str;
                                string4 = str2;
                            }
                            String str13 = strReplace;
                            String str14 = strM72817d42;
                            String str15 = string4;
                            if (arrayListM71819W2 != null && arrayListM71819W2.size() > 0) {
                                Iterator<Bundle> it5 = arrayListM71819W2.iterator();
                                String str16 = str11;
                                while (it5.hasNext()) {
                                    str16 = str16 + CDicEPFragment.this.m72085S4(it5.next());
                                }
                                string5 = string5 + CDicEPFragment.this.m72083Q4(122, "Samples", str16);
                            }
                            if (arrayListM71819W3 != null && arrayListM71819W3.size() > 0) {
                                Bundle bundle2 = arrayListM71819W3.get(0);
                                StringBuilder sb3 = new StringBuilder();
                                sb3.append(string5);
                                CDicEPFragment cDicEPFragment4 = CDicEPFragment.this;
                                sb3.append(cDicEPFragment4.m72086T4(123, "Verbs", cDicEPFragment4.m72075I4(compressHelper.m71773B(bundle2.getString("forms"), bundle2.getString("id"), "127"))));
                                string5 = sb3.toString();
                            }
                            if (string3 != null) {
                                string5 = string5 + CDicEPFragment.this.m72083Q4(222, "گرامر", compressHelper.m71773B(string3, str15, "127"));
                            }
                            CDicEPFragment.this.m72826m3();
                            CDicEPFragment.this.f89563A4 = str13 + string5 + str14;
                        }
                        if (compressHelper.m71903x1()) {
                            return;
                        }
                        CDicEPFragment cDicEPFragment5 = CDicEPFragment.this;
                        cDicEPFragment5.m72827m4(cDicEPFragment5.f89568F4);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        CDicEPFragment.this.f89595p4 = e2.getLocalizedMessage();
                    }
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicEPActivity.CDicEPFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    String str = CDicEPFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        CDicEPFragment cDicEPFragment = CDicEPFragment.this;
                        cDicEPFragment.m72780C4(cDicEPFragment.f89595p4);
                        return;
                    }
                    String strM71753g1 = CompressHelper.m71753g1(CDicEPFragment.this.f89566D4, "base");
                    CDicEPFragment cDicEPFragment2 = CDicEPFragment.this;
                    cDicEPFragment2.m72795O3(cDicEPFragment2.f89563A4, strM71753g1);
                    CDicEPFragment.this.m72836s4();
                    CDicEPFragment.this.m72831p4();
                    CDicEPFragment.this.mo72642f3(C5562R.menu.menu_cdic_e);
                    CDicEPFragment.this.m15358o2(false);
                    CDicEPFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) throws IllegalStateException {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_sound_us || itemId == C5562R.id.action_sound_uk) {
                new CompressHelper(m15366r());
                String str = itemId == C5562R.id.action_sound_us ? "US" : "UK";
                String str2 = str + "/" + this.f87875X4 + ".mp3";
                new File(CompressHelper.m71754h1(this.f89566D4, str + this.f87875X4 + ".mp3", "base"));
                String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, str + ".zip");
                String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, str + this.f87875X4 + ".mp3", "base");
                StringBuilder sb = new StringBuilder();
                sb.append(this.f87875X4);
                sb.append(".mp3");
                m72815c4(strM71753g1, str2, strM71754h1, sb.toString());
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            if (this.f87875X4 == null) {
                menu.removeItem(C5562R.id.action_sound_uk);
                menu.removeItem(C5562R.id.action_sound_us);
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws IllegalStateException {
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            new CompressHelper(m15366r());
            if (!str2.equals("sample")) {
                return true;
            }
            String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "Samples.zip");
            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, "sample" + str3 + ".mp3", "base");
            StringBuilder sb = new StringBuilder();
            sb.append(str3);
            sb.append(".mp3");
            m72815c4(strM71753g1, "Samples/" + str3 + ".mp3", strM71754h1, sb.toString());
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new CDicEPFragment(), bundle);
    }
}

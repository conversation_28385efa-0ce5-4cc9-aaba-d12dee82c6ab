package net.imedicaldoctor.imd.Fragments.Uptodate;

import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.exifinterface.media.ExifInterface;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import com.itextpdf.tool.xml.html.HTML;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersDecoration;
import java.io.File;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.ViewHolders.ChaptersSectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.UTDSearchAdapter;
import net.imedicaldoctor.imd.iMDActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class UTDSearchActivity extends iMDActivity {

    public static class UTDSearchFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        public AsyncTask f89502A4;

        /* renamed from: B4 */
        public TabLayout f89503B4;

        /* renamed from: C4 */
        public UTDSearchAdapter f89504C4;

        /* renamed from: D4 */
        private StickyRecyclerHeadersDecoration f89505D4;

        /* renamed from: n3 */
        private boolean m72727n3() {
            return new File(CompressHelper.m71753g1(this.f88788h4, "unidex.en.sqlite")).exists();
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72464R2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            ArrayList<Bundle> arrayListM71819W;
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_utdsearch, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72464R2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            this.f89503B4 = (TabLayout) this.f88797q4.findViewById(C5562R.id.tabs);
            if (m72727n3()) {
                this.f89503B4.setVisibility(0);
            } else {
                this.f89503B4.setVisibility(8);
            }
            this.f89503B4.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.1
                @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
                /* renamed from: a */
                public void mo40255a(TabLayout.Tab tab) {
                }

                @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
                /* renamed from: b */
                public void mo40256b(TabLayout.Tab tab) {
                    if (UTDSearchFragment.this.f88799s4.getQuery().length() > 0) {
                        SearchView searchView = UTDSearchFragment.this.f88799s4;
                        searchView.m2508k0(searchView.getQuery(), true);
                    }
                }

                @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
                /* renamed from: c */
                public void mo40257c(TabLayout.Tab tab) {
                }
            });
            String[] strArr = {"All", "Adult", "Pediatric", "Patient"};
            for (int i2 = 0; i2 < 4; i2++) {
                TabLayout.Tab tabM40228I = this.f89503B4.m40228I();
                tabM40228I.m40276D(strArr[i2]);
                this.f89503B4.m40248i(tabM40228I);
            }
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            if (m15387y().containsKey("Parent")) {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.2
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
                String string = m15387y().getString("Parent");
                arrayListM71819W = string.equals("99999") ? this.f88791k4.m71819W(this.f88788h4, "SELECT Text as title,URL as id,1 as leaf ,'Video Index' as section  FROM search where search.'table' match 'movie'", "fsearch.db") : this.f88791k4.m71819W(this.f88788h4, "SELECT * FROM toc where parentId=" + string, "utdtoc.db");
            } else {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
                arrayListM71819W = this.f88791k4.m71819W(this.f88788h4, "select 99999 as id, 'Video Index' as title, 0 as parentId, 0 as leaf,'' as section union SELECT * FROM toc where parentId=0", "utdtoc.db");
            }
            this.f88794n4 = arrayListM71819W;
            ArrayList<Bundle> arrayList = new ArrayList<>();
            ((TextView) this.f88797q4.findViewById(C5562R.id.toolbar_subtext_view)).setText(m72731m3());
            if (this.f88794n4 == null) {
                this.f88794n4 = new ArrayList<>();
                new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("The database is corrupt as the result of low disk space, corrupted download or cleaner apps. please delete uptodate database and download it again from the downloads page").mo1106p("Delete", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.4
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i3) {
                        new AlertDialog.Builder(UTDSearchFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you sure ?").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.4.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface2, int i4) {
                                UTDSearchFragment.this.m72728i3(new File(UTDSearchFragment.this.f88788h4.getString("Path")));
                                LocalBroadcastManager.m16410b(UTDSearchFragment.this.m15366r()).m16413d(new Intent("reload"));
                                UTDSearchFragment.this.f88791k4.m71830Z1(false);
                                UTDSearchFragment.this.f88791k4.m71830Z1(true);
                            }
                        }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.4.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface2, int i4) {
                            }
                        }).m1090I();
                    }
                }).mo1109s("More Info", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.3
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i3) {
                        UTDSearchFragment.this.m72732o3("http://imedicaldoctor.net/faq#null");
                        UTDSearchFragment.this.f88791k4.m71821W1(false);
                    }
                }).m1090I();
            }
            Iterator<Bundle> it2 = this.f88794n4.iterator();
            while (it2.hasNext()) {
                Bundle next = it2.next();
                if (next.getString(HTML.Tag.f74369V).equals("")) {
                    next.remove(HTML.Tag.f74369V);
                    next.putString(HTML.Tag.f74369V, "Table of Contents");
                }
                arrayList.add(next);
            }
            this.f88791k4.m71887r2(arrayList, HTML.Tag.f74369V);
            this.f88792l4 = new ChaptersSectionAdapter(m15366r(), this.f88794n4, "title", HTML.Tag.f74369V) { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.5
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersSectionAdapter
                /* renamed from: f0 */
                public void mo72538f0(Bundle bundle2, int i3) {
                    CompressHelper compressHelper;
                    Bundle bundle3;
                    StringBuilder sb;
                    String str;
                    UTDSearchFragment.this.m72468V2();
                    if (!bundle2.getString("leaf").equals(IcyHeaders.f28171a3)) {
                        Bundle bundle4 = new Bundle();
                        bundle4.putBundle("DB", UTDSearchFragment.this.f88788h4);
                        bundle4.putString("Parent", bundle2.getString("id"));
                        UTDSearchFragment.this.f88791k4.m71798N(UTDSearchActivity.class, UTDSearchFragment.class, bundle4);
                        return;
                    }
                    if (bundle2.getString("id").contains("Graphic-")) {
                        new CompressHelper(UTDSearchFragment.this.m15366r()).m71772A1(UTDSearchFragment.this.f88788h4, bundle2.getString("id"), null, null);
                        return;
                    }
                    String string2 = bundle2.getString("title");
                    UTDSearchFragment uTDSearchFragment = UTDSearchFragment.this;
                    CompressHelper compressHelper2 = uTDSearchFragment.f88791k4;
                    Bundle bundleM71890s1 = compressHelper2.m71890s1(compressHelper2.m71819W(uTDSearchFragment.f88788h4, "Select * from tocmap where tocId=" + bundle2.getString("id"), "utdtoc.db"));
                    if (bundleM71890s1 != null) {
                        compressHelper = new CompressHelper(UTDSearchFragment.this.m15366r());
                        bundle3 = UTDSearchFragment.this.f88788h4;
                        sb = new StringBuilder();
                        sb.append("Topic-");
                        str = "topicId";
                    } else {
                        UTDSearchFragment uTDSearchFragment2 = UTDSearchFragment.this;
                        CompressHelper compressHelper3 = uTDSearchFragment2.f88791k4;
                        bundleM71890s1 = compressHelper3.m71890s1(compressHelper3.m71819W(uTDSearchFragment2.f88788h4, "Select * from topic where title='" + string2.replace("'", "''") + "'", "unidex.en.sqlite"));
                        if (bundleM71890s1 == null) {
                            CompressHelper.m71767x2(UTDSearchFragment.this.m15366r(), "Sorry, Can't find it. use search to find similar topics", 1);
                            return;
                        }
                        compressHelper = new CompressHelper(UTDSearchFragment.this.m15366r());
                        bundle3 = UTDSearchFragment.this.f88788h4;
                        sb = new StringBuilder();
                        sb.append("Topic-");
                        str = "topic_id";
                    }
                    sb.append(bundleM71890s1.getString(str));
                    compressHelper.m71772A1(bundle3, sb.toString(), null, null);
                }
            };
            this.f89504C4 = new UTDSearchAdapter(m15366r(), this.f88795o4, "title", null) { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.6
                @Override // net.imedicaldoctor.imd.ViewHolders.UTDSearchAdapter
                /* renamed from: d0 */
                public void mo72733d0(Bundle bundle2, int i3) {
                    UTDSearchFragment.this.m72468V2();
                    String string2 = bundle2.getString("_id");
                    if (!string2.contains("-")) {
                        string2 = "Topic-" + string2;
                    }
                    new CompressHelper(UTDSearchFragment.this.m15366r()).m71772A1(UTDSearchFragment.this.f88788h4, string2, null, null);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            StickyRecyclerHeadersDecoration stickyRecyclerHeadersDecoration = new StickyRecyclerHeadersDecoration((StickyRecyclerHeadersAdapter) this.f88792l4);
            this.f89505D4 = stickyRecyclerHeadersDecoration;
            this.f88803w4.m27459p(stickyRecyclerHeadersDecoration);
            this.f88803w4.setLayoutManager(new LinearLayoutManager(m15366r()));
            this.f88803w4.setItemAnimator(new DefaultItemAnimator());
            this.f88803w4.m27459p(new CustomItemDecoration(m15366r()));
            this.f88792l4.m27502Z(new RecyclerView.AdapterDataObserver() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.7
                @Override // androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
                /* renamed from: a */
                public void mo27287a() {
                    UTDSearchFragment.this.f89505D4.m58207n();
                }
            });
            m15358o2(true);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: X2 */
        public void mo71973X2() {
            this.f89504C4.m73486e0(this.f88795o4);
            this.f88803w4.setAdapter(this.f89504C4);
            this.f89505D4.m58207n();
            this.f88803w4.m27380A1(this.f89505D4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: Z2 */
        public void mo72211Z2() {
            this.f88803w4.m27459p(this.f89505D4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) throws NumberFormatException {
            String strReplace = str.replace("'", "''");
            int i2 = 0;
            if (!m72727n3()) {
                String[] strArrSplit = strReplace.split(StringUtils.SPACE);
                String str2 = "";
                while (i2 < strArrSplit.length) {
                    if (str2.length() == 0) {
                        str2 = "'" + strArrSplit[i2] + "'";
                    } else {
                        str2 = str2 + " , '" + strArrSplit[i2] + "'";
                    }
                    i2++;
                }
                return this.f88791k4.m71819W(this.f88788h4, "select topic.topic_id _id, topic.title, sum( prof  ) * count( * ) * min(count(*),2)  weight , count(*) c from kw2topic_xref, topic, kw where kw.pk =kw2topic_xref.kw_fk and topic.topic_id =kw2topic_xref.topic_id and kw in (" + str2 + ") group by topic.topic_id, topic.title order by weight desc, c desc, topic.title asc limit 50", "utdkw.sqlite");
            }
            ArrayList<Bundle> arrayListM71819W = this.f88791k4.m71819W(this.f88788h4, "SELECT q.qbtype, x.topic_hits hits FROM query q, query_topic x WHERE q.disp = '" + strReplace + "'  AND x.nqid = q.nqid AND x.pref = '" + m72730l3() + "'", "unidex.en.sqlite");
            if (arrayListM71819W == null || arrayListM71819W.size() == 0) {
                ArrayList<Bundle> arrayListM71819W2 = this.f88791k4.m71819W(this.f88788h4, "select Text as title, URL as _id from search where search match '" + strReplace + "'  ORDER BY rank(matchinfo(search)) DESC limit 20", "fcontentsearch.db");
                if (arrayListM71819W2 != null && arrayListM71819W2.size() != 0) {
                    return arrayListM71819W2;
                }
                return this.f88791k4.m71819W(this.f88788h4, "select Text as title, URL as _id from search where search match '" + strReplace + "'  ORDER BY rank(matchinfo(search)) DESC limit 20", "fsearch.db");
            }
            String strM73444f = new VBHelper(m15366r()).m73444f(arrayListM71819W.get(0).getByteArray("hits"));
            ArrayList arrayList = new ArrayList();
            ArrayList arrayList2 = new ArrayList();
            int i3 = 4;
            while (i3 < strM73444f.length()) {
                int i4 = i3 + 8;
                int i5 = Integer.parseInt(strM73444f.substring(i3, i4), 16);
                arrayList.add(String.valueOf(i5));
                arrayList2.add("WHEN " + i5 + " THEN " + i2);
                i2++;
                i3 = i4;
            }
            String str3 = "order by case topic_id " + TextUtils.join(StringUtils.SPACE, arrayList2) + " end";
            return this.f88791k4.m71819W(this.f88788h4, "SELECT topic_id as _id, title FROM topic WHERE topic_id IN (" + TextUtils.join(",", arrayList) + ") " + str3, "unidex.en.sqlite");
        }

        /* JADX WARN: Removed duplicated region for block: B:16:0x006b  */
        /* JADX WARN: Removed duplicated region for block: B:17:0x007a  */
        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public java.util.ArrayList<android.os.Bundle> mo71951g3(java.lang.String r4) {
            /*
                r3 = this;
                int r0 = r4.length()
                r1 = 1
                java.lang.String r2 = "'"
                if (r0 != r1) goto L1e
                java.lang.StringBuilder r0 = new java.lang.StringBuilder
                r0.<init>()
                java.lang.String r1 = "q1='"
            L10:
                r0.append(r1)
                r0.append(r4)
                r0.append(r2)
            L19:
                java.lang.String r0 = r0.toString()
                goto L4f
            L1e:
                int r0 = r4.length()
                r1 = 2
                if (r0 != r1) goto L2d
                java.lang.StringBuilder r0 = new java.lang.StringBuilder
                r0.<init>()
                java.lang.String r1 = "q2='"
                goto L10
            L2d:
                int r0 = r4.length()
                r1 = 3
                if (r0 != r1) goto L3c
                java.lang.StringBuilder r0 = new java.lang.StringBuilder
                r0.<init>()
                java.lang.String r1 = "q3='"
                goto L10
            L3c:
                java.lang.StringBuilder r0 = new java.lang.StringBuilder
                r0.<init>()
                java.lang.String r1 = "q like '"
                r0.append(r1)
                r0.append(r4)
                java.lang.String r1 = "%'"
                r0.append(r1)
                goto L19
            L4f:
                java.lang.StringBuilder r1 = new java.lang.StringBuilder
                r1.<init>()
                java.lang.String r2 = "select q _id, u as word, case c when 0 then '' else 'c' || c || ' ' end || case c2 when 0 then '' else ' c_' || c2 || ' ' end u2, p, f from qf where "
                r1.append(r2)
                r1.append(r0)
                java.lang.String r0 = " order by f desc, q asc limit 30"
                r1.append(r0)
                java.lang.String r0 = r1.toString()
                boolean r1 = r3.m72727n3()
                if (r1 == 0) goto L7a
                java.lang.String r4 = r3.m72729j3(r4)
                net.imedicaldoctor.imd.Data.CompressHelper r0 = r3.f88791k4
                android.os.Bundle r1 = r3.f88788h4
                java.lang.String r2 = "unidex.en.sqlite"
                java.util.ArrayList r4 = r0.m71819W(r1, r4, r2)
                goto L84
            L7a:
                net.imedicaldoctor.imd.Data.CompressHelper r4 = r3.f88791k4
                android.os.Bundle r1 = r3.f88788h4
                java.lang.String r2 = "utdqf.sqlite"
                java.util.ArrayList r4 = r4.m71819W(r1, r0, r2)
            L84:
                return r4
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity.UTDSearchFragment.mo71951g3(java.lang.String):java.util.ArrayList");
        }

        /* renamed from: i3 */
        public void m72728i3(File file) {
            if (file.isDirectory()) {
                for (File file2 : file.listFiles()) {
                    m72728i3(file2);
                }
            }
            file.delete();
        }

        /* renamed from: j3 */
        public String m72729j3(String str) {
            StringBuilder sb;
            String str2;
            String strReplace = str.replace("'", "''");
            if (strReplace.length() == 1) {
                sb = new StringBuilder();
                str2 = "d1 = '";
            } else if (strReplace.length() == 2) {
                sb = new StringBuilder();
                str2 = "d2 = '";
            } else {
                if (strReplace.length() != 3) {
                    sb = new StringBuilder();
                    sb.append("disp like '");
                    sb.append(strReplace);
                    sb.append("%'");
                    String str3 = "SELECT rowid _id,disp as word, IFNULL(engl, disp) AS useq, weight, qbtype, trprov FROM query WHERE hide IS NULL AND " + sb.toString() + " ORDER BY weight DESC, disp ASC LIMIT 30";
                    iMDLogger.m73550f("Query:", str3);
                    return str3;
                }
                sb = new StringBuilder();
                str2 = "d3 = '";
            }
            sb.append(str2);
            sb.append(strReplace);
            sb.append("'");
            String str32 = "SELECT rowid _id,disp as word, IFNULL(engl, disp) AS useq, weight, qbtype, trprov FROM query WHERE hide IS NULL AND " + sb.toString() + " ORDER BY weight DESC, disp ASC LIMIT 30";
            iMDLogger.m73550f("Query:", str32);
            return str32;
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: l1 */
        public void mo15352l1() {
            super.mo15352l1();
            m72468V2();
        }

        /* renamed from: l3 */
        public String m72730l3() {
            int selectedTabPosition = this.f89503B4.getSelectedTabPosition();
            return selectedTabPosition == 0 ? "X" : selectedTabPosition == 1 ? ExifInterface.f16299W4 : selectedTabPosition == 2 ? "P" : selectedTabPosition == 3 ? "I" : "X";
        }

        /* renamed from: m3 */
        public String m72731m3() {
            return new SimpleDateFormat("d MMM yyyy").format(new SimpleDateFormat("yyyyMMdd").parse(this.f88788h4.getString("Version"), new ParsePosition(0)));
        }

        /* renamed from: o3 */
        public void m72732o3(String str) {
            mo15256D2(new Intent("android.intent.action.VIEW", Uri.parse(str)));
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_utdsearch);
        if (bundle == null) {
            UTDSearchFragment uTDSearchFragment = new UTDSearchFragment();
            uTDSearchFragment.m15342i2(getIntent().getExtras());
            m15416k0().m15664u().m15818f(C5562R.id.container, uTDSearchFragment).mo15164r();
        }
    }
}

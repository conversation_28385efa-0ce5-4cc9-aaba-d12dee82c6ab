package net.imedicaldoctor.imd.Fragments.UptodateDDX;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class UTDDSearchActivity extends iMDActivity {

    public static class UTDDSearchFragment extends SearchHelperFragment {
        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72462O2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            m72462O2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            ((RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout)).setVisibility(0);
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select id as _id,* from diagnoses where isMain=1 order by diagnosisName collate nocase asc");
            this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "diagnosisName") { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDSearchActivity.UTDDSearchFragment.1
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: f0 */
                public void mo71975f0(Bundle bundle2, int i2) {
                    UTDDSearchFragment.this.m72468V2();
                    UTDDSearchFragment uTDDSearchFragment = UTDDSearchFragment.this;
                    uTDDSearchFragment.f88791k4.m71772A1(uTDDSearchFragment.f88788h4, bundle2.getString("id"), null, null);
                }
            };
            this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "diagnosis", null) { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDSearchActivity.UTDDSearchFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
                /* renamed from: e0 */
                public void mo71953e0(Bundle bundle2, int i2) {
                    UTDDSearchFragment.this.m72468V2();
                    UTDDSearchFragment uTDDSearchFragment = UTDDSearchFragment.this;
                    uTDDSearchFragment.f88791k4.m71772A1(uTDDSearchFragment.f88788h4, bundle2.getString("id"), null, null);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(true);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select id as _id,* from search where diagnosisSearch match '" + str + "*' order by isMain desc");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
        }
    }

    /* renamed from: b1 */
    public void m72753b1() {
        try {
            ((InputMethodManager) getSystemService("input_method")).hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        } catch (Exception unused) {
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new UTDDSearchFragment());
    }
}

package net.imedicaldoctor.imd.Fragments.LexiInteract;

import android.os.Bundle;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class LXIVInteractResult extends iMDActivity {

    public static class LXIvInteractResultFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private ArrayList<Bundle> f88365A4;

        /* renamed from: B4 */
        private ArrayList<Bundle> f88366B4;

        /* renamed from: C4 */
        private ArrayList<Bundle> f88367C4;

        /* renamed from: D4 */
        private ArrayList<Bundle> f88368D4;

        /* renamed from: E4 */
        public NotStickySectionAdapter f88369E4;

        /* renamed from: F4 */
        private String f88370F4;

        public class HeaderViewHolder {

            /* renamed from: a */
            public final TextView f88376a;

            public HeaderViewHolder(View view) {
                this.f88376a = (TextView) view.findViewById(C5562R.id.header_text);
            }
        }

        public class InteractionViewHolder {

            /* renamed from: a */
            public final TextView f88378a;

            /* renamed from: b */
            public final ImageView f88379b;

            /* renamed from: c */
            public final TextView f88380c;

            public InteractionViewHolder(View view) {
                this.f88380c = (TextView) view.findViewById(C5562R.id.drug_two_text);
                this.f88378a = (TextView) view.findViewById(C5562R.id.drug_one_text);
                this.f88379b = (ImageView) view.findViewById(C5562R.id.image);
            }
        }

        /* renamed from: m3 */
        private void m72326m3() {
            ArrayList<Bundle> arrayList = this.f88365A4;
            if (arrayList == null || arrayList.size() == 0) {
                mo72473f3("No Information found");
            } else {
                mo72472e3();
            }
            m15366r().setTitle("Founded " + this.f88366B4.size() + " Interactions");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        }

        /* JADX WARN: Removed duplicated region for block: B:19:0x00ff  */
        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public android.view.View mo15303U0(android.view.LayoutInflater r9, android.view.ViewGroup r10, android.os.Bundle r11) throws android.content.res.Resources.NotFoundException {
            /*
                Method dump skipped, instructions count: 409
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIVInteractResult.LXIvInteractResultFragment.mo15303U0(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
        }

        /* renamed from: i3 */
        public Bundle m72327i3(int i2, ArrayList<Bundle> arrayList) {
            Iterator<Bundle> it2 = arrayList.iterator();
            int i3 = 0;
            while (it2.hasNext()) {
                Bundle next = it2.next();
                if (i2 == i3) {
                    Bundle bundle = new Bundle();
                    bundle.putString("Title", next.getString("title"));
                    return bundle;
                }
                int size = i3 + next.getParcelableArrayList("items").size();
                if (i2 <= size) {
                    int size2 = (i2 - (size - next.getParcelableArrayList("items").size())) - 1;
                    Bundle bundle2 = new Bundle();
                    bundle2.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                    return bundle2;
                }
                i3 = size + 1;
            }
            return null;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        /* renamed from: n3 */
        public int m72328n3(ArrayList<Bundle> arrayList) {
            int size = 0;
            if (arrayList == null) {
                return 0;
            }
            Iterator<Bundle> it2 = arrayList.iterator();
            while (it2.hasNext()) {
                size = size + it2.next().getParcelableArrayList("items").size() + 1;
            }
            return size;
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new LXIvInteractResultFragment());
    }
}

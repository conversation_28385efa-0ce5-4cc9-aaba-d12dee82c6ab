package net.imedicaldoctor.imd.Fragments.UWorld;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;

/* loaded from: classes3.dex */
public class CheckDialog extends DialogFragment {

    /* renamed from: F4 */
    private ArrayList<Integer> f89156F4;

    /* renamed from: G4 */
    private RecyclerView f89157G4;

    /* renamed from: H4 */
    private TreeViewAdapter f89158H4;

    /* renamed from: I4 */
    private ArrayList<TreeItem> f89159I4;

    /* renamed from: J4 */
    private ArrayList<TreeItem> f89160J4;

    /* renamed from: K4 */
    private Map<String, Boolean> f89161K4;

    /* renamed from: j3 */
    private ArrayList<TreeItem> m72547j3(ArrayList<Bundle> arrayList, String str, Set<Integer> set) {
        HashMap map = new HashMap();
        ArrayList<TreeItem> arrayList2 = new ArrayList<>();
        for (int i2 = 0; i2 < arrayList.size(); i2++) {
            String string = arrayList.get(i2).getString(str);
            if (string.contains("(")) {
                String strTrim = string.substring(0, string.lastIndexOf(40)).trim();
                String strSubstring = string.substring(string.lastIndexOf(40) + 1, string.lastIndexOf(41));
                TreeItem treeItem = (TreeItem) map.get(strTrim);
                if (treeItem == null) {
                    treeItem = new TreeItem(strTrim, true, -1, false);
                    map.put(strTrim, treeItem);
                    arrayList2.add(treeItem);
                }
                TreeItem treeItem2 = new TreeItem(strSubstring, false, i2, true);
                if (set.contains(Integer.valueOf(i2))) {
                    treeItem2.f89171d = true;
                    treeItem.f89171d = true;
                }
                treeItem.f89175h.add(treeItem2);
            } else {
                TreeItem treeItem3 = new TreeItem(string, false, i2, false);
                if (set.contains(Integer.valueOf(i2))) {
                    treeItem3.f89171d = true;
                }
                arrayList2.add(treeItem3);
            }
        }
        if (!arrayList2.isEmpty() && arrayList2.get(0).f89168a.startsWith("All")) {
            TreeItem treeItem4 = arrayList2.get(0);
            if (set.isEmpty()) {
                treeItem4.f89171d = true;
                set.add(Integer.valueOf(treeItem4.f89174g));
            } else {
                treeItem4.f89171d = set.contains(Integer.valueOf(treeItem4.f89174g));
            }
        }
        return arrayList2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: k3 */
    public void m72548k3(String str) {
        this.f89159I4.clear();
        if (TextUtils.isEmpty(str)) {
            this.f89159I4.addAll(this.f89160J4);
        } else {
            Iterator<TreeItem> it2 = this.f89160J4.iterator();
            while (it2.hasNext()) {
                TreeItem next = it2.next();
                if (!next.f89168a.toLowerCase().contains(str.toLowerCase())) {
                    for (TreeItem treeItem : next.f89175h) {
                        if (treeItem.f89168a.toLowerCase().contains(str.toLowerCase()) || treeItem.f89171d) {
                            this.f89159I4.add(next);
                            next.f89173f = true;
                            break;
                        }
                    }
                } else {
                    this.f89159I4.add(next);
                }
                if (next.f89171d && !this.f89159I4.contains(next)) {
                    this.f89159I4.add(next);
                }
                for (TreeItem treeItem2 : next.f89175h) {
                    if (treeItem2.f89171d && !this.f89159I4.contains(treeItem2)) {
                        if (!this.f89159I4.contains(next)) {
                            this.f89159I4.add(next);
                        }
                        next.f89173f = true;
                    }
                }
                if (next.f89169b && this.f89161K4.containsKey(next.f89168a)) {
                    next.f89173f = this.f89161K4.get(next.f89168a).booleanValue();
                }
            }
            if (!this.f89160J4.isEmpty() && this.f89160J4.get(0).f89168a.startsWith("All") && !this.f89159I4.contains(this.f89160J4.get(0))) {
                this.f89159I4.add(0, this.f89160J4.get(0));
            }
        }
        this.f89158H4.m27491G();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: m3 */
    public /* synthetic */ void m72550m3(Set set, View view) {
        mo15205N2();
        this.f89156F4.clear();
        this.f89156F4.addAll(set);
        if (m15351l0() instanceof UWMainActivityFragment) {
            ((UWMainActivityFragment) m15351l0()).m72593J3(m15387y().getString("Type"), this.f89156F4);
        }
    }

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_new_check_viewer, (ViewGroup) null);
        this.f89157G4 = (RecyclerView) viewInflate.findViewById(C5562R.id.recycler_view);
        final SearchView searchView = (SearchView) viewInflate.findViewById(C5562R.id.search_view);
        ArrayList<Bundle> parcelableArrayList = m15387y().getParcelableArrayList("Items");
        String string = m15387y().getString("TitleProperty");
        this.f89156F4 = m15387y().containsKey("Positions") ? m15387y().getIntegerArrayList("Positions") : new ArrayList<>();
        final HashSet hashSet = new HashSet(this.f89156F4);
        this.f89160J4 = m72547j3(parcelableArrayList, string, hashSet);
        this.f89161K4 = new HashMap();
        Iterator<TreeItem> it2 = this.f89160J4.iterator();
        while (it2.hasNext()) {
            TreeItem next = it2.next();
            if (next.f89169b) {
                this.f89161K4.put(next.f89168a, Boolean.valueOf(next.f89173f));
            }
        }
        this.f89159I4 = new ArrayList<>(this.f89160J4);
        TreeViewAdapter treeViewAdapter = new TreeViewAdapter(m15366r(), this.f89159I4, hashSet);
        this.f89158H4 = treeViewAdapter;
        this.f89157G4.setAdapter(treeViewAdapter);
        this.f89157G4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        this.f89157G4.m27459p(new CustomItemDecoration(m15366r()));
        ((SearchView.SearchAutoComplete) searchView.findViewById(C5562R.id.search_src_text)).setTextColor(ViewCompat.f13527y);
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.CheckDialog.1
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(String str) {
                CheckDialog.this.m72548k3(str);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str) {
                return false;
            }
        });
        searchView.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.a
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                searchView.setIconified(false);
            }
        });
        searchView.requestFocus();
        ((Button) viewInflate.findViewById(C5562R.id.done_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.b
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                this.f89454s.m72550m3(hashSet, view);
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

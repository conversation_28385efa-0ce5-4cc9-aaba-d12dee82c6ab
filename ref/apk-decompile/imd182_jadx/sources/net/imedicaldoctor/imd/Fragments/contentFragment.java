package net.imedicaldoctor.imd.Fragments;

import android.content.Context;
import android.database.Cursor;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.exifinterface.media.ExifInterface;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import com.itextpdf.tool.xml.html.HTML;
import com.timehop.stickyheadersrecyclerview.ItemVisibilityAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersDecoration;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersTouchListener;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.observers.DisposableObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class contentFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    private ArrayList<Bundle> f90197A4;

    /* renamed from: B4 */
    private SearchAdapter f90198B4;

    /* renamed from: C4 */
    private ProgressBar f90199C4;

    /* renamed from: D4 */
    private MenuItem f90200D4;

    /* renamed from: E4 */
    private AsyncTask f90201E4;

    /* renamed from: F4 */
    private boolean f90202F4;

    /* renamed from: G4 */
    private String f90203G4;

    /* renamed from: H4 */
    private Observable<Bundle> f90204H4;

    /* renamed from: I4 */
    private DisposableObserver<Bundle> f90205I4;

    /* renamed from: J4 */
    private StickyRecyclerHeadersDecoration f90206J4;

    /* renamed from: K4 */
    private ArrayList<String> f90207K4;

    /* renamed from: L4 */
    StickyRecyclerHeadersTouchListener f90208L4;

    /* renamed from: M4 */
    private LinearLayoutManager f90209M4;

    /* renamed from: N4 */
    private RecyclerView f90210N4;

    /* renamed from: O4 */
    private boolean f90211O4;

    /* renamed from: P4 */
    public CompressHelper f90212P4;

    /* renamed from: net.imedicaldoctor.imd.Fragments.contentFragment$6 */
    class C53766 implements ObservableOnSubscribe<String> {

        /* renamed from: a */
        final /* synthetic */ SearchView f90221a;

        C53766(SearchView searchView) {
            this.f90221a = searchView;
        }

        @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
        /* renamed from: a */
        public void mo59827a(@NonNull final ObservableEmitter<String> observableEmitter) throws Throwable {
            this.f90221a.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.6.1

                /* renamed from: a */
                private DisposableObserver<Bundle> f90223a;

                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: a */
                public boolean mo2514a(String str) {
                    observableEmitter.onNext(str);
                    return true;
                }

                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: b */
                public boolean mo2515b(final String str) {
                    iMDLogger.m73554j("OnQueryTextSubmit", "OnQueryTextSubmit");
                    contentFragment.this.f90203G4 = str;
                    contentFragment.this.f88786f4 = str;
                    DisposableObserver<Bundle> disposableObserver = this.f90223a;
                    if (disposableObserver != null) {
                        disposableObserver.onComplete();
                    }
                    observableEmitter.onNext("SoheilvbSoheilvbSoheilvb");
                    contentFragment.this.f90212P4.m71788I(str, "SearchContentAll");
                    iMDLogger.m73554j("OnQueryTextSubmit", "Building search observable");
                    Observable observableM59468A4 = Observable.m59451w1(new ObservableOnSubscribe<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.6.1.1
                        @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
                        /* renamed from: a */
                        public void mo59827a(@NonNull ObservableEmitter<Bundle> observableEmitter2) throws Throwable {
                            ArrayList<Bundle> arrayListM73089k3;
                            ArrayList arrayList = new ArrayList();
                            Iterator<Bundle> it2 = CompressHelper.f87345t.iterator();
                            while (it2.hasNext()) {
                                arrayList.add(it2.next());
                            }
                            Bundle bundle = new Bundle();
                            bundle.putString("Path", contentFragment.this.f90212P4.m71797M1());
                            bundle.putString("Name", "highlights.db");
                            bundle.putString("Title", "Highlights");
                            bundle.putString("Type", "highlight");
                            bundle.putString("IconName", "");
                            arrayList.add(0, bundle);
                            Bundle bundle2 = new Bundle();
                            bundle2.putString("Path", contentFragment.this.f90212P4.m71797M1());
                            bundle2.putString("Name", "highlights.db");
                            bundle2.putString("Title", "Notes");
                            bundle2.putString("Type", "note");
                            bundle2.putString("IconName", "");
                            arrayList.add(0, bundle2);
                            Iterator it3 = arrayList.iterator();
                            while (it3.hasNext()) {
                                Bundle bundle3 = (Bundle) it3.next();
                                try {
                                    arrayListM73089k3 = contentFragment.this.m73089k3(bundle3, str);
                                } catch (Exception e2) {
                                    FirebaseCrashlytics.m48010d().m48016g(e2);
                                    iMDLogger.m73550f("ContentSearch", "Error in querying " + bundle3.getString("Name"));
                                    arrayListM73089k3 = null;
                                }
                                if (arrayListM73089k3 != null && arrayListM73089k3.size() != 0) {
                                    iMDLogger.m73554j("Search", "Result from " + bundle3.getString("Title") + " : " + arrayListM73089k3.size());
                                    Bundle bundle4 = new Bundle();
                                    bundle4.putBundle("database", bundle3);
                                    bundle4.putParcelableArrayList("items", arrayListM73089k3);
                                    observableEmitter2.onNext(bundle4);
                                }
                            }
                            observableEmitter2.onComplete();
                        }
                    }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59468A4(new Function<Throwable, Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.6.1.2
                        @Override // io.reactivex.rxjava3.functions.Function
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public Bundle apply(Throwable th) throws Throwable {
                            return null;
                        }
                    });
                    contentFragment.this.f90204H4 = observableM59468A4;
                    DisposableObserver<Bundle> disposableObserver2 = new DisposableObserver<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.6.1.3
                        @Override // io.reactivex.rxjava3.observers.DisposableObserver
                        /* renamed from: a */
                        protected void mo61331a() {
                            super.mo61331a();
                            contentFragment.this.f90200D4.setVisible(true);
                            contentFragment.this.f90199C4.setIndeterminate(true);
                            iMDLogger.m73554j("SearchSubscriber", "On Start");
                            contentFragment.this.f90197A4 = new ArrayList();
                            contentFragment.this.m73085C3();
                            contentFragment.this.m72468V2();
                            C53766.this.f90221a.getSuggestionsAdapter().mo10519m(null);
                            contentFragment.this.mo72473f3("Searching");
                        }

                        @Override // io.reactivex.rxjava3.core.Observer
                        /* renamed from: c, reason: merged with bridge method [inline-methods] */
                        public void onNext(@NonNull Bundle bundle) {
                            contentFragment.this.mo72472e3();
                            contentFragment.this.f90197A4.add(bundle);
                            contentFragment.this.f90210N4.getAdapter().m27491G();
                            StringBuilder sb = new StringBuilder();
                            sb.append("On Next - ");
                            sb.append(bundle.getBundle("database").getString("Title"));
                            sb.append(" - ");
                            contentFragment contentfragment = contentFragment.this;
                            sb.append(contentfragment.m73086D3(contentfragment.f90197A4));
                            sb.append(" - IN Thread :");
                            sb.append(Thread.currentThread());
                            iMDLogger.m73554j("SearchSubscriber", sb.toString());
                        }

                        @Override // io.reactivex.rxjava3.core.Observer
                        public void onComplete() {
                            contentFragment.this.f90200D4.setVisible(false);
                            iMDLogger.m73554j("SearchSubscriber", "On Complete");
                            if (contentFragment.this.f90197A4 == null || contentFragment.this.f90197A4.size() == 0) {
                                contentFragment.this.mo72473f3("Nothing Found");
                            }
                            if (contentFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("ContentCollapsed", false)) {
                                contentFragment.this.m73091y3();
                            }
                        }

                        @Override // io.reactivex.rxjava3.core.Observer
                        public void onError(@NonNull Throwable th) {
                            contentFragment.this.f90200D4.setVisible(false);
                            iMDLogger.m73554j("SearchSubscriber", "On Error");
                            iMDLogger.m73550f("onQueryTextSubmit", "Some error on SearchObservable");
                        }
                    };
                    this.f90223a = disposableObserver2;
                    contentFragment.this.f90205I4 = disposableObserver2;
                    observableM59468A4.mo59651a(this.f90223a);
                    return true;
                }
            });
        }
    }

    public class EmptyViewHolder extends RecyclerView.ViewHolder {
        public EmptyViewHolder(View view) {
            super(view);
        }
    }

    public class SearchAdapter extends RecyclerView.Adapter implements StickyRecyclerHeadersAdapter {

        /* renamed from: d */
        private final Context f90236d;

        public SearchAdapter(Context context) {
            this.f90236d = context;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            contentFragment contentfragment = contentFragment.this;
            return contentfragment.m73088j3(i2, contentfragment.f90197A4).containsKey("Item") ? 0 : 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
            if (viewHolder.m27811F() == 1) {
                return;
            }
            SearchItemViewHolder searchItemViewHolder = (SearchItemViewHolder) viewHolder;
            contentFragment contentfragment = contentFragment.this;
            Bundle bundle = contentfragment.m73088j3(i2, contentfragment.f90197A4).getBundle("Item");
            searchItemViewHolder.f90244I.setText(bundle.getString("text"));
            if (bundle.containsKey("subText") && bundle.getString("subText").length() == 0) {
                searchItemViewHolder.f90245J.setText((CharSequence) null);
                searchItemViewHolder.f90245J.setVisibility(8);
            } else {
                searchItemViewHolder.f90245J.setVisibility(0);
                searchItemViewHolder.f90245J.setText(Html.fromHtml(bundle.getString("subText")));
            }
            searchItemViewHolder.f90246K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.SearchAdapter.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    String string;
                    String str;
                    Bundle bundleM71871n1;
                    String string2;
                    String str2;
                    String str3;
                    String string3;
                    CompressHelper compressHelper;
                    Bundle bundle2;
                    StringBuilder sb;
                    String str4;
                    CompressHelper compressHelper2;
                    Bundle bundle3;
                    String string4;
                    CompressHelper compressHelper3;
                    Bundle bundle4;
                    StringBuilder sb2;
                    String string5;
                    StringBuilder sb3;
                    String string6;
                    contentFragment contentfragment2 = contentFragment.this;
                    Bundle bundleM73088j3 = contentfragment2.m73088j3(i2, contentfragment2.f90197A4);
                    if (bundleM73088j3 == null) {
                        return;
                    }
                    Bundle bundle5 = bundleM73088j3.getBundle("Database");
                    Bundle bundle6 = bundleM73088j3.getBundle("Item");
                    String string7 = bundle5.getString("Type");
                    String string8 = bundle6.getString("id");
                    contentFragment contentfragment3 = contentFragment.this;
                    contentfragment3.f88788h4 = bundle5;
                    String[] strArrM72466T2 = contentfragment3.m72466T2(bundle6.getString("subText"));
                    if (string7.equals("lexi")) {
                        contentFragment.this.f90212P4.m71772A1(bundle5, string8, strArrM72466T2, null);
                        return;
                    }
                    if (string7.equals("skyscape")) {
                        if (!string8.contains("|")) {
                            contentFragment.this.f90212P4.m71772A1(bundle5, string8, strArrM72466T2, bundle6.getString(HTML.Tag.f74369V));
                            return;
                        }
                        Bundle bundle7 = new Bundle();
                        Bundle bundle8 = new Bundle();
                        bundle8.putString("docId", string8);
                        bundle8.putString("name", bundle6.getString("text"));
                        bundle8.putString(HTML.Tag.f74369V, bundle6.getString(HTML.Tag.f74369V));
                        bundle7.putBundle("SelectedItem", bundle8);
                        bundle7.putBundle("DB", bundle5);
                        bundle7.putInt("Mode", 2);
                        bundle7.putBundle("GotoSections", contentFragment.this.m73083z3(bundle8));
                        contentFragment.this.f90212P4.m71798N(SSSearchActivity.class, SSSearchActivity.SSSearchFragment.class, bundle7);
                        return;
                    }
                    if (!string7.equals("medhand")) {
                        if (string7.equals("visualdx")) {
                            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(string8, "-");
                            Bundle bundle9 = new Bundle();
                            bundle9.putInt("defaultModule", Integer.valueOf(strArrSplitByWholeSeparator[1]).intValue());
                            contentFragment.this.f90212P4.m71775B1(bundle5, strArrSplitByWholeSeparator[0], strArrM72466T2, null, bundle9);
                            return;
                        }
                        if (!string7.equals("uptodate")) {
                            if (string7.equals("accessmedicine")) {
                                String string9 = bundle6.getString("type");
                                string = bundle6.getString("contentId");
                                if (string9.equals(ExifInterface.f16317Y4)) {
                                    CompressHelper compressHelper4 = contentFragment.this.f90212P4;
                                    bundleM71871n1 = compressHelper4.m71871n1(compressHelper4.m71817V(bundle5, "select * from videos where id=" + string));
                                    if (bundleM71871n1 == null) {
                                        return;
                                    }
                                } else if (string9.equals(ExifInterface.f16326Z4)) {
                                    CompressHelper compressHelper5 = contentFragment.this.f90212P4;
                                    bundleM71871n1 = compressHelper5.m71871n1(compressHelper5.m71817V(bundle5, "select * from images where id=" + string));
                                    if (bundleM71871n1 == null) {
                                        return;
                                    }
                                } else {
                                    if (!string9.equals("4")) {
                                        str = null;
                                        if (!string9.equals("5")) {
                                            return;
                                        }
                                        contentFragment.this.f90212P4.m71772A1(bundle5, string, strArrM72466T2, str);
                                        return;
                                    }
                                    CompressHelper compressHelper6 = contentFragment.this.f90212P4;
                                    bundleM71871n1 = compressHelper6.m71871n1(compressHelper6.m71817V(bundle5, "select * from tables where id=" + string));
                                    if (bundleM71871n1 == null) {
                                        return;
                                    }
                                }
                                string2 = bundleM71871n1.getString("sectionId");
                                contentFragment.this.f90212P4.m71772A1(bundle5, string2, null, bundleM71871n1.getString("goto"));
                                return;
                            }
                            if (string7.equals("lww")) {
                                String string10 = bundle6.getString("type");
                                string6 = bundle6.getString("contentId");
                                if (string10.equals(ExifInterface.f16317Y4) || string10.equals(ExifInterface.f16326Z4) || string10.equals("4")) {
                                    contentFragment.this.f90212P4.m71772A1(bundle5, string6, null, null);
                                    return;
                                } else if (!string10.equals("5")) {
                                    return;
                                }
                            } else {
                                if (string7.equals("elsevier") || string7.equals("elseviernew")) {
                                    String string11 = bundle6.getString("type");
                                    string = bundle6.getString("contentId");
                                    if (string11.equals(ExifInterface.f16326Z4)) {
                                        CompressHelper compressHelper7 = contentFragment.this.f90212P4;
                                        bundleM71871n1 = compressHelper7.m71890s1(compressHelper7.m71817V(bundle5, "select * from images where id='" + string + "'"));
                                        if (bundleM71871n1 == null) {
                                            return;
                                        } else {
                                            string2 = bundleM71871n1.getString("docId");
                                        }
                                    } else {
                                        if (!string11.equals("4")) {
                                            str = null;
                                            if (!string11.equals("5")) {
                                                return;
                                            }
                                            contentFragment.this.f90212P4.m71772A1(bundle5, string, strArrM72466T2, str);
                                            return;
                                        }
                                        CompressHelper compressHelper8 = contentFragment.this.f90212P4;
                                        bundleM71871n1 = compressHelper8.m71871n1(compressHelper8.m71817V(bundle5, "select * from tables where id=" + string));
                                        if (bundleM71871n1 == null) {
                                            return;
                                        } else {
                                            string2 = bundleM71871n1.getString("sectionId");
                                        }
                                    }
                                    contentFragment.this.f90212P4.m71772A1(bundle5, string2, null, bundleM71871n1.getString("goto"));
                                    return;
                                }
                                if (string7.equals("ovid")) {
                                    String string12 = bundle6.getString("type");
                                    string = bundle6.getString("contentId");
                                    if (string12.equals(ExifInterface.f16317Y4)) {
                                        return;
                                    }
                                    if (string12.equals(ExifInterface.f16326Z4)) {
                                        CompressHelper compressHelper9 = contentFragment.this.f90212P4;
                                        bundleM71871n1 = compressHelper9.m71871n1(compressHelper9.m71817V(bundle5, "select * from images where imagename='" + string + "'"));
                                        if (bundleM71871n1 == null) {
                                            return;
                                        }
                                    } else {
                                        if (!string12.equals("4")) {
                                            str = null;
                                            if (!string12.equals("5")) {
                                                return;
                                            }
                                            contentFragment.this.f90212P4.m71772A1(bundle5, string, strArrM72466T2, str);
                                            return;
                                        }
                                        CompressHelper compressHelper10 = contentFragment.this.f90212P4;
                                        bundleM71871n1 = compressHelper10.m71871n1(compressHelper10.m71817V(bundle5, "select * from tables where id=" + string));
                                        if (bundleM71871n1 == null) {
                                            return;
                                        }
                                    }
                                    string2 = bundleM71871n1.getString("bookId");
                                    contentFragment.this.f90212P4.m71772A1(bundle5, string2, null, bundleM71871n1.getString("goto"));
                                    return;
                                }
                                if (string7.equals("epub")) {
                                    String string13 = bundle6.getString("type");
                                    string6 = bundle6.getString("contentId");
                                    if (!string13.equals("5")) {
                                        return;
                                    }
                                } else {
                                    if (!string7.equals("nejm")) {
                                        if (string7.equals("epocrate")) {
                                            String string14 = bundle6.getString("typeText");
                                            string5 = bundle6.getString("contentId");
                                            if (string14.equals("Dx")) {
                                                contentFragment contentfragment4 = contentFragment.this;
                                                compressHelper2 = contentfragment4.f90212P4;
                                                bundle3 = contentfragment4.f88788h4;
                                                sb3 = new StringBuilder();
                                                str2 = "dx-";
                                            } else if (string14.equals("Rx")) {
                                                contentFragment contentfragment5 = contentFragment.this;
                                                compressHelper2 = contentfragment5.f90212P4;
                                                bundle3 = contentfragment5.f88788h4;
                                                sb3 = new StringBuilder();
                                                str2 = "rx-";
                                            } else if (string14.equals("ID")) {
                                                contentFragment contentfragment6 = contentFragment.this;
                                                compressHelper2 = contentfragment6.f90212P4;
                                                bundle3 = contentfragment6.f88788h4;
                                                sb3 = new StringBuilder();
                                                str2 = "id-";
                                            } else if (string14.equals("Lab")) {
                                                contentFragment contentfragment7 = contentFragment.this;
                                                compressHelper2 = contentfragment7.f90212P4;
                                                bundle3 = contentfragment7.f88788h4;
                                                sb3 = new StringBuilder();
                                                str2 = "lab-";
                                            } else if (string14.equals("Guideline")) {
                                                contentFragment contentfragment8 = contentFragment.this;
                                                compressHelper2 = contentfragment8.f90212P4;
                                                bundle3 = contentfragment8.f88788h4;
                                                sb3 = new StringBuilder();
                                                str2 = "guideline-";
                                            } else {
                                                if (!string14.equals("Table")) {
                                                    return;
                                                }
                                                contentFragment contentfragment9 = contentFragment.this;
                                                compressHelper2 = contentfragment9.f90212P4;
                                                bundle3 = contentfragment9.f88788h4;
                                                sb3 = new StringBuilder();
                                                str2 = "table-";
                                            }
                                        } else {
                                            str2 = "doc,,,";
                                            if (!string7.equals("amirsys")) {
                                                if (string7.equals("statdx")) {
                                                    String string15 = bundle6.getString("contentId");
                                                    String string16 = bundle6.getString("type");
                                                    if (string16.equals(ExifInterface.f16326Z4) || string16.equals("6")) {
                                                        sb2 = new StringBuilder();
                                                        sb2.append("case,,,");
                                                    } else {
                                                        sb2 = new StringBuilder();
                                                        sb2.append("doc,,,");
                                                    }
                                                    sb2.append(string15);
                                                    string4 = sb2.toString();
                                                    contentFragment contentfragment10 = contentFragment.this;
                                                    compressHelper3 = contentfragment10.f90212P4;
                                                    bundle4 = contentfragment10.f88788h4;
                                                    str3 = null;
                                                } else {
                                                    str3 = null;
                                                    if (!string7.equals("martindale")) {
                                                        if (string7.equals("sanford")) {
                                                            string3 = contentFragment.this.f90212P4.m71893t1(StringUtils.splitByWholeSeparator(bundle6.getString("contentId"), "/")) + ".html";
                                                        } else {
                                                            if (string7.equals("micromedex-neofax")) {
                                                                String string17 = bundle6.getString("contentId");
                                                                if (bundle6.getString("typeText").equals("Drug")) {
                                                                    contentFragment contentfragment11 = contentFragment.this;
                                                                    compressHelper = contentfragment11.f90212P4;
                                                                    bundle2 = contentfragment11.f88788h4;
                                                                    sb = new StringBuilder();
                                                                    str4 = "drug-";
                                                                } else {
                                                                    contentFragment contentfragment12 = contentFragment.this;
                                                                    compressHelper = contentfragment12.f90212P4;
                                                                    bundle2 = contentfragment12.f88788h4;
                                                                    sb = new StringBuilder();
                                                                    str4 = "formula-";
                                                                }
                                                                sb.append(str4);
                                                                sb.append(string17);
                                                                compressHelper.m71772A1(bundle2, sb.toString(), strArrM72466T2, null);
                                                                return;
                                                            }
                                                            if (string7.equals("mksap")) {
                                                                String string18 = bundle6.getString("contentId");
                                                                string3 = "groups/" + StringUtils.splitByWholeSeparator(string18, "_")[2] + "/" + bundle6.getString("typeText").toLowerCase() + "s/" + string18;
                                                            } else {
                                                                if (string7.equals("highlight") || string7.equals("note")) {
                                                                    contentFragment.this.m73070A3(bundle6);
                                                                    return;
                                                                }
                                                                string3 = bundle6.getString("contentId");
                                                            }
                                                        }
                                                        contentFragment contentfragment13 = contentFragment.this;
                                                        compressHelper2 = contentfragment13.f90212P4;
                                                        bundle3 = contentfragment13.f88788h4;
                                                        compressHelper2.m71772A1(bundle3, string3, strArrM72466T2, null);
                                                        return;
                                                    }
                                                    string4 = bundle6.getString("contentId");
                                                    contentFragment contentfragment14 = contentFragment.this;
                                                    compressHelper3 = contentfragment14.f90212P4;
                                                    bundle4 = contentfragment14.f88788h4;
                                                }
                                                compressHelper3.m71772A1(bundle4, string4, strArrM72466T2, str3);
                                                return;
                                            }
                                            string5 = bundle6.getString("contentId");
                                            contentFragment contentfragment15 = contentFragment.this;
                                            compressHelper2 = contentfragment15.f90212P4;
                                            bundle3 = contentfragment15.f88788h4;
                                            sb3 = new StringBuilder();
                                        }
                                        sb3.append(str2);
                                        sb3.append(string5);
                                        string3 = sb3.toString();
                                        compressHelper2.m71772A1(bundle3, string3, strArrM72466T2, null);
                                        return;
                                    }
                                    String string19 = bundle6.getString("type");
                                    string6 = bundle6.getString("contentId");
                                    if (!string19.equals("5")) {
                                        return;
                                    }
                                }
                            }
                            contentFragment.this.f90212P4.m71772A1(bundle5, string6, strArrM72466T2, null);
                            return;
                        }
                    }
                    contentFragment.this.f90212P4.m71772A1(bundle5, bundle6.getString("URL"), strArrM72466T2, null);
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 1) {
                return contentFragment.this.new EmptyViewHolder(LayoutInflater.from(this.f90236d).inflate(C5562R.layout.list_view_item_header_keeper, viewGroup, false));
            }
            View viewInflate = LayoutInflater.from(this.f90236d).inflate(C5562R.layout.list_view_item_search_ripple, viewGroup, false);
            contentFragment contentfragment = contentFragment.this;
            return contentfragment.new SearchItemViewHolder(contentfragment.m15366r(), viewInflate);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            contentFragment contentfragment = contentFragment.this;
            return contentfragment.m73086D3(contentfragment.f90197A4);
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: o */
        public RecyclerView.ViewHolder mo58201o(ViewGroup viewGroup) {
            View viewInflate = LayoutInflater.from(this.f90236d).inflate(C5562R.layout.list_view_item_search_header, viewGroup, false);
            contentFragment contentfragment = contentFragment.this;
            return contentfragment.new SearchHeaderViewHolder(contentfragment.m15366r(), viewInflate);
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: p */
        public void mo58202p(RecyclerView.ViewHolder viewHolder, int i2) throws IOException {
            SearchHeaderViewHolder searchHeaderViewHolder = (SearchHeaderViewHolder) viewHolder;
            if (contentFragment.this.f90197A4 == null) {
                return;
            }
            contentFragment contentfragment = contentFragment.this;
            Bundle bundleM73088j3 = contentfragment.m73088j3(i2, contentfragment.f90197A4);
            Bundle bundle = bundleM73088j3.getBundle("Database");
            viewHolder.f33076a.setTag(bundleM73088j3);
            searchHeaderViewHolder.f90241J.setText(bundle.getString("Title"));
            String strM71724C = CompressHelper.m71724C(bundle);
            if (!strM71724C.contains("file:///android_asset/")) {
                searchHeaderViewHolder.f90240I.setImageURI(Uri.parse(strM71724C));
                return;
            }
            try {
                InputStream inputStreamOpen = contentFragment.this.m15366r().getAssets().open(strM71724C.replace("file:///android_asset/", ""));
                searchHeaderViewHolder.f90240I.setImageBitmap(BitmapFactory.decodeStream(inputStreamOpen));
                inputStreamOpen.close();
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.printStackTrace();
            }
        }

        @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
        /* renamed from: r */
        public long mo58203r(int i2) {
            contentFragment contentfragment = contentFragment.this;
            return contentfragment.m73087i3(i2, contentfragment.f90197A4);
        }
    }

    public class SearchHeaderViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public ImageView f90240I;

        /* renamed from: J */
        public TextView f90241J;

        /* renamed from: K */
        public ImageView f90242K;

        public SearchHeaderViewHolder(Context context, View view) {
            super(view);
            this.f90241J = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90240I = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f90242K = (ImageView) view.findViewById(C5562R.id.icon);
        }
    }

    public class SearchItemViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90244I;

        /* renamed from: J */
        public TextView f90245J;

        /* renamed from: K */
        public MaterialRippleLayout f90246K;

        public SearchItemViewHolder(Context context, View view) {
            super(view);
            this.f90244I = (TextView) view.findViewById(C5562R.id.title_text);
            this.f90245J = (TextView) view.findViewById(C5562R.id.subtitle_text);
            this.f90246K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: A3 */
    public void m73070A3(Bundle bundle) {
        Bundle bundleM71826Y0 = this.f90212P4.m71826Y0("Name", bundle.getString("dbName"));
        if (bundleM71826Y0 == null) {
            CompressHelper.m71767x2(m15366r(), "Database not found", 1);
            return;
        }
        String string = bundleM71826Y0.getString("Type");
        String string2 = bundle.getString("dbAddress");
        String string3 = bundle.getString("save");
        Bundle bundle2 = new Bundle();
        bundle2.putString("gotoHighlight", string3);
        if (!string.equals("lexi") && !string.equals("nejm") && !string.equals("skyscape") && !string.equals("medhand") && !string.equals("irandarou") && !string.equals("uptodateddx") && !string.equals("visualdx") && !string.equals("uptodate") && !string.equals("accessmedicine") && !string.equals("lww") && !string.equals("elsevier") && !string.equals("elseviernew") && !string.equals("ovid") && !string.equals("epub")) {
            string.equals("epocrate");
        }
        this.f90212P4.m71775B1(bundleM71826Y0, string2, null, null, bundle2);
        m72468V2();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: z3 */
    public Bundle m73083z3(Bundle bundle) {
        String str;
        String str2;
        Bundle bundle2 = new Bundle();
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(bundle.getString("docId"), "|");
        String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(bundle.getString(HTML.Tag.f74369V), "|");
        for (int i2 = 0; i2 < strArrSplitByWholeSeparator.length; i2++) {
            if (strArrSplitByWholeSeparator2.length > i2) {
                str = strArrSplitByWholeSeparator[i2];
                str2 = strArrSplitByWholeSeparator2[i2];
            } else {
                str = strArrSplitByWholeSeparator[i2];
                str2 = "";
            }
            bundle2.putString(str, str2);
        }
        return bundle2;
    }

    /* renamed from: B3 */
    public void m73084B3() {
        if (this.f90211O4) {
            this.f90210N4.m27380A1(this.f90206J4);
            this.f90211O4 = false;
        }
    }

    /* renamed from: C3 */
    public void m73085C3() {
        StickyRecyclerHeadersTouchListener stickyRecyclerHeadersTouchListener = new StickyRecyclerHeadersTouchListener(this.f90210N4, this.f90206J4);
        this.f90208L4 = stickyRecyclerHeadersTouchListener;
        stickyRecyclerHeadersTouchListener.m58212h(new StickyRecyclerHeadersTouchListener.OnHeaderClickListener() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.8
            @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersTouchListener.OnHeaderClickListener
            /* renamed from: a */
            public void mo58213a(View view, int i2, long j2) {
                String string = ((Bundle) contentFragment.this.f90197A4.get((int) j2)).getBundle("database").getString("Name");
                if (contentFragment.this.f90207K4.contains(string)) {
                    contentFragment.this.f90207K4.remove(string);
                } else {
                    contentFragment.this.f90207K4.add(string);
                }
                contentFragment.this.f90210N4.getAdapter().m27491G();
            }
        });
        this.f90210N4.m27466s(this.f90208L4);
        m73090x3();
        this.f90210N4.setAdapter(this.f90198B4);
        this.f90198B4.m27502Z(new RecyclerView.AdapterDataObserver() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.9
            @Override // androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
            /* renamed from: a */
            public void mo27287a() {
                contentFragment.this.f90206J4.m58207n();
            }
        });
    }

    /* renamed from: D3 */
    public int m73086D3(ArrayList<Bundle> arrayList) {
        int size = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            size += this.f90207K4.contains(next.getBundle("database").getString("Name")) ? 1 : next.getParcelableArrayList("items").size();
        }
        return size;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        try {
            m15366r().setTitle("");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        menuInflater.inflate(C5562R.menu.search, menu);
        final SearchView searchView = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
        this.f88799s4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        MenuItem menuItemFindItem = menu.findItem(C5562R.id.progress_menu);
        this.f90200D4 = menuItemFindItem;
        this.f90199C4 = (ProgressBar) menuItemFindItem.getActionView();
        searchView.setIconifiedByDefault(false);
        searchView.setQueryHint("Search Anything");
        final String str = this.f90203G4;
        this.f88799s4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.2
            @Override // java.lang.Runnable
            public void run() {
                contentFragment.this.f88799s4.m2508k0(str, false);
                String str2 = contentFragment.this.f88786f4;
                if (str2 == null || str2.length() <= 0) {
                    return;
                }
                if (contentFragment.this.f90197A4 == null || contentFragment.this.f90197A4.size() == 0) {
                    contentFragment contentfragment = contentFragment.this;
                    contentfragment.f88799s4.m2508k0(contentfragment.f88786f4, true);
                } else {
                    contentFragment contentfragment2 = contentFragment.this;
                    contentfragment2.f88799s4.m2508k0(contentfragment2.f88786f4, false);
                    contentFragment.this.mo72472e3();
                }
                contentFragment.this.m72468V2();
            }
        }, 10L);
        searchView.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.3
            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: a */
            public boolean mo2516a(int i2) {
                Cursor cursorMo10512c = searchView.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (searchView.getTag(1) != null && ((String) searchView.getTag(1)).length() > 0) {
                    string = searchView.getTag() + StringUtils.SPACE + string;
                }
                searchView.m2508k0(string, true);
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: b */
            public boolean mo2517b(int i2) {
                Cursor cursorMo10512c = searchView.getSuggestionsAdapter().mo10512c();
                if (!cursorMo10512c.moveToPosition(i2)) {
                    return false;
                }
                String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                if (searchView.getTag() != null && ((String) searchView.getTag()).length() > 0) {
                    string = searchView.getTag() + StringUtils.SPACE + string;
                }
                searchView.m2508k0(string, true);
                return false;
            }
        });
        ((ImageView) searchView.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                searchView.m2508k0("", false);
                searchView.clearFocus();
                contentFragment.this.mo72473f3("Search Anything");
                contentFragment.this.m72468V2();
            }
        });
        searchView.setSuggestionsAdapter(new CursorAdapter(m15366r(), null, 0) { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.5
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("word")));
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell, viewGroup, false);
                viewInflate.setTag(viewInflate.findViewById(C5562R.id.text));
                return viewInflate;
            }
        });
        Observable.m59451w1(new C53766(searchView)).m59804x1(500L, TimeUnit.MILLISECONDS).mo59651a(new DisposableObserver<String>() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.7
            @Override // io.reactivex.rxjava3.core.Observer
            /* renamed from: c, reason: merged with bridge method [inline-methods] */
            public void onNext(@NonNull String str2) throws IOException {
                if (str2.equals("SoheilvbSoheilvbSoheilvb")) {
                    searchView.getSuggestionsAdapter().mo10519m(null);
                    return;
                }
                if (str2.length() > 1) {
                    String[] strArrSplit = str2.trim().split(StringUtils.SPACE);
                    String str3 = strArrSplit[strArrSplit.length - 1];
                    String str4 = "";
                    for (int i2 = 0; i2 < strArrSplit.length - 1; i2++) {
                        str4 = str4 + StringUtils.SPACE + strArrSplit[i2];
                    }
                    searchView.setTag(str4.trim());
                    CompressHelper compressHelper = contentFragment.this.f90212P4;
                    compressHelper.m71839c0(compressHelper.m71770A(), "Select rowid as _id,word from contentspell where word match '" + str3 + "*'").m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<ArrayList<Bundle>>() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.7.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(ArrayList<Bundle> arrayList) throws Throwable {
                            searchView.getSuggestionsAdapter().mo10519m(contentFragment.this.f90212P4.m71850h(arrayList));
                        }
                    });
                }
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onComplete() {
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onError(@NonNull Throwable th) {
            }
        });
        m15366r().setTitle("");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View view = this.f88797q4;
        if (view != null) {
            return view;
        }
        this.f90207K4 = new ArrayList<>();
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_search, viewGroup, false);
        this.f88797q4 = viewInflate;
        if (bundle != null && bundle.containsKey("Position")) {
            this.f88785e4 = bundle.getInt("Position");
        }
        if (bundle != null && bundle.containsKey("Query")) {
            this.f88786f4 = bundle.getString("Query");
        }
        if (bundle != null && bundle.containsKey("mIsSubmitted")) {
            this.f90202F4 = bundle.getBoolean("mIsSubmitted");
        }
        this.f90210N4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f90197A4 = new ArrayList<>();
        if (bundle != null && bundle.containsKey("mResults")) {
            this.f90197A4 = bundle.getParcelableArrayList("mResults");
        }
        this.f90198B4 = new SearchAdapter(m15366r());
        this.f90212P4 = new CompressHelper(m15366r());
        this.f90206J4 = new StickyRecyclerHeadersDecoration(this.f90198B4, new ItemVisibilityAdapter() { // from class: net.imedicaldoctor.imd.Fragments.contentFragment.1
            @Override // com.timehop.stickyheadersrecyclerview.ItemVisibilityAdapter
            /* renamed from: a */
            public boolean mo58199a(int i2) {
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) contentFragment.this.f90210N4.getLayoutManager();
                linearLayoutManager.m27176B2();
                linearLayoutManager.m27178E2();
                boolean z = linearLayoutManager.m27176B2() <= i2 && linearLayoutManager.m27178E2() >= i2;
                iMDLogger.m73550f(CSS.Property.f74043m0, i2 + " visible + " + Boolean.valueOf(z));
                return z;
            }
        });
        this.f90210N4.setLayoutManager(new LinearLayoutManager(m15366r()));
        this.f90210N4.setItemAnimator(new DefaultItemAnimator());
        this.f90210N4.m27459p(new CustomItemDecoration(m15366r()));
        m15358o2(true);
        mo72473f3("Search Contents");
        return viewInflate;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: e3 */
    public void mo72472e3() {
        RecyclerView.Adapter adapter = this.f90210N4.getAdapter();
        SearchAdapter searchAdapter = this.f90198B4;
        if (adapter == searchAdapter) {
            searchAdapter.m27491G();
        } else {
            this.f90206J4.m58207n();
            this.f90210N4.setAdapter(this.f90198B4);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: f3 */
    public void mo72473f3(String str) {
        try {
            if (!str.equals("Searching")) {
                this.f90206J4.m58207n();
                m73084B3();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        this.f90210N4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
        this.f90210N4.setAdapter(new StatusAdapter(m15366r(), str));
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: g1 */
    public void mo15335g1() {
        super.mo15335g1();
    }

    /* renamed from: i3 */
    public int m73087i3(int i2, ArrayList<Bundle> arrayList) {
        int size = 0;
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            Bundle bundle = arrayList.get(i3);
            size += this.f90207K4.contains(bundle.getBundle("database").getString("Name")) ? 1 : bundle.getParcelableArrayList("items").size();
            if (i2 < size) {
                return i3;
            }
        }
        return 0;
    }

    /* renamed from: j3 */
    public Bundle m73088j3(int i2, ArrayList<Bundle> arrayList) {
        Iterator<Bundle> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String string = next.getBundle("database").getString("Name");
            int size = this.f90207K4.contains(string) ? 1 : next.getParcelableArrayList("items").size();
            i3 += size;
            if (i2 < i3) {
                int i4 = i2 - (i3 - size);
                Bundle bundle = new Bundle();
                bundle.putBundle("Database", next.getBundle("database"));
                if (this.f90207K4.contains(string)) {
                    if (i4 == 0) {
                        return bundle;
                    }
                    i4--;
                }
                bundle.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(i4));
                return bundle;
            }
        }
        return null;
    }

    /* renamed from: k3 */
    public ArrayList<Bundle> m73089k3(Bundle bundle, String str) {
        CompressHelper compressHelper;
        String str2;
        CompressHelper compressHelper2;
        String str3;
        String str4;
        CompressHelper compressHelper3;
        String str5;
        CompressHelper compressHelper4;
        String str6;
        String strReplace = str.replace("'", "''");
        String string = bundle.getString("Type");
        try {
            if (!string.equals("lexi")) {
                if (string.equals("skyscape")) {
                    compressHelper2 = this.f90212P4;
                    str3 = "Select URL as id, Text as text,snippet(search) as subText  from search where search match '" + strReplace + "' ORDER BY rank(matchinfo(search)) DESC limit 15";
                    str4 = "fcontentSearch.db";
                } else if (string.equals("medhand")) {
                    compressHelper4 = this.f90212P4;
                    str6 = "Select URL, Text as text,snippet(search) as subText  from search where search match '" + strReplace + "' ORDER BY rank(matchinfo(search)) DESC limit 15";
                } else if (string.equals("visualdx")) {
                    compressHelper4 = this.f90212P4;
                    str6 = "Select URL as id, Text || ' - ' || SubText as text, snippet(search) as subText  from search where search match '" + strReplace + "' ORDER BY rank(matchinfo(search)) DESC limit 15";
                } else {
                    if (!string.equals("uptodate")) {
                        if (string.equals("accessmedicine")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type,text || ' - ' || typeText as text, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:2 OR type:0)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                        } else if (string.equals("lww")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type,text || ' - ' || typeText as text, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:2 OR type:0)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                        } else if (string.equals("nejm")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type,text || ' - ' || typeText as text, snippet(search) as subText  from search where search match '" + strReplace + " AND (type:5)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                        } else if (string.equals("elsevier") || string.equals("elseviernew")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type,text || ' - ' || typeText as text, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:2 OR type:0)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                        } else if (string.equals("ovid")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type,text || ' - ' || typeText as text, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:2 OR type:0)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                        } else if (string.equals("highlight")) {
                            compressHelper = this.f90212P4;
                            str2 = "select dbTitle || ' - ' || dbDocName as text, snippet(highlight) as subText, dbAddress,dbName, type,note,save from highlight where text match '" + strReplace + "' ORDER BY rank(matchinfo(highlight)) DESC limit 100";
                        } else if (string.equals("note")) {
                            compressHelper = this.f90212P4;
                            str2 = "select dbTitle || ' - ' || dbDocName as text, snippet(highlight) as subText, dbAddress,dbName, type,note,save from highlight where note match '" + strReplace + "' ORDER BY rank(matchinfo(highlight)) DESC limit 100";
                        } else if (string.equals("epub")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type,text || ' - ' || typeText as text, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:0 OR type:1)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                        } else if (string.equals("epocrate")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type,text || ' - ' || typeText as text,typeText, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:0 OR type:1 OR type:2 OR type:3 OR type:4 OR type:6 OR type:7)' ORDER BY rank(matchinfo(search)) DESC limit 100";
                        } else if (string.equals("amirsys")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type, text || ' - ' || typeText as text,typeText, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:2)' ORDER BY rank(matchinfo(search)) DESC limit 100";
                        } else if (string.equals("statdx")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  contentId,type, text,typeText, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:3)' ORDER BY rank(matchinfo(search)) DESC,type asc limit 15";
                        } else if (string.equals("martindale")) {
                            compressHelper = this.f90212P4;
                            str2 = "Select  text, contentId,type,typeText, snippet(search) as subText  from search where search match '" + strReplace + " AND (type:5)' ORDER BY rank(matchinfo(search)) DESC,type asc limit 15";
                        } else {
                            if (!string.equals("facts")) {
                                if (string.equals("micromedex-drug")) {
                                    compressHelper3 = this.f90212P4;
                                    str5 = "Select  contentId,type, text, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:0)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                                } else if (string.equals("micromedex-neofax")) {
                                    compressHelper3 = this.f90212P4;
                                    str5 = "Select  contentId,type, text || ' - ' || typeText as text,typeText, snippet(search) as subText  from search where search match '" + strReplace + " NOT (type:1 OR type:0)' ORDER BY rank(matchinfo(search)) DESC limit 15";
                                } else if (string.equals("sanford")) {
                                    compressHelper2 = this.f90212P4;
                                    str3 = "Select path as contentId, title as text, snippet(search_base) as subText  from search_base where search_base match '" + strReplace + "' ORDER BY rank(matchinfo(search_base)) DESC limit 15";
                                    str4 = "fts.db";
                                } else if (string.equals("noskhe")) {
                                    compressHelper = this.f90212P4;
                                    str2 = "Select  Text as text, contentId,type,typeText, snippet(search) as subText  from search where search match '" + strReplace + " AND (type:5)' ORDER BY rank(matchinfo(search)) DESC,type asc limit 15";
                                } else if (string.equals("stockley")) {
                                    compressHelper = this.f90212P4;
                                    str2 = "Select  text, contentId,type,typeText, snippet(search) as subText  from search where search match '" + strReplace + " AND (type:5)' ORDER BY rank(matchinfo(search)) DESC,type asc limit 15";
                                } else {
                                    if (!string.equals("mksap")) {
                                        return null;
                                    }
                                    compressHelper = this.f90212P4;
                                    str2 = "Select  Text as text, contentId,type,typeText, snippet(search) as subText  from search where search match '" + strReplace + " AND (type:5) AND  (typeText:Topic OR typeText:Question)' ORDER BY rank(matchinfo(search)) DESC,type asc limit 15";
                                }
                                return compressHelper3.m71819W(bundle, str5, "fsearch.sqlite");
                            }
                            compressHelper = this.f90212P4;
                            str2 = "Select  text, contentId,type,typeText, snippet(search) as subText  from search where search match '" + strReplace + " AND (type:5)' ORDER BY rank(matchinfo(search)) DESC,type asc limit 15";
                        }
                        return compressHelper.m71817V(bundle, str2);
                    }
                    compressHelper4 = this.f90212P4;
                    str6 = "Select URL, Text || ' - ' || \"table\" as text, snippet(search) as subText, related_topic  from search where search match '" + strReplace + "' ORDER BY rank(matchinfo(search)) DESC limit 15";
                }
                return compressHelper2.m71819W(bundle, str3, str4);
            }
            if (!new File(CompressHelper.m71753g1(bundle, "fcontentsearch.db")).exists()) {
                return null;
            }
            compressHelper4 = this.f90212P4;
            str6 = "Select URL as id, Text as text,snippet(search) as subText  from search where search match '" + strReplace + "' ORDER BY rank(matchinfo(search)) DESC limit 15";
            return compressHelper4.m71819W(bundle, str6, "fcontentsearch.db");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            iMDLogger.m73550f("SearchInDB", "Error in searching " + bundle.getString("Title") + " : " + e2);
            return null;
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: l1 */
    public void mo15352l1() {
        super.mo15352l1();
        m72468V2();
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    /* renamed from: x3 */
    public void m73090x3() {
        if (this.f90211O4) {
            return;
        }
        this.f90210N4.m27459p(this.f90206J4);
        this.f90211O4 = true;
    }

    /* renamed from: y3 */
    public void m73091y3() {
        Bundle bundle;
        this.f90207K4 = new ArrayList<>();
        Iterator<Bundle> it2 = this.f90197A4.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (next != null && (bundle = next.getBundle("database")) != null) {
                this.f90207K4.add(bundle.getString("Name"));
            }
        }
        this.f90210N4.getAdapter().m27491G();
    }
}

package net.imedicaldoctor.imd.Fragments.Uptodate;

import android.app.Dialog;
import android.database.DataSetObserver;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.Uptodate.UTDViewerActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class UTDRelatedTopicsFragment extends DialogFragment {
    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_utdrelated_topics, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        m15366r().setFinishOnTouchOutside(true);
        try {
            final JSONArray jSONArray = new JSONArray(m15387y().getString("RELATED"));
            listView.setAdapter(new ListAdapter() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDRelatedTopicsFragment.1
                @Override // android.widget.ListAdapter
                public boolean areAllItemsEnabled() {
                    return true;
                }

                @Override // android.widget.Adapter
                public int getCount() {
                    return jSONArray.length();
                }

                @Override // android.widget.Adapter
                public Object getItem(int i2) {
                    try {
                        return jSONArray.getJSONObject(i2);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        return null;
                    }
                }

                @Override // android.widget.Adapter
                public long getItemId(int i2) {
                    return i2;
                }

                @Override // android.widget.Adapter
                public int getItemViewType(int i2) {
                    return 1;
                }

                @Override // android.widget.Adapter
                public View getView(int i2, View view, ViewGroup viewGroup) {
                    UTDRelatedTopicsFragment.this.m15366r().getLayoutInflater();
                    View viewInflate2 = LayoutInflater.from(UTDRelatedTopicsFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_related_topics, viewGroup, false);
                    try {
                        ((TextView) viewInflate2.findViewById(C5562R.id.title_text)).setText((UTDRelatedTopicsFragment.this.m15387y().containsKey("CALC") ? ((JSONObject) getItem(i2)).getJSONObject("topicInfo") : (JSONObject) getItem(i2)).getString("title"));
                    } catch (Exception unused) {
                    }
                    return viewInflate2;
                }

                @Override // android.widget.Adapter
                public int getViewTypeCount() {
                    return 1;
                }

                @Override // android.widget.Adapter
                public boolean hasStableIds() {
                    return true;
                }

                @Override // android.widget.Adapter
                public boolean isEmpty() {
                    return false;
                }

                @Override // android.widget.ListAdapter
                public boolean isEnabled(int i2) {
                    return true;
                }

                @Override // android.widget.Adapter
                public void registerDataSetObserver(DataSetObserver dataSetObserver) {
                }

                @Override // android.widget.Adapter
                public void unregisterDataSetObserver(DataSetObserver dataSetObserver) {
                }
            });
            listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDRelatedTopicsFragment.2
                @Override // android.widget.AdapterView.OnItemClickListener
                public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) throws JSONException {
                    UTDViewerActivity.UTDViewerFragment uTDViewerFragment;
                    String string;
                    JSONObject jSONObject = (JSONObject) adapterView.getAdapter().getItem(i2);
                    iMDLogger.m73554j("relatedTopicsFragment", "clicked : " + jSONObject.toString());
                    try {
                        if (UTDRelatedTopicsFragment.this.m15387y().containsKey("CALC")) {
                            uTDViewerFragment = (UTDViewerActivity.UTDViewerFragment) UTDRelatedTopicsFragment.this.m15351l0();
                            string = jSONObject.getJSONObject("topicInfo").getString("id");
                        } else {
                            uTDViewerFragment = (UTDViewerActivity.UTDViewerFragment) UTDRelatedTopicsFragment.this.m15351l0();
                            string = jSONObject.getString("id");
                        }
                        uTDViewerFragment.m72745S4(string);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f(getClass().toString(), "Error in getting id of topic " + e2);
                    }
                    UTDRelatedTopicsFragment.this.mo15203M2();
                }
            });
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f(getClass().toString(), "Error in parsing related Topics " + e2);
        }
        builder.setView(viewInflate);
        return builder.create();
    }
}

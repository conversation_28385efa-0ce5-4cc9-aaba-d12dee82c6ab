package net.imedicaldoctor.imd.Fragments.IranDaru;

import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.TextView;
import androidx.cursoradapter.widget.CursorAdapter;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class IDCategoryViewer extends iMDActivity {

    public static class IDCategoryViewerFragment extends SearchHelperFragment {
        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            ArrayList<Bundle> arrayListM71817V;
            Bundle bundle2;
            StringBuilder sb;
            String str;
            int i2 = 0;
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_general_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            super.mo15303U0(layoutInflater, viewGroup, bundle);
            final CompressHelper compressHelper = new CompressHelper(m15366r());
            String string = m15387y().getString("ID");
            this.f88788h4 = m15387y().getBundle("DB");
            String string2 = m15387y().getString("Category");
            m15387y().getString("Name");
            if (string2.equals("medical")) {
                bundle2 = this.f88788h4;
                sb = new StringBuilder();
                sb.append("Select  tDrugGenerics.fDrugGenericId as _id,tDrugGenerics.fDrugGenericId, fDrugGenericName from tMedicalGroupGenerics,tDrugGenerics where tMedicalGroupGenerics.fMedicalGroupId=");
                sb.append(string);
                str = " AND tDrugGenerics.fDrugGenericId=tMedicalGroupGenerics.fDrugGenericId";
            } else {
                if (!string2.equals("pharm")) {
                    arrayListM71817V = null;
                    this.f88787g4.setAdapter((ListAdapter) new CursorAdapter(m15366r(), compressHelper.m71850h(arrayListM71817V), i2) { // from class: net.imedicaldoctor.imd.Fragments.IranDaru.IDCategoryViewer.IDCategoryViewerFragment.1
                        @Override // androidx.cursoradapter.widget.CursorAdapter
                        /* renamed from: e */
                        public void mo2556e(View view, Context context, Cursor cursor) {
                            ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("fDrugGenericName")));
                        }

                        @Override // androidx.cursoradapter.widget.CursorAdapter
                        /* renamed from: j */
                        public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup2) {
                            View viewInflate2 = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_simple_text, viewGroup2, false);
                            viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                            return viewInflate2;
                        }
                    });
                    this.f88787g4.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.IranDaru.IDCategoryViewer.IDCategoryViewerFragment.2
                        @Override // android.widget.AdapterView.OnItemClickListener
                        public void onItemClick(AdapterView<?> adapterView, View view, int i3, long j2) {
                            IDCategoryViewerFragment.this.f88785e4 = i3;
                            Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                            if (cursorMo10512c.moveToPosition(i3)) {
                                compressHelper.m71772A1(IDCategoryViewerFragment.this.f88788h4, compressHelper.m71868m2(cursorMo10512c).getString("fDrugGenericID"), null, null);
                            }
                        }
                    });
                    mo72472e3();
                    m15366r().setTitle("Drugs");
                    return viewInflate;
                }
                bundle2 = this.f88788h4;
                sb = new StringBuilder();
                sb.append("Select tDrugGenerics.fDrugGenericId as _id,tDrugGenerics.fDrugGenericId, fDrugGenericName from tPharmGroupGenerics,tDrugGenerics where tPharmGroupGenerics.fPharmGroupId=");
                sb.append(string);
                str = " AND tDrugGenerics.fDrugGenericId=tPharmGroupGenerics.fDrugGenericId";
            }
            sb.append(str);
            arrayListM71817V = compressHelper.m71817V(bundle2, sb.toString());
            this.f88787g4.setAdapter((ListAdapter) new CursorAdapter(m15366r(), compressHelper.m71850h(arrayListM71817V), i2) { // from class: net.imedicaldoctor.imd.Fragments.IranDaru.IDCategoryViewer.IDCategoryViewerFragment.1
                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: e */
                public void mo2556e(View view, Context context, Cursor cursor) {
                    ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("fDrugGenericName")));
                }

                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: j */
                public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup2) {
                    View viewInflate2 = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_simple_text, viewGroup2, false);
                    viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                    return viewInflate2;
                }
            });
            this.f88787g4.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.IranDaru.IDCategoryViewer.IDCategoryViewerFragment.2
                @Override // android.widget.AdapterView.OnItemClickListener
                public void onItemClick(AdapterView<?> adapterView, View view, int i3, long j2) {
                    IDCategoryViewerFragment.this.f88785e4 = i3;
                    Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                    if (cursorMo10512c.moveToPosition(i3)) {
                        compressHelper.m71772A1(IDCategoryViewerFragment.this.f88788h4, compressHelper.m71868m2(cursorMo10512c).getString("fDrugGenericID"), null, null);
                    }
                }
            });
            mo72472e3();
            m15366r().setTitle("Drugs");
            return viewInflate;
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new IDCategoryViewerFragment());
    }
}

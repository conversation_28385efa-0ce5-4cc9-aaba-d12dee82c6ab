package net.imedicaldoctor.imd.Fragments.Micromedex;

import android.content.res.Resources;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.p003ts.TsExtractor;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.Annotation;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullDeleteViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class MMInteractSelectActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88565A4;

    /* renamed from: B4 */
    public ArrayList<Bundle> f88566B4;

    /* renamed from: C4 */
    public Button f88567C4;

    /* renamed from: D4 */
    public ArrayList<String> f88568D4;

    /* renamed from: E4 */
    public ArrayList<Bundle> f88569E4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_epointeract, viewGroup, false);
        this.f88566B4 = new ArrayList<>();
        this.f88568D4 = new ArrayList<>();
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(false, false);
        appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
                relativeLayout.setVisibility(0);
            }
        }, 800L);
        Button button = (Button) this.f88797q4.findViewById(C5562R.id.result_button);
        this.f88567C4 = button;
        button.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                if (MMInteractSelectActivityFragment.this.f88566B4.size() > 0) {
                    new Bundle().putParcelableArrayList("Items", MMInteractSelectActivityFragment.this.f88566B4);
                    ArrayList arrayList = new ArrayList();
                    Iterator<Bundle> it2 = MMInteractSelectActivityFragment.this.f88566B4.iterator();
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        String string = next.getString("text");
                        arrayList.add(MMInteractSelectActivityFragment.this.m72388j3(next.getString("contentId")) + ",,,,," + string);
                    }
                    new Bundle();
                    MMInteractSelectActivityFragment mMInteractSelectActivityFragment = MMInteractSelectActivityFragment.this;
                    mMInteractSelectActivityFragment.f88791k4.m71772A1(mMInteractSelectActivityFragment.f88788h4, "interactresult-" + StringUtils.join(arrayList, ";;;;;"), null, null);
                }
            }
        });
        ChaptersAdapter chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88566B4, "title", C5562R.layout.list_view_item_ripple_text_full_delete) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullDeleteViewHolder rippleTextFullDeleteViewHolder = (RippleTextFullDeleteViewHolder) viewHolder;
                rippleTextFullDeleteViewHolder.f101493I.setText(bundle2.getString("text"));
                rippleTextFullDeleteViewHolder.f101494J.setText(bundle2.getString(Annotation.f68283i3));
                rippleTextFullDeleteViewHolder.f101498N.setVisibility(0);
                rippleTextFullDeleteViewHolder.f101496L.setVisibility(0);
                if (bundle2.getString(Annotation.f68283i3).length() == 0) {
                    rippleTextFullDeleteViewHolder.f101494J.setVisibility(8);
                } else {
                    rippleTextFullDeleteViewHolder.f101494J.setVisibility(0);
                }
                final String strM72388j3 = MMInteractSelectActivityFragment.this.m72388j3(bundle2.getString("contentId"));
                rippleTextFullDeleteViewHolder.f101497M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.3.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        MMInteractSelectActivityFragment.this.m72468V2();
                        ArrayList arrayList = new ArrayList();
                        String string = bundle2.getString("text");
                        arrayList.add(MMInteractSelectActivityFragment.this.m72388j3(bundle2.getString("contentId")) + ",,,,," + string);
                        new Bundle();
                        MMInteractSelectActivityFragment mMInteractSelectActivityFragment = MMInteractSelectActivityFragment.this;
                        mMInteractSelectActivityFragment.f88791k4.m71772A1(mMInteractSelectActivityFragment.f88788h4, "interactresult-" + StringUtils.join(arrayList, ";;;;;"), null, null);
                    }
                });
                rippleTextFullDeleteViewHolder.f101498N.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.3.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        MMInteractSelectActivityFragment.this.f88566B4.remove(bundle2);
                        MMInteractSelectActivityFragment.this.f88568D4.remove(strM72388j3);
                        MMInteractSelectActivityFragment mMInteractSelectActivityFragment = MMInteractSelectActivityFragment.this;
                        ((ChaptersAdapter) mMInteractSelectActivityFragment.f88792l4).m73465g0(mMInteractSelectActivityFragment.f88566B4);
                        MMInteractSelectActivityFragment.this.m72387i3();
                        MMInteractSelectActivityFragment.this.f88792l4.m27491G();
                        MMInteractSelectActivityFragment mMInteractSelectActivityFragment2 = MMInteractSelectActivityFragment.this;
                        mMInteractSelectActivityFragment2.f88803w4.setAdapter(mMInteractSelectActivityFragment2.f88792l4);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                RippleTextFullDeleteViewHolder rippleTextFullDeleteViewHolder = new RippleTextFullDeleteViewHolder(view);
                rippleTextFullDeleteViewHolder.f101495K.setVisibility(8);
                return rippleTextFullDeleteViewHolder;
            }
        };
        this.f88792l4 = chaptersAdapter;
        chaptersAdapter.f101434h = "Search To Add Drug";
        this.f88565A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.4
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                rippleTextFullViewHolder.f101502L.setVisibility(8);
                if (bundle2.getString(Annotation.f68283i3).length() == 0) {
                    rippleTextFullViewHolder.f101500J.setVisibility(8);
                } else {
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                }
                final String strM72388j3 = MMInteractSelectActivityFragment.this.m72388j3(bundle2.getString("contentId"));
                if (MMInteractSelectActivityFragment.this.f88568D4.contains(strM72388j3)) {
                    rippleTextFullViewHolder.f101499I.setTextColor(Color.rgb(TsExtractor.f30466L, TsExtractor.f30466L, TsExtractor.f30466L));
                } else {
                    rippleTextFullViewHolder.f101499I.setTextColor(Color.rgb(0, 0, 0));
                    rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.4.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            MMInteractSelectActivityFragment.this.m72468V2();
                            MMInteractSelectActivityFragment.this.f88566B4.add(bundle2);
                            MMInteractSelectActivityFragment.this.f88568D4.add(strM72388j3);
                            MMInteractSelectActivityFragment.this.m72387i3();
                            MMInteractSelectActivityFragment.this.f88799s4.m2508k0("", false);
                            MMInteractSelectActivityFragment mMInteractSelectActivityFragment = MMInteractSelectActivityFragment.this;
                            ((ChaptersAdapter) mMInteractSelectActivityFragment.f88792l4).m73465g0(mMInteractSelectActivityFragment.f88566B4);
                            MMInteractSelectActivityFragment.this.f88792l4.m27491G();
                            MMInteractSelectActivityFragment mMInteractSelectActivityFragment2 = MMInteractSelectActivityFragment.this;
                            mMInteractSelectActivityFragment2.f88803w4.setAdapter(mMInteractSelectActivityFragment2.f88792l4);
                        }
                    });
                }
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                MMInteractSelectActivityFragment.this.m72468V2();
                MMInteractSelectActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88565A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88565A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71819W(this.f88788h4, "Select * from search where search match 'text:" + str + "* NOT (type:5)'", "fsearch.sqlite");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71819W(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'", "fsearch.sqlite");
    }

    /* renamed from: i3 */
    public void m72387i3() {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.5
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                MMInteractSelectActivityFragment.this.f88569E4 = new ArrayList<>();
                ArrayList<String> arrayList = MMInteractSelectActivityFragment.this.f88568D4;
                if (arrayList != null && arrayList.size() > 0) {
                    MMInteractSelectActivityFragment mMInteractSelectActivityFragment = MMInteractSelectActivityFragment.this;
                    mMInteractSelectActivityFragment.f88791k4.m71866m(mMInteractSelectActivityFragment.f88788h4, "delete from selected_drugs");
                    Iterator<Bundle> it2 = MMInteractSelectActivityFragment.this.f88566B4.iterator();
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        MMInteractSelectActivityFragment mMInteractSelectActivityFragment2 = MMInteractSelectActivityFragment.this;
                        mMInteractSelectActivityFragment2.f88791k4.m71866m(mMInteractSelectActivityFragment2.f88788h4, "Insert into selected_drugs values (" + next.getString("contentId") + ", '" + next.getString("text") + "', 0)");
                        Log.d("MMInteract", "Insert into selected_drugs values (" + next.getString("contentId") + ", '" + next.getString("text") + "', 0)");
                    }
                    MMInteractSelectActivityFragment mMInteractSelectActivityFragment3 = MMInteractSelectActivityFragment.this;
                    mMInteractSelectActivityFragment3.f88569E4 = mMInteractSelectActivityFragment3.f88791k4.m71817V(mMInteractSelectActivityFragment3.f88788h4, "select * from v_interactions");
                }
                observableEmitter.onNext("asdf");
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment.6
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                ArrayList<Bundle> arrayList = MMInteractSelectActivityFragment.this.f88569E4;
                if (arrayList == null || arrayList.size() == 0) {
                    MMInteractSelectActivityFragment.this.f88567C4.setEnabled(false);
                    MMInteractSelectActivityFragment.this.f88567C4.setBackgroundColor(Color.rgb(100, 100, 100));
                    MMInteractSelectActivityFragment.this.f88567C4.setText("Nothing Found");
                    return;
                }
                MMInteractSelectActivityFragment.this.f88567C4.setText(MMInteractSelectActivityFragment.this.f88569E4.size() + " Interactions Found");
                MMInteractSelectActivityFragment.this.f88567C4.setEnabled(true);
                MMInteractSelectActivityFragment.this.f88567C4.setBackgroundColor(Color.rgb(64, 140, 83));
            }
        });
    }

    /* renamed from: j3 */
    public String m72388j3(String str) {
        return str.contains("-") ? str.split("-")[1] : str;
    }
}

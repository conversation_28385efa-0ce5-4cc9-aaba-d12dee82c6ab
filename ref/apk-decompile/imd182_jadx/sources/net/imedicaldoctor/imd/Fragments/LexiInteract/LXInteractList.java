package net.imedicaldoctor.imd.Fragments.LexiInteract;

import android.content.res.Resources;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.p003ts.TsExtractor;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractResult;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullDeleteViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class LXInteractList extends iMDActivity {

    public static class LXInteractListFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private ArrayAdapter<Bundle> f88382A4;

        /* renamed from: B4 */
        private ArrayList<Bundle> f88383B4;

        /* renamed from: C4 */
        private ArrayList<String> f88384C4;

        /* renamed from: D4 */
        public Button f88385D4;

        /* renamed from: E4 */
        public SpellSearchAdapter f88386E4;

        /* renamed from: l3 */
        private void m72331l3() {
            if (this.f88383B4.size() == 0) {
                mo72473f3("Search to add Drugs");
            } else {
                mo72472e3();
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            ArrayList<String> arrayList;
            View view = this.f88797q4;
            if (view != null) {
                return view;
            }
            this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_epointeract, viewGroup, false);
            if (bundle == null || !bundle.containsKey("mDrugs")) {
                this.f88383B4 = new ArrayList<>();
                arrayList = new ArrayList<>();
            } else {
                this.f88383B4 = bundle.getParcelableArrayList("mDrugs");
                arrayList = bundle.getStringArrayList("mIds");
            }
            this.f88384C4 = arrayList;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            mo71990Q2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
            final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
            appBarLayout.m35746D(false, false);
            appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList.LXInteractListFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    relativeLayout.setVisibility(0);
                }
            }, 800L);
            Button button = (Button) this.f88797q4.findViewById(C5562R.id.result_button);
            this.f88385D4 = button;
            button.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList.LXInteractListFragment.2
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    if (LXInteractListFragment.this.f88383B4.size() == 0) {
                        CompressHelper.m71767x2(LXInteractListFragment.this.m15366r(), "There is no drug added", 1);
                        return;
                    }
                    Bundle bundle2 = new Bundle();
                    bundle2.putParcelableArrayList("Drugs", LXInteractListFragment.this.f88383B4);
                    bundle2.putBundle("DB", LXInteractListFragment.this.f88788h4);
                    new CompressHelper(LXInteractListFragment.this.m15366r()).m71798N(LXInteractResult.class, LXInteractResult.LXInteractResultFragment.class, bundle2);
                }
            });
            ChaptersAdapter chaptersAdapter = new ChaptersAdapter(m15366r(), this.f88383B4, "name", C5562R.layout.list_view_item_ripple_text_full_delete) { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList.LXInteractListFragment.3
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: e0 */
                public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                    RippleTextFullDeleteViewHolder rippleTextFullDeleteViewHolder = (RippleTextFullDeleteViewHolder) viewHolder;
                    rippleTextFullDeleteViewHolder.f101493I.setText(bundle2.getString("name"));
                    rippleTextFullDeleteViewHolder.f101498N.setVisibility(0);
                    rippleTextFullDeleteViewHolder.f101496L.setVisibility(0);
                    final String string = bundle2.getString("id");
                    rippleTextFullDeleteViewHolder.f101497M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList.LXInteractListFragment.3.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            LXInteractListFragment.this.m72468V2();
                            ArrayList<? extends Parcelable> arrayList2 = new ArrayList<>();
                            arrayList2.add(bundle2);
                            Bundle bundle3 = new Bundle();
                            bundle3.putParcelableArrayList("Drugs", arrayList2);
                            bundle3.putBundle("DB", LXInteractListFragment.this.f88788h4);
                            new CompressHelper(LXInteractListFragment.this.m15366r()).m71798N(LXInteractResult.class, LXInteractResult.LXInteractResultFragment.class, bundle3);
                        }
                    });
                    rippleTextFullDeleteViewHolder.f101498N.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList.LXInteractListFragment.3.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            LXInteractListFragment.this.f88383B4.remove(bundle2);
                            LXInteractListFragment.this.f88384C4.remove(string);
                            LXInteractListFragment lXInteractListFragment = LXInteractListFragment.this;
                            ((ChaptersAdapter) lXInteractListFragment.f88792l4).m73465g0(lXInteractListFragment.f88383B4);
                            LXInteractListFragment.this.m72332k3();
                            LXInteractListFragment.this.f88792l4.m27491G();
                            LXInteractListFragment lXInteractListFragment2 = LXInteractListFragment.this;
                            lXInteractListFragment2.f88803w4.setAdapter(lXInteractListFragment2.f88792l4);
                        }
                    });
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: h0 */
                public RecyclerView.ViewHolder mo71986h0(View view2) {
                    RippleTextFullDeleteViewHolder rippleTextFullDeleteViewHolder = new RippleTextFullDeleteViewHolder(view2);
                    rippleTextFullDeleteViewHolder.f101495K.setVisibility(8);
                    rippleTextFullDeleteViewHolder.f101494J.setVisibility(8);
                    return rippleTextFullDeleteViewHolder;
                }
            };
            this.f88792l4 = chaptersAdapter;
            chaptersAdapter.f101434h = "Search To Add Drug";
            this.f88386E4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "name", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList.LXInteractListFragment.4
                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: e0 */
                public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                    RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                    rippleTextFullViewHolder.f101499I.setText(bundle2.getString("name"));
                    rippleTextFullViewHolder.f101502L.setVisibility(8);
                    final String string = bundle2.getString("id");
                    if (LXInteractListFragment.this.f88384C4.contains(string)) {
                        rippleTextFullViewHolder.f101499I.setTextColor(Color.rgb(TsExtractor.f30466L, TsExtractor.f30466L, TsExtractor.f30466L));
                    } else {
                        rippleTextFullViewHolder.f101499I.setTextColor(Color.rgb(0, 0, 0));
                        rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList.LXInteractListFragment.4.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view2) {
                                LXInteractListFragment.this.m72468V2();
                                LXInteractListFragment.this.f88383B4.add(bundle2);
                                LXInteractListFragment.this.f88384C4.add(string);
                                LXInteractListFragment.this.f88799s4.m2508k0("", false);
                                LXInteractListFragment lXInteractListFragment = LXInteractListFragment.this;
                                ((ChaptersAdapter) lXInteractListFragment.f88792l4).m73465g0(lXInteractListFragment.f88383B4);
                                LXInteractListFragment.this.f88792l4.m27491G();
                                LXInteractListFragment lXInteractListFragment2 = LXInteractListFragment.this;
                                lXInteractListFragment2.f88803w4.setAdapter(lXInteractListFragment2.f88792l4);
                                LXInteractListFragment.this.m72332k3();
                            }
                        });
                    }
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: h0 */
                public void mo71977h0(Bundle bundle2) {
                    LXInteractListFragment.this.m72468V2();
                    LXInteractListFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: j0 */
                public RecyclerView.ViewHolder mo72196j0(View view2) {
                    RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view2);
                    rippleTextFullViewHolder.f101501K.setVisibility(8);
                    rippleTextFullViewHolder.f101500J.setVisibility(8);
                    return rippleTextFullViewHolder;
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(false);
            return this.f88797q4;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: X2 */
        public void mo71973X2() {
            this.f88386E4.m73478i0(this.f88795o4, this.f88796p4);
            this.f88803w4.setAdapter(this.f88386E4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71819W(this.f88788h4, "Select rowid as _id,* from search where name match '" + str + "*'", "fsearch.db");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71819W(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'", "fsearch.db");
        }

        /* renamed from: k3 */
        public void m72332k3() {
            ArrayList<Bundle> arrayList = this.f88383B4;
            if (arrayList == null || arrayList.size() == 0) {
                this.f88385D4.setEnabled(false);
                this.f88385D4.setBackgroundColor(Color.rgb(100, 100, 100));
                this.f88385D4.setText("Nothing Added");
            } else {
                this.f88385D4.setText("Show Interactions");
                this.f88385D4.setEnabled(true);
                this.f88385D4.setBackgroundColor(Color.rgb(64, 140, 83));
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new LXInteractListFragment());
    }
}

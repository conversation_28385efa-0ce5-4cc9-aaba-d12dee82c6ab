package net.imedicaldoctor.imd.Fragments.Statdx;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.palette.graphics.Palette;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.html.HTML;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.GridAutoFitLayoutManager;
import net.imedicaldoctor.imd.ViewHolders.ImageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class SDMenuActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public RecyclerView f88991X4;

    /* renamed from: Y4 */
    public ArrayList<Bundle> f88992Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f88993Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f88994a5;

    /* renamed from: b5 */
    public DiagnosisAdapter f88995b5;

    /* renamed from: c5 */
    public Bundle f88996c5;

    /* renamed from: d5 */
    public ArrayList<Bundle> f88997d5;

    /* renamed from: e5 */
    public String f88998e5;

    /* renamed from: f5 */
    public ArrayList<String> f88999f5;

    /* renamed from: g5 */
    public NotStickySectionAdapter f89000g5;

    /* renamed from: h5 */
    public ArrayList<Bundle> f89001h5;

    /* renamed from: i5 */
    public ArrayList<Bundle> f89002i5;

    /* renamed from: j5 */
    public ArrayList<Bundle> f89003j5;

    /* renamed from: k5 */
    public ArrayList<Bundle> f89004k5;

    /* renamed from: l5 */
    public ArrayList<Bundle> f89005l5;

    /* renamed from: m5 */
    public ArrayList<Bundle> f89006m5;

    /* renamed from: n5 */
    public String f89007n5;

    /* renamed from: o5 */
    private Bundle f89008o5;

    /* renamed from: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment$1 */
    class RunnableC50551 implements Runnable {
        RunnableC50551() {
        }

        @Override // java.lang.Runnable
        public void run() {
            SDMenuActivityFragment sDMenuActivityFragment = SDMenuActivityFragment.this;
            sDMenuActivityFragment.f88997d5 = sDMenuActivityFragment.f89579Q4.m71817V(sDMenuActivityFragment.f89566D4, "Select * from fields where topicId='" + SDMenuActivityFragment.this.f88998e5 + "'");
            SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
            sDMenuActivityFragment2.f88992Y4 = sDMenuActivityFragment2.f89579Q4.m71817V(sDMenuActivityFragment2.f89566D4, "Select * from images where docId='" + SDMenuActivityFragment.this.f88998e5 + "'");
            SDMenuActivityFragment sDMenuActivityFragment3 = SDMenuActivityFragment.this;
            if (sDMenuActivityFragment3.f88992Y4 == null) {
                sDMenuActivityFragment3.f88992Y4 = new ArrayList<>();
            }
            if (SDMenuActivityFragment.this.f88992Y4.size() > 0) {
                SDMenuActivityFragment sDMenuActivityFragment4 = SDMenuActivityFragment.this;
                sDMenuActivityFragment4.f88993Z4 = sDMenuActivityFragment4.f89579Q4.m71887r2(sDMenuActivityFragment4.f88992Y4, "category");
                SDMenuActivityFragment.this.f88999f5.add("Images");
                Iterator<Bundle> it2 = SDMenuActivityFragment.this.f88992Y4.iterator();
                while (it2.hasNext()) {
                    Bundle next = it2.next();
                    Bundle bundle = new Bundle();
                    String strM71754h1 = CompressHelper.m71754h1(SDMenuActivityFragment.this.f89566D4, next.getString("id") + ".jpg", "images-E");
                    SDMenuActivityFragment.this.m72804T3(next.getString("id"), "images-E");
                    bundle.putString("ImagePath", strM71754h1);
                    bundle.putString("id", next.getString("id"));
                    bundle.putString("Encrypted", IcyHeaders.f28171a3);
                    bundle.putString("DescriptionHTML2", SDMenuActivityFragment.this.f89579Q4.m71773B(next.getString(HTML.Tag.f74389g), next.getString("id"), "127"));
                    bundle.putBundle("db", SDMenuActivityFragment.this.f89566D4);
                    SDMenuActivityFragment.this.f88994a5.add(bundle);
                }
                SDMenuActivityFragment sDMenuActivityFragment5 = SDMenuActivityFragment.this;
                sDMenuActivityFragment5.f89000g5 = new NotStickySectionAdapter(sDMenuActivityFragment5.m15366r(), SDMenuActivityFragment.this.f88993Z4, "title", C5562R.layout.list_view_item_image, C5562R.layout.list_view_item_image_header) { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.1.1
                    @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                    /* renamed from: f0 */
                    public void mo72200f0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                        final ImageViewHolder imageViewHolder = (ImageViewHolder) viewHolder;
                        final String strM71754h12 = CompressHelper.m71754h1(SDMenuActivityFragment.this.f89566D4, bundle2.getString("id") + ".jpg", "small");
                        SDMenuActivityFragment.this.m72804T3(bundle2.getString("id"), "small");
                        Glide.m30041G(SDMenuActivityFragment.this.m15366r()).mo30124i(new File(strM71754h12)).m30165B2(imageViewHolder.f101461I);
                        if (SDMenuActivityFragment.this.f89008o5.containsKey(strM71754h12)) {
                            imageViewHolder.f101462J.setRippleColor(SDMenuActivityFragment.this.f89008o5.getInt(strM71754h12));
                        } else {
                            SDMenuActivityFragment.this.m72832q3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.1.1.1
                                @Override // java.lang.Runnable
                                public void run() {
                                    Palette.Swatch swatchM26489C = Palette.m26478b(BitmapFactory.decodeFile(strM71754h12)).m26518g().m26489C();
                                    if (swatchM26489C == null) {
                                        return;
                                    }
                                    int iM26530e = swatchM26489C.m26530e();
                                    if (SDMenuActivityFragment.this.f89008o5.containsKey(strM71754h12)) {
                                        return;
                                    }
                                    SDMenuActivityFragment.this.f89008o5.putInt(strM71754h12, iM26530e);
                                }
                            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.1.1.2
                                @Override // java.lang.Runnable
                                public void run() {
                                    imageViewHolder.f101462J.setRippleColor(SDMenuActivityFragment.this.f89008o5.getInt(strM71754h12));
                                }
                            });
                        }
                        imageViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.1.1.3
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                SDMenuActivityFragment.this.m72509O4(bundle2.getString("id"));
                            }
                        });
                    }

                    @Override // net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter
                    /* renamed from: k0 */
                    public RecyclerView.ViewHolder mo72202k0(View view) {
                        return new ImageViewHolder(view);
                    }
                };
            }
            if (!SDMenuActivityFragment.this.f89566D4.getString("Name").equals("expertpath.d")) {
                SDMenuActivityFragment sDMenuActivityFragment6 = SDMenuActivityFragment.this;
                sDMenuActivityFragment6.f89001h5 = sDMenuActivityFragment6.f89579Q4.m71817V(sDMenuActivityFragment6.f89566D4, "Select * from ddx where docId='" + SDMenuActivityFragment.this.f88998e5 + "'");
                ArrayList<Bundle> arrayList = SDMenuActivityFragment.this.f89001h5;
                if (arrayList != null && arrayList.size() > 0) {
                    SDMenuActivityFragment.this.f88999f5.add("ddx");
                }
                SDMenuActivityFragment sDMenuActivityFragment7 = SDMenuActivityFragment.this;
                sDMenuActivityFragment7.f89002i5 = sDMenuActivityFragment7.f89579Q4.m71817V(sDMenuActivityFragment7.f89566D4, "Select * from docs_cases where docId='" + SDMenuActivityFragment.this.f88998e5 + "'");
                ArrayList<Bundle> arrayList2 = SDMenuActivityFragment.this.f89002i5;
                if (arrayList2 != null && arrayList2.size() > 0) {
                    SDMenuActivityFragment sDMenuActivityFragment8 = SDMenuActivityFragment.this;
                    sDMenuActivityFragment8.f89003j5 = sDMenuActivityFragment8.f89579Q4.m71887r2(sDMenuActivityFragment8.f89002i5, "caseGroup");
                    Iterator<Bundle> it3 = SDMenuActivityFragment.this.f89003j5.iterator();
                    while (it3.hasNext()) {
                        Bundle next2 = it3.next();
                        SDMenuActivityFragment.this.f88999f5.add("case-" + SDMenuActivityFragment.this.f89003j5.indexOf(next2));
                    }
                }
                SDMenuActivityFragment sDMenuActivityFragment9 = SDMenuActivityFragment.this;
                sDMenuActivityFragment9.f89004k5 = sDMenuActivityFragment9.f89579Q4.m71817V(sDMenuActivityFragment9.f89566D4, "Select * from anatomy where docId='" + SDMenuActivityFragment.this.f88998e5 + "'");
                ArrayList<Bundle> arrayList3 = SDMenuActivityFragment.this.f89004k5;
                if (arrayList3 != null && arrayList3.size() > 0) {
                    SDMenuActivityFragment.this.f88999f5.add("anatomy");
                }
                SDMenuActivityFragment sDMenuActivityFragment10 = SDMenuActivityFragment.this;
                sDMenuActivityFragment10.f89005l5 = sDMenuActivityFragment10.f89579Q4.m71817V(sDMenuActivityFragment10.f89566D4, "Select * from ddx where id='" + SDMenuActivityFragment.this.f88998e5 + "'");
                ArrayList<Bundle> arrayList4 = SDMenuActivityFragment.this.f89005l5;
                if (arrayList4 != null && arrayList4.size() > 0) {
                    SDMenuActivityFragment.this.f88999f5.add("relatedxddx");
                }
                SDMenuActivityFragment sDMenuActivityFragment11 = SDMenuActivityFragment.this;
                sDMenuActivityFragment11.f89006m5 = sDMenuActivityFragment11.f89579Q4.m71817V(sDMenuActivityFragment11.f89566D4, "Select * from anatomy where id='" + SDMenuActivityFragment.this.f88998e5 + "'");
                ArrayList<Bundle> arrayList5 = SDMenuActivityFragment.this.f89006m5;
                if (arrayList5 != null && arrayList5.size() > 0) {
                    SDMenuActivityFragment.this.f88999f5.add("relatedxanatomy");
                }
            }
            SDMenuActivityFragment.this.f88999f5.add("Document");
        }
    }

    public class DiagnosisAdapter extends RecyclerView.Adapter {

        /* renamed from: f */
        private static final int f89025f = 0;

        /* renamed from: g */
        private static final int f89026g = 1;

        /* renamed from: h */
        private static final int f89027h = 2;

        /* renamed from: d */
        public Context f89028d;

        public DiagnosisAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            SDMenuActivityFragment sDMenuActivityFragment = SDMenuActivityFragment.this;
            Bundle bundleM72510I4 = sDMenuActivityFragment.m72510I4(i2, sDMenuActivityFragment.f88999f5);
            String string = bundleM72510I4.getString("Type");
            if (string.equals("Header")) {
                return 0;
            }
            if (string.equals("Item")) {
                return bundleM72510I4.getString("Section").equals("Images") ? 2 : 1;
            }
            return -1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            MaterialRippleLayout materialRippleLayout;
            View.OnClickListener onClickListener;
            SDMenuActivityFragment sDMenuActivityFragment = SDMenuActivityFragment.this;
            Bundle bundleM72510I4 = sDMenuActivityFragment.m72510I4(i2, sDMenuActivityFragment.f88999f5);
            int iM27811F = viewHolder.m27811F();
            if (iM27811F == 0) {
                ((HeaderCellViewHolder) viewHolder).f89044I.setText(SDMenuActivityFragment.this.m72512M4(bundleM72510I4.getString("Text")));
                return;
            }
            if (iM27811F == 2) {
                RecyclerViewViewHolder recyclerViewViewHolder = (RecyclerViewViewHolder) viewHolder;
                recyclerViewViewHolder.f89045I.setAdapter(SDMenuActivityFragment.this.f89000g5);
                final GridAutoFitLayoutManager gridAutoFitLayoutManager = new GridAutoFitLayoutManager(SDMenuActivityFragment.this.m15366r(), (int) (SDMenuActivityFragment.this.m15320b0().getDisplayMetrics().density * 100.0f));
                gridAutoFitLayoutManager.m27031R3(new GridLayoutManager.SpanSizeLookup() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.DiagnosisAdapter.1
                    @Override // androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
                    /* renamed from: f */
                    public int mo27056f(int i3) {
                        if (SDMenuActivityFragment.this.f89000g5.mo26845C(i3) == 1) {
                            return gridAutoFitLayoutManager.f101458b0;
                        }
                        return 1;
                    }
                });
                recyclerViewViewHolder.f89045I.setLayoutManager(gridAutoFitLayoutManager);
                return;
            }
            if (iM27811F == 1) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                String string = bundleM72510I4.getString("Section");
                int i3 = bundleM72510I4.getInt("Index");
                rippleTextFullViewHolder.f101500J.setVisibility(8);
                if (string.equals("Images")) {
                    return;
                }
                if (string.equals("Document")) {
                    final Bundle bundle = SDMenuActivityFragment.this.f88997d5.get(i3);
                    rippleTextFullViewHolder.f101499I.setText(bundle.getString("fieldTitle"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.DiagnosisAdapter.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
                            sDMenuActivityFragment2.f89579Q4.m71772A1(sDMenuActivityFragment2.f89566D4, "doc,,," + SDMenuActivityFragment.this.f88998e5, null, bundle.getString("fieldId"));
                        }
                    };
                } else if (string.equals("ddx")) {
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    final Bundle bundle2 = SDMenuActivityFragment.this.f89001h5.get(i3);
                    rippleTextFullViewHolder.f101499I.setText(bundle2.getString("docTitle"));
                    rippleTextFullViewHolder.f101500J.setText(bundle2.getString("docSection"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.DiagnosisAdapter.3
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
                            sDMenuActivityFragment2.f89579Q4.m71772A1(sDMenuActivityFragment2.f89566D4, "menu,,," + bundle2.getString("id"), null, null);
                        }
                    };
                } else if (string.startsWith("case-")) {
                    final Bundle bundle3 = (Bundle) SDMenuActivityFragment.this.f89003j5.get(Integer.valueOf(string.split("-")[1]).intValue()).getParcelableArrayList("items").get(i3);
                    rippleTextFullViewHolder.f101499I.setText(bundle3.getString("caseTitle"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.DiagnosisAdapter.4
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
                            sDMenuActivityFragment2.f89579Q4.m71772A1(sDMenuActivityFragment2.f89566D4, "case,,," + bundle3.getString("caseId"), null, null);
                        }
                    };
                } else if (string.equals("anatomy")) {
                    final Bundle bundle4 = SDMenuActivityFragment.this.f89004k5.get(i3);
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    rippleTextFullViewHolder.f101499I.setText(bundle4.getString("docTitle"));
                    rippleTextFullViewHolder.f101500J.setText(bundle4.getString("docSection"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.DiagnosisAdapter.5
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
                            sDMenuActivityFragment2.f89579Q4.m71772A1(sDMenuActivityFragment2.f89566D4, "menu,,," + bundle4.getString("id"), null, null);
                        }
                    };
                } else if (string.equals("relatedxddx")) {
                    final Bundle bundle5 = SDMenuActivityFragment.this.f89005l5.get(i3);
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    rippleTextFullViewHolder.f101499I.setText(bundle5.getString("topicTitle"));
                    rippleTextFullViewHolder.f101500J.setText(bundle5.getString("topicCategory"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.DiagnosisAdapter.6
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
                            sDMenuActivityFragment2.f89579Q4.m71772A1(sDMenuActivityFragment2.f89566D4, "menu,,," + bundle5.getString("docId"), null, null);
                        }
                    };
                } else {
                    if (!string.equals("relatedxanatomy")) {
                        return;
                    }
                    final Bundle bundle6 = SDMenuActivityFragment.this.f89006m5.get(i3);
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    rippleTextFullViewHolder.f101499I.setText(bundle6.getString("topicTitle"));
                    rippleTextFullViewHolder.f101500J.setText(bundle6.getString("topicCategory"));
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.DiagnosisAdapter.7
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
                            sDMenuActivityFragment2.f89579Q4.m71772A1(sDMenuActivityFragment2.f89566D4, "menu,,," + bundle6.getString("docId"), null, null);
                        }
                    };
                }
                materialRippleLayout.setOnClickListener(onClickListener);
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new HeaderCellViewHolder(LayoutInflater.from(SDMenuActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 2) {
                return new RecyclerViewViewHolder(LayoutInflater.from(SDMenuActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_recyclerview, viewGroup, false));
            }
            if (i2 != 1) {
                return null;
            }
            RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(LayoutInflater.from(SDMenuActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_full, viewGroup, false));
            rippleTextFullViewHolder.f101501K.setVisibility(8);
            return rippleTextFullViewHolder;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            SDMenuActivityFragment sDMenuActivityFragment = SDMenuActivityFragment.this;
            return sDMenuActivityFragment.m72514P4(sDMenuActivityFragment.f88999f5);
        }
    }

    public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f89044I;

        public HeaderCellViewHolder(View view) {
            super(view);
            this.f89044I = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    public static class RecyclerViewViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public RecyclerView f89045I;

        public RecyclerViewViewHolder(View view) {
            super(view);
            this.f89045I = (RecyclerView) view.findViewById(C5562R.id.recycler_view);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: O4 */
    public void m72509O4(String str) {
        int i2 = 0;
        int i3 = 0;
        while (true) {
            if (i3 >= this.f88994a5.size()) {
                break;
            }
            if (this.f88994a5.get(i3).getString("id").equals(str)) {
                i2 = i3;
                break;
            }
            i3++;
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", this.f88994a5);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public Bundle m72510I4(int i2, ArrayList<String> arrayList) {
        Iterator<String> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            String next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Text", next);
                bundle.putString("Type", "Header");
                return bundle;
            }
            int iM72513N4 = i3 + m72513N4(next);
            if (i2 <= iM72513N4) {
                int iM72513N42 = (i2 - (iM72513N4 - m72513N4(next))) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putString("Section", next);
                bundle2.putInt("Index", iM72513N42);
                bundle2.putString("Type", "Item");
                return bundle2;
            }
            i3 = iM72513N4 + 1;
        }
        return null;
    }

    /* renamed from: L4 */
    public void m72511L4() {
        this.f88991X4.setItemAnimator(new DefaultItemAnimator());
        this.f88991X4.m27459p(new CustomItemDecoration(m15366r()));
        this.f88991X4.setLayoutManager(new LinearLayoutManager(m15366r(), 1, false));
    }

    /* renamed from: M4 */
    public String m72512M4(String str) {
        if (str.equals("Images")) {
            return this.f88992Y4.size() + " Images";
        }
        if (str.equals("Document")) {
            return "Document";
        }
        if (str.equals("ddx")) {
            return "Differential Diagnosis";
        }
        if (str.startsWith("case-")) {
            return this.f89003j5.get(Integer.valueOf(str.split("-")[1]).intValue()).getString("title");
        }
        if (str.equals("anatomy")) {
            return "Related Anatomy";
        }
        if (str.equals("relatedxddx") || str.equals("relatedxanatomy")) {
            return "Related Dx";
        }
        iMDLogger.m73550f("numberOfRowsInSection", "Where is title for : " + str);
        return str;
    }

    /* renamed from: N4 */
    public int m72513N4(String str) {
        if (str.equals("Images")) {
            return 1;
        }
        if (str.equals("Document")) {
            return this.f88997d5.size();
        }
        if (str.equals("ddx")) {
            return this.f89001h5.size();
        }
        if (str.startsWith("case-")) {
            return this.f89003j5.get(Integer.valueOf(str.split("-")[1]).intValue()).getParcelableArrayList("items").size();
        }
        if (str.equals("anatomy")) {
            return this.f89004k5.size();
        }
        if (str.equals("relatedxddx")) {
            return this.f89005l5.size();
        }
        if (str.equals("relatedxanatomy")) {
            return this.f89006m5.size();
        }
        iMDLogger.m73550f("numberOfRowsInSection", "Where is row count for : " + str);
        return -1;
    }

    /* renamed from: P4 */
    public int m72514P4(ArrayList<String> arrayList) {
        int iM72513N4 = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            iM72513N4 = iM72513N4 + m72513N4(it2.next()) + 1;
        }
        return iM72513N4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        ArrayList<Bundle> arrayList = this.f88992Y4;
        if (arrayList == null || arrayList.size() <= 0) {
            return null;
        }
        Bundle bundleM72839v3 = m72839v3(this.f88992Y4);
        String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, bundleM72839v3.getString("id") + ".jpg", "images-E");
        m72804T3(bundleM72839v3.getString("id"), "images-E");
        return strM71754h1;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88991X4 = (RecyclerView) this.f89565C4.findViewById(C5562R.id.recycler_view);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        this.f89008o5 = new Bundle();
        String[] strArrSplit = this.f89567E4.split(",,,");
        if (strArrSplit.length == 2) {
            this.f88998e5 = strArrSplit[1];
        } else if (strArrSplit.length == 3) {
            this.f88998e5 = strArrSplit[1];
            this.f89007n5 = strArrSplit[2];
            this.f89567E4 = strArrSplit[0] + ",,," + strArrSplit[1];
        }
        if (!new File(CompressHelper.m71753g1(this.f89566D4, "base")).exists()) {
            new File(CompressHelper.m71753g1(this.f89566D4, "base")).mkdirs();
        }
        this.f88999f5 = new ArrayList<>();
        this.f88994a5 = new ArrayList<>();
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from docs where id='" + this.f88998e5 + "'");
        if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "Can't find the document, sorry", 1);
        } else {
            this.f88996c5 = arrayListM71817V.get(0);
            this.f89568F4 = this.f88996c5.getString("title") + " - " + this.f88996c5.getString("category");
            m72834r3(new RunnableC50551(), new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    SDMenuActivityFragment sDMenuActivityFragment = SDMenuActivityFragment.this;
                    if (sDMenuActivityFragment.f88992Y4 == null) {
                        sDMenuActivityFragment.f88992Y4 = new ArrayList<>();
                    }
                    SDMenuActivityFragment sDMenuActivityFragment2 = SDMenuActivityFragment.this;
                    final String str = sDMenuActivityFragment2.f89007n5;
                    if (str != null) {
                        sDMenuActivityFragment2.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.2.1
                            @Override // java.lang.Runnable
                            public void run() {
                                SDMenuActivityFragment.this.m72509O4(str);
                            }
                        }, 1000L);
                    }
                    if (SDMenuActivityFragment.this.m15387y().containsKey("SEARCH") && SDMenuActivityFragment.this.m15387y().getStringArray("SEARCH") != null) {
                        new Timer().schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.2.2
                            @Override // java.util.TimerTask, java.lang.Runnable
                            public void run() {
                                SDMenuActivityFragment sDMenuActivityFragment3 = SDMenuActivityFragment.this;
                                sDMenuActivityFragment3.f89579Q4.m71772A1(sDMenuActivityFragment3.f89566D4, "doc,,," + SDMenuActivityFragment.this.f88998e5, SDMenuActivityFragment.this.m15387y().getStringArray("SEARCH"), null);
                                SDMenuActivityFragment.this.m15387y().remove("SEARCH");
                            }
                        }, ExoPlayer.f21773a1);
                    }
                    SDMenuActivityFragment sDMenuActivityFragment3 = SDMenuActivityFragment.this;
                    sDMenuActivityFragment3.f88995b5 = sDMenuActivityFragment3.new DiagnosisAdapter();
                    SDMenuActivityFragment sDMenuActivityFragment4 = SDMenuActivityFragment.this;
                    sDMenuActivityFragment4.f88991X4.setAdapter(sDMenuActivityFragment4.f88995b5);
                    SDMenuActivityFragment.this.m72511L4();
                    SDMenuActivityFragment.this.mo72642f3(C5562R.menu.favorite);
                    SDMenuActivityFragment.this.m15358o2(false);
                    SDMenuActivityFragment.this.m72786G3();
                }
            });
        }
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        menuItem.getItemId();
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        super.mo71957e3(menu);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
        new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.4

            /* renamed from: a */
            byte[] f89023a;

            @Override // android.os.AsyncTask
            protected Object doInBackground(Object[] objArr) {
                try {
                    File file = new File(SDMenuActivityFragment.this.mo71955R2());
                    this.f89023a = new CompressHelper(SDMenuActivityFragment.this.m15366r()).m71899w(CompressHelper.m71748d2(file), file.getName(), "127");
                    return null;
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("ImageGallery", "Error in decrypting image");
                    return null;
                }
            }

            @Override // android.os.AsyncTask
            protected void onPostExecute(Object obj) {
                super.onPostExecute(obj);
                Glide.m30041G(SDMenuActivityFragment.this.m15366r()).mo30123h(this.f89023a).m30165B2(SDMenuActivityFragment.this.f89575M4);
            }
        }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
    }

    @Override // androidx.fragment.app.Fragment, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDMenuActivityFragment.3
            @Override // java.lang.Runnable
            public void run() {
                SDMenuActivityFragment.this.f88995b5.m27491G();
            }
        }, 500L);
    }
}

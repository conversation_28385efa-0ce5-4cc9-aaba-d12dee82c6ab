package net.imedicaldoctor.imd.Fragments.Dictionary;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import fi.iki.elonen.NanoHTTPD;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class WebViewDialog extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f87901F4;

    /* renamed from: G4 */
    private View f87902G4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_viewer, (ViewGroup) null);
        this.f87902G4 = viewInflate;
        WebView webView = (WebView) viewInflate.findViewById(C5562R.id.webView);
        this.f87901F4 = m15387y().getBundle("db");
        if (m15387y().containsKey("url")) {
            webView.loadUrl(m15387y().getString("url"));
        } else {
            webView.loadDataWithBaseURL(m15387y().getString("baseURL"), m15387y().getString("htmlString"), NanoHTTPD.f77082p, "utf-8", null);
        }
        webView.getSettings().setAllowFileAccess(true);
        webView.setWebChromeClient(new WebChromeClient() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.WebViewDialog.1
            @Override // android.webkit.WebChromeClient
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                iMDLogger.m73550f("DialogConsole", consoleMessage.message());
                return true;
            }
        });
        webView.setWebViewClient(new WebViewClient() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.WebViewDialog.2
            @Override // android.webkit.WebViewClient
            public boolean shouldOverrideUrlLoading(WebView webView2, String str) {
                return false;
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: p1 */
    public void mo15361p1(View view, Bundle bundle) {
        super.mo15361p1(view, bundle);
        ((WebView) this.f87902G4.findViewById(C5562R.id.webView)).loadUrl(m15387y().getString("url"));
    }
}

package net.imedicaldoctor.imd.Fragments.DRE;

import android.content.DialogInterface;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.core.content.res.ResourcesCompat;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.HeaderCellViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleSearchContentViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class DREMainActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public ArrayList<String> f87695A4;

    /* renamed from: B4 */
    public ArrayList<Bundle> f87696B4;

    /* renamed from: C4 */
    public ArrayList<Bundle> f87697C4;

    /* renamed from: D4 */
    public ArrayList<Bundle> f87698D4;

    /* renamed from: E4 */
    public ArrayList<Bundle> f87699E4;

    /* renamed from: F4 */
    public ArrayList<Bundle> f87700F4;

    /* renamed from: G4 */
    public ArrayList<Bundle> f87701G4;

    /* renamed from: H4 */
    public ArrayList<Bundle> f87702H4;

    /* renamed from: I4 */
    public ArrayList<Bundle> f87703I4;

    /* renamed from: J4 */
    public ArrayList<Bundle> f87704J4;

    /* renamed from: U4 */
    public boolean f87715U4;

    /* renamed from: K4 */
    public int f87705K4 = 0;

    /* renamed from: L4 */
    public int f87706L4 = 0;

    /* renamed from: M4 */
    public int f87707M4 = 0;

    /* renamed from: N4 */
    public int f87708N4 = 0;

    /* renamed from: O4 */
    public int f87709O4 = 0;

    /* renamed from: P4 */
    public int f87710P4 = 0;

    /* renamed from: Q4 */
    public int f87711Q4 = 40;

    /* renamed from: R4 */
    public int f87712R4 = 0;

    /* renamed from: S4 */
    public int f87713S4 = 0;

    /* renamed from: T4 */
    public int f87714T4 = 0;

    /* renamed from: V4 */
    private final String f87716V4 = "سوالات";

    /* renamed from: W4 */
    private final String f87717W4 = "ساخت آزمون";

    /* renamed from: X4 */
    private final String f87718X4 = "آزمون های قبلی";

    /* renamed from: Y4 */
    private final String f87719Y4 = "تنظیمات";

    /* renamed from: Z4 */
    private final String f87720Z4 = "subject";

    /* renamed from: a5 */
    private final String f87721a5 = "system";

    /* renamed from: b5 */
    private final String f87722b5 = "type";

    /* renamed from: c5 */
    private final String f87723c5 = "year";

    /* renamed from: d5 */
    private final String f87724d5 = "area";

    /* renamed from: e5 */
    private final String f87725e5 = "numberquestion";

    /* renamed from: f5 */
    private final String f87726f5 = "testMode";

    /* renamed from: g5 */
    private final String f87727g5 = "filter";

    /* renamed from: h5 */
    private final String f87728h5 = "hardness";

    public class AccountTextViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f87736I;

        /* renamed from: J */
        private final MaterialRippleLayout f87737J;

        public AccountTextViewHolder(View view) {
            super(view);
            this.f87736I = (TextView) view.findViewById(C5562R.id.text);
            this.f87737J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        }
    }

    public class UWAdapter extends RecyclerView.Adapter {

        /* renamed from: d */
        private final int f87739d = 0;

        /* renamed from: e */
        private final int f87740e = 4;

        /* renamed from: f */
        private final int f87741f = 1;

        /* renamed from: g */
        private final int f87742g = 2;

        /* renamed from: h */
        private final int f87743h = 3;

        public UWAdapter() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            DREMainActivityFragment dREMainActivityFragment = DREMainActivityFragment.this;
            Bundle bundleM72027i3 = dREMainActivityFragment.m72027i3(i2, dREMainActivityFragment.f87695A4);
            if (!bundleM72027i3.getString("Type").equals("Header") && bundleM72027i3.getString("Type").equals("Item")) {
                String string = bundleM72027i3.getString("Section");
                int i3 = bundleM72027i3.getInt("Index");
                if (string.equals("سوالات")) {
                    return 1;
                }
                if (string.equals("ساخت آزمون")) {
                    if (DREMainActivityFragment.this.f87715U4) {
                        if (i3 == 8) {
                            return 4;
                        }
                        return i3 == 7 ? 3 : 2;
                    }
                    if (i3 == 10) {
                        return 4;
                    }
                    return i3 == 9 ? 3 : 2;
                }
                if (string.equals("آزمون های قبلی")) {
                    return 1;
                }
                if (string.equals("تنظیمات")) {
                    return 3;
                }
            }
            return 0;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
            MaterialRippleLayout materialRippleLayout;
            View.OnClickListener onClickListener;
            AccountTextViewHolder accountTextViewHolder;
            HeaderCellViewHolder headerCellViewHolder;
            TextView textView;
            StringBuilder sb;
            DREMainActivityFragment dREMainActivityFragment = DREMainActivityFragment.this;
            Bundle bundleM72027i3 = dREMainActivityFragment.m72027i3(i2, dREMainActivityFragment.f87695A4);
            if (bundleM72027i3.getString("Type").equals("Header")) {
                HeaderCellViewHolder headerCellViewHolder2 = (HeaderCellViewHolder) viewHolder;
                headerCellViewHolder2.f101460I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                headerCellViewHolder2.f101460I.setText(bundleM72027i3.getString("Text"));
            }
            if (bundleM72027i3.getString("Type").equals("Item")) {
                String string = bundleM72027i3.getString("Section");
                int i3 = bundleM72027i3.getInt("Index");
                if (string.equals("سوالات")) {
                    RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                    rippleTextViewHolder.f101515I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                    if (i3 == 0) {
                        rippleTextViewHolder.f101515I.setText("تمام سوالات");
                        materialRippleLayout = rippleTextViewHolder.f101516J;
                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                DREMainActivityFragment dREMainActivityFragment2 = DREMainActivityFragment.this;
                                dREMainActivityFragment2.f88791k4.m71798N(DRETocActivity.class, DRETocActivityFragment.class, dREMainActivityFragment2.m72030l3("0"));
                            }
                        };
                    } else {
                        if (i3 != 1) {
                            return;
                        }
                        rippleTextViewHolder.f101515I.setText("سوالات دلخواه");
                        materialRippleLayout = rippleTextViewHolder.f101516J;
                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                DREMainActivityFragment dREMainActivityFragment2 = DREMainActivityFragment.this;
                                dREMainActivityFragment2.f88791k4.m71798N(DRETocActivity.class, DRETocActivityFragment.class, dREMainActivityFragment2.m72030l3(ExifInterface.f16326Z4));
                            }
                        };
                    }
                } else if (string.equals("ساخت آزمون")) {
                    DREMainActivityFragment dREMainActivityFragment2 = DREMainActivityFragment.this;
                    if (dREMainActivityFragment2.f87715U4) {
                        if (i3 == 8) {
                            headerCellViewHolder = (HeaderCellViewHolder) viewHolder;
                            textView = headerCellViewHolder.f101460I;
                            sb = new StringBuilder();
                            sb.append(DREMainActivityFragment.this.f87705K4);
                            sb.append(" سوال ");
                            textView.setText(sb.toString());
                            headerCellViewHolder.f101460I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                            return;
                        }
                        if (i3 == 7) {
                            accountTextViewHolder = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder.f87736I.setTextColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            accountTextViewHolder.f87736I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                            if (DREMainActivityFragment.this.f87705K4 > 0) {
                                accountTextViewHolder.f87737J.setBackgroundColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.green_dark));
                                accountTextViewHolder.f87736I.setText("ساخت امتحان");
                                materialRippleLayout = accountTextViewHolder.f87737J;
                                onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.3
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view) {
                                        DREMainActivityFragment dREMainActivityFragment3 = DREMainActivityFragment.this;
                                        String str = StringUtils.splitByWholeSeparator(dREMainActivityFragment3.f87701G4.get(dREMainActivityFragment3.f87711Q4).getString("title"), StringUtils.SPACE)[0];
                                        String strM72029k3 = DREMainActivityFragment.this.m72029k3(new Date());
                                        DREMainActivityFragment dREMainActivityFragment4 = DREMainActivityFragment.this;
                                        String string2 = dREMainActivityFragment4.f87696B4.get(dREMainActivityFragment4.f87706L4).getString("name");
                                        String strM72034p3 = DREMainActivityFragment.this.m72034p3();
                                        DREMainActivityFragment dREMainActivityFragment5 = DREMainActivityFragment.this;
                                        ArrayList<Bundle> arrayListM71817V = dREMainActivityFragment5.f88791k4.m71817V(dREMainActivityFragment5.f88788h4, "Select id from questions where " + strM72034p3 + " order by random() limit " + str);
                                        ArrayList arrayList = new ArrayList();
                                        Iterator<Bundle> it2 = arrayListM71817V.iterator();
                                        while (it2.hasNext()) {
                                            arrayList.add(it2.next().getString("id"));
                                        }
                                        String strJoin = StringUtils.join(arrayList, ",");
                                        ArrayList arrayList2 = new ArrayList();
                                        arrayList2.add("Reading");
                                        arrayList2.add("Testing");
                                        DREMainActivityFragment dREMainActivityFragment6 = DREMainActivityFragment.this;
                                        dREMainActivityFragment6.f88791k4.m71866m(dREMainActivityFragment6.f88788h4, "Insert into Tests (id, qIds, createdDate, qIndex, done, mode, right, wrong, subject, system, hard) values (null, '" + strJoin + "', '" + strM72029k3 + "', 0, 0, '" + ((String) arrayList2.get(DREMainActivityFragment.this.f87712R4)) + "', 0, 0, '" + string2 + "', '', '')");
                                        DREMainActivityFragment dREMainActivityFragment7 = DREMainActivityFragment.this;
                                        CompressHelper compressHelper = dREMainActivityFragment7.f88791k4;
                                        String string3 = compressHelper.m71890s1(compressHelper.m71817V(dREMainActivityFragment7.f88788h4, "SELECT id FROM Tests ORDER BY id DESC LIMIT 1")).getString("id");
                                        DREMainActivityFragment dREMainActivityFragment8 = DREMainActivityFragment.this;
                                        CompressHelper compressHelper2 = dREMainActivityFragment8.f88791k4;
                                        Bundle bundle = dREMainActivityFragment8.f88788h4;
                                        StringBuilder sb2 = new StringBuilder();
                                        sb2.append("test-");
                                        sb2.append(string3);
                                        compressHelper2.m71772A1(bundle, sb2.toString(), null, null);
                                    }
                                };
                            }
                            accountTextViewHolder.f87737J.setBackgroundColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.material_grey_700));
                            accountTextViewHolder.f87736I.setText("سوالی وجود ندارد");
                            return;
                        }
                        RippleTextViewHolder rippleTextViewHolder2 = (RippleTextViewHolder) viewHolder;
                        rippleTextViewHolder2.f101515I.setTypeface(ResourcesCompat.m7158j(dREMainActivityFragment2.m15366r(), C5562R.font.iransans));
                        if (i3 == 0) {
                            TextView textView2 = rippleTextViewHolder2.f101515I;
                            DREMainActivityFragment dREMainActivityFragment3 = DREMainActivityFragment.this;
                            textView2.setText(dREMainActivityFragment3.f87701G4.get(dREMainActivityFragment3.f87711Q4).getString("title"));
                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.4
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment4 = DREMainActivityFragment.this;
                                    dREMainActivityFragment4.m72037s3("numberquestion", dREMainActivityFragment4.f87701G4, "title", dREMainActivityFragment4.f87711Q4);
                                }
                            };
                        } else if (i3 == 1) {
                            TextView textView3 = rippleTextViewHolder2.f101515I;
                            StringBuilder sb2 = new StringBuilder();
                            sb2.append("حالت آزمون : ");
                            DREMainActivityFragment dREMainActivityFragment4 = DREMainActivityFragment.this;
                            sb2.append(dREMainActivityFragment4.f87702H4.get(dREMainActivityFragment4.f87712R4).getString("title"));
                            textView3.setText(sb2.toString());
                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.5
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment5 = DREMainActivityFragment.this;
                                    dREMainActivityFragment5.m72037s3("testMode", dREMainActivityFragment5.f87702H4, "title", dREMainActivityFragment5.f87712R4);
                                }
                            };
                        } else if (i3 == 2) {
                            TextView textView4 = rippleTextViewHolder2.f101515I;
                            DREMainActivityFragment dREMainActivityFragment5 = DREMainActivityFragment.this;
                            textView4.setText(dREMainActivityFragment5.f87696B4.get(dREMainActivityFragment5.f87706L4).getString("name"));
                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.6
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment6 = DREMainActivityFragment.this;
                                    dREMainActivityFragment6.m72037s3("subject", dREMainActivityFragment6.f87696B4, "name", dREMainActivityFragment6.f87706L4);
                                }
                            };
                        } else if (i3 == 3) {
                            TextView textView5 = rippleTextViewHolder2.f101515I;
                            StringBuilder sb3 = new StringBuilder();
                            sb3.append("نوع سوالات : ");
                            DREMainActivityFragment dREMainActivityFragment6 = DREMainActivityFragment.this;
                            sb3.append(dREMainActivityFragment6.f87698D4.get(dREMainActivityFragment6.f87709O4).getString("name"));
                            textView5.setText(sb3.toString());
                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.7
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment7 = DREMainActivityFragment.this;
                                    dREMainActivityFragment7.m72037s3("type", dREMainActivityFragment7.f87698D4, "name", dREMainActivityFragment7.f87709O4);
                                }
                            };
                        } else if (i3 == 4) {
                            TextView textView6 = rippleTextViewHolder2.f101515I;
                            StringBuilder sb4 = new StringBuilder();
                            sb4.append("منطقه : ");
                            DREMainActivityFragment dREMainActivityFragment7 = DREMainActivityFragment.this;
                            sb4.append(dREMainActivityFragment7.f87700F4.get(dREMainActivityFragment7.f87710P4).getString("name"));
                            textView6.setText(sb4.toString());
                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.8
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment8 = DREMainActivityFragment.this;
                                    dREMainActivityFragment8.m72037s3("area", dREMainActivityFragment8.f87700F4, "name", dREMainActivityFragment8.f87710P4);
                                }
                            };
                        } else if (i3 == 5) {
                            TextView textView7 = rippleTextViewHolder2.f101515I;
                            StringBuilder sb5 = new StringBuilder();
                            sb5.append("زمان : ");
                            DREMainActivityFragment dREMainActivityFragment8 = DREMainActivityFragment.this;
                            sb5.append(dREMainActivityFragment8.f87699E4.get(dREMainActivityFragment8.f87708N4).getString("name"));
                            textView7.setText(sb5.toString());
                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.9
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment9 = DREMainActivityFragment.this;
                                    dREMainActivityFragment9.m72037s3("year", dREMainActivityFragment9.f87699E4, "name", dREMainActivityFragment9.f87708N4);
                                }
                            };
                        } else {
                            if (i3 != 6) {
                                return;
                            }
                            TextView textView8 = rippleTextViewHolder2.f101515I;
                            StringBuilder sb6 = new StringBuilder();
                            sb6.append("محدود کردن : ");
                            DREMainActivityFragment dREMainActivityFragment9 = DREMainActivityFragment.this;
                            sb6.append(dREMainActivityFragment9.f87703I4.get(dREMainActivityFragment9.f87714T4).getString("title"));
                            textView8.setText(sb6.toString());
                            materialRippleLayout = rippleTextViewHolder2.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.10
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment10 = DREMainActivityFragment.this;
                                    dREMainActivityFragment10.m72037s3("filter", dREMainActivityFragment10.f87703I4, "title", dREMainActivityFragment10.f87714T4);
                                }
                            };
                        }
                    } else {
                        if (i3 == 10) {
                            headerCellViewHolder = (HeaderCellViewHolder) viewHolder;
                            textView = headerCellViewHolder.f101460I;
                            sb = new StringBuilder();
                            sb.append(DREMainActivityFragment.this.f87705K4);
                            sb.append(" سوال ");
                            textView.setText(sb.toString());
                            headerCellViewHolder.f101460I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                            return;
                        }
                        if (i3 == 9) {
                            accountTextViewHolder = (AccountTextViewHolder) viewHolder;
                            accountTextViewHolder.f87736I.setTextColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                            accountTextViewHolder.f87736I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                            if (DREMainActivityFragment.this.f87705K4 > 0) {
                                accountTextViewHolder.f87737J.setBackgroundColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.green_dark));
                                accountTextViewHolder.f87736I.setText("ساخت امتحان");
                                materialRippleLayout = accountTextViewHolder.f87737J;
                                onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.11
                                    @Override // android.view.View.OnClickListener
                                    public void onClick(View view) {
                                        DREMainActivityFragment dREMainActivityFragment10 = DREMainActivityFragment.this;
                                        String str = StringUtils.splitByWholeSeparator(dREMainActivityFragment10.f87701G4.get(dREMainActivityFragment10.f87711Q4).getString("title"), StringUtils.SPACE)[0];
                                        String strM72029k3 = DREMainActivityFragment.this.m72029k3(new Date());
                                        DREMainActivityFragment dREMainActivityFragment11 = DREMainActivityFragment.this;
                                        String string2 = dREMainActivityFragment11.f87696B4.get(dREMainActivityFragment11.f87706L4).getString("name");
                                        DREMainActivityFragment dREMainActivityFragment12 = DREMainActivityFragment.this;
                                        String string3 = dREMainActivityFragment12.f87697C4.get(dREMainActivityFragment12.f87707M4).getString("name");
                                        DREMainActivityFragment dREMainActivityFragment13 = DREMainActivityFragment.this;
                                        String string4 = dREMainActivityFragment13.f87704J4.get(dREMainActivityFragment13.f87713S4).getString("title");
                                        String strM72034p3 = DREMainActivityFragment.this.m72034p3();
                                        DREMainActivityFragment dREMainActivityFragment14 = DREMainActivityFragment.this;
                                        ArrayList<Bundle> arrayListM71817V = dREMainActivityFragment14.f88791k4.m71817V(dREMainActivityFragment14.f88788h4, "Select id from questions where " + strM72034p3 + " order by random() limit " + str);
                                        ArrayList arrayList = new ArrayList();
                                        Iterator<Bundle> it2 = arrayListM71817V.iterator();
                                        while (it2.hasNext()) {
                                            arrayList.add(it2.next().getString("id"));
                                        }
                                        String strJoin = StringUtils.join(arrayList, ",");
                                        ArrayList arrayList2 = new ArrayList();
                                        arrayList2.add("Reading");
                                        arrayList2.add("Testing");
                                        DREMainActivityFragment dREMainActivityFragment15 = DREMainActivityFragment.this;
                                        dREMainActivityFragment15.f88791k4.m71866m(dREMainActivityFragment15.f88788h4, "Insert into Tests (id, qIds, createdDate, qIndex, done, mode, right, wrong, subject, system, hard) values (null, '" + strJoin + "', '" + strM72029k3 + "', 0, 0, '" + ((String) arrayList2.get(DREMainActivityFragment.this.f87712R4)) + "', 0, 0, '" + string2 + "', '" + string3 + "', '" + string4 + "')");
                                        DREMainActivityFragment dREMainActivityFragment16 = DREMainActivityFragment.this;
                                        CompressHelper compressHelper = dREMainActivityFragment16.f88791k4;
                                        String string5 = compressHelper.m71890s1(compressHelper.m71817V(dREMainActivityFragment16.f88788h4, "SELECT id FROM Tests ORDER BY id DESC LIMIT 1")).getString("id");
                                        DREMainActivityFragment dREMainActivityFragment17 = DREMainActivityFragment.this;
                                        CompressHelper compressHelper2 = dREMainActivityFragment17.f88791k4;
                                        Bundle bundle = dREMainActivityFragment17.f88788h4;
                                        StringBuilder sb7 = new StringBuilder();
                                        sb7.append("test-");
                                        sb7.append(string5);
                                        compressHelper2.m71772A1(bundle, sb7.toString(), null, null);
                                    }
                                };
                            }
                            accountTextViewHolder.f87737J.setBackgroundColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.material_grey_700));
                            accountTextViewHolder.f87736I.setText("سوالی وجود ندارد");
                            return;
                        }
                        RippleTextViewHolder rippleTextViewHolder3 = (RippleTextViewHolder) viewHolder;
                        rippleTextViewHolder3.f101515I.setTypeface(ResourcesCompat.m7158j(dREMainActivityFragment2.m15366r(), C5562R.font.iransans));
                        if (i3 == 0) {
                            TextView textView9 = rippleTextViewHolder3.f101515I;
                            DREMainActivityFragment dREMainActivityFragment10 = DREMainActivityFragment.this;
                            textView9.setText(dREMainActivityFragment10.f87701G4.get(dREMainActivityFragment10.f87711Q4).getString("title"));
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.12
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment11 = DREMainActivityFragment.this;
                                    dREMainActivityFragment11.m72037s3("numberquestion", dREMainActivityFragment11.f87701G4, "title", dREMainActivityFragment11.f87711Q4);
                                }
                            };
                        } else if (i3 == 1) {
                            TextView textView10 = rippleTextViewHolder3.f101515I;
                            StringBuilder sb7 = new StringBuilder();
                            sb7.append("حالت آزمون : ");
                            DREMainActivityFragment dREMainActivityFragment11 = DREMainActivityFragment.this;
                            sb7.append(dREMainActivityFragment11.f87702H4.get(dREMainActivityFragment11.f87712R4).getString("title"));
                            textView10.setText(sb7.toString());
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.13
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment12 = DREMainActivityFragment.this;
                                    dREMainActivityFragment12.m72037s3("testMode", dREMainActivityFragment12.f87702H4, "title", dREMainActivityFragment12.f87712R4);
                                }
                            };
                        } else if (i3 == 2) {
                            TextView textView11 = rippleTextViewHolder3.f101515I;
                            DREMainActivityFragment dREMainActivityFragment12 = DREMainActivityFragment.this;
                            textView11.setText(dREMainActivityFragment12.f87696B4.get(dREMainActivityFragment12.f87706L4).getString("name"));
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.14
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment13 = DREMainActivityFragment.this;
                                    dREMainActivityFragment13.m72037s3("subject", dREMainActivityFragment13.f87696B4, "name", dREMainActivityFragment13.f87706L4);
                                }
                            };
                        } else if (i3 == 3) {
                            TextView textView12 = rippleTextViewHolder3.f101515I;
                            DREMainActivityFragment dREMainActivityFragment13 = DREMainActivityFragment.this;
                            textView12.setText(dREMainActivityFragment13.f87697C4.get(dREMainActivityFragment13.f87707M4).getString("name"));
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.15
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment14 = DREMainActivityFragment.this;
                                    dREMainActivityFragment14.m72037s3("system", dREMainActivityFragment14.f87697C4, "name", dREMainActivityFragment14.f87707M4);
                                }
                            };
                        } else if (i3 == 4) {
                            TextView textView13 = rippleTextViewHolder3.f101515I;
                            StringBuilder sb8 = new StringBuilder();
                            sb8.append("نوع سوالات : ");
                            DREMainActivityFragment dREMainActivityFragment14 = DREMainActivityFragment.this;
                            sb8.append(dREMainActivityFragment14.f87698D4.get(dREMainActivityFragment14.f87709O4).getString("name"));
                            textView13.setText(sb8.toString());
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.16
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment15 = DREMainActivityFragment.this;
                                    dREMainActivityFragment15.m72037s3("type", dREMainActivityFragment15.f87698D4, "name", dREMainActivityFragment15.f87709O4);
                                }
                            };
                        } else if (i3 == 5) {
                            TextView textView14 = rippleTextViewHolder3.f101515I;
                            StringBuilder sb9 = new StringBuilder();
                            sb9.append("منطقه : ");
                            DREMainActivityFragment dREMainActivityFragment15 = DREMainActivityFragment.this;
                            sb9.append(dREMainActivityFragment15.f87700F4.get(dREMainActivityFragment15.f87710P4).getString("name"));
                            textView14.setText(sb9.toString());
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.17
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment16 = DREMainActivityFragment.this;
                                    dREMainActivityFragment16.m72037s3("area", dREMainActivityFragment16.f87700F4, "name", dREMainActivityFragment16.f87710P4);
                                }
                            };
                        } else if (i3 == 6) {
                            TextView textView15 = rippleTextViewHolder3.f101515I;
                            StringBuilder sb10 = new StringBuilder();
                            sb10.append("زمان : ");
                            DREMainActivityFragment dREMainActivityFragment16 = DREMainActivityFragment.this;
                            sb10.append(dREMainActivityFragment16.f87699E4.get(dREMainActivityFragment16.f87708N4).getString("name"));
                            textView15.setText(sb10.toString());
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.18
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment17 = DREMainActivityFragment.this;
                                    dREMainActivityFragment17.m72037s3("year", dREMainActivityFragment17.f87699E4, "name", dREMainActivityFragment17.f87708N4);
                                }
                            };
                        } else if (i3 == 7) {
                            TextView textView16 = rippleTextViewHolder3.f101515I;
                            StringBuilder sb11 = new StringBuilder();
                            sb11.append("محدود کردن : ");
                            DREMainActivityFragment dREMainActivityFragment17 = DREMainActivityFragment.this;
                            sb11.append(dREMainActivityFragment17.f87703I4.get(dREMainActivityFragment17.f87714T4).getString("title"));
                            textView16.setText(sb11.toString());
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.19
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment18 = DREMainActivityFragment.this;
                                    dREMainActivityFragment18.m72037s3("filter", dREMainActivityFragment18.f87703I4, "title", dREMainActivityFragment18.f87714T4);
                                }
                            };
                        } else {
                            if (i3 != 8) {
                                return;
                            }
                            TextView textView17 = rippleTextViewHolder3.f101515I;
                            StringBuilder sb12 = new StringBuilder();
                            sb12.append("سختی : ");
                            DREMainActivityFragment dREMainActivityFragment18 = DREMainActivityFragment.this;
                            sb12.append(dREMainActivityFragment18.f87704J4.get(dREMainActivityFragment18.f87713S4).getString("title"));
                            textView17.setText(sb12.toString());
                            materialRippleLayout = rippleTextViewHolder3.f101516J;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.20
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    DREMainActivityFragment dREMainActivityFragment19 = DREMainActivityFragment.this;
                                    dREMainActivityFragment19.m72037s3("hardness", dREMainActivityFragment19.f87704J4, "title", dREMainActivityFragment19.f87713S4);
                                }
                            };
                        }
                    }
                } else if (string.equals("آزمون های قبلی")) {
                    RippleTextViewHolder rippleTextViewHolder4 = (RippleTextViewHolder) viewHolder;
                    rippleTextViewHolder4.f101515I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                    if (i3 == 0) {
                        rippleTextViewHolder4.f101515I.setText("آخرین آزمون");
                        materialRippleLayout = rippleTextViewHolder4.f101516J;
                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.21
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                CompressHelper compressHelper;
                                Bundle bundle;
                                StringBuilder sb13;
                                String str;
                                DREMainActivityFragment dREMainActivityFragment19 = DREMainActivityFragment.this;
                                CompressHelper compressHelper2 = dREMainActivityFragment19.f88791k4;
                                Bundle bundleM71890s1 = compressHelper2.m71890s1(compressHelper2.m71817V(dREMainActivityFragment19.f88788h4, "Select * from tests order by id desc limit 1"));
                                if (bundleM71890s1 == null) {
                                    CompressHelper.m71767x2(DREMainActivityFragment.this.m15366r(), "تا الان آزمونی نساختید", 0);
                                    return;
                                }
                                if (bundleM71890s1.getString("done").equals(IcyHeaders.f28171a3)) {
                                    DREMainActivityFragment dREMainActivityFragment20 = DREMainActivityFragment.this;
                                    compressHelper = dREMainActivityFragment20.f88791k4;
                                    bundle = dREMainActivityFragment20.f88788h4;
                                    sb13 = new StringBuilder();
                                    str = "testresult-";
                                } else {
                                    DREMainActivityFragment dREMainActivityFragment21 = DREMainActivityFragment.this;
                                    compressHelper = dREMainActivityFragment21.f88791k4;
                                    bundle = dREMainActivityFragment21.f88788h4;
                                    sb13 = new StringBuilder();
                                    str = "test-";
                                }
                                sb13.append(str);
                                sb13.append(bundleM71890s1.getString("id"));
                                compressHelper.m71772A1(bundle, sb13.toString(), null, null);
                            }
                        };
                    } else {
                        if (i3 != 1) {
                            return;
                        }
                        rippleTextViewHolder4.f101515I.setText("آزمون های پیشین");
                        materialRippleLayout = rippleTextViewHolder4.f101516J;
                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.22
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                DREMainActivityFragment dREMainActivityFragment19 = DREMainActivityFragment.this;
                                dREMainActivityFragment19.f88791k4.m71798N(DRETestsListActivity.class, DRETestsListActivityFragment.class, dREMainActivityFragment19.m72030l3("0"));
                            }
                        };
                    }
                } else {
                    if (!string.equals("تنظیمات")) {
                        return;
                    }
                    AccountTextViewHolder accountTextViewHolder2 = (AccountTextViewHolder) viewHolder;
                    accountTextViewHolder2.f87736I.setTypeface(ResourcesCompat.m7158j(DREMainActivityFragment.this.m15366r(), C5562R.font.iransans));
                    accountTextViewHolder2.f87736I.setTextColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.white));
                    accountTextViewHolder2.f87736I.setText("پاک کردن تاریخچه");
                    accountTextViewHolder2.f87737J.setBackgroundColor(DREMainActivityFragment.this.m15320b0().getColor(C5562R.color.red));
                    materialRippleLayout = accountTextViewHolder2.f87737J;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.23
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            new AlertDialog.Builder(DREMainActivityFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("This will delete all tests and history").mo1115y("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.23.2
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface, int i4) {
                                    DREMainActivityFragment dREMainActivityFragment19 = DREMainActivityFragment.this;
                                    dREMainActivityFragment19.f88791k4.m71866m(dREMainActivityFragment19.f88788h4, "delete from logs");
                                    DREMainActivityFragment dREMainActivityFragment20 = DREMainActivityFragment.this;
                                    dREMainActivityFragment20.f88791k4.m71866m(dREMainActivityFragment20.f88788h4, "delete from tests");
                                    DREMainActivityFragment dREMainActivityFragment21 = DREMainActivityFragment.this;
                                    dREMainActivityFragment21.f88791k4.m71866m(dREMainActivityFragment21.f88788h4, "delete from flags");
                                }
                            }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.UWAdapter.23.1
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface, int i4) {
                                }
                            }).m1090I();
                        }
                    };
                }
                materialRippleLayout.setOnClickListener(onClickListener);
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            if (i2 == 0) {
                return new HeaderCellViewHolder(LayoutInflater.from(DREMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
            }
            if (i2 == 1) {
                return new RippleTextViewHolder(LayoutInflater.from(DREMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
            }
            if (i2 == 2) {
                return new RippleTextViewHolder(LayoutInflater.from(DREMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_ripple_text_list, viewGroup, false));
            }
            if (i2 == 3) {
                return DREMainActivityFragment.this.new AccountTextViewHolder(LayoutInflater.from(DREMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_account_text, viewGroup, false));
            }
            if (i2 != 4) {
                return null;
            }
            return new HeaderCellViewHolder(LayoutInflater.from(DREMainActivityFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_footer, viewGroup, false));
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            DREMainActivityFragment dREMainActivityFragment = DREMainActivityFragment.this;
            return dREMainActivityFragment.m72038t3(dREMainActivityFragment.f87695A4);
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        m72462O2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(true, false);
        relativeLayout.setVisibility(0);
        this.f87696B4 = this.f88791k4.m71817V(this.f88788h4, "select 0 as id,'تمام دروس' as name , sum(count) as count,0 o from lessons union select id, name,count, 1 o from lessons order by o,name");
        ArrayList<Bundle> arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "select 0 as id, 'تمام بخش ها' as name , sum(count) as count,0 o from divisions union select id, name,count,1 o from divisions order by o,name");
        this.f87697C4 = arrayListM71817V;
        this.f87715U4 = arrayListM71817V == null;
        this.f87698D4 = this.f88791k4.m71817V(this.f88788h4, "select 'تمام امتحان ها' as name,0 o from questions union select distinct(type) as name,1 o from questions order by o asc,name desc");
        this.f87699E4 = this.f88791k4.m71817V(this.f88788h4, "select 'تمام سال ها' as name,0 o from questions union select distinct(year) as name,1 o from questions order by o,name");
        this.f87700F4 = this.f88791k4.m71817V(this.f88788h4, "select 'تمام مناطق' as name,0 o from questions union select distinct(area) as name,1 o from questions order by o,name");
        this.f87701G4 = new ArrayList<>();
        for (int i2 = 0; i2 < 101; i2++) {
            this.f87701G4.add(m72031m3(i2 + " سوال "));
        }
        ArrayList<Bundle> arrayList = new ArrayList<>();
        this.f87702H4 = arrayList;
        arrayList.add(m72031m3("مطالعه"));
        this.f87702H4.add(m72031m3("امتحان"));
        if (!this.f87715U4) {
            ArrayList<Bundle> arrayList2 = new ArrayList<>();
            this.f87704J4 = arrayList2;
            arrayList2.add(m72033o3("تمام سوالات", "0", "100"));
            this.f87704J4.add(m72033o3("آسان", "75", "100"));
            this.f87704J4.add(m72033o3("متوسط", "50", "75"));
            this.f87704J4.add(m72033o3("سخت", "25", "50"));
            this.f87704J4.add(m72033o3("خیلی سخت", "0", "25"));
        }
        ArrayList<Bundle> arrayList3 = new ArrayList<>();
        this.f87703I4 = arrayList3;
        arrayList3.add(m72032n3("تمام سوالات", ""));
        this.f87703I4.add(m72032n3("زده نشده", "not (id in (select distinct qid from logs))"));
        this.f87703I4.add(m72032n3("نادرست", "id in (select qid from (select qid,max(rowid),selectedanswer<>corrAnswer as res from logs group by qid) where res=1) "));
        this.f87703I4.add(m72032n3("انتخاب شده", "id in (select qid from flags)"));
        m15358o2(false);
        ArrayList<String> arrayList4 = new ArrayList<>();
        this.f87695A4 = arrayList4;
        arrayList4.add("سوالات");
        this.f87695A4.add("ساخت آزمون");
        this.f87695A4.add("آزمون های قبلی");
        this.f87695A4.add("تنظیمات");
        this.f88793m4 = new ContentSearchAdapter(m15366r(), this.f88795o4, "text", "subText") { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.1
            @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
            /* renamed from: d0 */
            public void mo72039d0(RecyclerView.ViewHolder viewHolder, int i3, Bundle bundle2) {
                String string;
                StringBuilder sb;
                String str;
                RippleSearchContentViewHolder rippleSearchContentViewHolder = (RippleSearchContentViewHolder) viewHolder;
                rippleSearchContentViewHolder.f101479I.setText(bundle2.getString("text"));
                final String string2 = bundle2.getString("type");
                final String string3 = bundle2.getString("contentId");
                if (string2.equals("0")) {
                    rippleSearchContentViewHolder.f101480J.setVisibility(8);
                    rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.1.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            DREMainActivityFragment dREMainActivityFragment = DREMainActivityFragment.this;
                            dREMainActivityFragment.f88791k4.m71772A1(dREMainActivityFragment.f88788h4, "question-" + string3, null, null);
                        }
                    });
                    return;
                }
                if (string2.equals(IcyHeaders.f28171a3)) {
                    sb = new StringBuilder();
                    sb.append("<font color=\"red\">");
                    str = "Question";
                } else if (string2.equals(ExifInterface.f16317Y4)) {
                    sb = new StringBuilder();
                    sb.append("<font color=\"red\">");
                    str = "Explanation";
                } else {
                    if (!string2.equals(ExifInterface.f16326Z4)) {
                        string = "";
                        final String str2 = string + StringUtils.SPACE + bundle2.getString("subText");
                        rippleSearchContentViewHolder.f101480J.setText(Html.fromHtml(str2));
                        rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.1.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                if (string2.equals(ExifInterface.f16317Y4)) {
                                    DREMainActivityFragment dREMainActivityFragment = DREMainActivityFragment.this;
                                    dREMainActivityFragment.f88791k4.m71772A1(dREMainActivityFragment.f88788h4, "answer-" + string3, DREMainActivityFragment.this.m72466T2(str2), null);
                                    return;
                                }
                                DREMainActivityFragment dREMainActivityFragment2 = DREMainActivityFragment.this;
                                dREMainActivityFragment2.f88791k4.m71772A1(dREMainActivityFragment2.f88788h4, "question-" + string3, DREMainActivityFragment.this.m72466T2(str2), null);
                            }
                        });
                    }
                    sb = new StringBuilder();
                    sb.append("<font color=\"red\">");
                    str = "Answer";
                }
                sb.append(str);
                sb.append("</font>");
                string = sb.toString();
                final String str22 = string + StringUtils.SPACE + bundle2.getString("subText");
                rippleSearchContentViewHolder.f101480J.setText(Html.fromHtml(str22));
                rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment.1.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        if (string2.equals(ExifInterface.f16317Y4)) {
                            DREMainActivityFragment dREMainActivityFragment = DREMainActivityFragment.this;
                            dREMainActivityFragment.f88791k4.m71772A1(dREMainActivityFragment.f88788h4, "answer-" + string3, DREMainActivityFragment.this.m72466T2(str22), null);
                            return;
                        }
                        DREMainActivityFragment dREMainActivityFragment2 = DREMainActivityFragment.this;
                        dREMainActivityFragment2.f88791k4.m71772A1(dREMainActivityFragment2.f88788h4, "question-" + string3, DREMainActivityFragment.this.m72466T2(str22), null);
                    }
                });
            }
        };
        m72028j3();
        UWAdapter uWAdapter = new UWAdapter();
        this.f88792l4 = uWAdapter;
        this.f88803w4.setAdapter(uWAdapter);
        m72461N2();
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88793m4.m73469f0(this.f88795o4);
        this.f88803w4.setAdapter(this.f88793m4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select Text as text,snippet(search) as subText, type, contentId from search where search match '" + str + "' ORDER BY rank(matchinfo(search)) DESC");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    /* renamed from: i3 */
    public Bundle m72027i3(int i2, ArrayList<String> arrayList) {
        Iterator<String> it2 = arrayList.iterator();
        int i3 = 0;
        while (it2.hasNext()) {
            String next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Text", next);
                bundle.putString("Type", "Header");
                return bundle;
            }
            int iM72035q3 = i3 + m72035q3(next);
            if (i2 <= iM72035q3) {
                int iM72035q32 = (i2 - (iM72035q3 - m72035q3(next))) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putString("Section", next);
                bundle2.putInt("Index", iM72035q32);
                bundle2.putString("Type", "Item");
                return bundle2;
            }
            i3 = iM72035q3 + 1;
        }
        return null;
    }

    /* renamed from: j3 */
    public void m72028j3() {
        CompressHelper compressHelper = this.f88791k4;
        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f88788h4, "select count(*) as c from questions where " + m72034p3()));
        this.f87705K4 = bundleM71890s1 == null ? 0 : Integer.valueOf(bundleM71890s1.getString("c")).intValue();
    }

    /* renamed from: k3 */
    public String m72029k3(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ").format(date);
    }

    /* renamed from: l3 */
    public Bundle m72030l3(String str) {
        Bundle bundle = new Bundle();
        bundle.putBundle("DB", this.f88788h4);
        bundle.putString("ParentId", str);
        return bundle;
    }

    /* renamed from: m3 */
    public Bundle m72031m3(String str) {
        Bundle bundle = new Bundle();
        bundle.putString("title", str);
        return bundle;
    }

    /* renamed from: n3 */
    public Bundle m72032n3(String str, String str2) {
        Bundle bundle = new Bundle();
        bundle.putString("title", str);
        bundle.putString("sql", str2);
        return bundle;
    }

    /* renamed from: o3 */
    public Bundle m72033o3(String str, String str2, String str3) {
        Bundle bundle = new Bundle();
        bundle.putString("title", str);
        bundle.putString("Min", str2);
        bundle.putString("Max", str3);
        return bundle;
    }

    /* renamed from: p3 */
    public String m72034p3() {
        ArrayList arrayList = new ArrayList();
        String string = this.f87696B4.get(this.f87706L4).getString("id");
        String string2 = !this.f87715U4 ? this.f87697C4.get(this.f87707M4).getString("id") : "";
        String string3 = this.f87698D4.get(this.f87709O4).getString("name");
        String string4 = this.f87699E4.get(this.f87708N4).getString("name");
        String string5 = this.f87700F4.get(this.f87710P4).getString("name");
        if (!this.f87715U4) {
            String string6 = this.f87704J4.get(this.f87713S4).getString("Min");
            String string7 = this.f87704J4.get(this.f87713S4).getString("Max");
            arrayList.add("CorrPerc > " + string6);
            arrayList.add("CorrPerc < " + string7);
        }
        String string8 = this.f87703I4.get(this.f87714T4).getString("sql");
        if (!string.equals("0")) {
            arrayList.add("lessonId = " + string);
        }
        if (!this.f87715U4 && !string2.equals("0")) {
            arrayList.add("divId = " + string2);
        }
        if (!string3.equals("تمام امتحان ها")) {
            arrayList.add("type = '" + string3 + "'");
        }
        if (!string4.equals("تمام سال ها")) {
            arrayList.add("Year = '" + string4 + "'");
        }
        if (!string5.equals("تمام مناطق")) {
            arrayList.add("area = '" + string5 + "'");
        }
        if (string8.length() > 0) {
            arrayList.add(string8);
        }
        if (arrayList.size() == 0) {
            arrayList.add("1=1");
        }
        return StringUtils.join(arrayList, " AND ");
    }

    /* renamed from: q3 */
    public int m72035q3(String str) {
        if (str.equals("سوالات")) {
            return 2;
        }
        if (str.equals("ساخت آزمون")) {
            return this.f87715U4 ? 9 : 11;
        }
        if (str.equals("آزمون های قبلی")) {
            return 2;
        }
        return str.equals("تنظیمات") ? 1 : 0;
    }

    /* renamed from: r3 */
    public void m72036r3(String str, Bundle bundle, int i2) {
        if (str.equals("filter")) {
            this.f87714T4 = i2;
        } else if (str.equals("hardness")) {
            this.f87713S4 = i2;
        } else if (str.equals("testMode")) {
            this.f87712R4 = i2;
        } else if (str.equals("numberquestion")) {
            this.f87711Q4 = i2;
        } else if (str.equals("system")) {
            this.f87707M4 = i2;
        } else if (str.equals("subject")) {
            this.f87706L4 = i2;
        } else if (str.equals("year")) {
            this.f87708N4 = i2;
        } else if (str.equals("area")) {
            this.f87710P4 = i2;
        } else if (str.equals("type")) {
            this.f87709O4 = i2;
        }
        m72028j3();
        this.f88792l4.m27491G();
    }

    /* renamed from: s3 */
    public void m72037s3(String str, ArrayList<Bundle> arrayList, String str2, int i2) {
        DRESelectDialog dRESelectDialog = new DRESelectDialog();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList("Items", arrayList);
        bundle.putString("TitleProperty", str2);
        bundle.putInt("Position", i2);
        bundle.putString("Type", str);
        dRESelectDialog.m15245A2(this, 0);
        dRESelectDialog.m15342i2(bundle);
        dRESelectDialog.mo15218Z2(true);
        dRESelectDialog.mo15222e3(m15283M(), "asdfasdfasdf");
    }

    /* renamed from: t3 */
    public int m72038t3(ArrayList<String> arrayList) {
        int iM72035q3 = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            iM72035q3 = iM72035q3 + m72035q3(it2.next()) + 1;
        }
        return iM72035q3;
    }
}

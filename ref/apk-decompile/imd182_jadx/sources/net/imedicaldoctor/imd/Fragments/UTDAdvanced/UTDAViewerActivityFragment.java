package net.imedicaldoctor.imd.Fragments.UTDAdvanced;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class UTDAViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f89144X4;

    /* renamed from: Y4 */
    public ArrayList<String> f89145Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f89146Z4;

    /* renamed from: a5 */
    public int f89147a5;

    /* renamed from: b5 */
    public String f89148b5;

    /* renamed from: I4 */
    public void m72539I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f89146Z4.add(bundle);
    }

    /* renamed from: J4 */
    public String m72540J4() throws IOException {
        iMDLogger.m73550f("Loading Document", this.f89567E4);
        String[] strArrSplit = this.f89567E4.split("-");
        this.f89146Z4 = new ArrayList<>();
        this.f89147a5 = 0;
        ArrayList<Bundle> arrayListM71819W = this.f89579Q4.m71819W(this.f89566D4, "Select * from pages where post='" + this.f89148b5 + "'", "pathways/" + strArrSplit[1] + ".db");
        if (arrayListM71819W == null || arrayListM71819W.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "Document doesn't exist", 1);
            return "";
        }
        this.f89144X4 = arrayListM71819W.get(0);
        this.f89568F4 = this.f89579Q4.m71819W(this.f89566D4, "Select * from TOC where id=" + strArrSplit[1], "pathways.db").get(0).getString("title");
        String string = this.f89144X4.getString("result");
        if (!string.contains("<form")) {
            string = new String(this.f89579Q4.m71897v(string, strArrSplit[1], "127"));
        }
        String strReplace = string.replace("jQuery.fn.handleCheckBoxClick(this)", "handleCheckBoxClick(this)");
        String strM72817d4 = m72817d4(m15366r(), "UTDAHeader.css");
        String strM72817d42 = m72817d4(m15366r(), "UTDAFooter.css");
        return strM72817d4.replace("[size]", "200").replace("[title]", this.f89568F4) + strReplace + strM72817d42;
    }

    /* renamed from: K4 */
    public String m72541K4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return m72840w3(this.f89145Y4);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        try {
            String str = this.f89563A4;
            if (str == null || str.length() == 0) {
                this.f89148b5 = "";
                this.f89563A4 = m72540J4();
            }
            String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
            m72826m3();
            m72795O3(this.f89563A4, strM71753g1);
            m72836s4();
            m72831p4();
            mo72642f3(C5562R.menu.elsviewer2);
            m15358o2(false);
            m72786G3();
        } catch (Exception e2) {
            m72779B4(e2);
        }
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = strM71753g1.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72541K4(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = strM71753g1 + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f89145Y4 = arrayList;
            mo71972o4();
        } else if (strArrSplit[0].equals("reload")) {
            this.f89148b5 = strArrSplit[1];
            this.f89563A4 = m72540J4();
            m72795O3(this.f89563A4, CompressHelper.m71753g1(this.f89566D4, "base"));
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        String strM71751f = CompressHelper.m71751f(this.f89563A4, "<script>", "</script>");
        this.f89569G4.m73433g("" + strM71751f);
        this.f89565C4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDAViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() {
            }
        }, 1000L);
        this.f89569G4.m73433g("jQuery.fn.animateTextViewAnswerChange();");
        this.f89569G4.m73433g("jQuery.fn.addSummaryButtonProperties();");
        this.f89569G4.m73433g("$('table.group').last()[0].scrollIntoView();");
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_gallery) {
            return true;
        }
        if (itemId == C5562R.id.action_menu) {
            ASSectionViewer aSSectionViewer = new ASSectionViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("Items", this.f89146Z4);
            bundle.putString("TitleProperty", "label");
            aSSectionViewer.m15245A2(this, 0);
            aSSectionViewer.m15342i2(bundle);
            aSSectionViewer.mo15218Z2(true);
            aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        if (str2.equals("image")) {
            return true;
        }
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        return true;
    }
}

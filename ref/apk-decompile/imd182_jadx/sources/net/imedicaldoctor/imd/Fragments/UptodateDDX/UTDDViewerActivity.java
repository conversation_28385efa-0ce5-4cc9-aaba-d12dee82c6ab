package net.imedicaldoctor.imd.Fragments.UptodateDDX;

import android.content.res.Resources;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TabHost;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;

/* loaded from: classes3.dex */
public class UTDDViewerActivity extends ViewerHelperActivity {

    public static class UTDDViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private ArrayList<Bundle> f89547X4;

        /* renamed from: Y4 */
        private ArrayList<Bundle> f89548Y4;

        /* renamed from: Z4 */
        private ArrayList<Bundle> f89549Z4;

        /* renamed from: a5 */
        private ListView f89550a5;

        /* renamed from: b5 */
        private BaseAdapter f89551b5;

        /* renamed from: M4 */
        public void m72758M4() {
            ListView listView = (ListView) this.f89565C4.findViewById(C5562R.id.list_view);
            TextView textView = (TextView) this.f89565C4.findViewById(C5562R.id.status_label);
            LinearLayout linearLayout = (LinearLayout) this.f89565C4.findViewById(C5562R.id.status_layout);
            listView.setVisibility(0);
            textView.setVisibility(8);
            linearLayout.setVisibility(8);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_utddviewer, menu);
            m72833q4(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_utdd_viewer, viewGroup, false);
            if (bundle != null && bundle.containsKey("Restoring")) {
                this.f89584e4 = true;
                if (bundle.containsKey("Find")) {
                    this.f89585f4 = bundle.getString("Find");
                    this.f89594o4 = bundle.getInt("FindIndex");
                }
                if (bundle.containsKey("mFinalHTML")) {
                    this.f89563A4 = bundle.getString("mFinalHTML");
                }
                if (bundle.containsKey("mTitle")) {
                    this.f89568F4 = bundle.getString("mTitle");
                }
                this.f89547X4 = bundle.getParcelableArrayList("mDiagnoses");
                this.f89548Y4 = bundle.getParcelableArrayList("mDiagnosesIn");
                this.f89549Z4 = bundle.getParcelableArrayList("mDescriptions");
            }
            this.f89565C4 = viewInflate;
            this.f89574L4 = (Toolbar) viewInflate.findViewById(C5562R.id.toolbar);
            this.f89566D4 = m15387y().getBundle("DB");
            this.f89567E4 = m15387y().getString("URL");
            this.f89579Q4 = new CompressHelper(m15366r());
            TabHost tabHost = (TabHost) viewInflate.findViewById(C5562R.id.findtabhost);
            this.f89603x4 = tabHost;
            if (tabHost != null) {
                tabHost.setup();
            }
            this.f89550a5 = (ListView) viewInflate.findViewById(C5562R.id.list_view);
            if (m15387y() == null) {
                return viewInflate;
            }
            try {
                if (this.f89547X4 == null) {
                    CompressHelper compressHelper = this.f89579Q4;
                    Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(this.f89566D4, "select * from diagnoses where id=" + this.f89567E4));
                    ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "select * from ddx where id1=" + this.f89567E4);
                    this.f89547X4 = arrayListM71817V;
                    if (arrayListM71817V == null) {
                        this.f89547X4 = new ArrayList<>();
                    }
                    ArrayList<Bundle> arrayListM71817V2 = this.f89579Q4.m71817V(this.f89566D4, "select * from ddx where id2=" + this.f89567E4);
                    this.f89548Y4 = arrayListM71817V2;
                    if (arrayListM71817V2 == null) {
                        this.f89548Y4 = new ArrayList<>();
                    }
                    this.f89549Z4 = new ArrayList<>();
                    this.f89568F4 = bundleM71907z.getString("diagnosisName");
                    this.f89563A4 = "";
                }
                BaseAdapter baseAdapter = new BaseAdapter() { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDViewerActivity.UTDDViewerFragment.1
                    /* renamed from: a */
                    public Bundle m72760a(int i2) {
                        int i3;
                        String str;
                        Bundle bundle2 = new Bundle();
                        if (UTDDViewerFragment.this.f89547X4.size() == 0) {
                            i2++;
                        }
                        int i4 = 0;
                        if (i2 == 0) {
                            bundle2.putString("Type", "Header");
                            str = "DIFFERENTIAL DIAGNOSES:";
                        } else {
                            if (i2 != UTDDViewerFragment.this.f89547X4.size() + UTDDViewerFragment.this.f89549Z4.size() + 1) {
                                if (i2 > UTDDViewerFragment.this.f89547X4.size() + UTDDViewerFragment.this.f89549Z4.size() + 1) {
                                    bundle2.putString("Type", "InItem");
                                    bundle2.putBundle("Item", (Bundle) UTDDViewerFragment.this.f89548Y4.get(i2 - ((UTDDViewerFragment.this.f89547X4.size() + UTDDViewerFragment.this.f89549Z4.size()) + 2)));
                                    bundle2.putInt("TypeInteger", 1);
                                } else {
                                    Iterator it2 = UTDDViewerFragment.this.f89547X4.iterator();
                                    while (true) {
                                        if (!it2.hasNext()) {
                                            break;
                                        }
                                        Bundle bundle3 = (Bundle) it2.next();
                                        int i5 = i4 + 1;
                                        if (i2 == i5) {
                                            if (UTDDViewerFragment.this.f89549Z4.contains(bundle3)) {
                                                bundle2.putString("Type", "DItemUp");
                                                bundle2.putBundle("Item", bundle3);
                                                bundle2.putInt("TypeInteger", 2);
                                            } else {
                                                bundle2.putString("Type", "DItemDown");
                                                bundle2.putBundle("Item", bundle3);
                                                i3 = 3;
                                            }
                                        } else if (UTDDViewerFragment.this.f89549Z4.contains(bundle3)) {
                                            i4 += 2;
                                            if (i2 == i4) {
                                                bundle2.putString("Type", "DItemDesc");
                                                bundle2.putBundle("Item", bundle3);
                                                i3 = 4;
                                                break;
                                            }
                                        } else {
                                            i4 = i5;
                                        }
                                    }
                                    bundle2.putInt("TypeInteger", i3);
                                }
                                return bundle2;
                            }
                            bundle2.putString("Type", "Header");
                            str = "IN DIFFERENTIAL DIAGNOSIS OF :";
                        }
                        bundle2.putString("Text", str);
                        bundle2.putInt("TypeInteger", 0);
                        return bundle2;
                    }

                    @Override // android.widget.BaseAdapter, android.widget.ListAdapter
                    public boolean areAllItemsEnabled() {
                        return false;
                    }

                    @Override // android.widget.Adapter
                    public int getCount() {
                        int size = (UTDDViewerFragment.this.f89547X4.size() > 0 ? 1 : 0) + UTDDViewerFragment.this.f89547X4.size() + UTDDViewerFragment.this.f89549Z4.size();
                        return UTDDViewerFragment.this.f89548Y4.size() > 0 ? size + 1 + UTDDViewerFragment.this.f89548Y4.size() : size;
                    }

                    @Override // android.widget.Adapter
                    public Object getItem(int i2) {
                        return m72760a(i2);
                    }

                    @Override // android.widget.Adapter
                    public long getItemId(int i2) {
                        return 0L;
                    }

                    @Override // android.widget.BaseAdapter, android.widget.Adapter
                    public int getItemViewType(int i2) {
                        return m72760a(i2).getInt("TypeInteger");
                    }

                    @Override // android.widget.Adapter
                    public View getView(int i2, View view, ViewGroup viewGroup2) {
                        TextView textView;
                        CharSequence charSequenceFromHtml;
                        ImageView imageView;
                        View.OnClickListener onClickListener;
                        String str;
                        Bundle bundle2 = (Bundle) getItem(i2);
                        String string = bundle2.getString("Type");
                        if (string.equals("Header")) {
                            if (view == null) {
                                view = LayoutInflater.from(UTDDViewerFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_database_header, viewGroup2, false);
                                view.setTag(view.findViewById(C5562R.id.header_text));
                            }
                            textView = (TextView) view.getTag();
                            str = "Text";
                        } else {
                            if (!string.equals("InItem")) {
                                if (string.equals("DItemUp")) {
                                    if (view == null) {
                                        view = LayoutInflater.from(UTDDViewerFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_arrow_up, viewGroup2, false);
                                        TextView textView2 = (TextView) view.findViewById(C5562R.id.text);
                                        textView2.setTextColor(-16776961);
                                        view.setTag(textView2);
                                    }
                                    final Bundle bundle3 = bundle2.getBundle("Item");
                                    ((TextView) view.getTag()).setText(bundle3.getString("id2diagnosis"));
                                    imageView = (ImageView) view.findViewById(C5562R.id.next_icon);
                                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDViewerActivity.UTDDViewerFragment.1.1
                                        @Override // android.view.View.OnClickListener
                                        public void onClick(View view2) {
                                            UTDDViewerFragment.this.f89549Z4.remove(bundle3);
                                            UTDDViewerFragment.this.f89551b5.notifyDataSetChanged();
                                        }
                                    };
                                } else {
                                    if (!string.equals("DItemDown")) {
                                        if (string.equals("DItemDesc")) {
                                            if (view == null) {
                                                view = LayoutInflater.from(UTDDViewerFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text, viewGroup2, false);
                                                view.setTag(view.findViewById(C5562R.id.text));
                                            }
                                            Bundle bundle4 = bundle2.getBundle("Item");
                                            textView = (TextView) view.getTag();
                                            charSequenceFromHtml = Html.fromHtml("<font color=\"red\"><b>Signs & Symptoms: </b></font></div>" + bundle4.getString("signs").replace("&deg;", "°") + "<font color=\"red\"><b><br/><br/>Tests: </b></div></font>" + bundle4.getString("tests").replace("&deg;", "°"));
                                            textView.setText(charSequenceFromHtml);
                                        }
                                        return view;
                                    }
                                    if (view == null) {
                                        view = LayoutInflater.from(UTDDViewerFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_arrow_down, viewGroup2, false);
                                        TextView textView3 = (TextView) view.findViewById(C5562R.id.text);
                                        textView3.setTextColor(-16776961);
                                        view.setTag(textView3);
                                    }
                                    final Bundle bundle5 = bundle2.getBundle("Item");
                                    ((TextView) view.getTag()).setText(bundle5.getString("id2diagnosis"));
                                    imageView = (ImageView) view.findViewById(C5562R.id.next_icon);
                                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDViewerActivity.UTDDViewerFragment.1.2
                                        @Override // android.view.View.OnClickListener
                                        public void onClick(View view2) {
                                            UTDDViewerFragment.this.f89549Z4.add(bundle5);
                                            UTDDViewerFragment.this.f89551b5.notifyDataSetChanged();
                                        }
                                    };
                                }
                                imageView.setOnClickListener(onClickListener);
                                return view;
                            }
                            if (view == null) {
                                view = LayoutInflater.from(UTDDViewerFragment.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_arrow, viewGroup2, false);
                                view.setTag(view.findViewById(C5562R.id.text));
                            }
                            bundle2 = bundle2.getBundle("Item");
                            textView = (TextView) view.getTag();
                            str = "id1diagnosis";
                        }
                        charSequenceFromHtml = bundle2.getString(str);
                        textView.setText(charSequenceFromHtml);
                        return view;
                    }

                    @Override // android.widget.BaseAdapter, android.widget.Adapter
                    public int getViewTypeCount() {
                        return 5;
                    }

                    @Override // android.widget.BaseAdapter, android.widget.Adapter
                    public boolean hasStableIds() {
                        return false;
                    }

                    @Override // android.widget.BaseAdapter, android.widget.Adapter
                    public boolean isEmpty() {
                        return false;
                    }

                    @Override // android.widget.BaseAdapter, android.widget.ListAdapter
                    public boolean isEnabled(int i2) {
                        return getItemViewType(i2) == 1;
                    }
                };
                this.f89551b5 = baseAdapter;
                this.f89550a5.setAdapter((ListAdapter) baseAdapter);
                if (!this.f89579Q4.m71903x1()) {
                    m72827m4(this.f89568F4);
                }
                this.f89550a5.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDViewerActivity.UTDDViewerFragment.2
                    @Override // android.widget.AdapterView.OnItemClickListener
                    public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                        Bundle bundle2 = (Bundle) adapterView.getItemAtPosition(i2);
                        if (bundle2.getString("Type").equals("InItem")) {
                            UTDDViewerFragment uTDDViewerFragment = UTDDViewerFragment.this;
                            uTDDViewerFragment.f89579Q4.m71772A1(uTDDViewerFragment.f89566D4, bundle2.getBundle("Item").getString("id1"), null, null);
                        }
                    }
                });
                m15366r().setTitle(this.f89568F4);
                m15358o2(false);
                this.f89574L4.setNavigationIcon(C5562R.drawable.back_icon_small);
                this.f89574L4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDViewerActivity.UTDDViewerFragment.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        if (!UTDDViewerFragment.this.m15387y().containsKey("Dialog")) {
                            UTDDViewerFragment.this.f89579Q4.m71821W1(false);
                            return;
                        }
                        try {
                            UTDDViewerFragment.this.m15391z().m15664u().m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out).mo15144B(UTDDViewerFragment.this).mo15164r();
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                            e2.printStackTrace();
                        }
                    }
                });
                this.f89574L4.setTitle(this.f89568F4);
                this.f89574L4.mo2678z(C5562R.menu.menu_utddviewer);
                m72833q4(this.f89574L4.getMenu());
                this.f89574L4.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDViewerActivity.UTDDViewerFragment.4
                    @Override // androidx.appcompat.widget.Toolbar.OnMenuItemClickListener
                    public boolean onMenuItemClick(MenuItem menuItem) {
                        UTDDViewerFragment.this.mo15329e1(menuItem);
                        return true;
                    }
                });
                m72818g3();
                m72786G3();
                return viewInflate;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                m72779B4(e2);
                return viewInflate;
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: l1 */
        public void mo15352l1() {
            super.mo15352l1();
            m72786G3();
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: u4 */
        public void mo72759u4() throws Resources.NotFoundException {
            if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
                float dimension = m15320b0().getDimension(C5562R.dimen.toolbar_padding);
                Toolbar toolbar = this.f89574L4;
                if (toolbar != null) {
                    toolbar.setPadding(0, (int) dimension, 0, 0);
                }
            }
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new UTDDViewerFragment(), bundle);
    }
}

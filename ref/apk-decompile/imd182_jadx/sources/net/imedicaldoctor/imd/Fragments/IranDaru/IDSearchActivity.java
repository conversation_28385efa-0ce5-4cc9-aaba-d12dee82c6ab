package net.imedicaldoctor.imd.Fragments.IranDaru;

import android.content.res.Resources;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class IDSearchActivity extends iMDActivity {

    public static class IDSearchFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private AsyncTask f88298A4;

        /* renamed from: B4 */
        private SpellSearchAdapter f88299B4;

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            mo71990Q2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            mo71990Q2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            ((RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout)).setVisibility(0);
            this.f88792l4 = new StatusAdapter(m15366r(), "Search Drugs");
            this.f88299B4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "drug", null) { // from class: net.imedicaldoctor.imd.Fragments.IranDaru.IDSearchActivity.IDSearchFragment.1
                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: g0 */
                public void mo71976g0(Bundle bundle2, int i2) {
                    IDSearchFragment.this.m72468V2();
                    IDSearchFragment iDSearchFragment = IDSearchFragment.this;
                    iDSearchFragment.f88791k4.m71772A1(iDSearchFragment.f88788h4, bundle2.getString("drugId"), null, null);
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: h0 */
                public void mo71977h0(Bundle bundle2) {
                    IDSearchFragment.this.m72468V2();
                    IDSearchFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(false);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: X2 */
        public void mo71973X2() {
            this.f88299B4.m73478i0(this.f88795o4, this.f88796p4);
            this.f88803w4.setAdapter(this.f88299B4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,* from Search where drugSearch match '" + str + "*' order by drug asc");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new IDSearchFragment());
    }
}

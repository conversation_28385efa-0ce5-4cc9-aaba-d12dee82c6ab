package net.imedicaldoctor.imd.Fragments.LexiInteract;

import android.app.Dialog;
import android.content.Context;
import android.database.Cursor;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class LXIvInteractDrugList extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f88432F4;

    /* renamed from: G4 */
    private String f88433G4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        Cursor cursor = null;
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_search_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        final SearchView searchView = (SearchView) viewInflate.findViewById(C5562R.id.search_view);
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        searchView.setQueryHint("Search IV Drugs");
        int i2 = 0;
        searchView.setIconifiedByDefault(false);
        ((TextView) searchView.findViewById(C5562R.id.search_src_text)).setTextColor(m15320b0().getColor(C5562R.color.black));
        this.f88432F4 = m15387y().getBundle("db");
        final String string = m15387y().getString("Drugs");
        final CompressHelper compressHelper = new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new CursorAdapter(m15366r(), cursor, i2) { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractDrugList.1
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor2) {
                ((TextView) view.getTag()).setText(cursor2.getString(cursor2.getColumnIndex("name")));
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getItemViewType(int i3) {
                return 0;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getViewTypeCount() {
                return 1;
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor2, ViewGroup viewGroup) {
                View viewInflate2 = LayoutInflater.from(LXIvInteractDrugList.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text, viewGroup, false);
                viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                return viewInflate2;
            }
        });
        searchView.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractDrugList.2
            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: a */
            public boolean mo2516a(int i3) {
                Cursor cursorMo10512c = searchView.getSuggestionsAdapter().mo10512c();
                if (cursorMo10512c.moveToPosition(i3)) {
                    Bundle bundleM71868m2 = new CompressHelper(LXIvInteractDrugList.this.m15366r()).m71868m2(cursorMo10512c);
                    if (bundleM71868m2.containsKey("word")) {
                        searchView.m2508k0(bundleM71868m2.getString("word"), false);
                        return false;
                    }
                    ((LXIvInteract.LXIvInteractFragment) LXIvInteractDrugList.this.m15351l0()).m72341p3(bundleM71868m2);
                    LXIvInteractDrugList.this.mo15203M2();
                }
                return false;
            }

            @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
            /* renamed from: b */
            public boolean mo2517b(int i3) {
                return mo2516a(i3);
            }
        });
        searchView.setSuggestionsAdapter(new CursorAdapter(m15366r(), cursor, i2) { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractDrugList.3
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor2) {
                ((TextView) view.getTag()).setText(cursor2.getString(cursor2.getColumnIndex(cursor2.getColumnIndex("word") <= -1 ? "name" : "word")));
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor2, ViewGroup viewGroup) {
                View viewInflate2 = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell, viewGroup, false);
                viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                return viewInflate2;
            }
        });
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractDrugList.4
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(final String str) {
                if (str.length() <= 1) {
                    return false;
                }
                new AsyncTask() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractDrugList.4.1
                    @Override // android.os.AsyncTask
                    protected Object doInBackground(Object[] objArr) {
                        String[] strArrSplit = str.trim().split(StringUtils.SPACE);
                        String str2 = strArrSplit[strArrSplit.length - 1];
                        String str3 = "";
                        for (int i3 = 0; i3 < strArrSplit.length - 1; i3++) {
                            str3 = str3 + StringUtils.SPACE + strArrSplit[i3];
                        }
                        searchView.setTag(str3.trim());
                        C49054 c49054 = C49054.this;
                        ArrayList<Bundle> arrayListM71819W = compressHelper.m71819W(LXIvInteractDrugList.this.f88432F4, "Select rowid as _id,rowid,* from search where name match '" + str + "*' AND (NOT generic_id in (" + string + "))", "fsearch.db");
                        if (arrayListM71819W != null) {
                            return arrayListM71819W;
                        }
                        C49054 c490542 = C49054.this;
                        return compressHelper.m71817V(LXIvInteractDrugList.this.f88432F4, "Select rowid as _id,rowid,word from spell where word match '" + str2 + "*'");
                    }

                    @Override // android.os.AsyncTask
                    protected void onPostExecute(Object obj) {
                        searchView.getSuggestionsAdapter().mo10519m(compressHelper.m71850h((ArrayList) obj));
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, new Object[0]);
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str) {
                return false;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteractDrugList.5
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i3, long j2) {
                Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                if (cursorMo10512c.moveToPosition(i3)) {
                    ((LXIvInteract.LXIvInteractFragment) LXIvInteractDrugList.this.m15351l0()).m72341p3(new CompressHelper(LXIvInteractDrugList.this.m15366r()).m71868m2(cursorMo10512c));
                    LXIvInteractDrugList.this.mo15203M2();
                }
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

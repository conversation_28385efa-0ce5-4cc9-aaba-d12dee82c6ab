package net.imedicaldoctor.imd.Fragments.Dictionary;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.fragment.app.FragmentManager;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Data.UnzipCompleted;
import net.imedicaldoctor.imd.Decompress;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class CDicEEActivity extends ViewerHelperActivity {

    public static class CDicEEFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private String f87865X4;

        /* renamed from: Y4 */
        private int f87866Y4;

        /* renamed from: Z4 */
        private Bundle f87867Z4;

        /* renamed from: a5 */
        private String f87868a5;

        /* renamed from: b5 */
        private Bundle f87869b5;

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: N4 */
        public String m72074N4(String str) {
            return str.replace("</S>", "</SPAN>").replace("<SC=", "<SPAN CLASS=").replace("<DC=", "<DIV CLASS=").replace("<PC=", "<P CLASS=");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_cdic_e, menu);
            m72833q4(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f87867Z4 = bundle.getBundle("mSound");
                this.f87868a5 = bundle.getString("mLastSampleSoundFileName");
                this.f87869b5 = bundle.getBundle("mMean");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            this.f87866Y4++;
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicEEActivity.CDicEEFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    Object obj;
                    String str;
                    String str2;
                    Object obj2;
                    String str3;
                    try {
                        final CompressHelper compressHelper = new CompressHelper(CDicEEFragment.this.m15366r());
                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(CDicEEFragment.this.f89567E4.split("-")[1], ",,,,,");
                        String str4 = strArrSplitByWholeSeparator[0];
                        String str5 = strArrSplitByWholeSeparator[1];
                        String str6 = strArrSplitByWholeSeparator[2];
                        String str7 = CDicEEFragment.this.f89563A4;
                        if (str7 == null || str7.length() == 0) {
                            CDicEEFragment.this.f89568F4 = str6;
                            obj = "5";
                            if (str4.equals("5")) {
                                obj2 = "10";
                                Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71819W(CDicEEFragment.this.f89566D4, "select * from LongMean where id in (" + str5 + ")", "LongMean.db"));
                                CDicEEFragment.this.f87869b5 = bundleM71907z;
                                Bundle bundleM71907z2 = compressHelper.m71907z(compressHelper.m71819W(CDicEEFragment.this.f89566D4, "select * from LongSound where word ='" + bundleM71907z.getString("word") + "'", "LongMean.db"));
                                if (bundleM71907z2 == null) {
                                    bundleM71907z2 = compressHelper.m71907z(compressHelper.m71819W(CDicEEFragment.this.f89566D4, "select * from LongSound where word ='" + bundleM71907z.getString("origWord") + "'", "LongMean.db"));
                                }
                                CDicEEFragment.this.f87867Z4 = bundleM71907z2;
                                String strM71773B = compressHelper.m71773B(bundleM71907z.getString("mean"), bundleM71907z.getString("id"), "127");
                                String strM71751f = CompressHelper.m71751f(strM71773B, "<body", ">");
                                if (strM71751f == null) {
                                    strM71751f = "";
                                }
                                String strReplace = strM71773B.replace("\"js.js\"", "\"file:///android_asset/js.js\"").replace("<body" + strM71751f + ">", "<body onload=\\\"onBodyLoad();\\\" style=\\\"-webkit-text-size-adjust:200%;\" " + strM71751f + "> <script src=\"log4javascript.js\" ></script><script src=\"core.js\" ></script><script src=\"dom.js\" ></script><script src=\"domrange.js\" ></script><script src=\"wrappedrange.js\" ></script><script src=\"wrappedselection.js\" ></script><script src=\"rangy-cssclassapplier.js\" ></script><script src=\"rangy-highlighter.js\" ></script><script src=\"hightlight.js\" ></script><script src=\"find.js\" ></script>");
                                CDicEEFragment cDicEEFragment = CDicEEFragment.this;
                                cDicEEFragment.f89563A4 = strReplace;
                                cDicEEFragment.m72828n3("Longman-tmp");
                                String string = bundleM71907z.getString("resources");
                                if (string.length() != 0) {
                                    for (final String str8 : StringUtils.splitByWholeSeparator(compressHelper.m71773B(string, bundleM71907z.getString("id"), "127"), "\t")) {
                                        Decompress.m71940f(CompressHelper.m71753g1(CDicEEFragment.this.f89566D4, "LongResources.zip"), "LongResources/" + new VBHelper(CDicEEFragment.this.m15366r()).m73455q(str8, "Resources") + ".res", new UnzipCompleted() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicEEActivity.CDicEEFragment.1.1
                                            @Override // net.imedicaldoctor.imd.Data.UnzipCompleted
                                            /* renamed from: a */
                                            public void mo71929a(String str9) {
                                                super.mo71929a(str9);
                                            }

                                            @Override // net.imedicaldoctor.imd.Data.UnzipCompleted
                                            /* renamed from: b */
                                            public void mo71930b(byte[] bArr) {
                                                byte[] bArrM71899w = compressHelper.m71899w(bArr, "Resource", "127");
                                                String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str8, "/");
                                                String strM71754h1 = CompressHelper.m71754h1(CDicEEFragment.this.f89566D4, strArrSplitByWholeSeparator2[0], "Longman-tmp");
                                                File file = new File(strM71754h1);
                                                if (!file.exists()) {
                                                    file.mkdirs();
                                                }
                                                String str9 = strM71754h1 + "/" + strArrSplitByWholeSeparator2[1];
                                                File file2 = new File(str9);
                                                if (!file2.exists()) {
                                                    try {
                                                        CompressHelper.m71728D2(file2, bArrM71899w);
                                                    } catch (Exception e2) {
                                                        FirebaseCrashlytics.m48010d().m48016g(e2);
                                                        iMDLogger.m73550f("CDicEEActivity Longman", "Error in writing data to " + str9 + " : " + e2);
                                                    }
                                                }
                                                CDicEEFragment.this.f89569G4.reload();
                                            }
                                        });
                                    }
                                }
                                str2 = "Longman-tmp";
                                str3 = "Oxford-tmp";
                                str = str4;
                            } else {
                                str = str4;
                                if (str.equals("10")) {
                                    Bundle bundle2 = CDicEEFragment.this.f89566D4;
                                    obj2 = "10";
                                    StringBuilder sb = new StringBuilder();
                                    str2 = "Longman-tmp";
                                    sb.append("select * from OxfMean where id in (");
                                    sb.append(str5);
                                    sb.append(")");
                                    Bundle bundleM71907z3 = compressHelper.m71907z(compressHelper.m71819W(bundle2, sb.toString(), "OxfMean.db"));
                                    CDicEEFragment.this.f87869b5 = bundleM71907z3;
                                    String strReplace2 = compressHelper.m71773B(bundleM71907z3.getString("mean"), bundleM71907z3.getString("id"), "127").replace("<BODY", "<body onload=\"onBodyLoad();\" style=\"-webkit-text-size-adjust:200%;\"").replace(" ontouchend=\"processClick(event)\" ontouchstart=\"touchStartCallback(event)\" ontouchmove=\"touchMoveCallback(event)\"", "");
                                    String strM71751f2 = CompressHelper.m71751f(strReplace2, "<body", ">");
                                    String str9 = strM71751f2 == null ? "" : strM71751f2;
                                    String strReplace3 = strReplace2.replace("<body" + str9 + ">", "<body onload=\\\"onBodyLoad();\\\" style=\\\"-webkit-text-size-adjust:200%;\" " + str9 + "> <script src=\"log4javascript.js\" ></script><script src=\"core.js\" ></script><script src=\"dom.js\" ></script><script src=\"domrange.js\" ></script><script src=\"wrappedrange.js\" ></script><script src=\"wrappedselection.js\" ></script><script src=\"rangy-cssclassapplier.js\" ></script><script src=\"rangy-highlighter.js\" ></script><script src=\"hightlight.js\" ></script><script src=\"find.js\" ></script>");
                                    str3 = "Oxford-tmp";
                                    CDicEEFragment.this.m72828n3(str3);
                                    CDicEEFragment.this.f89563A4 = strReplace3;
                                } else {
                                    str2 = "Longman-tmp";
                                    obj2 = "10";
                                    str3 = "Oxford-tmp";
                                    if (str.equals("15")) {
                                        Bundle bundleM71907z4 = compressHelper.m71907z(compressHelper.m71819W(CDicEEFragment.this.f89566D4, "select * from Webster where id in (" + str5 + ")", "HWeb.db"));
                                        CDicEEFragment.this.f87869b5 = bundleM71907z4;
                                        CDicEEFragment cDicEEFragment2 = CDicEEFragment.this;
                                        String strM72817d4 = cDicEEFragment2.m72817d4(cDicEEFragment2.m15366r(), "WebHeader.css");
                                        CDicEEFragment cDicEEFragment3 = CDicEEFragment.this;
                                        String strM72817d42 = cDicEEFragment3.m72817d4(cDicEEFragment3.m15366r(), "WebFooter.css");
                                        CDicEEFragment.this.f89563A4 = strM72817d4.replace("[size]", "200").replace("[title]", CDicEEFragment.this.f89568F4) + ("<div class=\"\"  DIR=\"LTR\" >" + CDicEEFragment.this.m72074N4(compressHelper.m71773B(bundleM71907z4.getString("mean"), bundleM71907z4.getString("id"), "127")) + "</div>") + strM72817d42;
                                        CDicEEFragment.this.m72826m3();
                                    }
                                }
                            }
                        } else {
                            str = str4;
                            str3 = "Oxford-tmp";
                            obj2 = "10";
                            str2 = "Longman-tmp";
                            obj = "5";
                        }
                        String strM71753g1 = CompressHelper.m71753g1(CDicEEFragment.this.f89566D4, "base");
                        if (str.equals(obj)) {
                            strM71753g1 = CompressHelper.m71753g1(CDicEEFragment.this.f89566D4, str2);
                        } else if (str.equals(obj2)) {
                            strM71753g1 = CompressHelper.m71753g1(CDicEEFragment.this.f89566D4, str3);
                        }
                        CDicEEFragment.this.f87865X4 = strM71753g1;
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        CDicEEFragment.this.f89595p4 = e2.getLocalizedMessage();
                    }
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicEEActivity.CDicEEFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    String str = CDicEEFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        CDicEEFragment cDicEEFragment = CDicEEFragment.this;
                        cDicEEFragment.m72780C4(cDicEEFragment.f89595p4);
                        return;
                    }
                    if (!CDicEEFragment.this.f89579Q4.m71903x1()) {
                        CDicEEFragment cDicEEFragment2 = CDicEEFragment.this;
                        cDicEEFragment2.m72827m4(cDicEEFragment2.f89568F4);
                    }
                    CDicEEFragment cDicEEFragment3 = CDicEEFragment.this;
                    cDicEEFragment3.m72795O3(cDicEEFragment3.f89563A4, cDicEEFragment3.f87865X4);
                    CDicEEFragment.this.m72836s4();
                    CDicEEFragment.this.m72831p4();
                    CDicEEFragment.this.mo72642f3(C5562R.menu.menu_cdic_e);
                    CDicEEFragment.this.m15358o2(false);
                    CDicEEFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            menuItem.getItemId();
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws UnsupportedEncodingException {
            String strDecode;
            String str4;
            String strDecode2;
            String string;
            String strM71753g1;
            String str5;
            String strM71754h1;
            WebViewDialog webViewDialog;
            FragmentManager fragmentManagerM15283M;
            try {
                iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
                CompressHelper compressHelper = new CompressHelper(m15366r());
                if (str2.equals("about")) {
                    String strSubstring = StringUtils.splitByWholeSeparator(str3, "?")[0].substring(1);
                    String strM71753g12 = CompressHelper.m71753g1(this.f89566D4, "about" + strSubstring);
                    compressHelper.m71772A1(this.f89566D4, "URL-file://" + new File(strM71753g12).getAbsolutePath(), null, null);
                    return true;
                }
                if (str2.equals("firsttap")) {
                    iMDLogger.m73550f("DicEEShould", "First Tap");
                    return true;
                }
                if (str2.equals(Annotation.f68285k3) || (str2.equals("http") && str3.contains("localhost:"))) {
                    String strM71754h12 = CompressHelper.m71754h1(this.f89566D4, "us_", "Longman-tmp");
                    String strM71754h13 = CompressHelper.m71754h1(this.f89566D4, "uk_", "Longman-tmp");
                    try {
                        strDecode = URLDecoder.decode(str3, "UTF-8");
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("CDicEEShould", "Error in decoding " + str3 + " : " + e2);
                        strDecode = str3;
                    }
                    int iIndexOf = strDecode.indexOf(strM71754h12);
                    String strM71753g13 = CompressHelper.m71753g1(this.f89566D4, "LongSounds.zip");
                    if (iIndexOf >= 0) {
                        if (!new File(strM71753g13).exists()) {
                            CompressHelper.m71767x2(m15366r(), "Not Available", 1);
                            return true;
                        }
                        m72815c4(strM71753g13, "Sounds/" + this.f87867Z4.getString("id") + "us.aif", CompressHelper.m71753g1(this.f89566D4, "USSound" + this.f87867Z4.getString("id") + "us.aif"), "USSound");
                        return true;
                    }
                    if (strDecode.indexOf(strM71754h13) < 0) {
                        if (strDecode.indexOf("#") >= 0 || strDecode.indexOf("Oxford-tmp/hide") >= 0) {
                            return true;
                        }
                        WebViewDialog webViewDialog2 = new WebViewDialog();
                        Bundle bundle = new Bundle();
                        bundle.putBundle("db", this.f89566D4);
                        bundle.putString("url", str);
                        webViewDialog2.m15342i2(bundle);
                        webViewDialog2.mo15218Z2(true);
                        webViewDialog2.m15245A2(this, 0);
                        webViewDialog2.mo15222e3(m15283M(), "CDicEEFragment");
                        return true;
                    }
                    if (!new File(strM71753g13).exists()) {
                        CompressHelper.m71767x2(m15366r(), "Not Available", 1);
                        return true;
                    }
                    m72815c4(strM71753g13, this.f87867Z4.getString("id") + "uk.aif", CompressHelper.m71753g1(this.f89566D4, "UKSound" + this.f87867Z4.getString("id") + "uk.aif"), "UKSound");
                    return true;
                }
                if (str2.equals("actionmenu")) {
                    StringUtils.splitByWholeSeparator(str3.replace("//", ""), "&");
                    return true;
                }
                if (!str2.equals("sound")) {
                    if (str2.equals("soundagain")) {
                        m72815c4(CompressHelper.m71753g1(this.f89566D4, "LongSamples.zip"), "LongSamples/" + this.f87868a5, CompressHelper.m71754h1(this.f89566D4, "LongSamples" + this.f87868a5, "base"), this.f87868a5);
                        return true;
                    }
                    if (str2.equals(HTML.Tag.f74331C)) {
                        String strM71751f = CompressHelper.m71751f(str, "list_idx=(", ")");
                        String strM71751f2 = CompressHelper.m71751f(str, "entry_idx=(", ")");
                        CompressHelper.m71751f(str, "label=(", ")");
                        if (strM71751f.equals("5")) {
                            String strM71754h14 = CompressHelper.m71754h1(this.f89566D4, "oxfordhelp.html", "Oxford-tmp");
                            WebViewDialog webViewDialog3 = new WebViewDialog();
                            Bundle bundle2 = new Bundle();
                            bundle2.putBundle("db", this.f89566D4);
                            bundle2.putString("url", "file://" + new File(strM71754h14).getAbsolutePath());
                            webViewDialog3.m15342i2(bundle2);
                            webViewDialog3.mo15218Z2(true);
                            webViewDialog3.m15245A2(this, 0);
                            webViewDialog3.mo15222e3(m15283M(), "CDicEEFragment");
                            return true;
                        }
                        str4 = "CDicEEFragment";
                        if (!strM71751f.equals(IcyHeaders.f28171a3)) {
                            return true;
                        }
                        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71819W(this.f89566D4, "Select * from OxfExtra where id = " + strM71751f2, "OxfMean.db"));
                        String strM71773B = compressHelper.m71773B(bundleM71890s1.getString("mean"), bundleM71890s1.getString("id"), "127");
                        String str6 = "file://" + new File(CompressHelper.m71753g1(this.f89566D4, "Oxford-tmp")).getAbsolutePath() + "/";
                        webViewDialog = new WebViewDialog();
                        Bundle bundle3 = new Bundle();
                        bundle3.putBundle("db", this.f89566D4);
                        bundle3.putString("baseURL", str6);
                        bundle3.putString("htmlString", strM71773B);
                        webViewDialog.m15342i2(bundle3);
                        webViewDialog.mo15218Z2(true);
                        webViewDialog.m15245A2(this, 0);
                        fragmentManagerM15283M = m15283M();
                    } else {
                        str4 = "CDicEEFragment";
                        if (str2.equals("extsnd")) {
                            try {
                                strDecode2 = URLDecoder.decode(str, "UTF-8");
                            } catch (Exception unused) {
                                strDecode2 = str;
                            }
                            String string2 = compressHelper.m71907z(compressHelper.m71817V(this.f89566D4, "select * from OxfSample where wordId=" + this.f87869b5.getString("id") + " AND sample = '" + strDecode2 + "'")).getString("id");
                            StringBuilder sb = new StringBuilder();
                            sb.append(string2);
                            sb.append(".mp3");
                            string = sb.toString();
                            String str7 = "OxfSamples" + (str3.indexOf("43464635") < 0 ? "US" : "UK");
                            strM71753g1 = CompressHelper.m71753g1(this.f89566D4, str7 + ".zip");
                            str5 = str7 + "/" + string;
                            strM71754h1 = CompressHelper.m71754h1(this.f89566D4, str7 + string, "base");
                        } else {
                            if (!str2.equals("image")) {
                                return true;
                            }
                            String strM71751f3 = CompressHelper.m71751f(str3 + ":::", "src=", ":::");
                            String strM71754h15 = CompressHelper.m71754h1(this.f89566D4, "Images", "Oxford-tmp");
                            webViewDialog = new WebViewDialog();
                            Bundle bundle4 = new Bundle();
                            bundle4.putBundle("db", this.f89566D4);
                            bundle4.putString("baseURL", "file://" + new File(strM71754h15).getAbsolutePath() + "/");
                            bundle4.putString("htmlString", "<html><head><meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0; maximum-scale=2.0; minimum-scale=0.1; user-scalable=1\"/></head><body><img src='" + (strM71754h15 + strM71751f3 + ".jpg") + "'/></body></html>");
                            webViewDialog.m15342i2(bundle4);
                            webViewDialog.mo15218Z2(true);
                            webViewDialog.m15245A2(this, 0);
                            fragmentManagerM15283M = m15283M();
                        }
                    }
                    webViewDialog.mo15222e3(fragmentManagerM15283M, str4);
                    return true;
                }
                int iIndexOf2 = str3.indexOf("src=");
                if (iIndexOf2 < 0) {
                    string = compressHelper.m71893t1(StringUtils.splitByWholeSeparator(str3, "/")).replace(".WAV", ".mp3");
                    this.f87868a5 = string;
                    if (!new File(CompressHelper.m71753g1(this.f89566D4, "LongSamples.zip")).exists()) {
                        CompressHelper.m71767x2(m15366r(), "Not Available", 1);
                        return true;
                    }
                    strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "LongSamples.zip");
                    str5 = "LongSamples/" + string;
                    strM71754h1 = CompressHelper.m71754h1(this.f89566D4, "LongSamples" + string, "base");
                } else {
                    string = str3.substring(iIndexOf2 + 4) + ".mp3";
                    if (!new File(CompressHelper.m71753g1(this.f89566D4, "OxfSounds.zip")).exists()) {
                        CompressHelper.m71767x2(m15366r(), "Not Available", 1);
                        return true;
                    }
                    strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "OxfSounds.zip");
                    str5 = "Sounds/" + string;
                    strM71754h1 = CompressHelper.m71754h1(this.f89566D4, "OxfSamples" + string, "base");
                }
                m72815c4(strM71753g1, str5, strM71754h1, string);
                return true;
            } catch (Exception e3) {
                FirebaseCrashlytics.m48010d().m48016g(e3);
                return true;
            }
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new CDicEEFragment(), bundle);
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.app.DownloadManager;
import android.content.ActivityNotFoundException;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.ActionMode;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.NotificationCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.slidingpanelayout.widget.SlidingPaneLayout;
import androidx.viewpager.widget.ViewPager;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import com.google.common.base.Charsets;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.observers.DisposableObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;
import it.neokree.materialtabs.MaterialTabHost;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Data.HistoryAdapter;
import net.imedicaldoctor.imd.NotificationActivity;
import net.imedicaldoctor.imd.TabsPagerAdapter;
import net.imedicaldoctor.imd.Utils.iMDWebView;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.extractingFragment;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class mainActivity extends AppCompatActivity implements ActionBar.TabListener {

    /* renamed from: V3 */
    private static String f90727V3 = null;

    /* renamed from: W3 */
    public static int f90728W3 = 1234;

    /* renamed from: A3 */
    private ActionBar f90729A3;

    /* renamed from: B3 */
    private MaterialTabHost f90730B3;

    /* renamed from: D3 */
    private extractingFragment f90732D3;

    /* renamed from: E3 */
    private InstallingFragment f90733E3;

    /* renamed from: G3 */
    private boolean f90735G3;

    /* renamed from: H3 */
    private SlidingPaneLayout f90736H3;

    /* renamed from: I3 */
    private Timer f90737I3;

    /* renamed from: J3 */
    public CompressHelper f90738J3;

    /* renamed from: K3 */
    public RecyclerView f90739K3;

    /* renamed from: L3 */
    private Toolbar f90740L3;

    /* renamed from: M3 */
    private TabLayout f90741M3;

    /* renamed from: N3 */
    private DrawerLayout f90742N3;

    /* renamed from: O3 */
    public String f90743O3;

    /* renamed from: P3 */
    private long f90744P3;

    /* renamed from: y3 */
    public ViewPager f90750y3;

    /* renamed from: z3 */
    private TabsPagerAdapter f90751z3;

    /* renamed from: C3 */
    private final String[] f90731C3 = {"Titles", "Databases", "Favorites", "Content", "Store", "Account"};

    /* renamed from: F3 */
    private ActionMode f90734F3 = null;

    /* renamed from: Q3 */
    private final String f90745Q3 = "android.intent.action.DOWNLOAD_COMPLETE";

    /* renamed from: R3 */
    private final IntentFilter f90746R3 = new IntentFilter("android.intent.action.DOWNLOAD_COMPLETE");

    /* renamed from: S3 */
    private final BroadcastReceiver f90747S3 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.9
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            Cursor cursorQuery;
            try {
                long longExtra = intent.getLongExtra("extra_download_id", 0L);
                if (longExtra == mainActivity.this.f90744P3 && (cursorQuery = ((DownloadManager) mainActivity.this.getSystemService("download")).query(new DownloadManager.Query().setFilterById(longExtra))) != null && cursorQuery.moveToFirst() && cursorQuery.getInt(cursorQuery.getColumnIndex(NotificationCompat.f11720T0)) == 8) {
                    Intent intent2 = new Intent("android.intent.action.VIEW");
                    intent2.setDataAndType(Uri.fromFile(new File(mainActivity.this.f90743O3)), "application/vnd.android.package-archive");
                    intent2.setFlags(268435456);
                    mainActivity.this.startActivity(intent2);
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }
    };

    /* renamed from: T3 */
    public BroadcastReceiver f90748T3 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.19
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            mainActivity.this.m73329n1();
        }
    };

    /* renamed from: U3 */
    public BroadcastReceiver f90749U3 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.20
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            try {
                if (mainActivity.this.f90736H3.m28126l()) {
                    return;
                }
                mainActivity.this.f90736H3.m28129o();
            } catch (Exception unused) {
            }
        }
    };

    public static class DatabaseCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f90788I;

        /* renamed from: J */
        public ImageView f90789J;

        public DatabaseCellViewHolder(View view) {
            super(view);
            this.f90788I = (TextView) view.findViewById(C5562R.id.database_title);
            this.f90789J = (ImageView) view.findViewById(C5562R.id.database_image);
        }
    }

    /* renamed from: Y0 */
    private void m73308Y0(File file) {
        if (file.isDirectory()) {
            for (File file2 : file.listFiles()) {
                m73308Y0(file2);
            }
        }
        file.delete();
    }

    /* renamed from: k1 */
    private boolean m73316k1() {
        VBHelper vBHelper = new VBHelper(this);
        if (vBHelper.m73439a(vBHelper.m73456r()) == null) {
            finish();
            startActivity(new Intent(this, (Class<?>) activationActivity.class));
            return false;
        }
        try {
            int length = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::").length;
            return true;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return true;
        }
    }

    /* renamed from: o1 */
    private View m73317o1(TabLayout tabLayout, CharSequence charSequence, int i2) {
        View viewInflate = LayoutInflater.from(tabLayout.getContext()).inflate(C5562R.layout.custom_tab, (ViewGroup) tabLayout, false);
        TextView textView = (TextView) viewInflate.findViewById(C5562R.id.tab_title);
        ImageView imageView = (ImageView) viewInflate.findViewById(C5562R.id.tab_icon);
        textView.setText(charSequence);
        imageView.setImageResource(i2);
        return viewInflate;
    }

    /* JADX WARN: Removed duplicated region for block: B:6:0x0062  */
    /* renamed from: p1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void m73318p1() throws android.content.res.Resources.NotFoundException {
        /*
            r8 = this;
            r0 = **********(0x7f0a0296, float:1.834469E38)
            android.view.View r0 = r8.findViewById(r0)
            androidx.viewpager.widget.ViewPager r0 = (androidx.viewpager.widget.ViewPager) r0
            r8.f90750y3 = r0
            android.app.Application r0 = r8.getApplication()
            net.imedicaldoctor.imd.iMD r0 = (net.imedicaldoctor.imd.iMD) r0
            androidx.viewpager.widget.ViewPager r1 = r8.f90750y3
            int r1 = r1.getId()
            r0.f101670Y2 = r1
            androidx.appcompat.app.ActionBar r0 = r8.m1122F0()
            r8.f90729A3 = r0
            net.imedicaldoctor.imd.TabsPagerAdapter r0 = new net.imedicaldoctor.imd.TabsPagerAdapter
            androidx.fragment.app.FragmentManager r1 = r8.m15416k0()
            r0.<init>(r1)
            r8.f90751z3 = r0
            androidx.viewpager.widget.ViewPager r1 = r8.f90750y3
            r1.setAdapter(r0)
            androidx.appcompat.app.ActionBar r0 = r8.f90729A3
            r1 = 0
            r0.mo958m0(r1)
            androidx.viewpager.widget.ViewPager r0 = r8.f90750y3
            r2 = 6
            r0.setOffscreenPageLimit(r2)
            androidx.viewpager.widget.ViewPager r0 = r8.f90750y3
            net.imedicaldoctor.imd.Fragments.mainActivity$25 r2 = new net.imedicaldoctor.imd.Fragments.mainActivity$25
            r2.<init>()
            r0.setOnPageChangeListener(r2)
            java.lang.String[] r0 = r8.f90731C3
            int r2 = r0.length
            r3 = 0
        L49:
            if (r3 >= r2) goto Laa
            r4 = r0[r3]
            androidx.appcompat.app.ActionBar r5 = r8.f90729A3
            androidx.appcompat.app.ActionBar$Tab r5 = r5.mo920H()
            androidx.appcompat.app.ActionBar$Tab r5 = r5.mo1000n(r8)
            java.lang.String r6 = "Titles"
            boolean r6 = r4.equals(r6)
            r7 = **********(0x7f080284, float:1.8078807E38)
            if (r6 == 0) goto L66
        L62:
            r5.mo998l(r7)
            goto La2
        L66:
            java.lang.String r6 = "Databases"
            boolean r6 = r4.equals(r6)
            if (r6 == 0) goto L75
            r4 = **********(0x7f08027f, float:1.8078797E38)
        L71:
            r5.mo998l(r4)
            goto La2
        L75:
            java.lang.String r6 = "Favorites"
            boolean r6 = r4.equals(r6)
            if (r6 == 0) goto L81
            r4 = **********(0x7f080281, float:1.80788E38)
            goto L71
        L81:
            java.lang.String r6 = "Content"
            boolean r6 = r4.equals(r6)
            if (r6 == 0) goto L8a
            goto L62
        L8a:
            java.lang.String r6 = "Store"
            boolean r6 = r4.equals(r6)
            if (r6 == 0) goto L96
            r4 = **********(0x7f080280, float:1.8078799E38)
            goto L71
        L96:
            java.lang.String r6 = "Account"
            boolean r4 = r4.equals(r6)
            if (r4 == 0) goto La2
            r4 = **********(0x7f080279, float:1.8078785E38)
            goto L71
        La2:
            androidx.appcompat.app.ActionBar r4 = r8.f90729A3
            r4.mo947h(r5)
            int r3 = r3 + 1
            goto L49
        Laa:
            androidx.appcompat.app.ActionBar r0 = r8.f90729A3
            r2 = 2
            r0.mo970s0(r2)
            java.lang.String r0 = "default_preferences"
            android.content.SharedPreferences r2 = r8.getSharedPreferences(r0, r1)
            java.lang.String r3 = "Tab"
            boolean r2 = r2.contains(r3)
            r4 = 1
            if (r2 == 0) goto Lde
            android.content.SharedPreferences r0 = r8.getSharedPreferences(r0, r1)
            java.lang.String r1 = ""
            java.lang.String r0 = r0.getString(r3, r1)
            java.util.ArrayList r1 = new java.util.ArrayList
            java.lang.String[] r2 = r8.f90731C3
            java.util.List r2 = java.util.Arrays.asList(r2)
            r1.<init>(r2)
            boolean r2 = r1.contains(r0)
            if (r2 == 0) goto Lde
            int r4 = r1.indexOf(r0)
        Lde:
            androidx.viewpager.widget.ViewPager r0 = r8.f90750y3
            r0.setCurrentItem(r4)
            androidx.appcompat.app.ActionBar r0 = r8.f90729A3
            r0.mo972t0(r4)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.mainActivity.m73318p1():void");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: q1 */
    public void m73319q1() throws Resources.NotFoundException {
        TabLayout tabLayout;
        int i2;
        View viewM73317o1;
        this.f90750y3 = (ViewPager) findViewById(C5562R.id.pager);
        ((iMD) getApplication()).f101670Y2 = this.f90750y3.getId();
        this.f90750y3.setVisibility(0);
        TextView textView = (TextView) findViewById(C5562R.id.loading_first);
        if (textView != null) {
            textView.setVisibility(8);
        }
        TabsPagerAdapter tabsPagerAdapter = new TabsPagerAdapter(m15416k0());
        this.f90751z3 = tabsPagerAdapter;
        this.f90750y3.setAdapter(tabsPagerAdapter);
        this.f90750y3.setOffscreenPageLimit(6);
        this.f90741M3.setupWithViewPager(this.f90750y3);
        int i3 = 0;
        while (true) {
            String[] strArr = this.f90731C3;
            if (i3 >= strArr.length) {
                break;
            }
            String str = strArr[i3];
            TabLayout.Tab tabM40224D = this.f90741M3.m40224D(i3);
            String str2 = "Titles";
            if (str.equals("Titles")) {
                viewM73317o1 = m73317o1(this.f90741M3, str2, C5562R.drawable.tab_search);
                tabM40224D.m40295v(viewM73317o1);
            } else {
                String str3 = "Databases";
                if (str.equals("Databases")) {
                    tabLayout = this.f90741M3;
                    i2 = C5562R.drawable.tab_databases;
                } else {
                    str3 = "Favorites";
                    if (str.equals("Favorites")) {
                        tabLayout = this.f90741M3;
                        i2 = C5562R.drawable.tab_favorite;
                    } else {
                        str2 = "Content";
                        if (!str.equals("Content")) {
                            if (str.equals("Store")) {
                                tabLayout = this.f90741M3;
                                str3 = "Downloads";
                                i2 = C5562R.drawable.tab_download;
                            } else {
                                str3 = "Account";
                                if (str.equals("Account")) {
                                    tabLayout = this.f90741M3;
                                    i2 = C5562R.drawable.tab_account;
                                }
                            }
                        }
                        viewM73317o1 = m73317o1(this.f90741M3, str2, C5562R.drawable.tab_search);
                        tabM40224D.m40295v(viewM73317o1);
                    }
                }
                viewM73317o1 = m73317o1(tabLayout, str3, i2);
                tabM40224D.m40295v(viewM73317o1);
            }
            i3++;
        }
        this.f90741M3.m40247h(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.24
            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: a */
            public void mo40255a(TabLayout.Tab tab) {
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: b */
            public void mo40256b(TabLayout.Tab tab) {
                View viewM40280g = tab.m40280g();
                if (viewM40280g != null) {
                    viewM40280g.setSelected(true);
                }
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: c */
            public void mo40257c(TabLayout.Tab tab) {
                View viewM40280g = tab.m40280g();
                if (viewM40280g != null) {
                    viewM40280g.setSelected(false);
                }
            }
        });
        int iIndexOf = 1;
        if (getSharedPreferences("default_preferences", 0).contains("Tab")) {
            String string = getSharedPreferences("default_preferences", 0).getString("Tab", "");
            ArrayList arrayList = new ArrayList(Arrays.asList(this.f90731C3));
            if (arrayList.contains(string)) {
                iIndexOf = arrayList.indexOf(string);
            }
        } else {
            ArrayList<Bundle> arrayList2 = ((iMD) getApplicationContext()).f101678s;
            if (arrayList2 == null || arrayList2.size() == 0) {
                this.f90750y3.setCurrentItem(4);
                return;
            }
        }
        this.f90750y3.setCurrentItem(iIndexOf);
    }

    /* JADX WARN: Code restructure failed: missing block: B:26:0x00a4, code lost:
    
        r2 = r2 + 1;
     */
    /* renamed from: t1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private boolean m73320t1() {
        /*
            r12 = this;
            net.imedicaldoctor.imd.Data.CompressHelper r0 = r12.f90738J3
            java.util.HashSet r0 = r0.m71875o1()
            java.util.Iterator r0 = r0.iterator()
        La:
            boolean r1 = r0.hasNext()
            r2 = 0
            if (r1 == 0) goto Lab
            java.lang.Object r1 = r0.next()
            java.lang.String r1 = (java.lang.String) r1
            java.io.File r3 = new java.io.File
            r3.<init>(r1)
            net.imedicaldoctor.imd.Fragments.mainActivity$4 r4 = new net.imedicaldoctor.imd.Fragments.mainActivity$4
            r4.<init>()
            java.lang.String[] r4 = r3.list(r4)
            r5 = 1
            if (r4 == 0) goto L2d
            int r4 = r4.length
            if (r4 != 0) goto L2c
            goto L2d
        L2c:
            return r5
        L2d:
            net.imedicaldoctor.imd.Fragments.mainActivity$5 r4 = new net.imedicaldoctor.imd.Fragments.mainActivity$5
            r4.<init>()
            java.lang.String[] r3 = r3.list(r4)
            if (r3 == 0) goto La
            int r4 = r3.length
            if (r4 != 0) goto L3c
            goto La
        L3c:
            int r4 = r3.length
            if (r2 >= r4) goto La
            r4 = r3[r2]
            r6 = 1
        L42:
            r7 = 11
            if (r6 >= r7) goto Laa
            java.lang.StringBuilder r7 = new java.lang.StringBuilder
            r7.<init>()
            r7.append(r1)
            java.lang.String r8 = "/"
            r7.append(r8)
            java.lang.StringBuilder r8 = new java.lang.StringBuilder
            r8.<init>()
            java.lang.String r9 = ".zip."
            r8.append(r9)
            r8.append(r6)
            java.lang.String r8 = r8.toString()
            java.lang.String r9 = ".zip.1"
            java.lang.String r8 = r4.replace(r9, r8)
            java.lang.StringBuilder r9 = new java.lang.StringBuilder
            r9.<init>()
            java.lang.String r10 = ".zipp."
            r9.append(r10)
            r9.append(r6)
            java.lang.String r9 = r9.toString()
            java.lang.String r10 = ".zipp.1"
            java.lang.String r8 = r8.replace(r10, r9)
            r7.append(r8)
            java.lang.String r7 = r7.toString()
            java.io.File r8 = new java.io.File
            r8.<init>(r7)
            boolean r8 = r8.exists()
            if (r8 != 0) goto L94
            goto La4
        L94:
            java.io.File r8 = new java.io.File
            r8.<init>(r7)
            long r7 = r8.length()
            r9 = 52428800(0x3200000, double:2.5903269E-316)
            int r11 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            if (r11 >= 0) goto La7
        La4:
            int r2 = r2 + 1
            goto L3c
        La7:
            int r6 = r6 + 1
            goto L42
        Laa:
            return r5
        Lab:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.mainActivity.m73320t1():boolean");
    }

    /* renamed from: u1 */
    private void m73321u1() {
        try {
            ((InputMethodManager) getSystemService("input_method")).hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
            getCurrentFocus().clearFocus();
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* renamed from: v1 */
    private boolean m73322v1(View view) {
        Rect rect = new Rect();
        return view.getGlobalVisibleRect(rect) && rect.bottom > 0 && rect.top < getResources().getDisplayMetrics().heightPixels;
    }

    @Override // androidx.appcompat.app.ActionBar.TabListener
    /* renamed from: C */
    public void mo1004C(ActionBar.Tab tab, FragmentTransaction fragmentTransaction) {
    }

    @Override // androidx.appcompat.app.ActionBar.TabListener
    /* renamed from: I */
    public void mo1005I(ActionBar.Tab tab, FragmentTransaction fragmentTransaction) {
        if (this.f90738J3.m71903x1()) {
            String str = "android:switcher:" + this.f90750y3.getId() + ":" + tab.mo990d();
            if (m15416k0().m15659s0(str) != null) {
                m15416k0().m15665u1(str, 1);
            }
        }
    }

    /* renamed from: Z0 */
    public void m73323Z0() {
        Bundle bundleM71826Y0;
        try {
            if (getSharedPreferences("default_preferences", 0).getBoolean("openaftercrash", true)) {
                CompressHelper compressHelper = this.f90738J3;
                Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71825Y(compressHelper.m71852h2(), "SELECT * FROM recent order by id desc limit 1"));
                if (bundleM71890s1 == null || (bundleM71826Y0 = this.f90738J3.m71826Y0("Name", bundleM71890s1.getString("dbName"))) == null) {
                    return;
                }
                this.f90738J3.m71772A1(bundleM71826Y0, bundleM71890s1.getString("dbAddress"), null, null);
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x0083 A[PHI: r4
      0x0083: PHI (r4v6 androidx.fragment.app.Fragment) = (r4v4 androidx.fragment.app.Fragment), (r4v7 androidx.fragment.app.Fragment) binds: [B:19:0x0081, B:10:0x004a] A[DONT_GENERATE, DONT_INLINE]] */
    /* renamed from: a1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m73324a1() {
        /*
            r8 = this;
            net.imedicaldoctor.imd.Data.CompressHelper r0 = r8.f90738J3
            boolean r0 = r0.m71903x1()
            if (r0 == 0) goto L8c
            androidx.viewpager.widget.ViewPager r0 = r8.f90750y3
            int r0 = r0.getCurrentItem()
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r2 = "android:switcher:"
            r1.append(r2)
            androidx.viewpager.widget.ViewPager r2 = r8.f90750y3
            int r2 = r2.getId()
            r1.append(r2)
            java.lang.String r2 = ":"
            r1.append(r2)
            java.lang.String r1 = r1.toString()
            r2 = 0
            r3 = 0
        L2c:
            java.lang.String[] r4 = r8.f90731C3
            int r4 = r4.length
            if (r3 >= r4) goto L8c
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            r4.append(r1)
            r4.append(r3)
            java.lang.String r4 = r4.toString()
            androidx.fragment.app.FragmentManager r5 = r8.m15416k0()
            if (r3 == r0) goto L4d
            androidx.fragment.app.Fragment r4 = r5.m15659s0(r4)
            if (r4 == 0) goto L89
            goto L83
        L4d:
            r6 = 2131362030(0x7f0a00ee, float:1.834383E38)
            androidx.fragment.app.Fragment r5 = r5.m15656r0(r6)
            if (r5 == 0) goto L79
            androidx.fragment.app.FragmentManager r6 = r8.m15416k0()
            androidx.fragment.app.Fragment r4 = r6.m15659s0(r4)
            java.lang.String r6 = r4.m15347k0()
            java.lang.String r7 = r5.m15347k0()
            boolean r6 = r6.endsWith(r7)
            if (r6 == 0) goto L72
            r4.m15365q2(r2)
            r4.m15249B2(r2)
        L72:
            r5.m15365q2(r2)
            r5.m15249B2(r2)
            goto L89
        L79:
            androidx.fragment.app.FragmentManager r5 = r8.m15416k0()
            androidx.fragment.app.Fragment r4 = r5.m15659s0(r4)
            if (r4 == 0) goto L89
        L83:
            r4.m15365q2(r2)
            r4.m15249B2(r2)
        L89:
            int r3 = r3 + 1
            goto L2c
        L8c:
            r8.mo1120B0()
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.mainActivity.m73324a1():void");
    }

    /* renamed from: b1 */
    public void m73325b1(int i2) {
        if (this.f90738J3.m71903x1()) {
            String str = "android:switcher:" + this.f90750y3.getId() + ":";
            for (int i3 = 0; i3 < this.f90731C3.length; i3++) {
                String str2 = str + i3;
                FragmentManager fragmentManagerM15416k0 = m15416k0();
                if (i3 != i2) {
                    Fragment fragmentM15659s0 = fragmentManagerM15416k0.m15659s0(str2);
                    if (fragmentM15659s0 != null) {
                        fragmentM15659s0.m15365q2(false);
                        fragmentM15659s0.m15249B2(false);
                    }
                } else {
                    Fragment fragmentM15656r0 = fragmentManagerM15416k0.m15656r0(C5562R.id.container);
                    if (fragmentM15656r0 != null) {
                        Fragment fragmentM15659s02 = m15416k0().m15659s0(str2);
                        if (fragmentM15659s02.m15347k0().endsWith(fragmentM15656r0.m15347k0())) {
                            fragmentM15659s02.m15365q2(false);
                            fragmentM15659s02.m15249B2(false);
                            fragmentM15656r0.m15365q2(true);
                            fragmentM15656r0.m15249B2(true);
                        } else {
                            fragmentM15656r0.m15365q2(false);
                            fragmentM15656r0.m15249B2(false);
                        }
                    } else {
                        Fragment fragmentM15659s03 = m15416k0().m15659s0(str2);
                        if (fragmentM15659s03 != null) {
                            fragmentM15659s03.m15365q2(true);
                            fragmentM15659s03.m15249B2(true);
                        }
                    }
                }
            }
        }
        mo1120B0();
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.appcompat.app.AppCompatCallback
    /* renamed from: i */
    public void mo1138i(androidx.appcompat.view.ActionMode actionMode) {
        super.mo1138i(actionMode);
        iMDLogger.m73554j("ACtionMode", "onSupportActionModeStarted");
        Menu menuMo1408e = actionMode.mo1408e();
        if (((WebView) findViewById(C5562R.id.webView)) == null) {
            return;
        }
        for (int i2 = 0; i2 < menuMo1408e.size(); i2++) {
            MenuItem item = menuMo1408e.getItem(i2);
            Drawable icon = item.getIcon();
            if (icon != null) {
                PorterDuff.Mode mode = PorterDuff.Mode.SRC_IN;
                icon.setColorFilter(new PorterDuffColorFilter(-1, mode));
                icon.setColorFilter(new PorterDuffColorFilter(-1, mode));
                item.setIcon(icon);
                if (!item.getTitle().equals("Share")) {
                    item.getTitle().equals("Web Search");
                }
            }
        }
        actionMode.mo1409f().inflate(C5562R.menu.webview2_menu, menuMo1408e);
        actionMode.mo1412k();
        LinearLayout linearLayout = (LinearLayout) findViewById(C5562R.id.highlight_bar);
        linearLayout.setVisibility(0);
        linearLayout.startAnimation(AnimationUtils.loadAnimation(this, C5562R.anim.snackbar_show_animation));
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.appcompat.app.AppCompatCallback
    /* renamed from: j */
    public void mo1139j(androidx.appcompat.view.ActionMode actionMode) throws Resources.NotFoundException {
        super.mo1139j(actionMode);
        iMDLogger.m73554j("ACtionMode", "onSupportActionModeFinished");
        if (((WebView) findViewById(C5562R.id.webView)) == null) {
            return;
        }
        final LinearLayout linearLayout = (LinearLayout) findViewById(C5562R.id.highlight_bar);
        Animation animationLoadAnimation = AnimationUtils.loadAnimation(this, C5562R.anim.snackbar_hide_animation);
        animationLoadAnimation.setAnimationListener(new Animation.AnimationListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.1
            @Override // android.view.animation.Animation.AnimationListener
            public void onAnimationEnd(Animation animation) {
                linearLayout.setVisibility(8);
            }

            @Override // android.view.animation.Animation.AnimationListener
            public void onAnimationRepeat(Animation animation) {
            }

            @Override // android.view.animation.Animation.AnimationListener
            public void onAnimationStart(Animation animation) {
            }
        });
        linearLayout.startAnimation(animationLoadAnimation);
    }

    /* renamed from: j1 */
    public String m73326j1(byte[] bArr) {
        StringBuffer stringBuffer = new StringBuffer();
        for (byte b2 : bArr) {
            int i2 = b2 & 255;
            if (i2 < 16) {
                stringBuffer.append("0");
            }
            stringBuffer.append(Integer.toHexString(i2));
        }
        return stringBuffer.toString();
    }

    /* renamed from: l1 */
    public void m73327l1(final boolean z) {
        Log.e("checkAppUpdate", "checking app update");
        try {
            if (new File(this.f90738J3.m71816U1() + "/version.txt").exists()) {
                new File(this.f90738J3.m71816U1() + "/version.txt").delete();
            }
            this.f90738J3.m71812T0(this.f90738J3.m71790J() + "/v.txt", this.f90738J3.m71816U1() + "/version.txt").m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.21
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(String str) throws Throwable {
                }
            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.22
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Throwable th) throws Throwable {
                    try {
                        iMDLogger.m73550f("checkAppUpdate", "Error in checking update " + th.getMessage());
                        th.printStackTrace();
                        if (z) {
                            CompressHelper.m71767x2(mainActivity.this, "Error in checking update", 1);
                        }
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                    }
                }
            }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.23
                @Override // io.reactivex.rxjava3.functions.Action
                public void run() throws Throwable {
                    try {
                        String strReplace = CompressHelper.m71750e2(new File(mainActivity.this.f90738J3.m71816U1() + "/version.txt")).replace(StringUtils.f103471LF, "");
                        int i2 = mainActivity.this.getPackageManager().getPackageInfo(mainActivity.this.getPackageName(), 0).versionCode;
                        final int iIntValue = Integer.valueOf(strReplace).intValue();
                        iMDLogger.m73554j("checkAppUpdate", "current version : " + i2 + " , UpdateVersion : " + iIntValue);
                        if (iIntValue > i2) {
                            if (mainActivity.this.getSharedPreferences("default_preferences", 0).getBoolean(iIntValue + "update", true) || z) {
                                new AlertDialog.Builder(mainActivity.this, C5562R.style.alertDialogTheme).mo1102l("An Update is available (version " + iIntValue + ") . \nWe strongly suggest that you update the app.").mo1115y("Download Update", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.23.3
                                    @Override // android.content.DialogInterface.OnClickListener
                                    public void onClick(DialogInterface dialogInterface, int i3) {
                                        String packageName = mainActivity.this.getPackageName();
                                        try {
                                            mainActivity.this.startActivity(new Intent("android.intent.action.VIEW", Uri.parse("market://details?id=" + packageName)));
                                        } catch (ActivityNotFoundException unused) {
                                            mainActivity.this.startActivity(new Intent("android.intent.action.VIEW", Uri.parse("https://play.google.com/store/apps/details?id=" + packageName)));
                                        }
                                    }
                                }).mo1109s("Ignore this version", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.23.2
                                    @Override // android.content.DialogInterface.OnClickListener
                                    public void onClick(DialogInterface dialogInterface, int i3) {
                                        mainActivity.this.getSharedPreferences("default_preferences", 0).edit().putBoolean(iIntValue + "update", false).commit();
                                    }
                                }).mo1106p("Remind me later", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.23.1
                                    @Override // android.content.DialogInterface.OnClickListener
                                    public void onClick(DialogInterface dialogInterface, int i3) {
                                    }
                                }).m1090I();
                            }
                        } else if (z) {
                            CompressHelper.m71767x2(mainActivity.this, "You have the latest version", 1);
                        }
                        new File(mainActivity.this.f90738J3.m71816U1() + "/version.txt").delete();
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        if (z) {
                            CompressHelper.m71767x2(mainActivity.this, "Error in Checking update", 1);
                        }
                        iMDLogger.m73550f("checkAppUpdate", "Error in reading version.txt " + e2.getMessage());
                        e2.printStackTrace();
                    }
                }
            });
        } catch (Exception unused) {
            Log.e("MainActivity", "Error in app update");
        }
    }

    /* renamed from: m1 */
    public void m73328m1() {
        Bundle extras = getIntent().getExtras();
        if (extras == null || !extras.containsKey("title")) {
            return;
        }
        String string = extras.getString("title");
        String string2 = extras.getString(Annotation.f68283i3);
        String string3 = extras.getString(HTML.Tag.f74331C);
        getIntent().replaceExtras(new Bundle());
        Intent intent = new Intent(this, (Class<?>) NotificationActivity.class);
        intent.putExtra("title", string);
        intent.putExtra(Annotation.f68283i3, string2);
        intent.putExtra(HTML.Tag.f74331C, string3);
        intent.addFlags(268435456);
        startActivity(intent);
    }

    /* renamed from: n1 */
    public void m73329n1() {
        try {
            if (m73320t1() && m15416k0().m15659s0("Installing") == null) {
                InstallingFragment installingFragment = new InstallingFragment();
                this.f90733E3 = installingFragment;
                installingFragment.m15342i2(null);
                this.f90733E3.mo15218Z2(false);
                this.f90733E3.mo15222e3(m15416k0(), "Installing");
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public void onActionModeFinished(ActionMode actionMode) {
        iMDLogger.m73554j("ACtionMode", "onActionModeFinished");
        if (((WebView) findViewById(C5562R.id.webView)) == null) {
            return;
        }
        if (actionMode == null) {
            actionMode = this.f90734F3;
        }
        if (Build.VERSION.SDK_INT <= 22) {
            if (!new File(new CompressHelper(this).m71797M1() + "/action.txt").exists()) {
                return;
            }
        }
        actionMode.getMenu().clear();
        iMDWebView imdwebview = (iMDWebView) findViewById(C5562R.id.webView);
        if (imdwebview != null) {
            imdwebview.m73433g("console.log('finisham,,,,,');");
        }
        super.onActionModeFinished(actionMode);
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x0040  */
    @Override // android.app.Activity, android.view.Window.Callback
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void onActionModeStarted(android.view.ActionMode r7) {
        /*
            r6 = this;
            java.lang.String r0 = "ACtionMode"
            java.lang.String r1 = "onActionModeStarted"
            net.imedicaldoctor.imd.iMDLogger.m73554j(r0, r1)
            r0 = **********(0x7f0a03b1, float:1.8345263E38)
            android.view.View r1 = r6.findViewById(r0)
            android.webkit.WebView r1 = (android.webkit.WebView) r1
            if (r1 != 0) goto L13
            return
        L13:
            r6.f90734F3 = r7
            int r2 = android.os.Build.VERSION.SDK_INT
            r3 = 22
            if (r2 > r3) goto L40
            java.io.File r3 = new java.io.File
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            net.imedicaldoctor.imd.Data.CompressHelper r5 = new net.imedicaldoctor.imd.Data.CompressHelper
            r5.<init>(r6)
            java.lang.String r5 = r5.m71797M1()
            r4.append(r5)
            java.lang.String r5 = "/action.txt"
            r4.append(r5)
            java.lang.String r4 = r4.toString()
            r3.<init>(r4)
            boolean r3 = r3.exists()
            if (r3 == 0) goto L8b
        L40:
            boolean r1 = r1.isFocused()
            if (r1 != 0) goto L47
            return
        L47:
            android.view.Menu r1 = r7.getMenu()
            r1.clear()
            r1 = 30
            if (r2 <= r1) goto L5a
            r1 = 100
            net.imedicaldoctor.imd.Fragments.C5283a.m72958a(r7, r1)
            r7.finish()
        L5a:
            android.view.View r0 = r6.findViewById(r0)
            net.imedicaldoctor.imd.Utils.iMDWebView r0 = (net.imedicaldoctor.imd.Utils.iMDWebView) r0
            if (r0 == 0) goto L8b
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r2 = "getRect("
            r1.append(r2)
            int r2 = r0.getWidth()
            r1.append(r2)
            java.lang.String r2 = ","
            r1.append(r2)
            int r2 = r0.getHeight()
            r1.append(r2)
            java.lang.String r2 = ")"
            r1.append(r2)
            java.lang.String r1 = r1.toString()
            r0.m73433g(r1)
        L8b:
            super.onActionModeStarted(r7)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.mainActivity.onActionModeStarted(android.view.ActionMode):void");
    }

    @Override // androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, android.app.Activity
    protected void onActivityResult(int i2, int i3, Intent intent) {
        if (intent == null) {
            return;
        }
        LocalBroadcastManager.m16410b(this).m16413d(new Intent("referesh.account.visible"));
        super.onActivityResult(i2, i3, intent);
    }

    @Override // android.view.ComponentActivity, android.app.Activity
    public void onBackPressed() {
        SlidingPaneLayout slidingPaneLayout;
        if (this.f90738J3.m71903x1()) {
            boolean zM71821W1 = this.f90738J3.m71821W1(false);
            if (!zM71821W1 && (slidingPaneLayout = (SlidingPaneLayout) findViewById(C5562R.id.sliding_layout)) != null) {
                if (slidingPaneLayout.m28126l()) {
                    zM71821W1 = this.f90738J3.m71821W1(true);
                } else {
                    slidingPaneLayout.m28129o();
                }
            }
            if (zM71821W1) {
                return;
            }
        }
        if (m15416k0().m15560B0() > 0) {
            m15416k0().m15657r1();
            m15416k0().m15651p(new FragmentManager.OnBackStackChangedListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.6
                @Override // androidx.fragment.app.FragmentManager.OnBackStackChangedListener
                /* renamed from: a */
                public void mo15700a() {
                    mainActivity mainactivity = mainActivity.this;
                    mainactivity.m73325b1(mainactivity.f90750y3.getCurrentItem());
                }
            });
            this.f90735G3 = false;
        } else {
            if (this.f90735G3) {
                super.onBackPressed();
                return;
            }
            this.f90735G3 = true;
            CompressHelper.m71767x2(this, "Please click BACK again to exit", 0);
            new Handler().postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.7
                @Override // java.lang.Runnable
                public void run() {
                    mainActivity.this.f90735G3 = false;
                }
            }, ExoPlayer.f21773a1);
        }
    }

    @Override // androidx.appcompat.app.AppCompatActivity, android.view.ComponentActivity, android.app.Activity, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        if (!this.f90738J3.m71903x1() || this.f90736H3 == null || this.f90729A3 == null || this.f90750y3 == null) {
            return;
        }
        iMDLogger.m73550f("mainActivity", "ON Configuration changed");
        iMDLogger.m73550f("mainActivity", "isOpen = " + this.f90736H3.m28126l());
        iMDLogger.m73550f("mainActivity", "isSlidable = " + this.f90736H3.m28127m());
        this.f90736H3.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.8
            @Override // java.lang.Runnable
            public void run() {
                ActionBar actionBar;
                int i2;
                if (mainActivity.this.f90736H3.m28126l()) {
                    mainActivity mainactivity = mainActivity.this;
                    mainactivity.m73325b1(mainactivity.f90750y3.getCurrentItem());
                    actionBar = mainActivity.this.f90729A3;
                    i2 = 2;
                } else {
                    mainActivity.this.m73324a1();
                    actionBar = mainActivity.this.f90729A3;
                    i2 = 0;
                }
                actionBar.mo970s0(i2);
            }
        }, 700L);
    }

    @Override // androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) throws Resources.NotFoundException {
        FrameLayout.LayoutParams layoutParams;
        View view;
        int i2;
        super.onCreate(bundle);
        if (getSharedPreferences("default_preferences", 0).getBoolean("dark", false)) {
            AppCompatDelegate.m1154c0(2);
        } else {
            AppCompatDelegate.m1154c0(1);
        }
        if (getSharedPreferences("default_preferences", 0).getBoolean("wakelock", true)) {
            getWindow().addFlags(128);
        }
        try {
            this.f90738J3 = new CompressHelper(this);
            Timer timer = new Timer();
            this.f90737I3 = timer;
            timer.schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.10
                @Override // java.util.TimerTask, java.lang.Runnable
                public void run() {
                    mainActivity.this.m73327l1(false);
                }
            }, 40000L);
            setContentView(C5562R.layout.activity_main);
            setTitle("");
            Toolbar toolbar = (Toolbar) findViewById(C5562R.id.toolbar);
            this.f90740L3 = toolbar;
            toolbar.m2666Y();
            m1129P0(this.f90740L3);
            m1122F0();
            this.f90740L3.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.11
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    mainActivity.this.m73332w1();
                }
            });
            this.f90741M3 = (TabLayout) findViewById(C5562R.id.tabs);
            DrawerLayout drawerLayout = (DrawerLayout) findViewById(C5562R.id.drawer_layout);
            this.f90742N3 = drawerLayout;
            drawerLayout.m14272a(new DrawerLayout.DrawerListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.12
                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: a */
                public void mo1008a(View view2) {
                    mainActivity mainactivity = mainActivity.this;
                    mainactivity.f90739K3.setAdapter(new HistoryAdapter(mainactivity, mainactivity.f90742N3));
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: b */
                public void mo1009b(View view2) {
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: c */
                public void mo1010c(int i3) {
                }

                @Override // androidx.drawerlayout.widget.DrawerLayout.DrawerListener
                /* renamed from: d */
                public void mo1011d(View view2, float f2) {
                }
            });
            RecyclerView recyclerView = (RecyclerView) findViewById(C5562R.id.drawer_view);
            this.f90739K3 = recyclerView;
            recyclerView.setLayoutManager(new LinearLayoutManager(this, 1, false));
            this.f90739K3.m27459p(new CustomItemDecoration(this));
            LocalBroadcastManager.m16410b(this).m16412c(this.f90747S3, this.f90746R3);
            if (this.f90738J3.m71903x1()) {
                TextView textView = (TextView) findViewById(C5562R.id.first_title);
                if (textView != null) {
                    try {
                        i2 = getPackageManager().getPackageInfo(getPackageName(), 0).versionCode;
                    } catch (Exception unused) {
                        i2 = 0;
                    }
                    textView.setText("iMD - Medical Resources (" + i2 + ")");
                }
                LocalBroadcastManager.m16410b(this).m16412c(this.f90749U3, new IntentFilter("showLeftPane"));
                SlidingPaneLayout slidingPaneLayout = (SlidingPaneLayout) findViewById(C5562R.id.sliding_layout);
                this.f90736H3 = slidingPaneLayout;
                slidingPaneLayout.setShadowResourceLeft(C5562R.drawable.slide_shadow);
                this.f90736H3.setSliderFadeColor(Color.parseColor("#FFFFFF"));
                FrameLayout frameLayout = (FrameLayout) findViewById(C5562R.id.detail_container);
                boolean z = getSharedPreferences("default_preferences", 0).getBoolean("Fullscreen", true);
                if (z) {
                    frameLayout.setLayoutParams(new SlidingPaneLayout.LayoutParams(-1, -1));
                }
                ImageButton imageButton = (ImageButton) findViewById(C5562R.id.menu_button_2);
                if (z) {
                    imageButton.setVisibility(0);
                } else {
                    imageButton.setVisibility(8);
                }
                imageButton.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.13
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view2) {
                        if (mainActivity.this.f90736H3.m28126l()) {
                            mainActivity.this.f90736H3.m28119c();
                        } else {
                            mainActivity.this.f90736H3.m28129o();
                        }
                    }
                });
                slidingPaneLayout.setPanelSlideListener(new SlidingPaneLayout.PanelSlideListener() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.14
                    @Override // androidx.slidingpanelayout.widget.SlidingPaneLayout.PanelSlideListener
                    /* renamed from: a */
                    public void mo28137a(View view2, float f2) {
                    }

                    @Override // androidx.slidingpanelayout.widget.SlidingPaneLayout.PanelSlideListener
                    /* renamed from: b */
                    public void mo28138b(View view2) {
                        mainActivity mainactivity = mainActivity.this;
                        mainactivity.m73325b1(mainactivity.f90750y3.getCurrentItem());
                        if (mainActivity.this.f90729A3 != null) {
                            mainActivity.this.f90729A3.mo970s0(2);
                        }
                    }

                    @Override // androidx.slidingpanelayout.widget.SlidingPaneLayout.PanelSlideListener
                    /* renamed from: c */
                    public void mo28139c(View view2) {
                        mainActivity.this.m73324a1();
                        if (mainActivity.this.f90729A3 != null) {
                            mainActivity.this.f90729A3.mo970s0(0);
                        }
                    }
                });
                this.f90736H3.m28129o();
            }
            iMDLogger.m73554j("OnCreate", "OnCreate");
            if (getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
                if (this.f90738J3.m71903x1()) {
                    getWindow().setFlags(67108864, 67108864);
                    layoutParams = (FrameLayout.LayoutParams) this.f90736H3.getLayoutParams();
                    layoutParams.setMargins(0, -m73331s1(), 0, 0);
                    view = this.f90736H3;
                } else {
                    getWindow().setFlags(67108864, 67108864);
                    layoutParams = (FrameLayout.LayoutParams) this.f90742N3.getLayoutParams();
                    layoutParams.setMargins(0, -m73331s1(), 0, 0);
                    view = this.f90742N3;
                }
                view.setLayoutParams(layoutParams);
                float dimension = getResources().getDimension(C5562R.dimen.toolbar_padding);
                AppBarLayout appBarLayout = (AppBarLayout) findViewById(C5562R.id.appbar);
                if (appBarLayout != null) {
                    appBarLayout.setPadding(0, (int) dimension, 0, 0);
                }
            }
            m73316k1();
            this.f90738J3.m71807R0(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.15
                @Override // java.lang.Runnable
                public void run() {
                    mainActivity.this.f90738J3.m71861k0();
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.16
                @Override // java.lang.Runnable
                public void run() throws Resources.NotFoundException {
                    mainActivity.this.m73319q1();
                    if (mainActivity.this.getIntent().hasExtra("crash")) {
                        mainActivity.this.m73323Z0();
                    }
                }
            });
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onDestroy() {
        try {
            LocalBroadcastManager.m16410b(this).m16415f(this.f90747S3);
            if (this.f90738J3.m71903x1()) {
                LocalBroadcastManager.m16410b(this).m16415f(this.f90749U3);
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        super.onDestroy();
    }

    @Override // android.view.ComponentActivity, android.app.Activity
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onPause() {
        LocalBroadcastManager.m16410b(this).m16415f(this.f90748T3);
        super.onPause();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, android.app.Activity
    public void onRequestPermissionsResult(int i2, @NonNull String[] strArr, @NonNull int[] iArr) {
        super.onRequestPermissionsResult(i2, strArr, iArr);
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onResume() throws Resources.NotFoundException {
        super.onResume();
        LocalBroadcastManager.m16410b(this).m16412c(this.f90748T3, new IntentFilter("checkzip"));
        new Handler().postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.2
            @Override // java.lang.Runnable
            public void run() {
                mainActivity.this.m73329n1();
            }
        }, 1000L);
        try {
            ((iMD) getApplicationContext()).m73538b();
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        m73328m1();
        iMD imd = (iMD) getApplicationContext();
        String str = imd.f101668X2;
        if (str == null || str.length() <= 0) {
            return;
        }
        final String str2 = imd.f101668X2;
        imd.f101668X2 = null;
        this.f90750y3.setCurrentItem(4);
        new Handler().postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.3
            @Override // java.lang.Runnable
            public void run() {
                downloadFragment downloadfragment = ((iMD) mainActivity.this.getApplicationContext()).f101675c3;
                if (downloadfragment.m73207K3()) {
                    downloadfragment.m73209M2();
                }
                mainActivity.this.f90741M3.m40224D(0).m40291r();
                downloadfragment.f90500m4.m2508k0(str2, true);
            }
        }, 1000L);
    }

    /* renamed from: r1 */
    public long m73330r1(String str) {
        Cursor cursorQuery = ((DownloadManager) getSystemService("download")).query(new DownloadManager.Query());
        cursorQuery.moveToFirst();
        new ArrayList();
        for (int i2 = 0; i2 < cursorQuery.getCount(); i2++) {
            cursorQuery.moveToPosition(i2);
            String string = cursorQuery.getString(cursorQuery.getColumnIndex("uri"));
            long j2 = cursorQuery.getLong(cursorQuery.getColumnIndex("_id"));
            iMDLogger.m73550f("URI", string);
            if (string.startsWith(str)) {
                return j2;
            }
        }
        return 0L;
    }

    @Override // androidx.appcompat.app.ActionBar.TabListener
    /* renamed from: s */
    public void mo1006s(ActionBar.Tab tab, FragmentTransaction fragmentTransaction) throws Resources.NotFoundException {
        ActionBar actionBarM1122F0;
        CharSequence charSequenceMo992f;
        this.f90750y3.setCurrentItem(tab.mo990d());
        f90727V3 = String.valueOf(tab.mo990d());
        if (this.f90738J3.m71903x1()) {
            actionBarM1122F0 = m1122F0();
            charSequenceMo992f = null;
        } else {
            actionBarM1122F0 = m1122F0();
            charSequenceMo992f = tab.mo992f();
        }
        actionBarM1122F0.mo910A0(charSequenceMo992f);
        ((iMD) getApplication()).f101672Z2 = tab.mo990d();
        mo1120B0();
    }

    /* renamed from: s1 */
    public int m73331s1() {
        int identifier = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (identifier > 0) {
            return getResources().getDimensionPixelSize(identifier);
        }
        return 0;
    }

    @Override // android.app.Activity, android.content.ContextWrapper, android.content.Context
    public void startActivity(Intent intent) {
        super.startActivity(intent);
        overridePendingTransition(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out);
    }

    /* renamed from: w1 */
    public void m73332w1() {
        this.f90742N3.m14260K(3);
    }

    /* renamed from: x1 */
    public void m73333x1() {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.17
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@io.reactivex.rxjava3.annotations.NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                Iterator<String> it2 = mainActivity.this.f90738J3.m71900w1().iterator();
                while (it2.hasNext()) {
                    String next = it2.next();
                    iMDLogger.m73550f("Root path : ", next);
                    mainActivity.this.f90738J3.f87352c = null;
                    mainActivity.this.f90738J3.m71856i2(new File(next).listFiles());
                }
                try {
                    String strEncodeToString = Base64.encodeToString(CompressHelper.m71747d1(mainActivity.this.f90738J3.f87352c.getBytes(Charsets.f60305c)), 0);
                    mainActivity.this.f90738J3.m71878p0("checkAccount|||||" + new VBHelper(mainActivity.this).m73451m() + "|||||" + strEncodeToString).m59701h6(Schedulers.m61579e()).m59775s4(Schedulers.m61579e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.17.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(String str) throws Throwable {
                            StringUtils.splitByWholeSeparator(str, "|||||");
                        }
                    }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.17.2
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(Throwable th) throws Throwable {
                        }
                    }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.17.3
                        @Override // io.reactivex.rxjava3.functions.Action
                        public void run() throws Throwable {
                        }
                    });
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
                observableEmitter.onComplete();
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(Schedulers.m61579e()).mo59651a(new DisposableObserver<String>() { // from class: net.imedicaldoctor.imd.Fragments.mainActivity.18
            @Override // io.reactivex.rxjava3.core.Observer
            /* renamed from: c, reason: merged with bridge method [inline-methods] */
            public void onNext(@io.reactivex.rxjava3.annotations.NonNull String str) {
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onComplete() {
            }

            @Override // io.reactivex.rxjava3.core.Observer
            public void onError(@io.reactivex.rxjava3.annotations.NonNull Throwable th) {
            }
        });
    }

    /* renamed from: y1 */
    public void m73334y1() {
        LocalBroadcastManager.m16410b(this).m16413d(new Intent("reload"));
        this.f90733E3.mo15205N2();
        this.f90733E3 = null;
    }
}

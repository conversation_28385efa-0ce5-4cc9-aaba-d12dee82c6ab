package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Lexi.LXSectionsViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class EPOTableViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public ArrayList<Bundle> f88230X4;

    /* renamed from: Y4 */
    public int f88231Y4;

    /* renamed from: Z4 */
    public String f88232Z4;

    /* renamed from: I4 */
    public void m72247I4(String str, int i2) {
        Bundle bundle = new Bundle();
        bundle.putString("sequence", String.valueOf(i2));
        bundle.putString("label", str);
        this.f88230X4.add(bundle);
    }

    /* renamed from: J4 */
    public String m72248J4(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        int i2 = this.f88231Y4 + 1;
        this.f88231Y4 = i2;
        String strValueOf = String.valueOf(i2);
        return "<a name=\"f" + strValueOf + "\"><div id=\"h" + strValueOf + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" onclick=\"collapse(f" + strValueOf + ");toggleHeaderExpanded(h" + strValueOf + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + strValueOf + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
    }

    /* renamed from: K4 */
    public String m72249K4(String str, String str2) {
        if (str != null && str.length() != 0) {
            CompressHelper compressHelper = this.f89579Q4;
            Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71819W(this.f89566D4, "select * from " + str2 + "_string where id=" + str, this.f88232Z4));
            if (bundleM71890s1 != null && bundleM71890s1.size() != 0) {
                return bundleM71890s1.getString("STRING");
            }
        }
        return "";
    }

    /* renamed from: L4 */
    public String m72250L4(String str) {
        return m72249K4(str, "general");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f88232Z4 = "RX.sqlite";
        if (m15387y() == null) {
            return this.f89565C4;
        }
        try {
            String str = this.f89563A4;
            if (str == null || str.length() == 0) {
                this.f88231Y4 = 0;
                this.f88230X4 = new ArrayList<>();
                iMDLogger.m73550f("Loading Document", this.f89567E4);
                String str2 = this.f89567E4.split("-")[1];
                ArrayList<Bundle> arrayListM71819W = this.f89579Q4.m71819W(this.f89566D4, "Select * from content_table where ID=" + str2, this.f88232Z4);
                if (arrayListM71819W != null && arrayListM71819W.size() != 0) {
                    Bundle bundle2 = arrayListM71819W.get(0);
                    this.f89568F4 = bundle2.getString("NAME");
                    ArrayList<Bundle> arrayListM71819W2 = this.f89579Q4.m71819W(this.f89566D4, "Select * from content_table_entry where table_id=" + bundle2.getString("ID") + " order by display_order asc", this.f88232Z4);
                    if (arrayListM71819W2 == null) {
                        arrayListM71819W2 = new ArrayList<>();
                    }
                    Iterator<Bundle> it2 = arrayListM71819W2.iterator();
                    String str3 = "";
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        String strM72250L4 = m72250L4(next.getString("HEADER_STRING_ID"));
                        String strM72248J4 = "<div style=\"margin:10px\"><div class=\"cellTitle\">" + m72250L4(next.getString("BRACKET_STRING_ID")) + "</div><div>" + m72250L4(next.getString("MSG_STRING_ID")) + "</div></div>";
                        if (strM72250L4.length() > 0) {
                            strM72248J4 = m72248J4(strM72250L4, "", "LTR", strM72248J4, "", "margin-left: 5px", "");
                            m72247I4(strM72250L4, this.f88231Y4);
                        }
                        str3 = str3 + strM72248J4;
                    }
                    String strM72817d4 = m72817d4(m15366r(), "EPOHeader.css");
                    String strM72817d42 = m72817d4(m15366r(), "EPOFooter.css");
                    this.f89563A4 = strM72817d4.replace("[size]", "200").replace("[title]", this.f89568F4).replace("[include]", "") + str3.replace("..", ".") + strM72817d42;
                }
                CompressHelper.m71767x2(m15366r(), "Document doesn't exist", 1);
                return this.f89565C4;
            }
            m72795O3(this.f89563A4, CompressHelper.m71752f1(this.f89566D4));
            m72836s4();
            m72831p4();
            mo72642f3(C5562R.menu.elsviewer2);
            m15358o2(false);
            m72786G3();
        } catch (Exception e2) {
            m72779B4(e2);
        }
        return this.f89565C4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() == C5562R.id.action_menu) {
            LXSectionsViewer lXSectionsViewer = new LXSectionsViewer();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("fields", this.f88230X4);
            lXSectionsViewer.m15342i2(bundle);
            lXSectionsViewer.mo15218Z2(true);
            lXSectionsViewer.m15245A2(this, 0);
            lXSectionsViewer.mo15222e3(m15283M(), "LXSectionsViewer");
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
        menu.removeItem(C5562R.id.action_gallery);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        if (!this.f89579Q4.m71800N1(this.f89566D4, str) && str3.contains("//current/")) {
            String str4 = StringUtils.splitByWholeSeparator(str3, "//current/")[1];
            this.f89579Q4.m71772A1(this.f89566D4, this.f89567E4 + "-" + str4, null, null);
        }
        return true;
    }
}

package net.imedicaldoctor.imd.Fragments.Dictionary;

import android.app.Dialog;
import android.content.Context;
import android.database.Cursor;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.Fragment;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.RecyclerView;
import com.itextpdf.tool.xml.html.HTML;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.CustomItemDecoration;
import net.imedicaldoctor.imd.Fragments.Dictionary.CDicEEActivity;
import net.imedicaldoctor.imd.Fragments.Dictionary.CDicEPActivity;
import net.imedicaldoctor.imd.Fragments.Lexi.LXItems;
import net.imedicaldoctor.imd.Fragments.Lexi.LXViewer;
import net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment;
import net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity;
import net.imedicaldoctor.imd.Fragments.Skyscape.SSViewerActivity;
import net.imedicaldoctor.imd.LinearLayoutManagerWithSmoothScroller;
import net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMDActivity;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class CDicSearchActivity extends iMDActivity {

    public static class CDicSearchFragment extends SearchDialogHelperFragment {

        /* renamed from: Y4 */
        private AsyncTask f87878Y4;

        /* renamed from: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity$CDicSearchFragment$5 */
        class C47605 implements ObservableOnSubscribe<String> {

            /* renamed from: a */
            final /* synthetic */ CompressHelper f87885a;

            /* renamed from: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity$CDicSearchFragment$5$1, reason: invalid class name */
            class AnonymousClass1 implements SearchView.OnQueryTextListener {

                /* renamed from: a */
                final /* synthetic */ ObservableEmitter f87887a;

                AnonymousClass1(ObservableEmitter observableEmitter) {
                    this.f87887a = observableEmitter;
                }

                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: a */
                public boolean mo2514a(String str) {
                    CDicSearchFragment cDicSearchFragment = CDicSearchFragment.this;
                    if (!cDicSearchFragment.f88745J4) {
                        return true;
                    }
                    cDicSearchFragment.f88742G4 = str;
                    this.f87887a.onNext(str);
                    return true;
                }

                @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
                /* renamed from: b */
                public boolean mo2515b(final String str) {
                    C47605 c47605 = C47605.this;
                    c47605.f87885a.m71835b0(CDicSearchFragment.this.f88744I4, "Select rowid as _id,* from search where word match '" + str + "' order by word collate nocase asc", "Search.db").m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<ArrayList<Bundle>>() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.5.1.1
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(ArrayList<Bundle> arrayList) throws Throwable {
                            if (arrayList == null) {
                                CDicSearchFragment.this.mo72093x3("Nothing Found");
                            } else {
                                CDicSearchFragment.this.mo72092w3();
                                CDicSearchFragment.this.mo72092w3();
                                final int i2 = 0;
                                if (arrayList.size() > 0) {
                                    Iterator<Bundle> it2 = arrayList.iterator();
                                    while (it2.hasNext() && !it2.next().getString("word").toLowerCase().startsWith(str.toLowerCase())) {
                                        i2++;
                                    }
                                }
                                CDicSearchFragment.this.f88748M4.m73469f0(arrayList);
                                if (arrayList.size() > 0) {
                                    CDicSearchFragment.this.f88759X4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.5.1.1.1
                                        @Override // java.lang.Runnable
                                        public void run() {
                                            CDicSearchFragment.this.f88759X4.m27425X1(i2);
                                        }
                                    }, 1000L);
                                }
                            }
                            CDicSearchFragment.this.m72446n3();
                        }
                    }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.5.1.2
                        @Override // io.reactivex.rxjava3.functions.Consumer
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public void accept(Throwable th) throws Throwable {
                        }
                    });
                    return false;
                }
            }

            C47605(CompressHelper compressHelper) {
                this.f87885a = compressHelper;
            }

            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                CDicSearchFragment.this.f88755T4.setOnQueryTextListener(new AnonymousClass1(observableEmitter));
            }
        }

        public class ListViewItemContentSearchPlaceHolder {

            /* renamed from: a */
            public TextView f87898a;

            /* renamed from: b */
            public TextView f87899b;

            public ListViewItemContentSearchPlaceHolder(View view) {
                this.f87898a = (TextView) view.findViewById(C5562R.id.title_text);
                this.f87899b = (TextView) view.findViewById(C5562R.id.subtitle_text);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: C3 */
        public Bundle m72088C3(Bundle bundle) {
            String str;
            String str2;
            Bundle bundle2 = new Bundle();
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(bundle.getString("docId"), "|");
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(bundle.getString(HTML.Tag.f74369V), "|");
            for (int i2 = 0; i2 < strArrSplitByWholeSeparator.length; i2++) {
                if (strArrSplitByWholeSeparator2.length > i2) {
                    str = strArrSplitByWholeSeparator[i2];
                    str2 = strArrSplitByWholeSeparator2[i2];
                } else {
                    str = strArrSplitByWholeSeparator[i2];
                    str2 = "";
                }
                bundle2.putString(str, str2);
            }
            return bundle2;
        }

        /* renamed from: B3 */
        public void m72089B3(final SearchView searchView) {
            searchView.setIconifiedByDefault(false);
            searchView.setQueryHint("Search Dictionary");
            m72449q3();
            searchView.setOnSuggestionListener(new SearchView.OnSuggestionListener() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.2
                @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
                /* renamed from: a */
                public boolean mo2516a(int i2) {
                    Cursor cursorMo10512c = searchView.getSuggestionsAdapter().mo10512c();
                    if (!cursorMo10512c.moveToPosition(i2)) {
                        return false;
                    }
                    String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                    if (searchView.getTag(1) != null && ((String) searchView.getTag(1)).length() > 0) {
                        string = searchView.getTag() + StringUtils.SPACE + string;
                    }
                    searchView.m2508k0(string, true);
                    return false;
                }

                @Override // androidx.appcompat.widget.SearchView.OnSuggestionListener
                /* renamed from: b */
                public boolean mo2517b(int i2) {
                    Cursor cursorMo10512c = searchView.getSuggestionsAdapter().mo10512c();
                    if (!cursorMo10512c.moveToPosition(i2)) {
                        return false;
                    }
                    String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("word"));
                    if (searchView.getTag() != null && ((String) searchView.getTag()).length() > 0) {
                        string = searchView.getTag() + StringUtils.SPACE + string;
                    }
                    searchView.m2508k0(string, true);
                    return false;
                }
            });
            ((ImageView) searchView.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    searchView.m2508k0("", false);
                    searchView.clearFocus();
                    CDicSearchFragment.this.mo72093x3("Search Anything");
                    CDicSearchFragment.this.m72446n3();
                }
            });
            searchView.setSuggestionsAdapter(new CursorAdapter(m15366r(), null, 0) { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.4
                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: e */
                public void mo2556e(View view, Context context, Cursor cursor) {
                    ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("word")));
                }

                @Override // androidx.cursoradapter.widget.CursorAdapter
                /* renamed from: j */
                public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                    View viewInflate = LayoutInflater.from(context).inflate(C5562R.layout.list_view_item_spell, viewGroup, false);
                    viewInflate.setTag(viewInflate.findViewById(C5562R.id.text));
                    return viewInflate;
                }
            });
            final CompressHelper compressHelper = new CompressHelper(m15366r());
            Observable.m59451w1(new C47605(compressHelper)).m59804x1(500L, TimeUnit.MILLISECONDS).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.6
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(String str) throws Throwable {
                    if (str.length() > 1) {
                        String[] strArrSplit = str.trim().split(StringUtils.SPACE);
                        String str2 = strArrSplit[strArrSplit.length - 1];
                        String str3 = "";
                        for (int i2 = 0; i2 < strArrSplit.length - 1; i2++) {
                            str3 = str3 + StringUtils.SPACE + strArrSplit[i2];
                        }
                        CDicSearchFragment.this.f88755T4.setTag(str3.trim());
                        compressHelper.m71835b0(CDicSearchFragment.this.f88744I4, "Select rowid as _id,word from spell where word match '" + str2 + "*'", "Search.db").m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59675d6(new Consumer<ArrayList<Bundle>>() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.6.1
                            @Override // io.reactivex.rxjava3.functions.Consumer
                            /* renamed from: a, reason: merged with bridge method [inline-methods] */
                            public void accept(ArrayList<Bundle> arrayList) throws Throwable {
                                CDicSearchFragment.this.f88755T4.getSuggestionsAdapter().mo10519m(compressHelper.m71850h(arrayList));
                            }
                        });
                    }
                }
            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.7
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Throwable th) throws Throwable {
                }
            });
            String str = this.f88742G4;
            if (str == null || str.length() <= 0) {
                return;
            }
            this.f88755T4.m2508k0(this.f88742G4, true);
        }

        /* renamed from: D3 */
        public void m72090D3(Fragment fragment, String str, Bundle bundle) {
            Bundle bundle2 = new Bundle();
            bundle2.putBundle("DB", bundle);
            bundle2.putString("URL", str);
            bundle2.putString("Dialog", IcyHeaders.f28171a3);
            fragment.m15342i2(bundle2);
            m15391z().m15664u().m15827o("something").m15813M(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out).m15803C(C5562R.id.dic, fragment).mo15164r();
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            SearchView searchView = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            this.f88755T4 = searchView;
            m72089B3(searchView);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            this.f88746K4 = new CompressHelper(m15366r());
            View viewInflate = layoutInflater.inflate(m15387y().containsKey("Dialog") ? C5562R.layout.fragment_cdic_dialog : C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88753R4 = viewInflate;
            m72447o3(bundle);
            m72443k3();
            SearchView searchView = (SearchView) this.f88753R4.findViewById(C5562R.id.search_view);
            this.f88755T4 = searchView;
            m72089B3(searchView);
            this.f88759X4 = (RecyclerView) this.f88753R4.findViewById(C5562R.id.recycler_view);
            RelativeLayout relativeLayout = (RelativeLayout) this.f88753R4.findViewById(C5562R.id.background_layout);
            if (relativeLayout != null) {
                relativeLayout.setVisibility(0);
            }
            this.f88748M4 = new ContentSearchAdapter(m15366r(), this.f88750O4, "word", "source") { // from class: net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity.CDicSearchFragment.1
                @Override // net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter
                /* renamed from: e0 */
                public void mo71953e0(Bundle bundle2, int i2) {
                    String string = bundle2.getString("word");
                    String string2 = bundle2.getString("source");
                    String string3 = bundle2.getString("sourceId");
                    String string4 = bundle2.getString("id");
                    String str = string4 + ",,,,," + string;
                    if (string2.equals("Persian")) {
                        if (CDicSearchFragment.this.m15387y().containsKey("Dialog")) {
                            CDicSearchFragment.this.m72090D3(new CDicEPActivity.CDicEPFragment(), "EP-" + str + ",,,,," + string, CDicSearchFragment.this.f88744I4);
                            return;
                        }
                        CDicSearchFragment cDicSearchFragment = CDicSearchFragment.this;
                        cDicSearchFragment.f88746K4.m71772A1(cDicSearchFragment.f88744I4, "EP-" + str + ",,,,," + string, null, null);
                    } else if (string3.equals("5") || string3.equals("10") || string3.equals("15")) {
                        new Bundle();
                        String string5 = bundle2.getString(HTML.Tag.f74369V).length() > 0 ? bundle2.getString(HTML.Tag.f74369V) : null;
                        if (CDicSearchFragment.this.m15387y().containsKey("Dialog")) {
                            CDicSearchFragment.this.m72090D3(new CDicEEActivity.CDicEEFragment(), "EE-" + string3 + ",,,,," + string4 + ",,,,," + string, CDicSearchFragment.this.f88744I4);
                            return;
                        }
                        CDicSearchFragment cDicSearchFragment2 = CDicSearchFragment.this;
                        cDicSearchFragment2.f88746K4.m71772A1(cDicSearchFragment2.f88744I4, "EE-" + string3 + ",,,,," + string4 + ",,,,," + string, null, string5);
                    } else if (string3.equals("30") || string3.equals("35") || string3.equals("40") || string3.equals("45")) {
                        Bundle bundleM71855i1 = CDicSearchFragment.this.f88746K4.m71855i1(string3.equals("30") ? "K3ZGDATA.db" : string3.equals("35") ? "K2AJDATA.db" : string3.equals("40") ? "K354DATA.db" : string3.equals("45") ? "K2WCDATA.db" : "");
                        if (bundleM71855i1 == null) {
                            CompressHelper.m71767x2(CDicSearchFragment.this.m15366r(), string2 + " Is Not Installed", 1);
                            return;
                        }
                        if (string4.contains("|")) {
                            Bundle bundle3 = new Bundle();
                            Bundle bundle4 = new Bundle();
                            bundle4.putString("docId", string4);
                            bundle4.putString("name", string);
                            bundle4.putString(HTML.Tag.f74369V, bundle2.getString(HTML.Tag.f74369V));
                            bundle3.putBundle("SelectedItem", bundle4);
                            bundle3.putBundle("DB", bundleM71855i1);
                            bundle3.putInt("Mode", 2);
                            bundle3.putBundle("GotoSections", CDicSearchFragment.this.m72088C3(bundle4));
                            CDicSearchFragment.this.f88746K4.m71798N(SSSearchActivity.class, SSSearchActivity.SSSearchFragment.class, bundle3);
                        } else {
                            if (CDicSearchFragment.this.m15387y().containsKey("Dialog")) {
                                CDicSearchFragment.this.m72090D3(new SSViewerActivity.SSViewerFragment(), string4, bundleM71855i1);
                                return;
                            }
                            CDicSearchFragment.this.f88746K4.m71772A1(bundleM71855i1, string4, null, bundle2.getString(HTML.Tag.f74369V));
                        }
                    } else if (string3.equals("20")) {
                        Bundle bundleM71855i12 = CDicSearchFragment.this.f88746K4.m71855i1("593_lww_abbrev.sqlite");
                        if (bundleM71855i12 == null) {
                            CompressHelper.m71767x2(CDicSearchFragment.this.m15366r(), string2 + " Is Not Installed", 1);
                            return;
                        }
                        CompressHelper compressHelper = CDicSearchFragment.this.f88746K4;
                        Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(bundleM71855i12, "Select count(*) as c from indexitem_document where indexitem_id=" + string4));
                        if (bundleM71907z == null || bundleM71907z.getString("c").equals(IcyHeaders.f28171a3)) {
                            CompressHelper compressHelper2 = CDicSearchFragment.this.f88746K4;
                            Bundle bundleM71907z2 = compressHelper2.m71907z(compressHelper2.m71817V(bundleM71855i12, "select document_id from indexitem_document where indexitem_id=" + string4));
                            if (CDicSearchFragment.this.m15387y().containsKey("Dialog")) {
                                CDicSearchFragment.this.m72090D3(new LXViewer.LXViewerFragment(), bundleM71907z2.getString("document_id"), bundleM71855i12);
                                return;
                            }
                            CDicSearchFragment.this.f88746K4.m71772A1(bundleM71855i12, bundleM71907z2.getString("document_id"), null, null);
                        } else {
                            Bundle bundle5 = new Bundle();
                            bundle5.putString("ParentId", string4);
                            bundle5.putInt("Mode", 2);
                            bundle5.putBundle("DB", bundleM71855i12);
                            CDicSearchFragment.this.f88746K4.m71798N(LXItems.class, LXItems.LXItemsFragment.class, bundle5);
                        }
                    }
                    try {
                        if (CDicSearchFragment.this.m15387y().containsKey("Dialog")) {
                            CDicSearchFragment.this.mo15203M2();
                        }
                    } catch (Exception unused) {
                    }
                }
            };
            mo72091g3();
            mo72093x3("Search Anything");
            if (m15387y().containsKey("Dialog")) {
                this.f88755T4.m2508k0(m15387y().getString("Dialog"), true);
                m15208Q2().getWindow().requestFeature(1);
            }
            m15358o2(false);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment
        /* renamed from: g3 */
        public void mo72091g3() {
            this.f88759X4.setItemAnimator(new DefaultItemAnimator());
            this.f88759X4.m27459p(new CustomItemDecoration(m15366r()));
            this.f88759X4.setLayoutManager(new LinearLayoutManagerWithSmoothScroller(m15366r(), 1, false));
        }

        @Override // androidx.fragment.app.DialogFragment, androidx.fragment.app.Fragment
        /* renamed from: n1 */
        public void mo15226n1() {
            super.mo15226n1();
            Dialog dialogM15208Q2 = m15208Q2();
            if (dialogM15208Q2 != null) {
                dialogM15208Q2.getWindow().setLayout(-1, -1);
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment
        /* renamed from: w3 */
        public void mo72092w3() {
            RecyclerView.Adapter adapter = this.f88759X4.getAdapter();
            ContentSearchAdapter contentSearchAdapter = this.f88748M4;
            if (adapter != contentSearchAdapter) {
                this.f88759X4.setAdapter(contentSearchAdapter);
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchDialogHelperFragment
        /* renamed from: x3 */
        public void mo72093x3(String str) {
            this.f88759X4.setAdapter(new StatusAdapter(m15366r(), str));
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new CDicSearchFragment());
    }
}

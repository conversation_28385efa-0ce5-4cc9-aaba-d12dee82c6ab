package net.imedicaldoctor.imd.Fragments.Statdx;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.exifinterface.media.ExifInterface;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;

/* loaded from: classes3.dex */
public class SDListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88977A4;

    /* renamed from: B4 */
    public String f88978B4;

    /* renamed from: C4 */
    public ArrayList<Bundle> f88979C4;

    /* renamed from: D4 */
    public ArrayList<Bundle> f88980D4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            this.f88978B4 = "0";
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDListActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            this.f88978B4 = m15387y().getString("ParentId");
        }
        this.f88979C4 = this.f88791k4.m71817V(this.f88788h4, "select * from categories where parent='" + this.f88978B4 + "'");
        this.f88980D4 = this.f88791k4.m71817V(this.f88788h4, "select * from docs where id in (Select docId from cats_docs where catId='" + this.f88978B4 + "')");
        if (this.f88979C4 == null) {
            this.f88979C4 = new ArrayList<>();
        }
        if (this.f88980D4 == null) {
            this.f88980D4 = new ArrayList<>();
        }
        this.f88792l4 = new ChaptersAdapter(m15366r(), null, "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDListActivityFragment.2
            @Override // androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: C */
            public int mo26845C(int i2) {
                return i2 < SDListActivityFragment.this.f88979C4.size() ? 0 : 1;
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter, androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: R */
            public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
                MaterialRippleLayout materialRippleLayout;
                View.OnClickListener onClickListener;
                if (viewHolder.m27811F() == 0) {
                    RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
                    final Bundle bundle2 = SDListActivityFragment.this.f88979C4.get(i2);
                    rippleTextViewHolder.f101515I.setText(bundle2.getString("title"));
                    materialRippleLayout = rippleTextViewHolder.f101516J;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDListActivityFragment.2.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            Bundle bundle3 = new Bundle();
                            bundle3.putBundle("DB", SDListActivityFragment.this.f88788h4);
                            bundle3.putString("ParentId", bundle2.getString("id"));
                            SDListActivityFragment.this.f88791k4.m71798N(SDListActivity.class, SDListActivityFragment.class, bundle3);
                        }
                    };
                } else {
                    RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                    SDListActivityFragment sDListActivityFragment = SDListActivityFragment.this;
                    final Bundle bundle3 = sDListActivityFragment.f88980D4.get(i2 - sDListActivityFragment.f88979C4.size());
                    rippleTextFullViewHolder.f101499I.setText(bundle3.getString("title"));
                    if (SDListActivityFragment.this.f88788h4.getString("Name").equals("expertpath.db")) {
                        rippleTextFullViewHolder.f101501K.setVisibility(8);
                    } else {
                        rippleTextFullViewHolder.f101501K.setVisibility(0);
                        rippleTextFullViewHolder.f101501K.setImageDrawable(SDListActivityFragment.this.m15366r().getResources().getDrawable(SDListActivityFragment.this.m72506i3(bundle3.getString("type"))));
                    }
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDListActivityFragment.2.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            SDListActivityFragment sDListActivityFragment2 = SDListActivityFragment.this;
                            sDListActivityFragment2.f88791k4.m71772A1(sDListActivityFragment2.f88788h4, "menu,,," + bundle3.getString("id"), null, null);
                        }
                    };
                }
                materialRippleLayout.setOnClickListener(onClickListener);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter, androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: T */
            public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup2, int i2) {
                if (i2 == 0) {
                    return new RippleTextViewHolder(LayoutInflater.from(this.f101430d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup2, false));
                }
                if (i2 != 1) {
                    return null;
                }
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(LayoutInflater.from(this.f101430d).inflate(C5562R.layout.list_view_item_ripple_text_full, viewGroup2, false));
                rippleTextFullViewHolder.f101500J.setVisibility(8);
                return rippleTextFullViewHolder;
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter, androidx.recyclerview.widget.RecyclerView.Adapter
            /* renamed from: b */
            public int mo26171b() {
                return SDListActivityFragment.this.f88979C4.size() + SDListActivityFragment.this.f88980D4.size();
            }
        };
        this.f88977A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", Annotation.f68283i3, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDListActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                if (SDListActivityFragment.this.f88788h4.getString("Name").equals("expertpath.db")) {
                    rippleTextFullViewHolder.f101501K.setVisibility(8);
                } else {
                    rippleTextFullViewHolder.f101501K.setVisibility(0);
                    rippleTextFullViewHolder.f101501K.setImageDrawable(SDListActivityFragment.this.m15366r().getResources().getDrawable(SDListActivityFragment.this.m72506i3(bundle2.getString("typeText"))));
                }
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                if (bundle2.getString("type").equals(ExifInterface.f16326Z4)) {
                    rippleTextFullViewHolder.f101500J.setVisibility(8);
                } else {
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                    rippleTextFullViewHolder.f101500J.setText(bundle2.getString(Annotation.f68283i3));
                }
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Statdx.SDListActivityFragment.3.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        CompressHelper compressHelper;
                        Bundle bundle3;
                        StringBuilder sb;
                        String str;
                        if (bundle2.getString("type").equals(ExifInterface.f16326Z4)) {
                            SDListActivityFragment sDListActivityFragment = SDListActivityFragment.this;
                            compressHelper = sDListActivityFragment.f88791k4;
                            bundle3 = sDListActivityFragment.f88788h4;
                            sb = new StringBuilder();
                            str = "case,,,";
                        } else {
                            SDListActivityFragment sDListActivityFragment2 = SDListActivityFragment.this;
                            compressHelper = sDListActivityFragment2.f88791k4;
                            bundle3 = sDListActivityFragment2.f88788h4;
                            sb = new StringBuilder();
                            str = "menu,,,";
                        }
                        sb.append(str);
                        sb.append(bundle2.getString("contentId"));
                        compressHelper.m71772A1(bundle3, sb.toString(), null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                SDListActivityFragment.this.m72468V2();
                SDListActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                return new RippleTextFullViewHolder(view);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88977A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88977A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match '(text:" + str + "*) AND (type:1 OR type:3)' order by type asc");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    /* renamed from: i3 */
    public int m72506i3(String str) {
        return (str.equals("di3-dx") || str.equals("dx")) ? C5562R.drawable.statdx_dx : str.equals("expert-ddx") ? C5562R.drawable.statdx_ddx : str.equals("anatomymodule") ? C5562R.drawable.statdx_person : str.equals("tsm") ? C5562R.drawable.statdx_tnm : str.equals("procedure") ? C5562R.drawable.statdx_syringe : str.equals("di3-generic") ? C5562R.drawable.statdx_dx : str.equals("di3-tsm") ? C5562R.drawable.statdx_tnm : str.equals("di3-procedure") ? C5562R.drawable.statdx_syringe : str.equals("ia2-module") ? C5562R.drawable.statdx_person : str.equals("di3-expert-ddx") ? C5562R.drawable.statdx_ddx : str.equals("table") ? C5562R.drawable.statdx_table : str.equals("Case") ? C5562R.drawable.statdx_case : C5562R.drawable.statdx_dx;
    }
}

package net.imedicaldoctor.imd.Fragments.DRE;

import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.appcompat.app.AlertDialog;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class DRETestViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f87809X4;

    /* renamed from: Y4 */
    public ArrayList<String> f87810Y4;

    /* renamed from: Z4 */
    public ArrayList<Bundle> f87811Z4;

    /* renamed from: a5 */
    public ArrayList<Bundle> f87812a5;

    /* renamed from: b5 */
    public ArrayList<Bundle> f87813b5;

    /* renamed from: c5 */
    public int f87814c5;

    /* renamed from: d5 */
    public Bundle f87815d5;

    /* renamed from: e5 */
    public Date f87816e5;

    /* renamed from: f5 */
    public String f87817f5;

    /* renamed from: g5 */
    public boolean f87818g5;

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: M4 */
    public void m72049M4() {
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from images where questionId = " + this.f87813b5.get(this.f87814c5).getString("id"));
        if (arrayListM71817V == null) {
            arrayListM71817V = new ArrayList<>();
        }
        Iterator<Bundle> it2 = arrayListM71817V.iterator();
        while (it2.hasNext()) {
            String string = it2.next().getString("filename");
            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, string, "media-E");
            if (new File(strM71754h1).exists()) {
                try {
                    byte[] bArrM71899w = this.f89579Q4.m71899w(CompressHelper.m71748d2(new File(strM71754h1)), string, "127");
                    String strM71754h12 = CompressHelper.m71754h1(this.f89566D4, string, "base");
                    if (new File(strM71754h12).exists()) {
                        new File(strM71754h12).delete();
                    }
                    CompressHelper.m71728D2(new File(strM71754h12), bArrM71899w);
                    new File(strM71754h12).deleteOnExit();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                }
            }
        }
        this.f87811Z4 = arrayListM71817V;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: N4 */
    public void m72050N4(ArrayList<String> arrayList) {
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, next, "media-E");
            if (new File(strM71754h1).exists()) {
                try {
                    byte[] bArrM71899w = this.f89579Q4.m71899w(CompressHelper.m71748d2(new File(strM71754h1)), next, "127");
                    String strM71754h12 = CompressHelper.m71754h1(this.f89566D4, next, "base");
                    if (new File(strM71754h12).exists()) {
                        new File(strM71754h12).delete();
                    }
                    CompressHelper.m71728D2(new File(strM71754h12), bArrM71899w);
                    new File(strM71754h12).deleteOnExit();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                }
            }
        }
        this.f87810Y4 = arrayList;
    }

    /* renamed from: X4 */
    private void m72053X4(String str) {
        ArrayList<String> arrayList = this.f87810Y4;
        if ((arrayList == null && this.f87811Z4 == null) || arrayList.size() + this.f87811Z4.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f87810Y4.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", CompressHelper.m71754h1(this.f89566D4, next, "base"));
            bundle.putString("Description", "");
            bundle.putString("id", next);
            arrayList2.add(bundle);
        }
        Iterator<Bundle> it3 = this.f87811Z4.iterator();
        while (it3.hasNext()) {
            Bundle next2 = it3.next();
            Bundle bundle2 = new Bundle();
            bundle2.putString("ImagePath", CompressHelper.m71754h1(this.f89566D4, next2.getString("filename"), "base"));
            bundle2.putString("Description", next2.getString("title"));
            bundle2.putString("id", next2.getString("mediaId"));
            if (next2.getString("filename").endsWith(".mov")) {
                bundle2.putString("isVideo", "");
            }
            if (next2.getString("filename").endsWith(".mp4")) {
                bundle2.putString("isVideo", "");
            }
            arrayList2.add(bundle2);
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: I4 */
    public void m72054I4() {
        this.f89598s4.findItem(C5562R.id.action_previous).setEnabled(false);
        this.f89598s4.findItem(C5562R.id.action_previous).setIcon(C5562R.drawable.ic_action_previous_item_disabled);
    }

    /* renamed from: J4 */
    public void m72055J4() {
        this.f89598s4.findItem(C5562R.id.action_next).setEnabled(false);
        this.f89598s4.findItem(C5562R.id.action_next).setIcon(C5562R.drawable.ic_action_next_item_disabled);
    }

    /* renamed from: K4 */
    public void m72056K4() {
        this.f89598s4.findItem(C5562R.id.action_previous).setEnabled(true);
        this.f89598s4.findItem(C5562R.id.action_previous).setIcon(C5562R.drawable.ic_action_previous_item);
    }

    /* renamed from: L4 */
    public void m72057L4() {
        this.f89598s4.findItem(C5562R.id.action_next).setEnabled(true);
        this.f89598s4.findItem(C5562R.id.action_next).setIcon(C5562R.drawable.ic_action_next_item);
    }

    /* renamed from: Q4 */
    public String m72058Q4(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ").format(date);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return null;
    }

    /* renamed from: R4 */
    public String m72059R4(String str) {
        return str.equals(IcyHeaders.f28171a3) ? "الف" : str.equals(ExifInterface.f16317Y4) ? "ب" : str.equals(ExifInterface.f16326Z4) ? "ج" : str.equals("4") ? "د" : str.equals("5") ? "ه" : str.equals("6") ? "خ" : str.equals("7") ? "G" : str.equals("8") ? "H" : str;
    }

    /* renamed from: S4 */
    public void m72060S4(final String str, final boolean z) {
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestViewerActivityFragment.3
            /* JADX WARN: Removed duplicated region for block: B:44:0x0275  */
            /* JADX WARN: Removed duplicated region for block: B:57:0x036d  */
            /* JADX WARN: Removed duplicated region for block: B:58:0x0376  */
            /* JADX WARN: Removed duplicated region for block: B:63:0x03ac  */
            /* JADX WARN: Removed duplicated region for block: B:65:0x03b0  */
            /* JADX WARN: Removed duplicated region for block: B:68:0x03b7  */
            /* JADX WARN: Removed duplicated region for block: B:69:0x03be  */
            @Override // java.lang.Runnable
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public void run() throws java.io.IOException {
                /*
                    Method dump skipped, instructions count: 985
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.DRE.DRETestViewerActivityFragment.RunnableC47423.run():void");
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestViewerActivityFragment.4
            @Override // java.lang.Runnable
            public void run() {
                String str2 = DRETestViewerActivityFragment.this.f89595p4;
                if (str2 != null && str2.length() > 0) {
                    DRETestViewerActivityFragment dRETestViewerActivityFragment = DRETestViewerActivityFragment.this;
                    dRETestViewerActivityFragment.m72780C4(dRETestViewerActivityFragment.f89595p4);
                } else {
                    DRETestViewerActivityFragment.this.m72062U4();
                    if (z) {
                        DRETestViewerActivityFragment.this.f87817f5 = "answerSectionsa";
                    }
                }
            }
        });
    }

    /* renamed from: T4 */
    public void m72061T4() {
        m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestViewerActivityFragment.1
            @Override // java.lang.Runnable
            public void run() throws IOException {
                DRETestViewerActivityFragment.this.f87816e5 = new Date();
                DRETestViewerActivityFragment.this.f89568F4 = "سوال " + (DRETestViewerActivityFragment.this.f87814c5 + 1) + " از " + DRETestViewerActivityFragment.this.f87813b5.size();
                DRETestViewerActivityFragment dRETestViewerActivityFragment = DRETestViewerActivityFragment.this;
                StringBuilder sb = new StringBuilder();
                sb.append("question-");
                DRETestViewerActivityFragment dRETestViewerActivityFragment2 = DRETestViewerActivityFragment.this;
                sb.append(dRETestViewerActivityFragment2.f87813b5.get(dRETestViewerActivityFragment2.f87814c5).getString("id"));
                dRETestViewerActivityFragment.f89567E4 = sb.toString();
                DRETestViewerActivityFragment dRETestViewerActivityFragment3 = DRETestViewerActivityFragment.this;
                Bundle bundle = dRETestViewerActivityFragment3.f87813b5.get(dRETestViewerActivityFragment3.f87814c5);
                String string = bundle.getString("id");
                Bundle bundle2 = DRETestViewerActivityFragment.this.f87815d5;
                String string2 = "100";
                if (bundle2 != null) {
                    String string3 = bundle2.getString("id");
                    DRETestViewerActivityFragment dRETestViewerActivityFragment4 = DRETestViewerActivityFragment.this;
                    ArrayList<Bundle> arrayListM71817V = dRETestViewerActivityFragment4.f89579Q4.m71817V(dRETestViewerActivityFragment4.f89566D4, "Select * from logs where testId=" + string3 + " AND qid = " + string);
                    if (arrayListM71817V == null || arrayListM71817V.size() <= 0) {
                        if (DRETestViewerActivityFragment.this.f87815d5.getString("done").equals(IcyHeaders.f28171a3)) {
                            DRETestViewerActivityFragment.this.m72060S4("100", false);
                            return;
                        }
                    } else if (!DRETestViewerActivityFragment.this.f87815d5.getString("mode").equals("Testing")) {
                        DRETestViewerActivityFragment.this.m72060S4(arrayListM71817V.get(0).getString("selectedAnswer"), false);
                        return;
                    } else {
                        if (!DRETestViewerActivityFragment.this.f87815d5.getString("done").equals("0")) {
                            DRETestViewerActivityFragment.this.m72060S4(arrayListM71817V.get(0).getString("selectedAnswer"), false);
                            return;
                        }
                        string2 = arrayListM71817V.get(0).getString("selectedAnswer");
                    }
                }
                String strM71773B = DRETestViewerActivityFragment.this.f89579Q4.m71773B(bundle.getString("question"), string, "127");
                String string4 = bundle.getString("explanation");
                if (string4 != null) {
                    DRETestViewerActivityFragment.this.f89579Q4.m71773B(string4, string, "127");
                }
                DRETestViewerActivityFragment dRETestViewerActivityFragment5 = DRETestViewerActivityFragment.this;
                String strM72817d4 = dRETestViewerActivityFragment5.m72817d4(dRETestViewerActivityFragment5.m15366r(), "DREHeader.css");
                StringBuilder sb2 = new StringBuilder();
                sb2.append(strM72817d4);
                DRETestViewerActivityFragment dRETestViewerActivityFragment6 = DRETestViewerActivityFragment.this;
                sb2.append(dRETestViewerActivityFragment6.m72817d4(dRETestViewerActivityFragment6.m15366r(), "DREQuestion.css"));
                String strReplace = sb2.toString().replace("[size]", "200");
                ArrayList arrayList = new ArrayList();
                String string5 = bundle.getString("otherMedias");
                if (string5.length() > 0) {
                    Collections.addAll(arrayList, StringUtils.splitByWholeSeparator(string5, ","));
                }
                DRETestViewerActivityFragment.this.m72050N4(arrayList);
                DRETestViewerActivityFragment.this.m72049M4();
                DRETestViewerActivityFragment dRETestViewerActivityFragment7 = DRETestViewerActivityFragment.this;
                ArrayList<Bundle> arrayListM71817V2 = dRETestViewerActivityFragment7.f89579Q4.m71817V(dRETestViewerActivityFragment7.f89566D4, "select * from answers where qId = " + string);
                String strReplace2 = strReplace.replace("[correctID]", bundle.getString("corrAns")).replace("[Question]", strM71773B);
                Iterator<Bundle> it2 = arrayListM71817V2.iterator();
                String str = "";
                while (it2.hasNext()) {
                    Bundle next = it2.next();
                    String strM71773B2 = DRETestViewerActivityFragment.this.f89579Q4.m71773B(next.getString("answerText"), string, "127");
                    String string6 = next.getString("row");
                    String strM72059R4 = DRETestViewerActivityFragment.this.m72059R4(string6);
                    str = str + ("<tr><td width=\"16\" id=\"Qbank-Answer-Row-Image-" + string6 + "\"></td><td><input type=\"radio\" name=\"Qbank-Answer-Button-Group\" onclick=\"answerChanged(" + string6 + ")\" " + (string6.equals(string2) ? "checked=\"checked\"" : "") + "></td><td class=\"answerOptionNumber\"><span>" + strM72059R4 + ". </span></td><td><span id=\"AnswerText" + string6 + "\" onclick=\"answerClickedForStrikeout(" + string6 + ");\">" + strM71773B2 + "</span></td></tr>");
                }
                DRETestViewerActivityFragment.this.f89563A4 = strReplace2.replace("[Answers]", str + "<p style=\"text-align:left;font-size:small;\">" + (bundle.getString("type") + " - " + bundle.getString("area") + " - " + bundle.getString("Month") + " - " + bundle.getString("Year")) + "</p>").replace(";font-family:", ";disable-font-family:").replace("style=\"font-family:", "style=\"disable-font-family:");
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestViewerActivityFragment.2
            @Override // java.lang.Runnable
            public void run() {
                String str = DRETestViewerActivityFragment.this.f89595p4;
                if (str == null || str.length() <= 0) {
                    DRETestViewerActivityFragment.this.m72062U4();
                } else {
                    DRETestViewerActivityFragment dRETestViewerActivityFragment = DRETestViewerActivityFragment.this;
                    dRETestViewerActivityFragment.m72780C4(dRETestViewerActivityFragment.f89595p4);
                }
            }
        });
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException, IOException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        this.f87818g5 = this.f89579Q4.m71817V(this.f89566D4, "Select CorrPerc from Questions limit 1") == null;
        if (m15387y() == null) {
            return this.f89565C4;
        }
        try {
            String str = this.f89563A4;
            if (str == null || str.length() == 0) {
                m72831p4();
                m15358o2(false);
                mo72642f3(C5562R.menu.uworld_test);
                m72786G3();
                iMDLogger.m73550f("Loading Document", this.f89567E4);
                if (this.f89567E4.contains("-")) {
                    String[] strArrSplit = this.f89567E4.split("-");
                    if (strArrSplit[0].equals("test")) {
                        CompressHelper compressHelper = this.f89579Q4;
                        this.f87815d5 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "Select * from tests where id =" + strArrSplit[1]));
                        this.f87813b5 = this.f89579Q4.m71817V(this.f89566D4, "Select * from Questions where id in (" + this.f87815d5.getString("qIds") + ")");
                        this.f87814c5 = Integer.valueOf(this.f87815d5.getString("qIndex")).intValue();
                        if (m15387y().containsKey("gotoQIndex")) {
                            this.f87814c5 = m15387y().getInt("gotoQIndex");
                        }
                    } else if (strArrSplit[0].equals("question")) {
                        this.f87813b5 = this.f89579Q4.m71817V(this.f89566D4, "Select * from Questions where id=" + strArrSplit[1]);
                        this.f87814c5 = 0;
                    } else if (strArrSplit[0].equals("answer")) {
                        this.f87813b5 = this.f89579Q4.m71817V(this.f89566D4, "Select * from Questions where id=" + strArrSplit[1]);
                        this.f87814c5 = 0;
                        m72060S4(null, false);
                    }
                } else {
                    this.f87813b5 = this.f89579Q4.m71817V(this.f89566D4, m15387y().getString("Query"));
                    this.f87814c5 = m15387y().getInt("QuestionIndex");
                }
                m72061T4();
            }
            m72826m3();
            m72836s4();
        } catch (Exception e2) {
            e2.printStackTrace();
            m72779B4(e2);
        }
        return this.f89565C4;
    }

    /* renamed from: U4 */
    public void m72062U4() {
        m72795O3(this.f89563A4, CompressHelper.m71753g1(this.f89566D4, "base"));
        String str = this.f89568F4;
        if (str != null) {
            this.f89574L4.setTitle(str);
            m72829n4(this.f89568F4);
            m72792M2();
        }
        mo71972o4();
        m72833q4(this.f89574L4.getMenu());
        m72065Y4();
    }

    /* renamed from: V4 */
    public void m72063V4(String str) {
        String str2;
        String string = this.f87813b5.get(this.f87814c5).getString("id");
        String string2 = this.f87813b5.get(this.f87814c5).getString("corrAns");
        Date date = new Date();
        String strM72058Q4 = m72058Q4(date);
        long time = (date.getTime() - this.f87816e5.getTime()) / 1000;
        Bundle bundle = this.f87815d5;
        String string3 = bundle != null ? bundle.getString("id") : "null";
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from logs where testId=" + string3 + " AND qid = " + string);
        if (arrayListM71817V == null || arrayListM71817V.size() <= 0) {
            str2 = "Insert into logs (id, qid, selectedAnswer, corrAnswer, answerDate, time, testId) values (null, " + string + ", " + str + ", " + string2 + ", '" + str + "', " + time + ", " + string3 + ")";
        } else {
            str2 = "Update logs set selectedAnswer = " + str + ", answerDate='" + strM72058Q4 + "', time=" + time + " where id=" + arrayListM71817V.get(0).getString("id");
        }
        this.f89579Q4.m71866m(this.f89566D4, str2);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = strM71753g1.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72064W4(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = strM71753g1 + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f87810Y4 = arrayList;
            mo71972o4();
        } else if (strArrSplit[0].equals("answer")) {
            String str3 = strArrSplit[1];
            m72063V4(str3);
            Bundle bundle = this.f87815d5;
            if (bundle == null || !bundle.getString("mode").equals("Testing")) {
                m72060S4(str3, true);
            }
        }
        return super.mo71969W3(consoleMessage);
    }

    /* renamed from: W4 */
    public String m72064W4(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    /* renamed from: Y4 */
    public void m72065Y4() {
        this.f89598s4.findItem(C5562R.id.action_stop).setVisible(false);
        if (this.f87815d5 != null) {
            this.f89598s4.findItem(C5562R.id.action_stop).setVisible(true);
        }
        m72056K4();
        m72057L4();
        if (this.f87814c5 <= 0) {
            m72054I4();
        }
        if (this.f87814c5 >= this.f87813b5.size() - 1) {
            m72055J4();
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        this.f89569G4.m73433g("for (var i=0; i < document.images.length; i++) { document.images[i].onclick = function(e){window.location.href=\"image://\" + e.target.src;}}");
        this.f89569G4.m73433g("onBodyLoad();");
        String str2 = this.f87817f5;
        if (str2 != null) {
            mo71967C3(str2);
            this.f87817f5 = null;
        }
        super.mo71956Z3(webView, str);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        int i2;
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_favorites) {
            new Bundle();
            String str = "Question " + this.f87813b5.get(this.f87814c5).getString("id") + " - " + this.f87813b5.get(this.f87814c5).getString("title");
            if (menuItem.getTitle().equals("Add Favorite")) {
                m72791L2(str, this.f89567E4);
                menuItem.setTitle("Remove Favorite");
                i2 = C5562R.drawable.ic_action_favorite_yellow;
            } else {
                mo72686p3(this.f89567E4);
                menuItem.setTitle("Add Favorite");
                i2 = C5562R.drawable.ic_action_favorite;
            }
            menuItem.setIcon(i2);
            return true;
        }
        if (itemId == C5562R.id.action_gallery) {
            m72053X4("asdfafdsaf");
            return true;
        }
        if (itemId == C5562R.id.action_previous) {
            this.f87814c5--;
            m72061T4();
            m72065Y4();
        }
        if (itemId == C5562R.id.action_next) {
            this.f87814c5++;
            m72061T4();
            m72065Y4();
            if (this.f87815d5 != null) {
                this.f89579Q4.m71866m(this.f89566D4, "Update tests set qIndex=" + this.f87814c5 + " where id=" + this.f87815d5.getString("id"));
            }
        }
        if (itemId == C5562R.id.action_stop) {
            new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("اتمام آزمون ؟").mo1115y("بله", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestViewerActivityFragment.6
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                    DRETestViewerActivityFragment dRETestViewerActivityFragment = DRETestViewerActivityFragment.this;
                    if (dRETestViewerActivityFragment.f87815d5 == null) {
                        return;
                    }
                    Iterator<Bundle> it2 = dRETestViewerActivityFragment.f89579Q4.m71817V(dRETestViewerActivityFragment.f89566D4, "Select questions.id,selectedAnswer,corrAnswer,time  from Questions left outer join (select * from logs where testId=" + DRETestViewerActivityFragment.this.f87815d5.getString("id") + ") as logs2 on questions.id=logs2.qid where questions.id in (" + DRETestViewerActivityFragment.this.f87815d5.getString("qIds") + ")").iterator();
                    float f2 = 0.0f;
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        if (next.getString("selectedAnswer").length() != 0 && next.getString("selectedAnswer").equals(next.getString("corrAnswer"))) {
                            f2 += 1.0f;
                        }
                    }
                    int size = (int) ((f2 / r6.size()) * 100.0f);
                    DRETestViewerActivityFragment dRETestViewerActivityFragment2 = DRETestViewerActivityFragment.this;
                    dRETestViewerActivityFragment2.f89579Q4.m71866m(dRETestViewerActivityFragment2.f89566D4, "Update tests set score='" + size + "', done=1 where id=" + DRETestViewerActivityFragment.this.f87815d5.getString("id"));
                    DRETestViewerActivityFragment dRETestViewerActivityFragment3 = DRETestViewerActivityFragment.this;
                    dRETestViewerActivityFragment3.f89579Q4.m71772A1(dRETestViewerActivityFragment3.f89566D4, "testresult-" + DRETestViewerActivityFragment.this.f87815d5.getString("id"), null, null);
                }
            }).mo1106p("خیر", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.DRE.DRETestViewerActivityFragment.5
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                }
            }).m1090I();
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: v4 */
    public boolean mo71959v4() {
        return false;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        if (str2.equals("image")) {
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, "/");
            m72053X4(strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1]);
            return true;
        }
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        if (str3.contains("USMLEWorld-Question-Answer-Changed")) {
            this.f89569G4.m73433g("console.log(\"answer,,,,,\" + prevAnswerID);");
            return true;
        }
        if (str3.contains("/2323")) {
            m72053X4("soheilvb");
            return true;
        }
        if (str2.equals(Annotation.f68285k3) || (str2.equals("http") & str3.contains("localhost:"))) {
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str3, "/");
            m72053X4(strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1]);
        }
        return true;
    }
}

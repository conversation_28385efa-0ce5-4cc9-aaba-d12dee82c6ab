package net.imedicaldoctor.imd.Fragments;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import at.grabner.circleprogress.CircleProgressView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.google.android.material.tabs.TabLayout;
import com.google.common.net.UrlEscapers;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.p008dd.CircularProgressButton;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.observers.DisposableObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.HttpURLConnection;
import java.nio.channels.FileChannel;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.ViewHolders.StatusAdapter;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class downloadFragment extends Fragment {

    /* renamed from: A5 */
    public static final String f90430A5 = "Delta";

    /* renamed from: B5 */
    public static final String f90431B5 = "Update";

    /* renamed from: C5 */
    public static final String f90432C5 = "Rebuilding";

    /* renamed from: D5 */
    public static final String f90433D5 = "Parts";

    /* renamed from: E5 */
    public static final String f90434E5 = "folderSizeKey";

    /* renamed from: F5 */
    public static final String f90435F5 = "videoIdKey";

    /* renamed from: G5 */
    public static final String f90436G5 = "savePathKey";

    /* renamed from: H5 */
    public static final String f90437H5 = "LatestKey";

    /* renamed from: I5 */
    public static final String f90438I5 = "HiddenKey";

    /* renamed from: J5 */
    public static final String f90439J5 = "SpeedReceived";

    /* renamed from: N4 */
    private static ArrayList<Bundle> f90440N4 = null;

    /* renamed from: O4 */
    private static ArrayList<Bundle> f90441O4 = null;

    /* renamed from: P4 */
    private static HashMap<String, CircleProgressView> f90442P4 = null;

    /* renamed from: Q4 */
    private static HashMap<String, Runnable> f90443Q4 = null;

    /* renamed from: R4 */
    private static HashMap<String, Runnable> f90444R4 = null;

    /* renamed from: S4 */
    private static HashMap<String, Runnable> f90445S4 = null;

    /* renamed from: T4 */
    private static ArrayList<Bundle> f90446T4 = null;

    /* renamed from: U4 */
    public static HashMap<String, Bundle> f90447U4 = null;

    /* renamed from: V4 */
    private static ArrayList<Bundle> f90448V4 = null;

    /* renamed from: W4 */
    private static ArrayList<Bundle> f90449W4 = null;

    /* renamed from: X4 */
    private static Bundle f90450X4 = null;

    /* renamed from: Y4 */
    private static String f90451Y4 = null;

    /* renamed from: Z4 */
    private static String f90452Z4 = null;

    /* renamed from: a5 */
    private static String f90453a5 = null;

    /* renamed from: b5 */
    public static Bundle f90454b5 = null;

    /* renamed from: c5 */
    public static int f90455c5 = 0;

    /* renamed from: d5 */
    public static HashMap<String, DisposableObserver<HttpURLConnection>> f90456d5 = null;

    /* renamed from: e5 */
    public static final String f90457e5 = "bytesDownloaded";

    /* renamed from: f5 */
    public static final String f90458f5 = "bytesTotal";

    /* renamed from: g5 */
    public static final String f90459g5 = "avgSpeed";

    /* renamed from: h5 */
    public static final String f90460h5 = "remaining";

    /* renamed from: i5 */
    public static final String f90461i5 = "Progress";

    /* renamed from: j5 */
    public static final String f90462j5 = "Title";

    /* renamed from: k5 */
    public static final String f90463k5 = "URL";

    /* renamed from: l5 */
    public static final String f90464l5 = "FileName";

    /* renamed from: m5 */
    public static final String f90465m5 = "MD5";

    /* renamed from: n5 */
    public static final String f90466n5 = "PartFileSize";

    /* renamed from: o5 */
    public static final String f90467o5 = "price";

    /* renamed from: p5 */
    public static final String f90468p5 = "Buy";

    /* renamed from: q5 */
    public static final String f90469q5 = "downloader";

    /* renamed from: r5 */
    public static final String f90470r5 = "retry";

    /* renamed from: s5 */
    public static final String f90471s5 = "completed";

    /* renamed from: t5 */
    public static final String f90472t5 = "Installed";

    /* renamed from: u5 */
    public static final String f90473u5 = "error";

    /* renamed from: v5 */
    public static final String f90474v5 = "fileSize";

    /* renamed from: w5 */
    public static final String f90475w5 = "Icon";

    /* renamed from: x5 */
    public static final String f90476x5 = "name";

    /* renamed from: y5 */
    public static final String f90477y5 = "type";

    /* renamed from: z5 */
    public static final String f90478z5 = "version";

    /* renamed from: A4 */
    private String f90479A4;

    /* renamed from: B4 */
    private boolean f90480B4;

    /* renamed from: C4 */
    private boolean f90481C4;

    /* renamed from: D4 */
    private Activity f90482D4;

    /* renamed from: E4 */
    public Typeface f90483E4;

    /* renamed from: F4 */
    public BetterLinearLayoutManager f90484F4;

    /* renamed from: G4 */
    public String f90485G4;

    /* renamed from: H4 */
    public boolean f90486H4;

    /* renamed from: I4 */
    CompressHelper f90487I4;

    /* renamed from: J4 */
    public DownloadsAdapter f90488J4;

    /* renamed from: K4 */
    public Handler f90489K4 = new Handler(Looper.getMainLooper()) { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.7
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            downloadFragment.this.m73234b4();
        }
    };

    /* renamed from: L4 */
    public Handler f90490L4 = new Handler() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.8
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            downloadFragment.this.m73236v3();
        }
    };

    /* renamed from: M4 */
    public BroadcastReceiver f90491M4 = new BroadcastReceiver() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.26
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            if (downloadFragment.this.m73198C3()) {
                return;
            }
            SearchView searchView = downloadFragment.this.f90500m4;
            if (searchView != null) {
                searchView.clearFocus();
            }
            if (!downloadFragment.this.m73207K3()) {
                downloadFragment.this.m73209M2();
            }
            downloadFragment.this.m73197B3();
        }
    };

    /* renamed from: e4 */
    private Bundle f90492e4;

    /* renamed from: f4 */
    private Bundle f90493f4;

    /* renamed from: g4 */
    private Observable<String> f90494g4;

    /* renamed from: h4 */
    private Bundle f90495h4;

    /* renamed from: i4 */
    private View f90496i4;

    /* renamed from: j4 */
    public RecyclerView f90497j4;

    /* renamed from: k4 */
    private MenuItem f90498k4;

    /* renamed from: l4 */
    private ProgressBar f90499l4;

    /* renamed from: m4 */
    public SearchView f90500m4;

    /* renamed from: n4 */
    public VBHelper f90501n4;

    /* renamed from: o4 */
    private Bundle f90502o4;

    /* renamed from: p4 */
    public long f90503p4;

    /* renamed from: q4 */
    public Bundle f90504q4;

    /* renamed from: r4 */
    public TabLayout f90505r4;

    /* renamed from: s4 */
    public CardView f90506s4;

    /* renamed from: t4 */
    public ImageView f90507t4;

    /* renamed from: u4 */
    public Button f90508u4;

    /* renamed from: v4 */
    private boolean f90509v4;

    /* renamed from: w4 */
    private Timer f90510w4;

    /* renamed from: x4 */
    private Timer f90511x4;

    /* renamed from: y4 */
    private String f90512y4;

    /* renamed from: z4 */
    private String f90513z4;

    /* renamed from: net.imedicaldoctor.imd.Fragments.downloadFragment$18 */
    class DialogInterfaceOnClickListenerC543218 implements DialogInterface.OnClickListener {
        DialogInterfaceOnClickListenerC543218() {
        }

        @Override // android.content.DialogInterface.OnClickListener
        public void onClick(DialogInterface dialogInterface, int i2) {
        }
    }

    /* renamed from: net.imedicaldoctor.imd.Fragments.downloadFragment$19 */
    class DialogInterfaceOnClickListenerC543319 implements DialogInterface.OnClickListener {
        DialogInterfaceOnClickListenerC543319() {
        }

        @Override // android.content.DialogInterface.OnClickListener
        public void onClick(DialogInterface dialogInterface, int i2) {
            ActivityCompat.m5644N(downloadFragment.this.m15366r(), new String[]{"android.permission.WRITE_EXTERNAL_STORAGE"}, 1);
        }
    }

    private static class BetterLinearLayoutManager extends LinearLayoutManager {
        public BetterLinearLayoutManager(Context context) {
            super(context);
        }

        @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.LayoutManager
        /* renamed from: n2 */
        public boolean mo27044n2() {
            return false;
        }

        public BetterLinearLayoutManager(Context context, int i2, boolean z) {
            super(context, i2, z);
        }

        public BetterLinearLayoutManager(Context context, AttributeSet attributeSet, int i2, int i3) {
            super(context, attributeSet, i2, i3);
        }
    }

    public class DownloadCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90574I;

        /* renamed from: J */
        private final TextView f90575J;

        /* renamed from: K */
        private final TextView f90576K;

        /* renamed from: L */
        private final TextView f90577L;

        /* renamed from: M */
        private final TextView f90578M;

        /* renamed from: N */
        private final ImageView f90579N;

        /* renamed from: O */
        private final Button f90580O;

        /* renamed from: P */
        private final CircleProgressView f90581P;

        public DownloadCellViewHolder(View view) {
            super(view);
            this.f90574I = (TextView) view.findViewById(C5562R.id.title);
            this.f90575J = (TextView) view.findViewById(C5562R.id.subtitle);
            this.f90576K = (TextView) view.findViewById(C5562R.id.latest);
            this.f90577L = (TextView) view.findViewById(C5562R.id.demo);
            this.f90579N = (ImageView) view.findViewById(C5562R.id.image);
            Button button = (Button) view.findViewById(C5562R.id.download_button);
            this.f90580O = button;
            this.f90578M = (TextView) view.findViewById(C5562R.id.desc);
            button.setTypeface(downloadFragment.this.f90483E4);
            this.f90581P = (CircleProgressView) view.findViewById(C5562R.id.circleView);
        }
    }

    public class DownloadsAdapter extends RecyclerView.Adapter {

        /* renamed from: net.imedicaldoctor.imd.Fragments.downloadFragment$DownloadsAdapter$6 */
        class ViewOnClickListenerC54716 implements View.OnClickListener {

            /* renamed from: s */
            final /* synthetic */ Bundle f90595s;

            /* renamed from: net.imedicaldoctor.imd.Fragments.downloadFragment$DownloadsAdapter$6$1, reason: invalid class name */
            class AnonymousClass1 extends DisposableObserver<String> {

                /* renamed from: net.imedicaldoctor.imd.Fragments.downloadFragment$DownloadsAdapter$6$1$2, reason: invalid class name */
                class AnonymousClass2 implements DialogInterface.OnClickListener {
                    AnonymousClass2() {
                    }

                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i2) {
                        DisposableObserver<String> disposableObserver = new DisposableObserver<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.6.1.2.1
                            @Override // io.reactivex.rxjava3.observers.DisposableObserver
                            /* renamed from: a */
                            protected void mo61331a() {
                                super.mo61331a();
                                downloadFragment.this.f90498k4.setVisible(true);
                                downloadFragment.this.f90499l4.setIndeterminate(true);
                            }

                            @Override // io.reactivex.rxjava3.core.Observer
                            /* renamed from: c, reason: merged with bridge method [inline-methods] */
                            public void onNext(@NonNull String str) throws NumberFormatException {
                                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "|||||");
                                if (!strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                                    if (strArrSplitByWholeSeparator.length > 1) {
                                        if (strArrSplitByWholeSeparator[1].contains("Not Enough Money")) {
                                            new AlertDialog.Builder(downloadFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("You don't have enough credit in your account. what do you want to do ?").mo1115y("Buy Database", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.6.1.2.1.2
                                                @Override // android.content.DialogInterface.OnClickListener
                                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                                    StringBuilder sb = new StringBuilder();
                                                    sb.append("http://imedicaldoctor.net/buydb.php?user=");
                                                    downloadFragment downloadfragment = downloadFragment.this;
                                                    sb.append(downloadfragment.f90501n4.m73452n(downloadfragment.f90487I4.m71905y1(), "127"));
                                                    sb.append("&db=");
                                                    ViewOnClickListenerC54716 viewOnClickListenerC54716 = ViewOnClickListenerC54716.this;
                                                    sb.append(downloadFragment.this.f90501n4.m73452n(viewOnClickListenerC54716.f90595s.getString("name"), "127"));
                                                    downloadFragment.this.f90487I4.m71803P(sb.toString());
                                                }
                                            }).mo1106p("Cancel", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.6.1.2.1.1
                                                @Override // android.content.DialogInterface.OnClickListener
                                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                                }
                                            }).m1090I();
                                            return;
                                        } else {
                                            CompressHelper.m71767x2(downloadFragment.this.m73240z3(), strArrSplitByWholeSeparator[1], 1);
                                            return;
                                        }
                                    }
                                    return;
                                }
                                downloadFragment.this.f90487I4.m71894t2(strArrSplitByWholeSeparator[1]);
                                ViewOnClickListenerC54716 viewOnClickListenerC54716 = ViewOnClickListenerC54716.this;
                                downloadFragment.this.m73221T2(viewOnClickListenerC54716.f90595s, "Buy");
                                downloadFragment.this.m73173Q2();
                                downloadFragment.this.m73216Q3();
                                ViewOnClickListenerC54716 viewOnClickListenerC547162 = ViewOnClickListenerC54716.this;
                                downloadFragment.this.m73224V2(viewOnClickListenerC547162.f90595s);
                            }

                            @Override // io.reactivex.rxjava3.core.Observer
                            public void onComplete() {
                                downloadFragment.this.f90498k4.setVisible(false);
                            }

                            @Override // io.reactivex.rxjava3.core.Observer
                            public void onError(@NonNull Throwable th) {
                                downloadFragment.this.f90498k4.setVisible(false);
                                CompressHelper.m71767x2(downloadFragment.this.m73240z3(), "Error in contacting server, try again later", 1);
                            }
                        };
                        downloadFragment downloadfragment = downloadFragment.this;
                        CompressHelper compressHelper = downloadfragment.f90487I4;
                        compressHelper.m71789I0(downloadfragment, compressHelper.m71874o0("BuyItem|||||" + downloadFragment.this.f90501n4.m73451m() + "|||||" + ViewOnClickListenerC54716.this.f90595s.getString("name"))).mo59651a(disposableObserver);
                    }
                }

                AnonymousClass1() {
                }

                @Override // io.reactivex.rxjava3.observers.DisposableObserver
                /* renamed from: a */
                protected void mo61331a() {
                    super.mo61331a();
                    downloadFragment.this.f90498k4.setVisible(true);
                    downloadFragment.this.f90499l4.setIndeterminate(true);
                }

                @Override // io.reactivex.rxjava3.core.Observer
                /* renamed from: c, reason: merged with bridge method [inline-methods] */
                public void onNext(@NonNull String str) {
                    StringBuilder sb;
                    String str2;
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "|||||");
                    if (!strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                        CompressHelper.m71767x2(downloadFragment.this.m73240z3(), strArrSplitByWholeSeparator[1], 1);
                        return;
                    }
                    String str3 = strArrSplitByWholeSeparator[1];
                    Log.e("Price", "Response : " + str);
                    Log.e("Price", str3);
                    Log.e("Price 2", ViewOnClickListenerC54716.this.f90595s.getString("price"));
                    if (!str3.equals(ViewOnClickListenerC54716.this.f90595s.getString("price").replace(".0", ""))) {
                        Log.e("iMD", "Wrong price");
                        downloadFragment.this.m73212N3();
                        return;
                    }
                    if (str3.equals("0")) {
                        sb = new StringBuilder();
                        sb.append("Are You Sure You Want To Buy ");
                        sb.append(ViewOnClickListenerC54716.this.f90595s.getString("Title"));
                        str2 = " For Free ?";
                    } else {
                        sb = new StringBuilder();
                        sb.append("Are You Sure You Want To Buy ");
                        sb.append(ViewOnClickListenerC54716.this.f90595s.getString("Title"));
                        sb.append(" For ");
                        sb.append(str3);
                        str2 = " Toman ?";
                    }
                    sb.append(str2);
                    new AlertDialog.Builder(downloadFragment.this.m73240z3(), C5562R.style.alertDialogTheme).mo1102l(sb.toString()).mo1115y("Yes", new AnonymousClass2()).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.6.1.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                        }
                    }).m1090I();
                }

                @Override // io.reactivex.rxjava3.core.Observer
                public void onComplete() {
                    downloadFragment.this.f90498k4.setVisible(false);
                }

                @Override // io.reactivex.rxjava3.core.Observer
                public void onError(@NonNull Throwable th) {
                    downloadFragment.this.f90498k4.setVisible(false);
                    CompressHelper.m71767x2(downloadFragment.this.m73240z3(), "Error in contacting server, try again later", 1);
                }
            }

            ViewOnClickListenerC54716(Bundle bundle) {
                this.f90595s = bundle;
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws NumberFormatException {
                Log.e("imd", "here");
                if (!this.f90595s.containsKey("Buy")) {
                    if (downloadFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).contains("DownloadServer")) {
                        downloadFragment.this.m73224V2(this.f90595s);
                        return;
                    } else {
                        new AlertDialog.Builder(downloadFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Choose which server to download from. if you are connecting from Iran choose Iran Server and if not, select Germany. ").mo1115y("Iran Server", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.6.3
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i2) {
                                if (downloadFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).contains("DownloadServer")) {
                                    downloadFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().remove("DownloadServer").commit();
                                }
                                downloadFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putString("DownloadServer", "idl").commit();
                                SearchView searchView = downloadFragment.this.f90500m4;
                                if (searchView != null) {
                                    searchView.clearFocus();
                                }
                                downloadFragment.this.m73209M2();
                                Toast.makeText(downloadFragment.this.m15366r(), "You can change it later in the account tab", 1).show();
                            }
                        }).mo1106p("Germany Server", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.6.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i2) throws NumberFormatException {
                                if (downloadFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).contains("DownloadServer")) {
                                    downloadFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().remove("DownloadServer").commit();
                                }
                                downloadFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putString("DownloadServer", "dl").commit();
                                Toast.makeText(downloadFragment.this.m15366r(), "You can change it later in the account tab", 1).show();
                                ViewOnClickListenerC54716 viewOnClickListenerC54716 = ViewOnClickListenerC54716.this;
                                downloadFragment.this.m73224V2(viewOnClickListenerC54716.f90595s);
                            }
                        }).m1090I();
                        return;
                    }
                }
                AnonymousClass1 anonymousClass1 = new AnonymousClass1();
                downloadFragment downloadfragment = downloadFragment.this;
                CompressHelper compressHelper = downloadfragment.f90487I4;
                compressHelper.m71789I0(downloadfragment, compressHelper.m71874o0("QueryPrice|||||" + downloadFragment.this.f90501n4.m73451m() + "|||||" + this.f90595s.getString("name"))).mo59651a(anonymousClass1);
            }
        }

        public DownloadsAdapter() {
        }

        /* renamed from: d0 */
        private void m73264d0(final CircularProgressButton circularProgressButton) {
            circularProgressButton.setProgress(1);
            circularProgressButton.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.9
                @Override // java.lang.Runnable
                public void run() {
                    circularProgressButton.setProgress(0);
                    circularProgressButton.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.9.1
                        @Override // java.lang.Runnable
                        public void run() {
                            circularProgressButton.setProgress(1);
                        }
                    }, 100L);
                }
            }, 100L);
        }

        /* renamed from: e0 */
        private double m73265e0(Bundle bundle, String str) {
            try {
                if (bundle.containsKey(str)) {
                    return bundle.getDouble(str);
                }
                return 0.0d;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                return 0.0d;
            }
        }

        /* renamed from: g0 */
        private long m73266g0(Bundle bundle, String str) {
            try {
                if (bundle.containsKey(str)) {
                    return bundle.getLong(str);
                }
                return 0L;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                return 0L;
            }
        }

        /* renamed from: h0 */
        private String m73267h0(int i2) {
            return m73268j0(i2 / 3600) + " : " + m73268j0((i2 % 3600) / 60) + " : " + m73268j0(i2 % 60);
        }

        /* renamed from: j0 */
        private String m73268j0(int i2) {
            if (i2 == 0) {
                return "00";
            }
            if (i2 / 10 != 0) {
                return String.valueOf(i2);
            }
            return "0" + i2;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: C */
        public int mo26845C(int i2) {
            return 1;
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: R */
        public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) throws Resources.NotFoundException {
            TextView textView;
            View.OnClickListener onClickListener;
            TextView textView2;
            String strM73210M3;
            TextView textView3;
            int color;
            TextView textView4;
            String str;
            DownloadCellViewHolder downloadCellViewHolder = (DownloadCellViewHolder) viewHolder;
            if (downloadFragment.f90440N4 == null || downloadFragment.f90440N4.size() - 1 < i2) {
                return;
            }
            final Bundle bundle = (Bundle) downloadFragment.f90440N4.get(i2);
            downloadCellViewHolder.f90574I.setText(bundle.getString("Title"));
            m73270i0(downloadCellViewHolder.f90579N, bundle);
            if (bundle.containsKey("LatestKey")) {
                downloadCellViewHolder.f90576K.setVisibility(0);
                if (bundle.getString("LatestKey").equals("0")) {
                    textView4 = downloadCellViewHolder.f90576K;
                    str = "Show Latest Version";
                } else {
                    textView4 = downloadCellViewHolder.f90576K;
                    str = "Show Free Version";
                }
                textView4.setText(str);
                downloadCellViewHolder.f90576K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        Bundle next;
                        String str2;
                        Bundle bundle2;
                        String str3;
                        Bundle bundleM73464z = downloadFragment.this.f90501n4.m73464z();
                        String str4 = downloadFragment.this.f90487I4.m71816U1() + "/DBs.db";
                        if (!bundle.getString("LatestKey").equals("0")) {
                            if (bundle.getString("LatestKey").equals(IcyHeaders.f28171a3)) {
                                Iterator<Bundle> it2 = downloadFragment.this.f90487I4.m71825Y(str4, "select id,Title,name,Version, IconName,folderSize, url, fileSize, md5,price, partfilesize,type from Dbs where name = '" + bundle.getString("name") + "' order by Version desc").iterator();
                                while (true) {
                                    if (!it2.hasNext()) {
                                        next = null;
                                        break;
                                    }
                                    next = it2.next();
                                    String string = bundleM73464z.getString(bundle.getString("name"));
                                    String string2 = next.getString("Version");
                                    Iterator<Bundle> it3 = it2;
                                    if (string2.length() == 6) {
                                        string = string.substring(0, 6);
                                    }
                                    if (string.compareTo(string2) >= 0) {
                                        break;
                                    } else {
                                        it2 = it3;
                                    }
                                }
                                if (next == null) {
                                    Toast.makeText(downloadFragment.this.m15366r(), "There is no previous versions available (more than 6 month ago)", 1).show();
                                    return;
                                }
                                downloadFragment.this.m73221T2(bundle, "version");
                                bundle.putString("version", next.getString("Version"));
                                downloadFragment.this.m73221T2(bundle, "URL");
                                bundle.putString("URL", downloadFragment.this.f90487I4.m71810S1("http://" + downloadFragment.this.f90513z4 + ".imedicaldoctor.net" + next.getString("url")));
                                downloadFragment.this.m73221T2(bundle, "fileSize");
                                bundle.putString("fileSize", next.getString("fileSize"));
                                downloadFragment.this.m73221T2(bundle, "folderSizeKey");
                                bundle.putString("folderSizeKey", next.getString("folderSize"));
                                downloadFragment.this.m73221T2(bundle, "MD5");
                                bundle.putString("MD5", next.getString("md5"));
                                downloadFragment.this.m73221T2(bundle, "PartFileSize");
                                bundle.putString("PartFileSize", next.getString("partfilesize"));
                                downloadFragment.this.m73221T2(bundle, "Buy");
                                downloadFragment.this.m73221T2(bundle, "Parts");
                                str2 = "LatestKey";
                                downloadFragment.this.m73221T2(bundle, str2);
                                bundle2 = bundle;
                                str3 = "0";
                            }
                            downloadFragment.this.m73213O2();
                        }
                        Bundle bundle3 = downloadFragment.this.f90487I4.m71825Y(str4, "select id,Title,name,Version, IconName,folderSize, url, fileSize, md5,price, partfilesize,type from Dbs where name = '" + bundle.getString("name") + "' order by Version desc").get(0);
                        downloadFragment.this.m73221T2(bundle, "version");
                        bundle.putString("version", bundle3.getString("Version"));
                        downloadFragment.this.m73221T2(bundle, "URL");
                        bundle.putString("URL", downloadFragment.this.f90487I4.m71810S1("http://" + downloadFragment.this.f90513z4 + ".imedicaldoctor.net" + bundle3.getString("url")));
                        downloadFragment.this.m73221T2(bundle, "fileSize");
                        bundle.putString("fileSize", bundle3.getString("fileSize"));
                        downloadFragment.this.m73221T2(bundle, "folderSizeKey");
                        bundle.putString("folderSizeKey", bundle3.getString("folderSize"));
                        downloadFragment.this.m73221T2(bundle, "MD5");
                        bundle.putString("MD5", bundle3.getString("md5"));
                        downloadFragment.this.m73221T2(bundle, "PartFileSize");
                        bundle.putString("PartFileSize", bundle3.getString("partfilesize"));
                        downloadFragment.this.m73221T2(bundle, "Parts");
                        String string3 = bundleM73464z.getString(bundle.getString("name"));
                        String string4 = bundle3.getString("Version");
                        if (string4.length() == 6) {
                            string3 = string3.substring(0, 6);
                        }
                        if (string3.compareTo(string4) >= 0) {
                            downloadFragment.this.m73221T2(bundle, "Buy");
                        } else {
                            bundle.putString("Buy", "");
                        }
                        str2 = "LatestKey";
                        downloadFragment.this.m73221T2(bundle, str2);
                        bundle2 = bundle;
                        str3 = IcyHeaders.f28171a3;
                        bundle2.putString(str2, str3);
                        downloadFragment.this.m73213O2();
                    }
                });
            } else {
                downloadCellViewHolder.f90576K.setVisibility(8);
            }
            if (bundle.containsKey("completed")) {
                if (bundle.containsKey(downloadFragment.f90472t5)) {
                    downloadCellViewHolder.f90575J.setText("ّInstalled");
                    downloadCellViewHolder.f90575J.setTextColor(downloadFragment.this.m15320b0().getColor(C5562R.color.blue_dark));
                    downloadCellViewHolder.f90580O.setVisibility(0);
                    downloadCellViewHolder.f90580O.setText("Open");
                    downloadCellViewHolder.f90580O.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.3
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) throws Resources.NotFoundException {
                            downloadFragment.this.m73197B3();
                            ((mainActivity) downloadFragment.this.m15366r()).f90750y3.setCurrentItem(1);
                            downloadFragment.this.f90496i4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.3.1
                                @Override // java.lang.Runnable
                                public void run() {
                                    ViewOnClickListenerC54683 viewOnClickListenerC54683 = ViewOnClickListenerC54683.this;
                                    downloadFragment.this.f90487I4.m71909z1(bundle.getBundle("DB"));
                                }
                            }, 1000L);
                        }
                    });
                } else {
                    downloadCellViewHolder.f90575J.setText("Download Completed");
                    downloadCellViewHolder.f90575J.setTextColor(downloadFragment.this.m15320b0().getColor(C5562R.color.green_real));
                    downloadCellViewHolder.f90580O.setVisibility(8);
                }
                if (bundle.containsKey("Rebuilding")) {
                    downloadCellViewHolder.f90575J.setText("Rebuilding ...");
                }
                downloadCellViewHolder.f90577L.setVisibility(8);
                downloadCellViewHolder.f90581P.setVisibility(8);
                return;
            }
            if (bundle.containsKey("downloader")) {
                downloadCellViewHolder.f90576K.setVisibility(8);
                downloadCellViewHolder.f90577L.setVisibility(8);
                DecimalFormat decimalFormat = new DecimalFormat("#,##0.#");
                downloadCellViewHolder.f90580O.setVisibility(8);
                downloadCellViewHolder.f90581P.setVisibility(0);
                double dM73265e0 = m73265e0(bundle, "bytesDownloaded");
                double dM73265e02 = m73265e0(bundle, "bytesTotal");
                long jM73266g0 = m73266g0(bundle, "avgSpeed");
                long jM73266g02 = m73266g0(bundle, "remaining");
                int iM73269f0 = m73269f0(bundle, "Progress");
                String str2 = decimalFormat.format((dM73265e0 / 1024.0d) / 1024.0d) + " of " + decimalFormat.format((dM73265e02 / 1024.0d) / 1024.0d) + " MB(" + downloadFragment.this.m73210M3(jM73266g0) + "/s), " + m73267h0((int) jM73266g02) + " remaining";
                downloadCellViewHolder.f90575J.setTextColor(downloadFragment.this.m15320b0().getColor(C5562R.color.darkGrey));
                if (dM73265e0 == 0.0d) {
                    downloadCellViewHolder.f90581P.m29176u();
                    str2 = "Preparing Download";
                } else {
                    downloadCellViewHolder.f90581P.m29177v();
                }
                downloadCellViewHolder.f90575J.setText(str2);
                iMDLogger.m73548d("ONBindViewHolder", "Progress : " + iM73269f0);
                downloadCellViewHolder.f90581P.setValue(iM73269f0 == 0 ? 1.0f : iM73269f0);
                downloadCellViewHolder.f90581P.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.8
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        downloadFragment.this.m73226W2(bundle);
                    }
                });
                return;
            }
            downloadCellViewHolder.f90580O.setVisibility(0);
            downloadCellViewHolder.f90581P.setVisibility(8);
            downloadCellViewHolder.f90577L.setVisibility(8);
            try {
                if (bundle.containsKey("Buy") && downloadFragment.this.m73199D3(bundle.getString("name"))) {
                    downloadCellViewHolder.f90578M.setText(downloadFragment.this.m73211N2(bundle.getString("name")).equals("1year") ? "Free monthly updates for one year" : "Free monthly updates for two years");
                    downloadCellViewHolder.f90578M.setVisibility(0);
                } else {
                    downloadCellViewHolder.f90578M.setVisibility(8);
                }
                if (bundle.containsKey("error")) {
                    downloadCellViewHolder.f90575J.setText(bundle.getString("error"));
                    textView3 = downloadCellViewHolder.f90575J;
                    color = downloadFragment.this.m15320b0().getColor(C5562R.color.red);
                } else {
                    if (downloadFragment.this.m73199D3(bundle.getString("name"))) {
                        textView2 = downloadCellViewHolder.f90575J;
                        strM73210M3 = downloadFragment.this.m73210M3(Long.valueOf(bundle.getString("fileSize")).longValue()) + " - " + CompressHelper.m71746c1(bundle.getString("version"));
                    } else {
                        textView2 = downloadCellViewHolder.f90575J;
                        strM73210M3 = downloadFragment.this.m73210M3(Long.valueOf(bundle.getString("fileSize")).longValue());
                    }
                    textView2.setText(strM73210M3);
                    textView3 = downloadCellViewHolder.f90575J;
                    color = downloadFragment.this.m15320b0().getColor(C5562R.color.darkGrey);
                }
                textView3.setTextColor(color);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                downloadCellViewHolder.f90575J.setText("Error occured, try again");
                e2.printStackTrace();
            }
            if (!bundle.containsKey("Update")) {
                if (bundle.containsKey("Buy")) {
                    if (!bundle.getString("price").equals("0")) {
                        downloadCellViewHolder.f90580O.setText(bundle.getString("price") + " T");
                        if (downloadFragment.this.f90501n4.m73460v(bundle.getString("type"))) {
                            downloadCellViewHolder.f90577L.setVisibility(0);
                            textView = downloadCellViewHolder.f90577L;
                            onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.5
                                @Override // android.view.View.OnClickListener
                                public void onClick(View view) {
                                    downloadFragment.this.m73221T2(bundle, "Buy");
                                    downloadFragment.this.m73213O2();
                                }
                            };
                            textView.setOnClickListener(onClickListener);
                        }
                    }
                    downloadCellViewHolder.f90580O.setText("Free");
                } else {
                    downloadCellViewHolder.f90580O.setText("Download");
                }
                downloadCellViewHolder.f90577L.setVisibility(8);
            } else if (bundle.containsKey("Buy")) {
                if (!bundle.getString("price").equals("0")) {
                    downloadCellViewHolder.f90580O.setText(bundle.getString("price") + " T");
                    if (downloadFragment.this.f90501n4.m73460v(bundle.getString("type"))) {
                        downloadCellViewHolder.f90577L.setVisibility(0);
                        textView = downloadCellViewHolder.f90577L;
                        onClickListener = new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.4
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                downloadFragment.this.m73221T2(bundle, "Buy");
                                downloadFragment.this.m73213O2();
                                view.setVisibility(8);
                            }
                        };
                        textView.setOnClickListener(onClickListener);
                    }
                    downloadCellViewHolder.f90577L.setVisibility(8);
                }
                downloadCellViewHolder.f90580O.setText("Free");
            } else {
                downloadCellViewHolder.f90580O.setText("Update");
            }
            downloadCellViewHolder.f90580O.setOnClickListener(new ViewOnClickListenerC54716(bundle));
            downloadCellViewHolder.f90580O.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.7
                @Override // android.view.View.OnLongClickListener
                public boolean onLongClick(View view) {
                    return true;
                }
            });
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: T */
        public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
            View viewInflate = LayoutInflater.from(downloadFragment.this.m73240z3()).inflate(C5562R.layout.list_view_item_download, viewGroup, false);
            viewInflate.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.DownloadsAdapter.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    downloadFragment.this.m73197B3();
                }
            });
            return downloadFragment.this.new DownloadCellViewHolder(viewInflate);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.Adapter
        /* renamed from: b */
        public int mo26171b() {
            if (downloadFragment.f90440N4 == null) {
                return 0;
            }
            return downloadFragment.f90440N4.size();
        }

        /* renamed from: f0 */
        public int m73269f0(Bundle bundle, String str) {
            if (bundle.containsKey(str)) {
                return bundle.getInt(str);
            }
            return 0;
        }

        /* renamed from: i0 */
        public void m73270i0(ImageView imageView, Bundle bundle) {
            RequestBuilder<Drawable> requestBuilderMo30129t;
            ArrayList arrayList = new ArrayList();
            arrayList.add("visualdx.png");
            arrayList.add("uptodate.png");
            arrayList.add("irandarou.png");
            if (arrayList.contains(bundle.getString("Icon"))) {
                requestBuilderMo30129t = Glide.m30040F(downloadFragment.this).mo30122g(Uri.parse("file:///android_asset/" + bundle.getString("Icon")));
            } else {
                requestBuilderMo30129t = Glide.m30040F(downloadFragment.this).mo30129t(downloadFragment.this.f90487I4.m71810S1("http://" + downloadFragment.this.f90513z4 + ".imedicaldoctor.net/Icons/" + bundle.getString("Icon")));
            }
            requestBuilderMo30129t.m30165B2(imageView);
        }
    }

    public class PackageCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        private final TextView f90611I;

        /* renamed from: J */
        private final Button f90612J;

        public PackageCellViewHolder(View view) {
            super(view);
            this.f90611I = (TextView) view.findViewById(C5562R.id.title);
            this.f90612J = (Button) view.findViewById(C5562R.id.download_button);
        }
    }

    /* renamed from: F3 */
    private Observable<String> m73170F3(final Bundle bundle) {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.40
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                FileOutputStream fileOutputStream;
                String str;
                String str2;
                String str3;
                String strM71904y = downloadFragment.this.f90487I4.m71904y();
                StringBuilder sb = new StringBuilder();
                sb.append(strM71904y);
                String str4 = "/";
                sb.append("/");
                String str5 = "FileName";
                sb.append(bundle.getString("FileName"));
                sb.append(".tmp");
                String string = sb.toString();
                File file = new File(string);
                if (file.exists()) {
                    iMDLogger.m73554j("joinFiles", string + " Already exists : " + downloadFragment.this.m73210M3(file.length()));
                    file.length();
                    downloadFragment.this.f90487I4.m71857j(file.getAbsolutePath());
                } else {
                    try {
                        if (!file.createNewFile()) {
                            iMDLogger.m73550f("JoinFiles", "Error in creating " + string + " Without error");
                        }
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("JoinFiles", "Error in creating " + string + " With Error : " + e2);
                        observableEmitter.onError(e2);
                    }
                }
                try {
                    fileOutputStream = new FileOutputStream(file);
                } catch (Exception e3) {
                    FirebaseCrashlytics.m48010d().m48016g(e3);
                    iMDLogger.m73550f("JoiningFiles", "Error in opening file output stream for " + file.getAbsolutePath());
                    fileOutputStream = null;
                }
                FileOutputStream fileOutputStream2 = fileOutputStream;
                if (fileOutputStream2 == null) {
                    observableEmitter.onError(new Throwable("No Opening Stream"));
                }
                int i2 = 1;
                long j2 = 0;
                while (i2 < 11) {
                    String str6 = strM71904y + str4 + bundle.getString(str5) + "." + i2;
                    File file2 = new File(str6);
                    if (file2.exists()) {
                        str = string;
                    } else {
                        iMDLogger.m73550f("JoinFiles", str6 + " don't exist");
                        str = string;
                        downloadFragment.this.f90487I4.m71857j(file.getAbsolutePath());
                        observableEmitter.onError(new Throwable(str6 + " don't exist"));
                    }
                    String str7 = str5;
                    try {
                        long length = file2.length();
                        FileInputStream fileInputStream = new FileInputStream(file2);
                        int i3 = 0;
                        while (i3 < length) {
                            int i4 = i3 + 1000000;
                            str2 = strM71904y;
                            str3 = str4;
                            if (i4 > length) {
                                int i5 = ((int) length) - i3;
                                try {
                                    byte[] bArr = new byte[i5];
                                    fileInputStream.read(bArr, 0, i5);
                                    fileOutputStream2.write(bArr);
                                } catch (Exception e4) {
                                    e = e4;
                                    FirebaseCrashlytics.m48010d().m48016g(e);
                                    iMDLogger.m73550f("Join Files", "Error combining parts of " + bundle.getString("PartFileSize") + " with error : " + e);
                                    observableEmitter.onError(e);
                                    i2++;
                                    string = str;
                                    str5 = str7;
                                    strM71904y = str2;
                                    str4 = str3;
                                }
                            } else {
                                byte[] bArr2 = new byte[1000000];
                                fileInputStream.read(bArr2, 0, 1000000);
                                fileOutputStream2.write(bArr2);
                            }
                            i3 = i4;
                            strM71904y = str2;
                            str4 = str3;
                        }
                        str2 = strM71904y;
                        str3 = str4;
                        fileInputStream.close();
                        j2 += length;
                        iMDLogger.m73554j("JoinFiles", "IN " + i2 + " :" + j2 + " , " + file.length());
                    } catch (Exception e5) {
                        e = e5;
                        str2 = strM71904y;
                        str3 = str4;
                    }
                    i2++;
                    string = str;
                    str5 = str7;
                    strM71904y = str2;
                    str4 = str3;
                }
                String str8 = strM71904y;
                String str9 = str4;
                String str10 = string;
                String str11 = str5;
                fileOutputStream2.close();
                iMDLogger.m73554j("Joining Files", "Compare " + file.length() + " With " + bundle.getString("fileSize"));
                long jAbs = Math.abs(file.length() - Long.valueOf(bundle.getString("fileSize")).longValue());
                if (jAbs != 0 && jAbs != 16) {
                    Log.d("Joining Files", "Comparing failed. deleteing all parts");
                    for (int i6 = 1; i6 < 11; i6++) {
                        String str12 = str8 + str9 + bundle.getString(str11) + "." + i6;
                        new File(str12);
                        downloadFragment.this.f90487I4.m71857j(str12);
                    }
                    observableEmitter.onError(new Throwable("Rebuild unsuccesfull"));
                    return;
                }
                int i7 = 1;
                file.setReadable(true, false);
                while (i7 < 11) {
                    StringBuilder sb2 = new StringBuilder();
                    String str13 = str10;
                    sb2.append(str13);
                    sb2.append(".");
                    sb2.append(i7);
                    String strReplace = sb2.toString().replace("tmp.", "");
                    File file3 = new File(strReplace);
                    try {
                        downloadFragment.this.f90487I4.m71857j(strReplace);
                    } catch (Exception e6) {
                        FirebaseCrashlytics.m48010d().m48016g(e6);
                        file3.deleteOnExit();
                    }
                    i7++;
                    str10 = str13;
                }
                File file4 = new File(str8 + str9 + bundle.getString(str11));
                if (bundle.containsKey("savePathKey")) {
                    file4 = new File(bundle.getString("savePathKey"));
                    if (!file4.getParentFile().exists()) {
                        file4.getParentFile().mkdirs();
                    }
                }
                if (file4.exists()) {
                    downloadFragment.this.f90487I4.m71857j(file4.getAbsolutePath());
                }
                try {
                    downloadFragment.this.m73171L3(file, file4);
                } catch (Exception unused) {
                    observableEmitter.onError(new Throwable("Renamed failed to " + file4.getAbsolutePath()));
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: L3 */
    public void m73171L3(File file, File file2) throws Throwable {
        FileChannel channel;
        FileChannel channel2 = null;
        try {
            channel = new FileOutputStream(file2).getChannel();
            try {
                channel2 = new FileInputStream(file).getChannel();
                channel2.transferTo(0L, channel2.size(), channel);
                channel2.close();
                this.f90487I4.m71857j(file.getAbsolutePath());
                channel2.close();
                if (channel != null) {
                    channel.close();
                }
            } catch (Throwable th) {
                th = th;
                if (channel2 != null) {
                    channel2.close();
                }
                if (channel != null) {
                    channel.close();
                }
                throw th;
            }
        } catch (Throwable th2) {
            th = th2;
            channel = null;
        }
    }

    /* renamed from: P3 */
    private String m73172P3(String str) {
        return str;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: Q2 */
    public void m73173Q2() {
        VBHelper vBHelper = this.f90501n4;
        String strM73462x = vBHelper.m73462x(vBHelper.m73451m());
        if (strM73462x == null) {
            return;
        }
        String[] strArrSplit = TextUtils.split(strM73462x.replace("||", "::"), "::");
        String str = strArrSplit[1];
        String[] strArrSplit2 = TextUtils.split(strArrSplit[3], ",");
        f90451Y4 = strArrSplit[5];
        new ArrayList(Arrays.asList(strArrSplit2));
        if (f90450X4 == null) {
            f90450X4 = this.f90501n4.m73464z();
        }
        f90452Z4 = strArrSplit[4];
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: U2 */
    public void m73174U2() {
        String str;
        if (this.f90485G4 != null) {
            ((TextView) this.f90496i4.findViewById(C5562R.id.status_label)).setText(this.f90485G4);
            this.f90485G4 = null;
            return;
        }
        ArrayList<Bundle> arrayList = f90440N4;
        if (arrayList != null && arrayList.size() != 0) {
            m73227W3();
            m73213O2();
            this.f90497j4.m27425X1(0);
            return;
        }
        if (!CompressHelper.f87349x) {
            String str2 = f90453a5;
            if (str2 == null || str2.length() <= 0) {
                TabLayout tabLayout = this.f90505r4;
                if (tabLayout.m40224D(tabLayout.getSelectedTabPosition()).m40287n().equals("Updates")) {
                    str = "All your databases are up to date.";
                } else {
                    TabLayout tabLayout2 = this.f90505r4;
                    if (tabLayout2.m40224D(tabLayout2.getSelectedTabPosition()).m40287n().equals("Paid")) {
                        str = "You haven't purchased any databases yet";
                    }
                }
            }
            m73229X3("No Databases Found");
            return;
        }
        str = "Loading Databases";
        m73229X3(str);
    }

    /* renamed from: A3 */
    public String m73196A3(Bundle bundle) {
        StringBuilder sb;
        String str;
        String str2 = "<font color=\"#337a33\" size=\"18\"><b>" + bundle.getString("Title") + " </b></font></div><br/>";
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(bundle.getString("Titles"), "||||");
        for (int i2 = 0; i2 < strArrSplitByWholeSeparator.length; i2++) {
            String str3 = StringUtils.f103471LF + strArrSplitByWholeSeparator[i2];
            if (i2 % 2 == 0) {
                sb = new StringBuilder();
                str = "<font color=\"#000000\" size=\"14\"><b>";
            } else {
                sb = new StringBuilder();
                str = "<font color=\"#777777\" size=\"14\"><b>";
            }
            sb.append(str);
            sb.append(str3);
            sb.append(" </b></font></div><br/>");
            String string = sb.toString();
            if (i2 == strArrSplitByWholeSeparator.length - 1) {
                string = string.replace("<br/>", "");
            }
            str2 = str2 + string;
        }
        return str2;
    }

    /* renamed from: B3 */
    public void m73197B3() {
        try {
            ((InputMethodManager) m73240z3().getSystemService("input_method")).hideSoftInputFromWindow(m73240z3().getCurrentFocus().getWindowToken(), 0);
            if (m73240z3().getCurrentFocus() != null) {
                m73240z3().getCurrentFocus().clearFocus();
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: C3 */
    public boolean m73198C3() {
        Bundle bundle = this.f90493f4;
        if (bundle == null) {
            return false;
        }
        Iterator<String> it2 = bundle.keySet().iterator();
        while (it2.hasNext()) {
            if (this.f90493f4.getBundle(it2.next()).containsKey("downloader")) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: D3 */
    public boolean m73199D3(String str) {
        Bundle bundle = this.f90502o4;
        if (bundle == null) {
            return false;
        }
        return bundle.containsKey(str);
    }

    /* renamed from: E3 */
    public boolean m73200E3(String str) {
        TabLayout tabLayout = this.f90505r4;
        tabLayout.m40224D(tabLayout.getSelectedTabPosition());
        TabLayout tabLayout2 = this.f90505r4;
        return tabLayout2.m40224D(tabLayout2.getSelectedTabPosition()).m40287n().toString().toLowerCase().equals(str.toLowerCase());
    }

    /* renamed from: G3 */
    public boolean m73201G3() {
        ArrayList<Bundle> arrayList = ((iMD) m15366r().getApplicationContext()).f101678s;
        if (arrayList == null || arrayList.size() == 0) {
            return true;
        }
        return m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("loaddownload", false);
    }

    /* JADX WARN: Removed duplicated region for block: B:131:0x0649  */
    /* JADX WARN: Removed duplicated region for block: B:140:0x0671  */
    /* JADX WARN: Removed duplicated region for block: B:147:0x068c  */
    /* JADX WARN: Removed duplicated region for block: B:148:0x0690  */
    /* renamed from: H3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m73202H3() {
        /*
            Method dump skipped, instructions count: 1723
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.downloadFragment.m73202H3():void");
    }

    /* renamed from: I3 */
    public Observable<String> m73203I3() {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.13
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    downloadFragment.this.m73202H3();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: J2 */
    public float m73204J2(int i2, String str) {
        if (!f90454b5.containsKey(str)) {
            f90454b5.putIntegerArrayList(str, new ArrayList<>());
            return 0.0f;
        }
        ArrayList<Integer> integerArrayList = f90454b5.getIntegerArrayList(str);
        if (integerArrayList.size() > 5) {
            integerArrayList.remove(0);
        }
        integerArrayList.add(Integer.valueOf(i2));
        return m73206K2(integerArrayList) / 2.0f;
    }

    /* renamed from: J3 */
    public void m73205J3() {
        if (this.f90481C4) {
            return;
        }
        this.f90481C4 = true;
        this.f90497j4.m27434b2();
        final long jCurrentTimeMillis = System.currentTimeMillis();
        this.f90503p4 = jCurrentTimeMillis;
        iMDLogger.m73550f("DownloadAndLoadDBs", "Successful");
        iMDLogger.m73550f("DownloadAndLoadDBs", "Loading Downloads");
        iMDLogger.m73550f("CompressHelper", "Loading DBs after download");
        Observable<String> observableM73203I3 = m73203I3();
        this.f90494g4 = observableM73203I3;
        Observable<String> observableM59775s4 = observableM73203I3.m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e());
        this.f90494g4 = observableM59775s4;
        observableM59775s4.m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.23
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.24
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                try {
                    downloadFragment.this.f90481C4 = false;
                    th.printStackTrace();
                    iMDLogger.m73550f("downloadFragment", "Error in loaddownloads");
                    downloadFragment.this.m73229X3("Error occured . Tap to Try Again");
                    th.printStackTrace();
                } catch (Exception unused) {
                }
            }
        }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.25
            @Override // io.reactivex.rxjava3.functions.Action
            public void run() throws Throwable {
                iMDLogger.m73550f("downloadFragment", "Search called");
                if (downloadFragment.f90440N4 == null) {
                    downloadFragment.this.f90481C4 = false;
                    downloadFragment.this.m73229X3("Error occured in reading database . Tap to try again");
                    return;
                }
                long j2 = jCurrentTimeMillis;
                downloadFragment downloadfragment = downloadFragment.this;
                if (j2 != downloadfragment.f90503p4) {
                    Log.e("download", "other query executed");
                } else {
                    downloadfragment.f90481C4 = false;
                    downloadFragment.this.m73174U2();
                }
            }
        });
    }

    /* renamed from: K2 */
    public float m73206K2(ArrayList<Integer> arrayList) {
        long jIntValue = 0;
        while (arrayList.iterator().hasNext()) {
            jIntValue += r0.next().intValue();
        }
        return jIntValue / arrayList.size();
    }

    /* renamed from: K3 */
    public boolean m73207K3() {
        return this.f90506s4.getVisibility() != 8;
    }

    /* JADX WARN: Removed duplicated region for block: B:40:0x01a9  */
    /* JADX WARN: Removed duplicated region for block: B:42:0x01af  */
    /* JADX WARN: Removed duplicated region for block: B:46:0x01c7  */
    /* JADX WARN: Removed duplicated region for block: B:63:0x01cc A[SYNTHETIC] */
    /* renamed from: L2 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m73208L2(android.os.Bundle r24) {
        /*
            Method dump skipped, instructions count: 484
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.downloadFragment.m73208L2(android.os.Bundle):void");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: M0 */
    public void mo15284M0(Activity activity) {
        super.mo15284M0(activity);
        this.f90482D4 = activity;
    }

    /* renamed from: M2 */
    public void m73209M2() {
        this.f90506s4.setVisibility(8);
        SearchView searchView = this.f90500m4;
        if (searchView != null) {
            searchView.clearFocus();
        }
        m73229X3("Loading Databases");
        iMDLogger.m73550f("DownloadAndLoadDBs", "Starting");
        if (m73215P2().booleanValue()) {
            this.f90487I4.m71863l(this).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.20
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(String str) throws Throwable {
                }
            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.21
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Throwable th) throws Throwable {
                    try {
                        iMDLogger.m73550f("DownloadAndLoadDBs", "Error Occured");
                        downloadFragment.this.m73229X3("Failed loading databases .tap to try again");
                    } catch (Exception unused) {
                    }
                }
            }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.22
                @Override // io.reactivex.rxjava3.functions.Action
                public void run() throws Throwable {
                    ArrayList unused = downloadFragment.f90449W4 = null;
                    downloadFragment.this.f90495h4 = null;
                    downloadFragment.this.m73205J3();
                    LocalBroadcastManager.m16410b(downloadFragment.this.m15366r()).m16413d(new Intent("reloadaccountdownloads"));
                    downloadFragment.this.m73236v3();
                }
            });
        } else {
            iMDLogger.m73550f("DownloadAndLoadDBs", "No Permission");
            m73229X3("Storage Permission not granted. Tap to Allow");
        }
    }

    /* renamed from: M3 */
    public String m73210M3(long j2) {
        if (j2 <= 0) {
            return "0";
        }
        double d2 = j2;
        int iLog10 = (int) (Math.log10(d2) / Math.log10(1024.0d));
        return new DecimalFormat("#,##0.#").format(d2 / Math.pow(1024.0d, iLog10)) + StringUtils.SPACE + new String[]{"B", "KB", "MB", "GB", "TB"}[iLog10];
    }

    /* renamed from: N2 */
    public String m73211N2(String str) {
        if (this.f90502o4.containsKey(str)) {
            return this.f90502o4.getString(str);
        }
        return null;
    }

    /* renamed from: N3 */
    public void m73212N3() {
        iMDLogger.m73550f("RefereshDBs", "Clicked");
        try {
            this.f90487I4.m71857j(this.f90487I4.m71816U1() + "/DBs.db");
            this.f90487I4.m71857j(this.f90487I4.m71816U1() + "/DBs.z");
            this.f90487I4.m71857j(this.f90487I4.m71816U1() + "/DBs.md5");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        iMDLogger.m73550f("RefreshDBs", "DownloadingAndLoadDBS");
        if (m73207K3()) {
            return;
        }
        m73209M2();
    }

    /* renamed from: O2 */
    public void m73213O2() {
        if (this.f90481C4) {
            return;
        }
        iMDLogger.m73550f("download", "NotifyDatasetChange");
        this.f90497j4.getRecycledViewPool().m27690c();
        this.f90497j4.getAdapter().m27491G();
    }

    /* renamed from: O3 */
    public Observable<Bundle> m73214O3() {
        return Observable.m59451w1(new ObservableOnSubscribe<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.10
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<Bundle> observableEmitter) throws Throwable {
                if (downloadFragment.this.f90493f4 == null) {
                    return;
                }
                Iterator<String> it2 = downloadFragment.this.f90493f4.keySet().iterator();
                while (it2.hasNext()) {
                    Bundle bundle = downloadFragment.this.f90493f4.getBundle(it2.next());
                    if (downloadFragment.this.m73230Y2(bundle) != 100) {
                        observableEmitter.onNext(bundle);
                    }
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: P2 */
    public Boolean m73215P2() {
        return Boolean.TRUE;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: Q0 */
    public void mo15207Q0(Bundle bundle) {
        super.mo15207Q0(bundle);
        LocalBroadcastManager.m16410b(m73240z3()).m16412c(this.f90491M4, new IntentFilter("reloadDownloads"));
    }

    /* renamed from: Q3 */
    public void m73216Q3() {
        try {
            iMDLogger.m73548d("sendFavorite", "Sending FavoriteChanged message");
            LocalBroadcastManager.m16410b(m73240z3()).m16413d(new Intent("referesh.account"));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    /* renamed from: R2 */
    public int m73217R2(Bundle bundle, String str) {
        if (bundle.containsKey(str)) {
            return bundle.getInt(str);
        }
        return 0;
    }

    /* renamed from: R3 */
    public void m73218R3(String str, Runnable runnable) {
        f90443Q4.remove(str);
        f90443Q4.put(str, runnable);
    }

    /* renamed from: S2 */
    public Observable<String> m73219S2() {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.11
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
            }
        });
    }

    /* renamed from: S3 */
    public void m73220S3(String str, Runnable runnable) {
        f90444R4.remove(str);
        f90444R4.put(str, runnable);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: T0 */
    public void mo15301T0(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(C5562R.menu.download, menu);
        final SearchView searchView = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
        MenuItem menuItemFindItem = menu.findItem(C5562R.id.progress_menu);
        this.f90498k4 = menuItemFindItem;
        this.f90499l4 = (ProgressBar) menuItemFindItem.getActionView();
        this.f90500m4 = searchView;
        if (Build.VERSION.SDK_INT >= 26) {
            searchView.setImportantForAutofill(8);
        }
        searchView.setIconifiedByDefault(false);
        searchView.setQueryHint("Search Store");
        final String str = this.f90512y4;
        this.f90500m4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.27
            @Override // java.lang.Runnable
            public void run() {
                iMDLogger.m73554j("DownloadFragment", "Running post delay");
                downloadFragment.this.f90480B4 = true;
                downloadFragment.this.f90500m4.m2508k0(str, false);
                if (downloadFragment.this.f90479A4 == null) {
                    iMDLogger.m73554j("DownloadFragment", "mQuery is null");
                    return;
                }
                if (downloadFragment.this.f90479A4.length() == 0) {
                    iMDLogger.m73554j("DownloadFragment", "mQuery is 0 length !!");
                }
                if (downloadFragment.this.f90479A4 == null || downloadFragment.this.f90479A4.length() <= 0) {
                    return;
                }
                iMDLogger.m73554j("DownloadFragment", "mQuery is " + downloadFragment.this.f90479A4);
                if (downloadFragment.f90440N4 == null || downloadFragment.f90440N4.size() == 0) {
                    iMDLogger.m73554j("DownloadFragment", "setting query true");
                    downloadFragment downloadfragment = downloadFragment.this;
                    downloadfragment.f90500m4.m2508k0(downloadfragment.f90479A4, true);
                } else {
                    iMDLogger.m73554j("DownloadFragment", "setting query false");
                    downloadFragment downloadfragment2 = downloadFragment.this;
                    downloadfragment2.f90500m4.m2508k0(downloadfragment2.f90479A4, false);
                    downloadFragment.this.m73227W3();
                }
                downloadFragment.this.m73197B3();
            }
        }, 10L);
        this.f90480B4 = false;
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.28
            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: a */
            public boolean mo2514a(String str2) {
                if (!downloadFragment.this.f90480B4) {
                    return true;
                }
                if (downloadFragment.this.f90512y4 == null && (str2 == null || str2.length() == 0)) {
                    return true;
                }
                downloadFragment.this.f90512y4 = str2;
                downloadFragment.this.f90479A4 = str2;
                String unused = downloadFragment.f90453a5 = str2;
                downloadFragment.this.m73205J3();
                return true;
            }

            @Override // androidx.appcompat.widget.SearchView.OnQueryTextListener
            /* renamed from: b */
            public boolean mo2515b(String str2) {
                return false;
            }
        });
        ((ImageView) searchView.findViewById(C5562R.id.search_close_btn)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.29
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                searchView.m2508k0("", false);
                searchView.clearFocus();
                String unused = downloadFragment.f90453a5 = null;
                downloadFragment.this.f90512y4 = null;
                downloadFragment.this.f90479A4 = null;
                downloadFragment.this.m73205J3();
                downloadFragment.this.m73197B3();
            }
        });
        super.mo15301T0(menu, menuInflater);
    }

    /* renamed from: T2 */
    public void m73221T2(Bundle bundle, String str) {
        if (str.equals("downloader") && bundle.containsKey("id")) {
            Log.e("here", "here");
        }
        if (bundle.containsKey(str)) {
            bundle.remove(str);
        }
    }

    /* renamed from: T3 */
    public void m73222T3(String str, Runnable runnable) {
        f90445S4.remove(str);
        f90445S4.put(str, runnable);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        this.f90483E4 = Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf");
        this.f90493f4 = new Bundle();
        ((iMD) m15366r().getApplicationContext()).f101675c3 = this;
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_download, viewGroup, false);
        this.f90487I4 = new CompressHelper(m15366r());
        this.f90501n4 = new VBHelper(m15366r());
        if (bundle != null && bundle.containsKey("Query")) {
            this.f90479A4 = bundle.getString("Query");
            iMDLogger.m73554j("DownloadFragment", "Loading mQuery " + this.f90479A4);
        }
        if (bundle != null && bundle.containsKey("LastStatus")) {
            this.f90485G4 = bundle.getString("LastStatus");
        }
        this.f90496i4 = viewInflate;
        RecyclerView recyclerView = (RecyclerView) viewInflate.findViewById(C5562R.id.recycler_view);
        this.f90497j4 = recyclerView;
        recyclerView.m27459p(new CustomItemDecoration(m15366r()));
        BetterLinearLayoutManager betterLinearLayoutManager = new BetterLinearLayoutManager(m73240z3(), 1, false);
        this.f90484F4 = betterLinearLayoutManager;
        this.f90497j4.setLayoutManager(betterLinearLayoutManager);
        this.f90497j4.setItemAnimator(null);
        m15358o2(true);
        f90442P4 = new HashMap<>();
        f90443Q4 = new HashMap<>();
        f90444R4 = new HashMap<>();
        f90445S4 = new HashMap<>();
        f90446T4 = new ArrayList<>();
        DownloadsAdapter downloadsAdapter = new DownloadsAdapter();
        this.f90488J4 = downloadsAdapter;
        this.f90497j4.setAdapter(downloadsAdapter);
        this.f90505r4 = (TabLayout) this.f90496i4.findViewById(C5562R.id.tab_layout);
        this.f90506s4 = (CardView) this.f90496i4.findViewById(C5562R.id.load_screen);
        this.f90507t4 = (ImageView) this.f90496i4.findViewById(C5562R.id.load_image);
        this.f90508u4 = (Button) this.f90496i4.findViewById(C5562R.id.load_button);
        this.f90506s4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                downloadFragment.this.m73209M2();
            }
        });
        this.f90508u4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                downloadFragment.this.m73209M2();
            }
        });
        this.f90507t4.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                downloadFragment.this.m73209M2();
            }
        });
        this.f90508u4.setTypeface(this.f90483E4);
        f90453a5 = "";
        m73231Y3();
        if (f90454b5 == null) {
            f90454b5 = new Bundle();
            f90456d5 = new HashMap<>();
            this.f90505r4.m40224D(0).m40291r();
            m73173Q2();
            if (m73201G3()) {
                m73209M2();
            }
        } else {
            this.f90505r4.m40224D(f90455c5).m40291r();
        }
        this.f90505r4.m40247h(new TabLayout.OnTabSelectedListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.4
            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: a */
            public void mo40255a(TabLayout.Tab tab) {
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: b */
            public void mo40256b(TabLayout.Tab tab) {
                downloadFragment.f90455c5 = tab.m40284k();
                downloadFragment.this.m73205J3();
            }

            @Override // com.google.android.material.tabs.TabLayout.BaseOnTabSelectedListener
            /* renamed from: c */
            public void mo40257c(TabLayout.Tab tab) {
            }
        });
        return this.f90496i4;
    }

    /* renamed from: U3 */
    public void m73223U3(String str, CircleProgressView circleProgressView) {
        f90442P4.remove(str);
        f90442P4.put(str, circleProgressView);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: V0 */
    public void mo15306V0() {
        super.mo15306V0();
        try {
            LocalBroadcastManager.m16410b(m73240z3()).m16415f(this.f90491M4);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:40:0x00e3  */
    /* JADX WARN: Removed duplicated region for block: B:44:0x0130  */
    /* JADX WARN: Removed duplicated region for block: B:53:0x01d4  */
    /* JADX WARN: Removed duplicated region for block: B:55:0x023d  */
    /* JADX WARN: Type inference failed for: r16v10 */
    /* JADX WARN: Type inference failed for: r16v11 */
    /* JADX WARN: Type inference failed for: r16v16 */
    /* JADX WARN: Type inference failed for: r16v17 */
    /* JADX WARN: Type inference failed for: r16v18 */
    /* JADX WARN: Type inference failed for: r16v19 */
    /* JADX WARN: Type inference failed for: r16v21 */
    /* JADX WARN: Type inference failed for: r16v8, types: [int] */
    /* JADX WARN: Type inference failed for: r16v9 */
    /* renamed from: V2 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m73224V2(android.os.Bundle r36) throws java.lang.NumberFormatException {
        /*
            Method dump skipped, instructions count: 1112
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.downloadFragment.m73224V2(android.os.Bundle):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:19:0x00af A[PHI: r0
      0x00af: PHI (r0v5 java.lang.String) = (r0v3 java.lang.String), (r0v2 java.lang.String) binds: [B:24:0x00f1, B:18:0x00ad] A[DONT_GENERATE, DONT_INLINE]] */
    /* renamed from: V3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m73225V3(android.os.Bundle r6, java.lang.String r7) {
        /*
            r5 = this;
            java.lang.String r0 = "DownloadPath"
            boolean r1 = r7.equals(r0)
            r2 = 0
            java.lang.String r3 = "default_preferences"
            if (r1 == 0) goto L56
            if (r6 == 0) goto Lf4
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            boolean r7 = r7.contains(r0)
            if (r7 == 0) goto L2e
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            android.content.SharedPreferences$Editor r7 = r7.edit()
            android.content.SharedPreferences$Editor r7 = r7.remove(r0)
            r7.commit()
        L2e:
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            android.content.SharedPreferences$Editor r7 = r7.edit()
            java.lang.String r1 = "Path"
            java.lang.String r6 = r6.getString(r1)
            android.content.SharedPreferences$Editor r6 = r7.putString(r0, r6)
            r6.commit()
            androidx.fragment.app.FragmentActivity r6 = r5.m15366r()
            android.content.Context r6 = r6.getApplicationContext()
            net.imedicaldoctor.imd.iMD r6 = (net.imedicaldoctor.imd.iMD) r6
            r7 = 0
            r6.f101671Z = r7
            goto Lf4
        L56:
            java.lang.String r0 = "Tab"
            boolean r1 = r7.equals(r0)
            java.lang.String r4 = "Title"
            if (r1 == 0) goto L99
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            boolean r7 = r7.contains(r0)
            if (r7 == 0) goto L81
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            android.content.SharedPreferences$Editor r7 = r7.edit()
            android.content.SharedPreferences$Editor r7 = r7.remove(r0)
            r7.commit()
        L81:
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            android.content.SharedPreferences$Editor r7 = r7.edit()
            java.lang.String r6 = r6.getString(r4)
            android.content.SharedPreferences$Editor r6 = r7.putString(r0, r6)
            r6.commit()
            goto Lf4
        L99:
            java.lang.String r0 = "SearchResult"
            boolean r1 = r7.equals(r0)
            if (r1 == 0) goto Ldd
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            boolean r7 = r7.contains(r0)
            if (r7 == 0) goto Lc2
        Laf:
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            android.content.SharedPreferences$Editor r7 = r7.edit()
            android.content.SharedPreferences$Editor r7 = r7.remove(r0)
            r7.commit()
        Lc2:
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            android.content.SharedPreferences$Editor r7 = r7.edit()
            java.lang.String r6 = r6.getString(r4)
            android.content.SharedPreferences$Editor r6 = r7.putString(r0, r6)
            r6.commit()
            r5.m73213O2()
            goto Lf4
        Ldd:
            java.lang.String r0 = "ContentSearchResult"
            boolean r7 = r7.equals(r0)
            if (r7 == 0) goto Lf4
            androidx.fragment.app.FragmentActivity r7 = r5.m15307V1()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r3, r2)
            boolean r7 = r7.contains(r0)
            if (r7 == 0) goto Lc2
            goto Laf
        Lf4:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.downloadFragment.m73225V3(android.os.Bundle, java.lang.String):void");
    }

    /* renamed from: W2 */
    public void m73226W2(final Bundle bundle) {
        m73221T2(bundle, "downloader");
        m73228X2(bundle);
        this.f90487I4.m71787H0(m73240z3(), Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.38
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                ArrayList parcelableArrayList;
                iMDLogger.m73548d("StopDownload", bundle.getString("URL"));
                if (bundle.containsKey("Parts") && (parcelableArrayList = bundle.getParcelableArrayList("Parts")) != null) {
                    for (int i2 = 0; i2 < parcelableArrayList.size(); i2++) {
                        Bundle bundle2 = (Bundle) parcelableArrayList.get(i2);
                        if (bundle2.containsKey("downloader")) {
                            downloadFragment.this.m73221T2(bundle2.getBundle("downloader"), "Go");
                            bundle2.remove("downloader");
                        }
                    }
                    bundle.remove("downloader");
                    bundle.remove("Parts");
                }
            }
        })).m59675d6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.39
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                downloadFragment.this.m73228X2(bundle);
            }
        });
    }

    /* renamed from: W3 */
    public void m73227W3() {
        if (this.f90481C4) {
            return;
        }
        RecyclerView.Adapter adapter = this.f90497j4.getAdapter();
        DownloadsAdapter downloadsAdapter = this.f90488J4;
        if (adapter != downloadsAdapter) {
            this.f90497j4.setAdapter(downloadsAdapter);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:31:0x00b7  */
    /* JADX WARN: Removed duplicated region for block: B:33:0x00c2  */
    /* renamed from: X2 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m73228X2(android.os.Bundle r5) {
        /*
            r4 = this;
            boolean r0 = r4.f90481C4
            if (r0 == 0) goto L5
            return
        L5:
            java.lang.String r0 = "videoIdKey"
            boolean r1 = r5.containsKey(r0)
            if (r1 == 0) goto L4b
            java.util.HashMap<java.lang.String, at.grabner.circleprogress.CircleProgressView> r1 = net.imedicaldoctor.imd.Fragments.downloadFragment.f90442P4
            java.lang.String r2 = r5.getString(r0)
            boolean r1 = r1.containsKey(r2)
            if (r1 == 0) goto L2f
            java.util.HashMap<java.lang.String, at.grabner.circleprogress.CircleProgressView> r1 = net.imedicaldoctor.imd.Fragments.downloadFragment.f90442P4
            java.lang.String r2 = r5.getString(r0)
            java.lang.Object r1 = r1.get(r2)
            at.grabner.circleprogress.CircleProgressView r1 = (at.grabner.circleprogress.CircleProgressView) r1
            java.lang.String r2 = "Progress"
            int r2 = r4.m73217R2(r5, r2)
            float r2 = (float) r2
            r1.setValue(r2)
        L2f:
            java.util.HashMap<java.lang.String, java.lang.Runnable> r1 = net.imedicaldoctor.imd.Fragments.downloadFragment.f90445S4
            java.lang.String r2 = r5.getString(r0)
            boolean r1 = r1.containsKey(r2)
            if (r1 == 0) goto L4a
            java.util.HashMap<java.lang.String, java.lang.Runnable> r1 = net.imedicaldoctor.imd.Fragments.downloadFragment.f90445S4
            java.lang.String r5 = r5.getString(r0)
            java.lang.Object r5 = r1.get(r5)
            java.lang.Runnable r5 = (java.lang.Runnable) r5
            r5.run()
        L4a:
            return
        L4b:
            java.util.ArrayList<android.os.Bundle> r0 = net.imedicaldoctor.imd.Fragments.downloadFragment.f90440N4
            if (r0 == 0) goto Le7
            android.os.Bundle r0 = r4.f90492e4
            if (r0 == 0) goto Le7
            java.lang.String r1 = "id"
            java.lang.String r2 = r5.getString(r1)
            boolean r0 = r0.containsKey(r2)
            if (r0 == 0) goto Le7
            androidx.recyclerview.widget.RecyclerView r0 = r4.f90497j4
            androidx.recyclerview.widget.RecyclerView$LayoutManager r0 = r0.getLayoutManager()
            net.imedicaldoctor.imd.Fragments.downloadFragment$BetterLinearLayoutManager r0 = (net.imedicaldoctor.imd.Fragments.downloadFragment.BetterLinearLayoutManager) r0
            int r2 = r0.m27176B2()     // Catch: java.lang.Exception -> L72
            int r0 = r0.m27178E2()     // Catch: java.lang.Exception -> L70
            goto L84
        L70:
            r0 = move-exception
            goto L74
        L72:
            r0 = move-exception
            r2 = 0
        L74:
            com.google.firebase.crashlytics.FirebaseCrashlytics r3 = com.google.firebase.crashlytics.FirebaseCrashlytics.m48010d()
            r3.m48016g(r0)
            java.lang.String r0 = "DownloadFragment"
            java.lang.String r3 = "Error in getting first and last visible position"
            net.imedicaldoctor.imd.iMDLogger.m73550f(r0, r3)
            r0 = 10000(0x2710, float:1.4013E-41)
        L84:
            android.os.Bundle r3 = r4.f90492e4
            java.lang.String r5 = r5.getString(r1)
            int r5 = r3.getInt(r5)
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r3 = "Contains "
            r1.append(r3)
            r1.append(r5)
            java.lang.String r3 = " . first : "
            r1.append(r3)
            r1.append(r2)
            java.lang.String r3 = ", Last : "
            r1.append(r3)
            r1.append(r0)
            java.lang.String r1 = r1.toString()
            java.lang.String r3 = "download"
            net.imedicaldoctor.imd.iMDLogger.m73550f(r3, r1)
            r1 = -1
            if (r2 != r1) goto Lc2
            androidx.recyclerview.widget.RecyclerView r5 = r4.f90497j4
            net.imedicaldoctor.imd.Fragments.downloadFragment$DownloadsAdapter r0 = new net.imedicaldoctor.imd.Fragments.downloadFragment$DownloadsAdapter
            r0.<init>()
            r5.setAdapter(r0)
            return
        Lc2:
            if (r5 < r2) goto Le7
            if (r5 > r0) goto Le7
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r1 = "NotifyItemChanged "
            r0.append(r1)
            r0.append(r5)
            java.lang.String r0 = r0.toString()
            net.imedicaldoctor.imd.iMDLogger.m73548d(r3, r0)
            boolean r0 = r4.f90481C4
            if (r0 != 0) goto Le7
            androidx.recyclerview.widget.RecyclerView r0 = r4.f90497j4
            androidx.recyclerview.widget.RecyclerView$Adapter r0 = r0.getAdapter()
            r0.m27492H(r5)
        Le7:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.downloadFragment.m73228X2(android.os.Bundle):void");
    }

    /* renamed from: X3 */
    public void m73229X3(String str) {
        RecyclerView recyclerView;
        StatusAdapter statusAdapter;
        this.f90497j4.setLayoutManager(new BetterLinearLayoutManager(m15366r(), 1, false));
        if (str.toLowerCase().contains("Tap to".toLowerCase())) {
            recyclerView = this.f90497j4;
            statusAdapter = new StatusAdapter(m15366r(), str) { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.12
                @Override // net.imedicaldoctor.imd.ViewHolders.StatusAdapter
                /* renamed from: d0 */
                public void mo73158d0() {
                    downloadFragment.this.m73212N3();
                }
            };
        } else {
            recyclerView = this.f90497j4;
            statusAdapter = new StatusAdapter(m15366r(), str);
        }
        recyclerView.setAdapter(statusAdapter);
    }

    /* renamed from: Y2 */
    public int m73230Y2(Bundle bundle) {
        double d2;
        float f2;
        String str;
        long j2;
        String str2;
        double d3;
        StringBuilder sb;
        if (bundle.containsKey("error") || !bundle.containsKey("downloader")) {
            return 100;
        }
        String strM71904y = this.f90487I4.m71904y();
        ArrayList parcelableArrayList = bundle.getParcelableArrayList("Parts");
        if (parcelableArrayList == null) {
            return 100;
        }
        double length = 0.0d;
        long j3 = 0;
        for (int i2 = 0; i2 < parcelableArrayList.size(); i2++) {
            Bundle bundle2 = (Bundle) parcelableArrayList.get(i2);
            if (bundle2.containsKey("completed")) {
                sb = new StringBuilder();
                sb.append(strM71904y);
                sb.append("/");
                sb.append(bundle2.getString("FileName"));
                length += new File(sb.toString()).length();
            } else {
                sb = new StringBuilder();
                sb.append(strM71904y);
                sb.append("/");
                sb.append(bundle2.getString("FileName"));
                sb.append(".download");
                String string = sb.toString();
                long length2 = new File(string).exists() ? new File(string).length() : 0L;
                length += length2;
                j3 += length2;
            }
        }
        double dDoubleValue = Double.valueOf(bundle.getString("fileSize")).doubleValue();
        float f3 = length > 0.0d ? (((float) length) / ((float) dDoubleValue)) * 100.0f : 0.0f;
        long j4 = j3 - bundle.getLong(f90439J5);
        if (j4 < 0) {
            j4 = 0;
        } else {
            m73221T2(bundle, f90439J5);
            bundle.putLong(f90439J5, j3);
        }
        long time = new Date().getTime();
        if (bundle.containsKey("DateUpdated")) {
            f2 = f3;
            long j5 = bundle.getLong("DateUpdated");
            if (bundle.containsKey("bytesDownloaded")) {
                str2 = "bytesDownloaded";
                d3 = bundle.getDouble("bytesDownloaded");
            } else {
                str2 = "bytesDownloaded";
                d3 = 0.0d;
            }
            d2 = dDoubleValue;
            Log.e("New Speed", "Received: " + length + " , OldDownloaded : " + d3 + ", DAtenow : " + time + ", Date prev :" + j5);
            m73221T2(bundle, "DateUpdated");
            bundle.putLong("DateUpdated", time);
            j2 = (long) ((length - d3) / (((double) (time - j5)) / 1000.0d));
            str = str2;
        } else {
            d2 = dDoubleValue;
            f2 = f3;
            bundle.putLong("DateUpdated", time);
            str = "bytesDownloaded";
            j2 = 0;
        }
        m73221T2(bundle, str);
        m73221T2(bundle, "bytesTotal");
        m73221T2(bundle, "Progress");
        m73221T2(bundle, "remaining");
        m73221T2(bundle, "avgSpeed");
        bundle.putLong("avgSpeed", j2);
        m73204J2((int) j4, bundle.getString("URL"));
        bundle.putDouble(str, length);
        double d4 = d2;
        bundle.putDouble("bytesTotal", d4);
        bundle.putLong("remaining", j2 > 0 ? ((long) (d4 - length)) / j2 : 0L);
        bundle.putInt("Progress", (int) f2);
        return f2 == 100.0f ? 50 : 0;
    }

    /* renamed from: Y3 */
    public void m73231Y3() {
        iMDLogger.m73550f("downloadFragment", "Start Reloading Tables");
        Timer timer = new Timer();
        this.f90510w4 = timer;
        timer.scheduleAtFixedRate(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.9
            @Override // java.util.TimerTask, java.lang.Runnable
            public void run() {
                downloadFragment.this.f90489K4.obtainMessage(1).sendToTarget();
            }
        }, ExoPlayer.f21773a1, ExoPlayer.f21773a1);
    }

    /* renamed from: Z3 */
    public boolean m73232Z3(String str) {
        Bundle bundleM73238x3 = m73238x3(str);
        if (bundleM73238x3 == null) {
            return false;
        }
        m73224V2(bundleM73238x3);
        return true;
    }

    /* renamed from: a4 */
    public boolean m73233a4(String str) {
        Bundle bundleM73238x3 = m73238x3(str);
        if (bundleM73238x3 == null) {
            return false;
        }
        m73226W2(bundleM73238x3);
        return true;
    }

    /* renamed from: b4 */
    public void m73234b4() {
        try {
            m73214O3().m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<Bundle>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.5
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Bundle bundle) throws Throwable {
                    if (bundle.getInt("Progress") != 100) {
                        downloadFragment.this.m73228X2(bundle);
                        return;
                    }
                    iMDLogger.m73550f("downloadFragment", "Update " + bundle.getString("id") + " progress is 100!!!!");
                    downloadFragment.this.m73237w3(bundle);
                }
            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.6
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Throwable th) throws Throwable {
                    try {
                        iMDLogger.m73550f("UpdateStatus", "Error occured in reloading tables : " + th.getMessage());
                        th.printStackTrace();
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        Log.e("FoundIT", "FoundIT . it was here in updatestatus");
                    }
                }
            });
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) {
        if (menuItem.getItemId() != C5562R.id.referesh_button) {
            return super.mo15329e1(menuItem);
        }
        if (m73198C3()) {
            new AlertDialog.Builder(m73240z3(), C5562R.style.alertDialogTheme).mo1102l("You are downloading some files, if you do this, your downloads would cancel ? are you sure ?").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.31
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                    downloadFragment.this.m73212N3();
                }
            }).mo1106p("Hell, No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.30
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                }
            }).m1090I();
            return true;
        }
        m73212N3();
        return true;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    /* renamed from: u3 */
    public void m73235u3(String str, String str2, String str3, String str4, String str5, String str6, String str7) {
        Bundle bundle = new Bundle();
        bundle.putString("Title", str);
        bundle.putString("URL", str2);
        bundle.putString("FileName", str5);
        bundle.putString("fileSize", str4);
        bundle.putString("Icon", "");
        bundle.putString("name", str5);
        bundle.putString("videoIdKey", str6);
        bundle.putString("id", str6);
        bundle.putString(f90438I5, IcyHeaders.f28171a3);
        bundle.putString("PartFileSize", str7);
        bundle.putString("savePathKey", str3);
        if (f90447U4 == null) {
            f90447U4 = new HashMap<>();
        }
        if (f90440N4 == null) {
            f90440N4 = new ArrayList<>();
        }
        if (f90446T4 == null) {
            f90446T4 = new ArrayList<>();
        }
        f90447U4.put(str6, bundle);
        f90440N4.add(bundle);
        f90446T4.add(bundle);
    }

    /* renamed from: v3 */
    public void m73236v3() {
        iMDLogger.m73554j("CheckDBsMD5", "Checking DBs MD5");
        this.f90487I4.m71812T0(this.f90487I4.m71790J() + "/dbs.md5", this.f90487I4.m71816U1() + "/DBs.md5").m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.15
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.16
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                try {
                    iMDLogger.m73550f("CheckDBSMD5", "Error in downloading dbs.md5 " + th.getMessage());
                    th.printStackTrace();
                } catch (Exception unused) {
                }
            }
        }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.17
            @Override // io.reactivex.rxjava3.functions.Action
            public void run() throws Throwable {
                if (new File(downloadFragment.this.f90487I4.m71816U1() + "/DBs.md5").exists()) {
                    try {
                        if (CompressHelper.m71750e2(new File(downloadFragment.this.f90487I4.m71816U1() + "/DBs.md5")).equalsIgnoreCase(downloadFragment.this.f90487I4.m71804P1(new File(downloadFragment.this.f90487I4.m71816U1() + "/DBs.db")).replace(StringUtils.f103471LF, ""))) {
                            iMDLogger.m73554j("CheckDBsMD5", "MD5s match , don't delete it");
                        } else {
                            iMDLogger.m73554j("CheckDBsMD5", "MD5s dont match ,  delete it");
                            if (!downloadFragment.this.m73198C3()) {
                                iMDLogger.m73554j("CheckDBsMD5", "Isn't downloading . ");
                                downloadFragment.this.m73212N3();
                            }
                        }
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        iMDLogger.m73550f("CheckDBsMD5", "Error in comparing md5 of dbs.db : " + e2.getMessage());
                        e2.printStackTrace();
                    }
                }
            }
        });
    }

    /* renamed from: w3 */
    public void m73237w3(final Bundle bundle) {
        ArrayList parcelableArrayList;
        if (bundle.containsKey("completed") || (parcelableArrayList = bundle.getParcelableArrayList("Parts")) == null) {
            return;
        }
        boolean z = true;
        for (int i2 = 0; i2 < parcelableArrayList.size(); i2++) {
            if (!((Bundle) parcelableArrayList.get(i2)).containsKey("completed")) {
                z = false;
            }
        }
        if (parcelableArrayList.size() >= 10 ? z : false) {
            iMDLogger.m73550f("Completed", bundle.getString("URL") + " Download completed ");
            bundle.putString("Rebuilding", "");
            bundle.putString("completed", "");
            m73221T2(bundle, "downloader");
            m73228X2(bundle);
            if (!bundle.getString("FileName").contains(".zip") || Long.valueOf(bundle.getString("fileSize")).longValue() <= 524288000) {
                this.f90487I4.m71787H0(m73240z3(), m73170F3(bundle)).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.41
                    @Override // io.reactivex.rxjava3.functions.Consumer
                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                    public void accept(String str) throws Throwable {
                    }
                }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.42
                    @Override // io.reactivex.rxjava3.functions.Consumer
                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                    public void accept(Throwable th) throws Throwable {
                        try {
                            File file = new File(downloadFragment.this.f90487I4.m71904y() + "/" + bundle.getString("FileName"));
                            if (file.exists()) {
                                downloadFragment.this.f90487I4.m71857j(file.getAbsolutePath());
                            }
                            downloadFragment.this.m73221T2(bundle, "Rebuilding");
                            downloadFragment.this.m73221T2(bundle, "completed");
                            downloadFragment.this.m73221T2(bundle, "downloader");
                            downloadFragment.this.m73221T2(bundle, "error");
                            bundle.putString("error", "Rebuild failed : " + th.getMessage());
                            downloadFragment.this.m73228X2(bundle);
                        } catch (Exception unused) {
                        }
                    }
                }, new Action() { // from class: net.imedicaldoctor.imd.Fragments.downloadFragment.43
                    @Override // io.reactivex.rxjava3.functions.Action
                    public void run() throws Throwable {
                        downloadFragment.this.m73221T2(bundle, "Rebuilding");
                        downloadFragment.this.m73221T2(bundle, "completed");
                        downloadFragment.this.m73228X2(bundle);
                        if (downloadFragment.this.f90493f4.containsKey(bundle.getString("id"))) {
                            downloadFragment.this.f90493f4.remove(bundle.getString("id"));
                        }
                        LocalBroadcastManager.m16410b(downloadFragment.this.m73240z3()).m16413d(new Intent("checkzip"));
                        if (bundle.containsKey("videoIdKey")) {
                            String string = bundle.getString("videoIdKey");
                            if (downloadFragment.f90443Q4.containsKey(string)) {
                                try {
                                    ((Runnable) downloadFragment.f90443Q4.get(string)).run();
                                } catch (Exception e2) {
                                    FirebaseCrashlytics.m48010d().m48016g(e2);
                                }
                            }
                        }
                    }
                });
                return;
            }
            m73221T2(bundle, "Rebuilding");
            if (this.f90493f4.containsKey(bundle.getString("id"))) {
                this.f90493f4.remove(bundle.getString("id"));
            }
            m73228X2(bundle);
            LocalBroadcastManager.m16410b(m73240z3()).m16413d(new Intent("checkzip"));
        }
    }

    /* renamed from: x3 */
    public Bundle m73238x3(String str) {
        HashMap<String, Bundle> map = f90447U4;
        if (map != null && map.containsKey(str)) {
            return f90447U4.get(str);
        }
        return null;
    }

    /* renamed from: y3 */
    public String m73239y3(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        String str2 = (String) arrayList.get(arrayList.size() - 1);
        arrayList.remove(arrayList.size() - 1);
        try {
            str = StringUtils.join(arrayList, "/") + "/" + UrlEscapers.m46127b().mo44830b(str2);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        return str.replace("http:/", "http://");
    }

    /* renamed from: z3 */
    public Activity m73240z3() {
        return this.f90482D4;
    }
}

package net.imedicaldoctor.imd.Fragments.UWorld;

import android.R;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.core.widget.TextViewCompat;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.Locale;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.Utils.iMDWebView;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class UWTestViewerActivityFragment extends ViewerHelperFragment {

    /* renamed from: X4 */
    public Bundle f89379X4;

    /* renamed from: Y4 */
    public Boolean f89380Y4;

    /* renamed from: Z4 */
    public String f89381Z4;

    /* renamed from: a5 */
    public ArrayList<String> f89382a5;

    /* renamed from: b5 */
    public ArrayList<Bundle> f89383b5;

    /* renamed from: c5 */
    public ArrayList<Bundle> f89384c5;

    /* renamed from: d5 */
    public ArrayList<Bundle> f89385d5;

    /* renamed from: e5 */
    public int f89386e5;

    /* renamed from: f5 */
    public Bundle f89387f5;

    /* renamed from: g5 */
    public Date f89388g5;

    /* renamed from: h5 */
    public String f89389h5;

    /* renamed from: i5 */
    private TextView f89390i5;

    /* renamed from: j5 */
    private Handler f89391j5;

    /* renamed from: l5 */
    private String f89393l5;

    /* renamed from: m5 */
    private Boolean f89394m5;

    /* renamed from: n5 */
    private Boolean f89395n5;

    /* renamed from: o5 */
    private boolean f89396o5;

    /* renamed from: p5 */
    private boolean f89397p5;

    /* renamed from: q5 */
    public int f89398q5;

    /* renamed from: r5 */
    public int f89399r5;

    /* renamed from: k5 */
    private long f89392k5 = 0;

    /* renamed from: s5 */
    private Runnable f89400s5 = new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestViewerActivityFragment.1
        @Override // java.lang.Runnable
        public void run() {
            if (UWTestViewerActivityFragment.this.f89392k5 == -1) {
                return;
            }
            int iUptimeMillis = (int) ((SystemClock.uptimeMillis() - UWTestViewerActivityFragment.this.f89392k5) / 1000);
            int i2 = iUptimeMillis / 60;
            int i3 = iUptimeMillis % 60;
            UWTestViewerActivityFragment.this.f89390i5.setText(String.format("%02d:%02d", Integer.valueOf(i2), Integer.valueOf(i3)));
            if (i3 % 5 == 0) {
                UWTestViewerActivityFragment.this.m72662o5();
            }
            UWTestViewerActivityFragment.this.f89391j5.postDelayed(this, 1000L);
        }
    };

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: M4 */
    public void m72647M4() {
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from images where questionId = " + this.f89385d5.get(this.f89386e5).getString("id"));
        if (arrayListM71817V == null) {
            arrayListM71817V = new ArrayList<>();
        }
        Iterator<Bundle> it2 = arrayListM71817V.iterator();
        while (it2.hasNext()) {
            String string = it2.next().getString("filename");
            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, string, "media-E");
            if (new File(strM71754h1).exists()) {
                try {
                    byte[] bArrM71899w = this.f89579Q4.m71899w(CompressHelper.m71748d2(new File(strM71754h1)), string, "127");
                    String strM71754h12 = CompressHelper.m71754h1(this.f89566D4, string, "base");
                    if (new File(strM71754h12).exists()) {
                        new File(strM71754h12).delete();
                    }
                    CompressHelper.m71728D2(new File(strM71754h12), bArrM71899w);
                    new File(strM71754h12).deleteOnExit();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                }
            }
        }
        this.f89383b5 = arrayListM71817V;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: N4 */
    public void m72648N4(ArrayList<String> arrayList) {
        ArrayList<String> arrayList2 = new ArrayList<>();
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            String strReplace = next.replace(".JPG", ".jpg").replace(".PNG", ".png");
            Log.e("UW", "Media2 : " + strReplace);
            String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, strReplace, "media-E");
            if (new File(strM71754h1).exists()) {
                arrayList2.add(next);
                File file = new File(strM71754h1);
                try {
                    String strReplace2 = CompressHelper.m71754h1(this.f89566D4, next, "base").replace(".mov", ".mp4");
                    if (!new File(strReplace2).exists()) {
                        byte[] bArrM71748d2 = CompressHelper.m71748d2(file);
                        Log.e("UW", "GET DATA " + strReplace);
                        byte[] bArrM71899w = this.f89579Q4.m71899w(bArrM71748d2, strReplace, "127");
                        Log.e("UW", "New Image Path : " + strReplace2);
                        CompressHelper.m71728D2(new File(strReplace2), bArrM71899w);
                    }
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    e2.printStackTrace();
                }
            }
        }
        this.f89382a5 = arrayList2;
    }

    /* renamed from: e5 */
    private String m72660e5(String str, String str2, String str3) {
        return "<a name=\"f" + str3 + "\"><div id=\"h" + str3 + "\" class=\"headerExpanded\"  DIR=\"LTR\" onclick=\"collapse(f" + str3 + ");toggleHeaderExpanded(h" + str3 + ");\"><span class=\"fieldname\">" + str + "</span></div></a><div class=\"content\" DIR=\"LTR\" id=\"f" + str3 + "\">" + str2 + "</div>";
    }

    /* renamed from: m5 */
    private void m72661m5() throws NumberFormatException {
        Bundle bundle = this.f89387f5;
        if (bundle != null) {
            String[] strArrSplit = bundle.getString("createdDate").split("\\|");
            if (strArrSplit.length <= 1) {
                this.f89392k5 = SystemClock.uptimeMillis();
                return;
            }
            int i2 = Integer.parseInt(strArrSplit[1].split(":")[0]);
            this.f89392k5 = SystemClock.uptimeMillis() - (((i2 * 60) + Integer.parseInt(r0[1])) * 1000);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: o5 */
    public void m72662o5() {
        if (this.f89387f5 != null) {
            int iUptimeMillis = (int) ((SystemClock.uptimeMillis() - this.f89392k5) / 1000);
            String str = String.format("%02d:%02d", Integer.valueOf(iUptimeMillis / 60), Integer.valueOf(iUptimeMillis % 60));
            this.f89579Q4.m71866m(this.f89566D4, "UPDATE tests SET createdDate = '" + (this.f89387f5.getString("createdDate").split("\\|")[0] + "|" + str) + "' WHERE id = " + this.f89387f5.getString("id"));
        }
    }

    /* renamed from: p5 */
    private void m72663p5() {
        Toolbar toolbar = (Toolbar) this.f89565C4.findViewById(C5562R.id.toolbar);
        AppCompatTextView appCompatTextView = new AppCompatTextView(m15246B());
        this.f89390i5 = appCompatTextView;
        appCompatTextView.setTextSize(16.0f);
        this.f89390i5.setTextColor(m15320b0().getColor(R.color.white));
        this.f89390i5.setGravity(GravityCompat.f13298c);
        TextViewCompat.m10463r(this.f89390i5, 1);
        toolbar.addView(this.f89390i5);
        if (this.f89387f5.getString("done").equals("0")) {
            m72665s5();
            return;
        }
        String[] strArrSplit = this.f89387f5.getString("createdDate").split("\\|");
        if (strArrSplit.length > 1) {
            this.f89390i5.setText(strArrSplit[1]);
        }
    }

    /* renamed from: q5 */
    private void m72664q5(String str) {
        ArrayList<String> arrayList = this.f89382a5;
        if ((arrayList == null && this.f89383b5 == null) || arrayList.size() + this.f89383b5.size() == 0) {
            CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
            return;
        }
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = this.f89382a5.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", CompressHelper.m71754h1(this.f89566D4, next, "base"));
            bundle.putString("Description", "");
            bundle.putString("id", next);
            if (next.endsWith(".mov") || next.endsWith(".mp4") || next.endsWith(".mp3")) {
                bundle.putString("isVideo", IcyHeaders.f28171a3);
            }
            arrayList2.add(bundle);
        }
        Iterator<Bundle> it3 = this.f89383b5.iterator();
        while (it3.hasNext()) {
            Bundle next2 = it3.next();
            Bundle bundle2 = new Bundle();
            Iterator<Bundle> it4 = it3;
            bundle2.putString("ImagePath", CompressHelper.m71754h1(this.f89566D4, next2.getString("filename"), "base"));
            bundle2.putString("Description", next2.getString("title"));
            bundle2.putString("id", next2.getString("mediaId"));
            if (next2.getString("filename").endsWith(".mov")) {
                bundle2.putString("isVideo", IcyHeaders.f28171a3);
            }
            if (next2.getString("filename").endsWith(".mp4")) {
                bundle2.putString("isVideo", IcyHeaders.f28171a3);
            }
            if (next2.getString("filename").endsWith(".mp3")) {
                bundle2.putString("isVideo", IcyHeaders.f28171a3);
            }
            arrayList2.add(bundle2);
            it3 = it4;
        }
        int i2 = 0;
        for (int i3 = 0; i3 < arrayList2.size(); i3++) {
            if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                i2 = i3;
            }
        }
        Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
        intent.putExtra("Images", arrayList2);
        intent.putExtra("Start", i2);
        mo15256D2(intent);
    }

    /* renamed from: s5 */
    private void m72665s5() {
        this.f89391j5 = new Handler();
        this.f89392k5 = SystemClock.uptimeMillis();
        this.f89391j5.postDelayed(this.f89400s5, 0L);
    }

    /* renamed from: I4 */
    public void m72666I4() {
        this.f89598s4.findItem(C5562R.id.action_previous).setEnabled(false);
        this.f89598s4.findItem(C5562R.id.action_previous).setIcon(C5562R.drawable.ic_action_previous_item_disabled);
    }

    /* renamed from: J4 */
    public void m72667J4() {
        this.f89598s4.findItem(C5562R.id.action_next).setEnabled(false);
        this.f89598s4.findItem(C5562R.id.action_next).setIcon(C5562R.drawable.ic_action_next_item_disabled);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: K3 */
    public Boolean mo72668K3(String str) {
        CompressHelper compressHelper = this.f89579Q4;
        String strM71823X0 = compressHelper.m71823X0();
        StringBuilder sb = new StringBuilder();
        sb.append("select * from favorites where dbName='");
        sb.append(this.f89579Q4.m71833a1(this.f89566D4.getString("Name")));
        sb.append("' AND (dbAddress='");
        sb.append(this.f89579Q4.m71833a1(str));
        sb.append("' OR dbAddress='question-");
        sb.append(this.f89579Q4.m71833a1(str));
        sb.append("' OR dbAddress='answer-");
        sb.append(this.f89579Q4.m71833a1(str));
        sb.append("')");
        return Boolean.valueOf(compressHelper.m71890s1(compressHelper.m71825Y(strM71823X0, sb.toString())) != null);
    }

    /* renamed from: K4 */
    public void m72669K4() {
        this.f89598s4.findItem(C5562R.id.action_previous).setEnabled(true);
        this.f89598s4.findItem(C5562R.id.action_previous).setIcon(C5562R.drawable.ic_action_previous_item);
    }

    /* renamed from: L4 */
    public void m72670L4() {
        this.f89598s4.findItem(C5562R.id.action_next).setEnabled(true);
        this.f89598s4.findItem(C5562R.id.action_next).setIcon(C5562R.drawable.ic_action_next_item);
    }

    /* renamed from: O4 */
    public void m72671O4() {
        if (this.f89391j5 != null) {
            try {
                if (this.f89392k5 != -1) {
                    m72662o5();
                }
                this.f89392k5 = -1L;
                this.f89391j5.removeCallbacks(this.f89400s5);
            } catch (Exception unused) {
            }
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: R2 */
    public String mo71955R2() {
        return null;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException, IOException, NumberFormatException {
        View view = this.f89565C4;
        if (view != null) {
            return view;
        }
        View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
        this.f89565C4 = viewInflate;
        m72835r4(viewInflate, bundle);
        if (m15387y() == null) {
            return this.f89565C4;
        }
        this.f89394m5 = Boolean.valueOf(!this.f89579Q4.m71817V(this.f89566D4, "select count(distinct  corrTaken) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3));
        this.f89395n5 = Boolean.valueOf(!this.f89579Q4.m71817V(this.f89566D4, "select count(distinct  lastUpdated) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3));
        this.f89396o5 = !this.f89579Q4.m71817V(this.f89566D4, "select count(distinct  subId) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3);
        this.f89397p5 = !this.f89579Q4.m71817V(this.f89566D4, "select count(distinct  sysId) as c  from questions").get(0).getString("c").equals(IcyHeaders.f28171a3);
        try {
            String str = this.f89563A4;
            if (str == null || str.length() == 0) {
                m72831p4();
                m15358o2(false);
                if (this.f89566D4.getString("Name").toLowerCase(Locale.ROOT).contains("uworld")) {
                    mo72642f3(C5562R.menu.uworld_test_uworld);
                } else {
                    mo72642f3(C5562R.menu.uworld_test);
                }
                m72786G3();
                iMDLogger.m73550f("Loading Document", this.f89567E4);
                this.f89393l5 = "";
                if (this.f89567E4.contains("-")) {
                    String[] strArrSplit = this.f89567E4.split("-");
                    if (strArrSplit[0].equals("test")) {
                        CompressHelper compressHelper = this.f89579Q4;
                        Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71817V(this.f89566D4, "Select * from tests where id =" + strArrSplit[1]));
                        this.f89387f5 = bundleM71890s1;
                        m72682j5(bundleM71890s1.getString("qIds"));
                        ArrayList<Bundle> arrayList = this.f89385d5;
                        if (arrayList != null && arrayList.size() != 0) {
                            this.f89386e5 = Integer.valueOf(this.f89387f5.getString("qIndex")).intValue();
                            if (m15387y().containsKey("gotoQIndex")) {
                                this.f89386e5 = m15387y().getInt("gotoQIndex");
                            }
                        }
                        CompressHelper.m71767x2(m15366r(), "No Questions Found", 1);
                        this.f89579Q4.m71821W1(false);
                        return this.f89565C4;
                    }
                    if (strArrSplit[0].equals("question")) {
                        this.f89385d5 = this.f89579Q4.m71817V(this.f89566D4, "Select * from Questions where id=" + strArrSplit[1]);
                        this.f89386e5 = 0;
                    } else if (strArrSplit[0].equals("answer")) {
                        this.f89385d5 = this.f89579Q4.m71817V(this.f89566D4, "Select * from Questions where id=" + strArrSplit[1]);
                        this.f89386e5 = 0;
                        m72679g5("", false);
                    }
                } else {
                    this.f89385d5 = this.f89579Q4.m71817V(this.f89566D4, m15387y().getString("Query"));
                    this.f89386e5 = m15387y().getInt("QuestionIndex");
                }
                m72680h5();
            }
            m72826m3();
            m72836s4();
            Bundle bundle2 = this.f89387f5;
            if (bundle2 != null && bundle2.getString("mode").equals("Testing (Timed)")) {
                m72663p5();
                m72661m5();
            }
            return this.f89565C4;
        } catch (Exception e2) {
            e2.printStackTrace();
            m72779B4(e2);
            return this.f89565C4;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: W3 */
    public boolean mo71969W3(ConsoleMessage consoleMessage) {
        String strSubstring;
        String[] strArrSplit = consoleMessage.message().split(",,,,,");
        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "base");
        if (strArrSplit[0].equals("images")) {
            if (strArrSplit.length < 2) {
                return true;
            }
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
            ArrayList<String> arrayList = new ArrayList<>();
            for (String str : strArrSplitByWholeSeparator) {
                if (str.contains("/")) {
                    String strReplace = strM71753g1.replace("file://", "");
                    strSubstring = strReplace.substring(0, strReplace.length() - 1);
                    for (String str2 : StringUtils.splitByWholeSeparator(str, "/")) {
                        strSubstring = str2.equals("..") ? m72684l5(strSubstring) : strSubstring + "/" + str2;
                    }
                } else {
                    strSubstring = strM71753g1 + "/" + str;
                }
                if (new File(strSubstring).length() > ExoPlayer.f21773a1) {
                    arrayList.add(strSubstring);
                }
                iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strSubstring);
            }
            this.f89382a5 = arrayList;
            mo71972o4();
        } else if (strArrSplit[0].equals("answer")) {
            String str3 = strArrSplit[1];
            m72683k5(str3);
            Bundle bundle = this.f89387f5;
            if (bundle == null || !bundle.getString("mode").equals("Testing (Timed)")) {
                m72679g5(str3, true);
            }
        }
        return super.mo71969W3(consoleMessage);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: X0 */
    public void mo15214X0() {
        super.mo15214X0();
        m72671O4();
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: Z3 */
    public void mo71956Z3(WebView webView, String str) {
        int i2 = this.f89398q5;
        if (i2 > 0 || this.f89399r5 > 0) {
            this.f89569G4.scrollTo(this.f89399r5, i2);
            this.f89398q5 = 0;
            this.f89399r5 = 0;
        }
        this.f89569G4.m73433g("ConvertAllImages();");
        this.f89569G4.m73433g("onBodyLoad();");
        String str2 = this.f89389h5;
        if (str2 != null) {
            mo71967C3(str2);
            this.f89389h5 = null;
        }
        super.mo71956Z3(webView, str);
        this.f89569G4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestViewerActivityFragment.4
            @Override // java.lang.Runnable
            public void run() {
                UWTestViewerActivityFragment uWTestViewerActivityFragment = UWTestViewerActivityFragment.this;
                int i3 = uWTestViewerActivityFragment.f89398q5;
                if (i3 > 0 || uWTestViewerActivityFragment.f89399r5 > 0) {
                    uWTestViewerActivityFragment.f89569G4.scrollTo(uWTestViewerActivityFragment.f89399r5, i3);
                    UWTestViewerActivityFragment uWTestViewerActivityFragment2 = UWTestViewerActivityFragment.this;
                    uWTestViewerActivityFragment2.f89398q5 = 0;
                    uWTestViewerActivityFragment2.f89399r5 = 0;
                }
            }
        }, 200L);
    }

    /* renamed from: a5 */
    public String m72672a5(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ZZZ").format(date);
    }

    /* renamed from: b5 */
    public String m72673b5() {
        return "Question " + this.f89385d5.get(this.f89386e5).getString("id") + " (" + this.f89385d5.get(this.f89386e5).getString("title") + ")";
    }

    /* renamed from: c5 */
    public String m72674c5(String str) throws NumberFormatException {
        try {
            int i2 = Integer.parseInt(str);
            if (i2 > 0) {
                return String.valueOf((char) (i2 + 64));
            }
        } catch (NumberFormatException e2) {
            e2.printStackTrace();
        }
        return str;
    }

    /* renamed from: d5 */
    public String m72675d5(ArrayList<String> arrayList) {
        ArrayList arrayList2 = new ArrayList();
        Iterator<String> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            arrayList2.add(m72674c5(it2.next()));
        }
        return StringUtils.join(arrayList2, ",");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: e1 */
    public boolean mo15329e1(MenuItem menuItem) throws IOException, NumberFormatException {
        int i2;
        int itemId = menuItem.getItemId();
        if (itemId == C5562R.id.action_favorites) {
            new Bundle();
            String strM72673b5 = m72673b5();
            if (menuItem.getTitle().equals("Add Favorite")) {
                m72791L2(strM72673b5, mo72688s3());
                menuItem.setTitle("Remove Favorite");
                i2 = C5562R.drawable.ic_action_favorite_yellow;
            } else {
                mo72686p3(mo72688s3());
                menuItem.setTitle("Add Favorite");
                i2 = C5562R.drawable.ic_action_favorite;
            }
            menuItem.setIcon(i2);
            return true;
        }
        if (itemId == C5562R.id.lab_values) {
            m72687r5();
            return true;
        }
        if (itemId == C5562R.id.action_gallery) {
            m72664q5("asdfafdsaf");
            return true;
        }
        if (itemId == C5562R.id.action_previous) {
            this.f89386e5--;
            m72680h5();
            m72689t5();
            if (this.f89387f5 != null) {
                this.f89579Q4.m71866m(this.f89566D4, "Update tests set qIndex=" + this.f89386e5 + " where id=" + this.f89387f5.getString("id"));
            }
        }
        if (itemId == C5562R.id.action_next) {
            this.f89386e5++;
            m72680h5();
            m72689t5();
            if (this.f89387f5 != null) {
                this.f89579Q4.m71866m(this.f89566D4, "Update tests set qIndex=" + this.f89386e5 + " where id=" + this.f89387f5.getString("id"));
            }
        }
        if (itemId == C5562R.id.action_stop) {
            new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("Do you want to END this test ?").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestViewerActivityFragment.6
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                    UWTestViewerActivityFragment uWTestViewerActivityFragment = UWTestViewerActivityFragment.this;
                    if (uWTestViewerActivityFragment.f89387f5 == null) {
                        return;
                    }
                    uWTestViewerActivityFragment.m72671O4();
                    UWTestViewerActivityFragment uWTestViewerActivityFragment2 = UWTestViewerActivityFragment.this;
                    Iterator<Bundle> it2 = uWTestViewerActivityFragment2.f89579Q4.m71817V(uWTestViewerActivityFragment2.f89566D4, "Select questions.id,pplTaken,corrTaken,title,selectedAnswer,corrAnswer,time  from Questions left outer join (select * from logs where testId=" + UWTestViewerActivityFragment.this.f89387f5.getString("id") + ") as logs2 on questions.id=logs2.qid where questions.id in (" + UWTestViewerActivityFragment.this.f89387f5.getString("qIds") + ")").iterator();
                    float f2 = 0.0f;
                    while (it2.hasNext()) {
                        Bundle next = it2.next();
                        if (next.getString("selectedAnswer").length() != 0 && next.getString("selectedAnswer").equals(next.getString("corrAnswer"))) {
                            f2 += 1.0f;
                        }
                    }
                    int size = (int) ((f2 / r6.size()) * 100.0f);
                    UWTestViewerActivityFragment uWTestViewerActivityFragment3 = UWTestViewerActivityFragment.this;
                    uWTestViewerActivityFragment3.f89579Q4.m71866m(uWTestViewerActivityFragment3.f89566D4, "Update tests set score='" + size + "', done=1 where id=" + UWTestViewerActivityFragment.this.f89387f5.getString("id"));
                    UWTestViewerActivityFragment uWTestViewerActivityFragment4 = UWTestViewerActivityFragment.this;
                    uWTestViewerActivityFragment4.f89579Q4.m71772A1(uWTestViewerActivityFragment4.f89566D4, "testresult-" + UWTestViewerActivityFragment.this.f89387f5.getString("id"), null, null);
                }
            }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestViewerActivityFragment.5
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i3) {
                }
            }).m1090I();
        }
        return super.mo15329e1(menuItem);
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e3 */
    public void mo71957e3(Menu menu) {
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: e4 */
    public String mo72676e4() {
        if (this.f89387f5 == null) {
            return this.f89567E4;
        }
        return "test-" + this.f89387f5.getString("id");
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: f4 */
    public String mo72677f4() {
        if (this.f89387f5 == null) {
            return m72673b5();
        }
        return "Test #" + this.f89387f5.getString("id");
    }

    /* renamed from: f5 */
    public void m72678f5(String str) throws NumberFormatException {
        this.f89569G4.evaluateJavascript("highlighter.removeAllHighlights();", null);
        iMDWebView imdwebview = this.f89569G4;
        imdwebview.evaluateJavascript("highlighter.deserialize('" + ("type:textContent|" + str) + "');", null);
        this.f89569G4.evaluateJavascript("gotoHighlight('" + str + "');", null);
        this.f89569G4.evaluateJavascript("highlighter.removeAllHighlights();", null);
        this.f89569G4.evaluateJavascript("var element = document.getElementById('orientation'); if (element) { element.parentNode.removeChild(element); }", null);
        m72802S3();
    }

    /* renamed from: g5 */
    public void m72679g5(final String str, final boolean z) {
        ArrayList arrayList;
        try {
            arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, ",")));
        } catch (Exception unused) {
            arrayList = null;
        }
        if (arrayList == null) {
            arrayList = new ArrayList();
        }
        if (arrayList.size() > 0) {
            m72685n5();
        }
        m72832q3(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestViewerActivityFragment.2
            /* JADX WARN: Can't wrap try/catch for region: R(40:0|2|93|3|(1:7)|8|(1:10)|11|(3:13|(1:15)|99)|16|(1:18)(1:19)|20|(1:22)|23|(1:25)|26|(2:27|(1:29)(1:100))|30|(14:33|(1:35)|36|(1:38)(1:39)|40|(1:42)|43|(1:45)(1:47)|46|48|(2:52|55)(1:53)|54|56|31)|101|57|(1:59)(2:62|(17:64|61|(1:67)(1:68)|69|(3:71|(1:73)(1:75)|74)|76|(1:78)|79|(1:81)|95|82|97|83|88|(1:90)|91|92)(1:65))|60|61|(0)(0)|69|(0)|76|(0)|79|(0)|95|82|97|83|88|(0)|91|92|(1:(0))) */
            /* JADX WARN: Code restructure failed: missing block: B:86:0x04f7, code lost:
            
                r2 = "";
             */
            /* JADX WARN: Code restructure failed: missing block: B:87:0x04f9, code lost:
            
                r4 = "";
             */
            /* JADX WARN: Removed duplicated region for block: B:67:0x03d4  */
            /* JADX WARN: Removed duplicated region for block: B:68:0x03d7  */
            /* JADX WARN: Removed duplicated region for block: B:71:0x0420  */
            /* JADX WARN: Removed duplicated region for block: B:78:0x0484  */
            /* JADX WARN: Removed duplicated region for block: B:81:0x0492  */
            /* JADX WARN: Removed duplicated region for block: B:90:0x0512  */
            @Override // java.lang.Runnable
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public void run() throws java.io.IOException, java.lang.NumberFormatException {
                /*
                    Method dump skipped, instructions count: 1329
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.UWorld.UWTestViewerActivityFragment.RunnableC51332.run():void");
            }
        }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.UWorld.UWTestViewerActivityFragment.3
            @Override // java.lang.Runnable
            public void run() {
                UWTestViewerActivityFragment.this.m72681i5();
            }
        });
    }

    /* renamed from: h5 */
    public void m72680h5() throws IOException, NumberFormatException {
        this.f89388g5 = new Date();
        this.f89568F4 = "Question " + (this.f89386e5 + 1) + " Of " + this.f89385d5.size();
        StringBuilder sb = new StringBuilder();
        sb.append("question-");
        sb.append(this.f89385d5.get(this.f89386e5).getString("id"));
        this.f89567E4 = sb.toString();
        Bundle bundle = this.f89385d5.get(this.f89386e5);
        String string = bundle.getString("id");
        boolean zContains = bundle.getString("corrAns").contains(",");
        Bundle bundle2 = this.f89387f5;
        String string2 = "100";
        if (bundle2 != null) {
            ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from logs where testId=" + bundle2.getString("id") + " AND qid = " + string);
            if (arrayListM71817V == null || arrayListM71817V.size() <= 0) {
                if (this.f89387f5.getString("done").equals(IcyHeaders.f28171a3)) {
                    m72679g5("100", false);
                    return;
                }
            } else if (!this.f89387f5.getString("mode").equals("Testing (Timed)")) {
                m72679g5(arrayListM71817V.get(0).getString("selectedAnswer"), false);
                return;
            } else {
                if (!this.f89387f5.getString("done").equals("0")) {
                    m72679g5(arrayListM71817V.get(0).getString("selectedAnswer"), false);
                    return;
                }
                string2 = arrayListM71817V.get(0).getString("selectedAnswer");
            }
        }
        String strM71773B = this.f89579Q4.m71773B(bundle.getString("question"), string, "127");
        this.f89579Q4.m71773B(bundle.getString("explanation"), string, "127");
        String strM72776j4 = ViewerHelperFragment.m72776j4(m72817d4(m15366r(), "UWHeader.css") + m72817d4(m15366r(), "UWQuestion.css"), "[size]", "200");
        ArrayList<String> arrayList = new ArrayList<>();
        String string3 = bundle.getString("mediaName");
        String string4 = bundle.getString("otherMedias");
        if (string3.length() > 0) {
            Collections.addAll(arrayList, StringUtils.splitByWholeSeparator(string3, ";"));
        }
        if (string4.length() > 0) {
            for (String str : StringUtils.splitByWholeSeparator(string4, ",")) {
                arrayList.add(str.replace("/", "_"));
            }
        }
        m72648N4(arrayList);
        m72647M4();
        ArrayList<Bundle> arrayListM71817V2 = this.f89579Q4.m71817V(this.f89566D4, "select * from answers where qId = " + string);
        if (arrayListM71817V2 == null) {
            arrayListM71817V2 = new ArrayList<>();
        }
        if (arrayListM71817V2.size() == 0) {
            arrayListM71817V2 = this.f89579Q4.m71817V(this.f89566D4, "select '1' as id, '" + string + "' as qId,'" + bundle.getString("corrAns") + "' as answerId, 'I Know the Answer' as answerText, '0' as correctPercentage UNION ALL select '2' as id, '" + string + "' as qId,'" + (Integer.valueOf(bundle.getString("corrAns")).intValue() + 1) + "' as answerId, 'I Don''t Know the Answer' as answerText, '0' as correctPercentage");
        }
        String strM72776j42 = ViewerHelperFragment.m72776j4(ViewerHelperFragment.m72776j4(strM72776j4, "[correctID]", bundle.getString("corrAns")), "[Question]", strM71773B);
        StringBuilder sb2 = new StringBuilder();
        Iterator<Bundle> it2 = arrayListM71817V2.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String string5 = next.getString("answerText");
            if (!string5.contains("the Answer")) {
                string5 = this.f89579Q4.m71773B(string5, string, "127");
            }
            String string6 = next.getString("answerId");
            sb2.append("<tr><td width=\"16\" id=\"Qbank-Answer-Row-Image-" + string6 + "\"></td><td><input type=\"" + (zContains ? "checkbox" : "radio") + "\" name=\"Qbank-Answer-Button-Group\" value=\"" + string6 + "\" onclick=\"answerChanged()\" " + (string6.equals(string2) ? "checked=\"checked\"" : "") + "></td><td class=\"answerOptionNumber\"><span>" + m72674c5(string6) + ". </span></td><td><span id=\"AnswerText" + string6 + "\" onclick=\"answerClickedForStrikeout(" + string6 + ");\">" + string5 + "</span></td></tr>");
        }
        this.f89563A4 = ViewerHelperFragment.m72776j4(ViewerHelperFragment.m72776j4(ViewerHelperFragment.m72776j4(strM72776j42, "[Answers]", sb2.toString()), "highresdefault/", "highresdefault_"), "[isMultipleAnswers]", String.valueOf(zContains));
        m72681i5();
    }

    /* renamed from: i5 */
    public void m72681i5() {
        m72795O3(this.f89563A4, CompressHelper.m71753g1(this.f89566D4, "base"));
        String str = this.f89568F4;
        if (str != null) {
            this.f89574L4.setTitle(str);
            m72829n4(this.f89568F4);
            m72792M2();
        }
        mo71972o4();
        m72833q4(this.f89574L4.getMenu());
        m72689t5();
    }

    /* renamed from: j5 */
    public void m72682j5(String str) {
        String[] strArrSplit = str.split(",");
        StringBuilder sb = new StringBuilder("CASE id ");
        for (int i2 = 0; i2 < strArrSplit.length; i2++) {
            sb.append("WHEN ");
            sb.append(strArrSplit[i2]);
            sb.append(" THEN ");
            sb.append(i2);
            sb.append(StringUtils.SPACE);
        }
        sb.append("END");
        this.f89385d5 = this.f89579Q4.m71817V(this.f89566D4, "SELECT * FROM Questions WHERE id IN (" + str + ") ORDER BY " + sb.toString());
    }

    /* renamed from: k5 */
    public void m72683k5(String str) {
        String str2;
        String string = this.f89385d5.get(this.f89386e5).getString("id");
        String string2 = this.f89385d5.get(this.f89386e5).getString("corrAns");
        Date date = new Date();
        String strM72672a5 = m72672a5(date);
        long time = (date.getTime() - this.f89388g5.getTime()) / 1000;
        Bundle bundle = this.f89387f5;
        String string3 = bundle != null ? bundle.getString("id") : "null";
        ArrayList<Bundle> arrayListM71817V = this.f89579Q4.m71817V(this.f89566D4, "Select * from logs where testId=" + string3 + " AND qid = " + string);
        StringBuilder sb = new StringBuilder();
        sb.append(time);
        sb.append(" Seconds");
        this.f89393l5 = sb.toString();
        if (arrayListM71817V == null || arrayListM71817V.size() <= 0) {
            str2 = "Insert into logs (id, qid, selectedAnswer, corrAnswer, answerDate, time, testId) values (null, " + string + ", '" + str + "', '" + string2 + "', '" + strM72672a5 + "', " + time + ", " + string3 + ")";
        } else {
            str2 = "Update logs set selectedAnswer = '" + str + "', answerDate='" + strM72672a5 + "', time=" + time + " where id=" + arrayListM71817V.get(0).getString("id");
        }
        this.f89579Q4.m71866m(this.f89566D4, str2);
    }

    /* renamed from: l5 */
    public String m72684l5(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    /* renamed from: n5 */
    public void m72685n5() {
        this.f89398q5 = this.f89569G4.getScrollY();
        this.f89399r5 = this.f89569G4.getScrollX();
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: o4 */
    public void mo71972o4() {
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: p3 */
    public void mo72686p3(String str) {
        CompressHelper compressHelper = this.f89579Q4;
        compressHelper.m71881q(compressHelper.m71823X0(), "delete from favorites where dbName='" + this.f89579Q4.m71833a1(this.f89566D4.getString("Name")) + "' AND (dbAddress='" + this.f89579Q4.m71833a1(str) + "' OR dbAddress='question-" + this.f89579Q4.m71833a1(str) + "' OR dbAddress='answer-" + this.f89579Q4.m71833a1(str) + "')");
        m72825l4();
    }

    /* renamed from: r5 */
    public void m72687r5() {
        try {
            String strM72817d4 = m72817d4(m15366r(), "LXHeader.css");
            String strM72817d42 = m72817d4(m15366r(), "LXFooter.css");
            String strReplace = strM72817d4.replace("[size]", "200").replace("[title]", this.f89568F4).replace("[include]", "");
            Iterator it2 = this.f89579Q4.m71784G(new JSONObject(m72817d4(m15366r(), "l.json"))).getParcelableArrayList("USMLE").iterator();
            int i2 = 0;
            String str = "";
            while (it2.hasNext()) {
                Bundle bundle = (Bundle) it2.next();
                i2++;
                str = str + m72660e5(bundle.getString("name"), bundle.getString(Annotation.f68283i3).replace("display:none", ""), i2 + "");
            }
            m72783E4(strReplace + str + strM72817d42, "Lab Values");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("LXViewer", "Error in reading LXHeader and LXFooter : " + e2.getLocalizedMessage());
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: s3 */
    public String mo72688s3() {
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(this.f89567E4, "-");
        return strArrSplitByWholeSeparator.length > 1 ? strArrSplitByWholeSeparator[1] : this.f89567E4;
    }

    /* renamed from: t5 */
    public void m72689t5() {
        this.f89598s4.findItem(C5562R.id.action_stop).setVisible(false);
        if (this.f89387f5 != null) {
            this.f89598s4.findItem(C5562R.id.action_stop).setVisible(true);
        }
        m72669K4();
        m72670L4();
        if (this.f89386e5 <= 0) {
            m72666I4();
        }
        if (this.f89386e5 >= this.f89385d5.size() - 1) {
            m72667J4();
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: v4 */
    public boolean mo71959v4() {
        return false;
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
    /* renamed from: y4 */
    public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
        String strM71750e2;
        if (str2.equals("image")) {
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str3, "/");
            m72664q5(strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1]);
            return true;
        }
        iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
        if (str3.contains("USMLEWorld-Question-Answer-Changed")) {
            this.f89569G4.m73433g("console.log(\"answer,,,,,\" + prevAnswerID);");
            return true;
        }
        if (str3.contains("/2323")) {
            m72664q5("soheilvb");
            return true;
        }
        if (str2.equals(Annotation.f68285k3) || (str2.equals("http") & str3.contains("localhost:"))) {
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str3, "/");
            String str4 = strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1];
            if (str4.endsWith(".html")) {
                try {
                    strM71750e2 = CompressHelper.m71750e2(new File(CompressHelper.m71754h1(this.f89566D4, str4, "base")));
                } catch (Exception unused) {
                    strM71750e2 = "";
                }
                m72783E4(strM71750e2, "UWORLD");
                return true;
            }
            m72664q5(str4);
        }
        return true;
    }
}

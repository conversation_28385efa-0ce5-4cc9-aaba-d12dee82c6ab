package net.imedicaldoctor.imd.Fragments.Elsevier;

import android.app.Dialog;
import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.fragment.app.DialogFragment;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Elsevier.ELSViewerActivity;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class ELSSectionsViewer extends DialogFragment {

    /* renamed from: F4 */
    private Bundle f88002F4;

    /* renamed from: G4 */
    private String f88003G4;

    /* renamed from: H4 */
    private String f88004H4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_general_section_viewer, (ViewGroup) null);
        ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f88002F4 = m15387y().getBundle("db");
        this.f88003G4 = m15387y().getString("docId");
        this.f88004H4 = m15387y().getString("parentId");
        CompressHelper compressHelper = new CompressHelper(m15366r());
        listView.setAdapter((ListAdapter) new CursorAdapter(m15366r(), compressHelper.m71850h(compressHelper.m71817V(this.f88002F4, "Select rowid as _id, * from sections where docId = '" + this.f88003G4 + "' And parent = " + this.f88004H4)), 0) { // from class: net.imedicaldoctor.imd.Fragments.Elsevier.ELSSectionsViewer.1
            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: e */
            public void mo2556e(View view, Context context, Cursor cursor) {
                ((TextView) view.getTag()).setText(cursor.getString(cursor.getColumnIndex("sectionName")));
                if (cursor.getString(cursor.getColumnIndex("leaf")).equals("0")) {
                    ImageView imageView = (ImageView) view.findViewById(C5562R.id.next_icon);
                    final String string = cursor.getString(cursor.getColumnIndex("sectionId"));
                    imageView.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Elsevier.ELSSectionsViewer.1.1
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view2) {
                            ELSSectionsViewer eLSSectionsViewer = new ELSSectionsViewer();
                            Bundle bundle2 = new Bundle();
                            bundle2.putBundle("db", ELSSectionsViewer.this.f88002F4);
                            bundle2.putString("docId", ELSSectionsViewer.this.f88003G4);
                            bundle2.putString("parentId", string);
                            eLSSectionsViewer.m15342i2(bundle2);
                            eLSSectionsViewer.mo15218Z2(true);
                            eLSSectionsViewer.m15245A2(ELSSectionsViewer.this.m15351l0(), 0);
                            eLSSectionsViewer.mo15222e3(ELSSectionsViewer.this.m15305V(), "ELSSectionsViewer");
                            ELSSectionsViewer.this.mo15203M2();
                        }
                    });
                }
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getItemViewType(int i2) {
                Cursor cursor = (Cursor) getItem(i2);
                return cursor.getString(cursor.getColumnIndex("leaf")).equals(IcyHeaders.f28171a3) ? 0 : 1;
            }

            @Override // android.widget.BaseAdapter, android.widget.Adapter
            public int getViewTypeCount() {
                return 2;
            }

            @Override // androidx.cursoradapter.widget.CursorAdapter
            /* renamed from: j */
            public View mo2557j(Context context, Cursor cursor, ViewGroup viewGroup) {
                View viewInflate2 = LayoutInflater.from(ELSSectionsViewer.this.m15366r()).inflate(cursor.getString(cursor.getColumnIndex("leaf")).equals(IcyHeaders.f28171a3) ? C5562R.layout.list_view_item_simple_text : C5562R.layout.list_view_item_simple_text_arrow, viewGroup, false);
                viewInflate2.setTag(viewInflate2.findViewById(C5562R.id.text));
                return viewInflate2;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Elsevier.ELSSectionsViewer.2
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i2, long j2) {
                Cursor cursorMo10512c = ((CursorAdapter) adapterView.getAdapter()).mo10512c();
                if (cursorMo10512c.moveToPosition(i2)) {
                    String string = cursorMo10512c.getString(cursorMo10512c.getColumnIndex("sectionhRef"));
                    iMDLogger.m73554j("ElsSectionsViewer", "Goto : " + string);
                    ((ELSViewerActivity.ELSViewerFragment) ELSSectionsViewer.this.m15351l0()).mo71967C3(string);
                    ELSSectionsViewer.this.mo15203M2();
                }
            }
        });
        builder.setView(viewInflate);
        return builder.create();
    }
}

package net.imedicaldoctor.imd.Fragments.Epocrate;

import android.content.res.Resources;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.exifinterface.media.ExifInterface;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import net.imedicaldoctor.imd.ViewHolders.StickySectionAdapter;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class EPOIDListActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88056A4;

    /* renamed from: B4 */
    public String f88057B4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        RecyclerView.Adapter adapter;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            this.f88057B4 = null;
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
        } else {
            this.f88057B4 = m15387y().getString("ParentId");
            appBarLayout.m35746D(false, false);
            appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOIDListActivityFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    relativeLayout.setVisibility(0);
                }
            }, 800L);
        }
        String str = this.f88057B4;
        if (str == null) {
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select * from id_locations");
            adapter = new ChaptersAdapter(m15366r(), this.f88794n4, "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOIDListActivityFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: f0 */
                public void mo71975f0(Bundle bundle2, int i2) {
                    EPOIDListActivityFragment.this.m72188i3(bundle2, i2);
                }
            };
        } else {
            String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, ",,,,,");
            String strValueOf = String.valueOf(Integer.valueOf(strArrSplitByWholeSeparator[0]).intValue() + 1);
            String str2 = strArrSplitByWholeSeparator[1];
            ArrayList<Bundle> arrayListM71817V = this.f88791k4.m71817V(this.f88788h4, "select * from id_cats_indications inner join id_indications on id_indications.id=id_cats_indications.indicationId where id_indications.source=" + strValueOf + " and id_cats_indications.source=" + strValueOf + " and catId=" + str2);
            this.f88794n4 = arrayListM71817V;
            adapter = new StickySectionAdapter(m15366r(), m72189j3(arrayListM71817V), "title", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOIDListActivityFragment.3
                @Override // net.imedicaldoctor.imd.ViewHolders.StickySectionAdapter
                /* renamed from: h0 */
                public void mo72190h0(Bundle bundle2, int i2) {
                    EPOIDListActivityFragment.this.m72188i3(bundle2, i2);
                }
            };
        }
        this.f88792l4 = adapter;
        this.f88056A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null) { // from class: net.imedicaldoctor.imd.Fragments.Epocrate.EPOIDListActivityFragment.4
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: g0 */
            public void mo71976g0(Bundle bundle2, int i2) {
                EPOIDListActivityFragment.this.m72468V2();
                EPOIDListActivityFragment ePOIDListActivityFragment = EPOIDListActivityFragment.this;
                ePOIDListActivityFragment.f88791k4.m71772A1(ePOIDListActivityFragment.f88788h4, "id-" + bundle2.getString("contentId"), null, null);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                EPOIDListActivityFragment.this.m72468V2();
                EPOIDListActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88056A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88056A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match 'text:" + str + "* AND typeText:ID AND type:1'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: c3 */
    public void mo72171c3() {
        this.f88800t4.setImageDrawable(m15320b0().getDrawable(C5562R.drawable.id_tx_icon));
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        return "Infectious Disease Treatment";
    }

    /* renamed from: i3 */
    public void m72188i3(Bundle bundle, int i2) {
        m72468V2();
        String string = bundle.getString("type");
        if (string.equals(ExifInterface.f16317Y4)) {
            Bundle bundle2 = new Bundle();
            bundle2.putBundle("DB", this.f88788h4);
            bundle2.putString("ParentId", bundle.getString("source") + ",,,,," + bundle.getString("id2"));
            this.f88791k4.m71798N(EPOIDListActivity.class, EPOIDListActivityFragment.class, bundle2);
            return;
        }
        if (string.equals(ExifInterface.f16326Z4)) {
            this.f88791k4.m71772A1(this.f88788h4, "id-" + bundle.getString("id2"), null, null);
        }
    }

    /* renamed from: j3 */
    public ArrayList<Bundle> m72189j3(ArrayList<Bundle> arrayList) {
        ArrayList<Bundle> arrayList2 = new ArrayList<>();
        ArrayList<? extends Parcelable> arrayList3 = new ArrayList<>();
        Iterator<Bundle> it2 = arrayList.iterator();
        String str = "";
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String string = next.getString("type");
            String string2 = next.getString("title");
            if (string.equals(IcyHeaders.f28171a3)) {
                arrayList3 = new ArrayList<>();
                str = string2;
            } else if (string.equals("0")) {
                Bundle bundle = new Bundle();
                bundle.putString("title", str);
                bundle.putParcelableArrayList("items", arrayList3);
                arrayList2.add(bundle);
            } else {
                arrayList3.add(next);
            }
        }
        if (arrayList3.size() > 0) {
            Bundle bundle2 = new Bundle();
            bundle2.putString("title", str);
            bundle2.putParcelableArrayList("items", arrayList3);
            arrayList2.add(bundle2);
        }
        return arrayList2;
    }
}

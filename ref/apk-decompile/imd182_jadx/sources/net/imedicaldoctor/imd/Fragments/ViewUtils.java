package net.imedicaldoctor.imd.Fragments;

import android.view.View;
import androidx.core.view.ViewCompat;

/* loaded from: classes3.dex */
public class ViewUtils {
    /* renamed from: a */
    public static boolean m72761a(View view, int i2, int i3) {
        int iM9034B0 = (int) (ViewCompat.m9034B0(view) + 0.5f);
        int iM9038C0 = (int) (ViewCompat.m9038C0(view) + 0.5f);
        return i2 >= view.getLeft() + iM9034B0 && i2 <= view.getRight() + iM9034B0 && i3 >= view.getTop() + iM9038C0 && i3 <= view.getBottom() + iM9038C0;
    }
}

package net.imedicaldoctor.imd.Fragments;

import android.animation.Animator;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.core.internal.view.SupportMenu;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.IOException;
import java.util.Arrays;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Utils.Devices;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.Views.ProgressBarCircularIndeterminate;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class activationActivity extends iMDActivity {

    public static class activationFragment extends Fragment {

        /* renamed from: e4 */
        private ExecutorService f90164e4;

        /* renamed from: f4 */
        private Handler f90165f4;

        /* renamed from: g4 */
        public View f90166g4;

        /* renamed from: h4 */
        public VBHelper f90167h4;

        /* renamed from: i4 */
        private int f90168i4 = 0;

        /* renamed from: j4 */
        private String f90169j4;

        /* renamed from: k4 */
        private TextView f90170k4;

        /* renamed from: l4 */
        private EditText f90171l4;

        /* renamed from: m4 */
        private EditText f90172m4;

        /* JADX INFO: Access modifiers changed from: private */
        static class ResultWrapper {

            /* renamed from: a */
            String f90194a;

            private ResultWrapper() {
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:36:0x0136  */
        /* JADX WARN: Removed duplicated region for block: B:39:0x013e  */
        /* JADX WARN: Removed duplicated region for block: B:46:0x0143 A[EXC_TOP_SPLITTER, SYNTHETIC] */
        /* JADX WARN: Type inference failed for: r2v0, types: [net.imedicaldoctor.imd.Data.CompressHelper] */
        /* JADX WARN: Type inference failed for: r2v11, types: [java.net.HttpURLConnection, java.net.URLConnection] */
        /* JADX WARN: Type inference failed for: r2v3 */
        /* renamed from: O2 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private java.lang.String m73035O2(java.lang.String r10) throws java.lang.Throwable {
            /*
                Method dump skipped, instructions count: 362
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.m73035O2(java.lang.String):java.lang.String");
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: c3 */
        public void m73049c3() {
            if (m15366r() == null) {
                return;
            }
            try {
                TextView textView = (TextView) m15366r().findViewById(C5562R.id.status_label);
                textView.setVisibility(0);
                textView.setTextColor(SupportMenu.f12679c);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        /* renamed from: e3 */
        private void m73050e3() {
            try {
                ((ProgressBarCircularIndeterminate) m15366r().findViewById(C5562R.id.progress_bar)).setVisibility(8);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: f3 */
        public void m73051f3() {
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: g3 */
        public void m73052g3() {
            try {
                InputMethodManager inputMethodManager = (InputMethodManager) m15366r().getSystemService("input_method");
                if (m15366r().getCurrentFocus() != null) {
                    inputMethodManager.hideSoftInputFromWindow(m15366r().getCurrentFocus().getWindowToken(), 0);
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        /* renamed from: h3 */
        private void m73053h3(final View view, long j2) {
            view.animate().alpha(0.0f).setDuration(j2).setListener(new Animator.AnimatorListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.12
                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationCancel(Animator animator) {
                }

                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationEnd(Animator animator) {
                    view.setVisibility(8);
                }

                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationRepeat(Animator animator) {
                }

                @Override // android.animation.Animator.AnimatorListener
                public void onAnimationStart(Animator animator) {
                }
            });
        }

        /* renamed from: i3 */
        public static boolean m73054i3(Context context) {
            return Settings.Global.getInt(context.getContentResolver(), "airplane_mode_on", 0) != 0;
        }

        /* renamed from: j3 */
        public static boolean m73055j3(Context context) {
            NetworkCapabilities networkCapabilities;
            ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
            if (connectivityManager == null) {
                return false;
            }
            if (Build.VERSION.SDK_INT < 23) {
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                return activeNetworkInfo != null && activeNetworkInfo.isConnected();
            }
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork == null || (networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)) == null) {
                return false;
            }
            return networkCapabilities.hasTransport(1) || networkCapabilities.hasTransport(0);
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: k3 */
        public /* synthetic */ void m73056k3(Context context) {
            Log.d("performDevice", "Navigating to mainActivity.");
            context.startActivity(new Intent(context, (Class<?>) mainActivity.class));
            m15307V1().finish();
            m15307V1().overridePendingTransition(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out);
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: l3 */
        public /* synthetic */ void m73057l3(ResultWrapper resultWrapper, final Context context) {
            String str;
            Log.d("performDevice", "Handling result on the main thread.");
            try {
                if (resultWrapper.f90194a == null) {
                    Log.e("performDevice", "Device result is null. Showing error.");
                    m73067w3("Error in contacting server. Please check your internet connection and tap here to try again");
                    m73066v3();
                    return;
                }
                try {
                    Log.d("performDevice", "Processing result: " + resultWrapper.f90194a);
                    String strReplace = resultWrapper.f90194a.replace("|||||", ":::::");
                    resultWrapper.f90194a = strReplace;
                    String[] strArrSplit = TextUtils.split(strReplace, ":::::");
                    Log.d("performDevice", "Split result into parts: " + Arrays.toString(strArrSplit));
                    if (strArrSplit[0].equals(IcyHeaders.f28171a3) && strArrSplit[0].length() == 1) {
                        Log.d("performDevice", "Device activation successful.");
                        m73051f3();
                        this.f90169j4 = null;
                        this.f90170k4.setText("Your Device Activated Successfully. Enjoy!");
                        context.getSharedPreferences("default_preferences", 0).edit().putString("ActivationCode", strArrSplit[1]).apply();
                        Log.d("performDevice", "Activation code saved: " + strArrSplit[1]);
                        this.f90165f4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.e
                            @Override // java.lang.Runnable
                            public final void run() {
                                this.f90615s.m73056k3(context);
                            }
                        }, ExoPlayer.f21773a1);
                        return;
                    }
                    if (strArrSplit[0].equals("0")) {
                        Log.e("performDevice", "Server returned error: " + strArrSplit[1]);
                        m73066v3();
                        str = strArrSplit[1];
                    } else {
                        Log.e("performDevice", "Unknown error in adding device.");
                        m73066v3();
                        str = "Error in adding device";
                    }
                    m73067w3(str);
                } catch (Exception e2) {
                    Log.e("performDevice", "Error while parsing the result.", e2);
                    m73067w3("Data error. Please try again.");
                }
            } catch (Exception e3) {
                Log.e("performDevice", "Unexpected error occurred.", e3);
                m73067w3("An unexpected error occurred. Please try again.");
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: m3 */
        public /* synthetic */ void m73058m3(String str) {
            Log.d("performDevice", "ExecutorService started for device registration.");
            final ResultWrapper resultWrapper = new ResultWrapper();
            String strM73393b = Devices.m73393b();
            Log.d("performDevice", "Device name: " + strM73393b);
            final FragmentActivity fragmentActivityM15307V1 = m15307V1();
            int i2 = 0;
            try {
                i2 = fragmentActivityM15307V1.getPackageManager().getPackageInfo(fragmentActivityM15307V1.getPackageName(), 0).versionCode;
                Log.d("performDevice", "App version code: " + i2);
            } catch (Exception e2) {
                Log.e("performDevice", "Error fetching version code.", e2);
            }
            String str2 = "addDevice|||||" + str + "|||||" + this.f90167h4.m73451m() + "|||||" + Build.USER + "|||||" + strM73393b + "|||||android|||||" + m73068d3() + "|||||android-" + i2;
            Log.d("performDevice", "Constructed command: " + str2);
            try {
                resultWrapper.f90194a = this.f90167h4.m73448j(m73035O2(str2), "127");
                Log.d("performDevice", "Decrypted response: " + resultWrapper.f90194a);
            } catch (Exception e3) {
                Log.e("performDevice", "Error during command sending or decryption.", e3);
                resultWrapper.f90194a = null;
            }
            this.f90165f4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.g
                @Override // java.lang.Runnable
                public final void run() {
                    this.f90724s.m73057l3(resultWrapper, fragmentActivityM15307V1);
                }
            });
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:19:0x00f8 -> B:23:0x0133). Please report as a decompilation issue!!! */
        /* renamed from: n3 */
        public /* synthetic */ void m73059n3(ResultWrapper resultWrapper) {
            Log.d("performLoginTask", "Handling result on the main thread.");
            try {
                if (resultWrapper.f90194a == null) {
                    Log.e("performLoginTask", "loginResult.result is null. Showing error.");
                    m73067w3("Error in contacting server. Please check your internet connection and tap here to try again");
                    m73066v3();
                } else {
                    try {
                        Log.d("performLoginTask", "Processing result: " + resultWrapper.f90194a);
                        String strReplace = resultWrapper.f90194a.replace("|||||", ":::::");
                        resultWrapper.f90194a = strReplace;
                        String[] strArrSplit = TextUtils.split(strReplace, ":::::");
                        Log.d("performLoginTask", "Split result into parts: " + Arrays.toString(strArrSplit));
                        if (strArrSplit[0].equals(IcyHeaders.f28171a3) && strArrSplit[0].length() == 1) {
                            Log.d("performLoginTask", "Login successful.");
                            m73051f3();
                            this.f90169j4 = null;
                            this.f90170k4.setText("Login Successful");
                            m15307V1().getSharedPreferences("default_preferences", 0).edit().putString("Username", this.f90167h4.m73452n(this.f90171l4.getText().toString(), "127")).putString("Password", this.f90167h4.m73452n(this.f90172m4.getText().toString(), "127")).remove("DS").apply();
                            Log.d("performLoginTask", "Username and password saved.");
                            m73063r3(strArrSplit[1]);
                        } else {
                            Log.e("performLoginTask", "Wrong username or password.");
                            m73066v3();
                            m73067w3("Wrong Username or Password");
                            m15307V1().getSharedPreferences("default_preferences", 0).edit().remove("Username").remove("Password").apply();
                        }
                    } catch (Exception e2) {
                        Log.e("performLoginTask", "Error while parsing the result.", e2);
                        m73067w3("Data error. Please try again." + e2.getLocalizedMessage());
                    }
                }
            } catch (Exception e3) {
                Log.e("performLoginTask", "Unexpected error occurred.", e3);
                m73067w3("An unexpected error occurred. Please try again." + e3.getLocalizedMessage());
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: o3 */
        public /* synthetic */ void m73060o3(String str) throws Throwable {
            Log.d("performLoginTask", "ExecutorService started background task.");
            final ResultWrapper resultWrapper = new ResultWrapper();
            try {
                Log.d("performLoginTask", "Sending command: " + str);
                String strM73035O2 = m73035O2(str);
                Log.d("performLoginTask", "Received response: " + strM73035O2);
                resultWrapper.f90194a = this.f90167h4.m73448j(strM73035O2, "127");
                Log.d("performLoginTask", "Decrypted response: " + resultWrapper.f90194a);
            } catch (Exception e2) {
                Log.e("performLoginTask", "Error during command sending or decryption.", e2);
                resultWrapper.f90194a = null;
            }
            this.f90165f4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.f
                @Override // java.lang.Runnable
                public final void run() {
                    this.f90617s.m73059n3(resultWrapper);
                }
            });
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: p3 */
        public void m73061p3() {
            TextView textView = (TextView) m15366r().findViewById(C5562R.id.status_label);
            textView.setVisibility(0);
            textView.setTextColor(-16711936);
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: q3 */
        public void m73062q3(String str) {
            mo15256D2(new Intent("android.intent.action.VIEW", Uri.parse(str)));
        }

        /* renamed from: r3 */
        private void m73063r3(final String str) {
            Log.d("performDevice", "Starting device registration...");
            if (this.f90167h4 != null && this.f90170k4 != null) {
                this.f90164e4.execute(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.h
                    @Override // java.lang.Runnable
                    public final void run() {
                        this.f90726s.m73058m3(str);
                    }
                });
            } else {
                Log.e("performDevice", "Essential objects are null. Aborting.");
                m73067w3("Application error. Please restart the app.");
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: t3 */
        public void m73064t3(String str) {
            try {
                m73050e3();
                if (str != null) {
                    this.f90170k4.setText(str);
                    this.f90170k4.setVisibility(0);
                } else {
                    this.f90170k4.setText("");
                    this.f90170k4.setVisibility(8);
                    this.f90169j4 = str;
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: u3 */
        public void m73065u3() {
            ProgressBarCircularIndeterminate progressBarCircularIndeterminate = (ProgressBarCircularIndeterminate) m15366r().findViewById(C5562R.id.progress_bar);
            progressBarCircularIndeterminate.setBackgroundColor(Color.parseColor("#1e88e5"));
            progressBarCircularIndeterminate.setVisibility(0);
            this.f90170k4.setVisibility(8);
        }

        /* renamed from: v3 */
        private void m73066v3() {
        }

        /* renamed from: w3 */
        private void m73067w3(String str) {
            m73050e3();
            this.f90169j4 = null;
            this.f90170k4.setText(str);
            m73049c3();
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: Q0 */
        public void mo15207Q0(Bundle bundle) {
            super.mo15207Q0(bundle);
            this.f90164e4 = Executors.newSingleThreadExecutor();
            this.f90165f4 = new Handler(Looper.getMainLooper());
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
            View view = this.f90166g4;
            if (view != null) {
                return view;
            }
            this.f90167h4 = new VBHelper(m15366r());
            this.f90166g4 = layoutInflater.inflate(C5562R.layout.fragment_new_login, viewGroup, false);
            new Timer().schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.1
                @Override // java.util.TimerTask, java.lang.Runnable
                public void run() {
                    if (activationFragment.this.f90169j4 == null) {
                        activationFragment.this.f90168i4 = 0;
                        return;
                    }
                    activationFragment.this.f90168i4++;
                    final String str = "";
                    for (int i2 = 0; i2 < activationFragment.this.f90168i4; i2++) {
                        str = str + ".";
                    }
                    activationFragment.this.f90170k4.post(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.1.1
                        @Override // java.lang.Runnable
                        public void run() {
                            TextView textView = (TextView) activationFragment.this.f90166g4.findViewById(C5562R.id.status_label);
                            if (activationFragment.this.f90169j4 != null) {
                                textView.setText(activationFragment.this.f90169j4 + str);
                            }
                        }
                    });
                    if (activationFragment.this.f90168i4 >= 3) {
                        activationFragment.this.f90168i4 = 0;
                    }
                }
            }, 500L, 500L);
            ((TextView) this.f90166g4.findViewById(C5562R.id.imd_title)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.2
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    String strM71865l2 = new CompressHelper(activationFragment.this.m15366r()).m71865l2();
                    if (strM71865l2.length() <= 0) {
                        strM71865l2 = "No Message Available";
                    }
                    new AlertDialog.Builder(activationFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l(strM71865l2).mo1106p("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.2.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                        }
                    }).m1090I();
                }
            });
            this.f90170k4 = (TextView) this.f90166g4.findViewById(C5562R.id.status_label);
            this.f90171l4 = (EditText) this.f90166g4.findViewById(C5562R.id.user_text);
            this.f90172m4 = (EditText) this.f90166g4.findViewById(C5562R.id.password_text);
            this.f90171l4.setText(this.f90167h4.m73448j(m15307V1().getSharedPreferences("default_preferences", 0).getString("Username", ""), "127"));
            this.f90172m4.setText(this.f90167h4.m73448j(m15307V1().getSharedPreferences("default_preferences", 0).getString("Password", ""), "127"));
            final CompressHelper compressHelper = new CompressHelper(m15366r());
            Button button = (Button) this.f90166g4.findViewById(C5562R.id.login_button);
            ((LinearLayout) this.f90166g4.findViewById(C5562R.id.upper_layout)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    activationFragment.this.m73052g3();
                }
            });
            button.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    if (activationFragment.this.f90171l4.getText().toString().length() < 1) {
                        activationFragment.this.m73049c3();
                        activationFragment.this.m73064t3("Please enter your Username");
                        return;
                    }
                    activationFragment.this.m73061p3();
                    if (activationFragment.this.f90172m4.getText().toString().length() < 1) {
                        activationFragment.this.m73049c3();
                        activationFragment.this.m73064t3("Please enter your Password");
                        return;
                    }
                    if (activationFragment.m73054i3(activationFragment.this.m15366r())) {
                        activationFragment.this.m73049c3();
                        activationFragment.this.m73064t3("Please turn off Airplane Mode");
                        return;
                    }
                    activationFragment.this.m73052g3();
                    activationFragment.this.m73065u3();
                    activationFragment.this.m73051f3();
                    activationFragment.this.m73069s3("checkUser|||||" + activationFragment.this.f90171l4.getText().toString() + "|||||" + activationFragment.this.f90172m4.getText().toString());
                }
            });
            ((TextView) this.f90166g4.findViewById(C5562R.id.register_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.5
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    compressHelper.m71803P("http://imedicaldoctor.net/buyaccount.php");
                }
            });
            final TextView textView = (TextView) this.f90166g4.findViewById(C5562R.id.change_server_button);
            textView.setText(m15307V1().getSharedPreferences("default_preferences", 0).getString("MainServer", "Iran") + " Server (Tap to change)");
            textView.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.6
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    activationFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).edit().putString("MainServer", activationFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("MainServer", "Iran").equals("Iran") ? "Germany" : "Iran").commit();
                    textView.setText(activationFragment.this.m15307V1().getSharedPreferences("default_preferences", 0).getString("MainServer", "Iran") + " Server (Tap to change)");
                }
            });
            TextView textView2 = (TextView) this.f90166g4.findViewById(C5562R.id.forgot_label);
            textView2.setOnLongClickListener(new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.7
                @Override // android.view.View.OnLongClickListener
                public boolean onLongClick(View view2) {
                    String strM71865l2 = compressHelper.m71865l2();
                    if (strM71865l2.length() <= 0) {
                        return false;
                    }
                    new AlertDialog.Builder(activationFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l(strM71865l2).mo1106p("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.7.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                        }
                    }).m1090I();
                    return false;
                }
            });
            textView2.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.8
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    compressHelper.m71803P("http://imedicaldoctor.net/forgot.php");
                }
            });
            ((ImageView) this.f90166g4.findViewById(C5562R.id.telegram_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.9
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    activationFragment.this.m73062q3("http://imedicaldoctor.net/telegramandroid.php");
                }
            });
            ((ImageView) this.f90166g4.findViewById(C5562R.id.instagram_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.10
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    activationFragment.this.m73062q3("http://instagram.com/imedicaldoctor");
                }
            });
            ((ImageView) this.f90166g4.findViewById(C5562R.id.mail_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.activationActivity.activationFragment.11
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    activationFragment.this.m73062q3("mailto:<EMAIL>");
                }
            });
            return this.f90166g4;
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: V0 */
        public void mo15306V0() {
            super.mo15306V0();
            this.f90164e4.shutdown();
        }

        /* renamed from: d3 */
        public String m73068d3() {
            String str = Build.VERSION.RELEASE;
            return "Android SDK: " + Build.VERSION.SDK_INT + " (" + str + ")";
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: p1 */
        public void mo15361p1(View view, Bundle bundle) {
            super.mo15361p1(view, bundle);
            this.f90170k4.setText("");
        }

        /* renamed from: s3 */
        public void m73069s3(final String str) {
            Log.d("performLoginTask", "Starting login task...");
            if (this.f90167h4 == null || this.f90171l4 == null || this.f90172m4 == null || this.f90170k4 == null) {
                Log.e("performLoginTask", "Essential objects are null. Aborting.");
                m73067w3("Application error. Please restart the app.");
            } else {
                Log.d("performLoginTask", "All essential objects are initialized.");
                this.f90164e4.execute(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.d
                    @Override // java.lang.Runnable
                    public final void run() throws Throwable {
                        this.f90249s.m73060o3(str);
                    }
                });
            }
        }
    }

    /* renamed from: b1 */
    public static Bitmap m73029b1(Context context, String str) {
        try {
            return BitmapFactory.decodeStream(context.getAssets().open(str));
        } catch (IOException unused) {
            return null;
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, android.view.ComponentActivity, android.app.Activity
    public void onBackPressed() {
        super.onBackPressed();
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_activation);
        if (bundle == null) {
            m15416k0().m15664u().m15818f(C5562R.id.container, new activationFragment()).mo15164r();
        }
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        return true;
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        menuItem.getItemId();
        return super.onOptionsItemSelected(menuItem);
    }
}

package net.imedicaldoctor.imd.Fragments.VisualDDX;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import com.bumptech.glide.Glide;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import info.hoang8f.android.segmented.SegmentedGroup;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBuilderActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class VDDxBodyPartDialog extends DialogFragment {

    /* renamed from: F4 */
    public RelativeLayout f89720F4;

    /* renamed from: G4 */
    private Bundle f89721G4;

    /* renamed from: H4 */
    private Bundle f89722H4;

    /* renamed from: I4 */
    private SegmentedGroup f89723I4;

    /* renamed from: J4 */
    private ArrayList<Bundle> f89724J4;

    /* renamed from: K4 */
    private ArrayList<Bundle> f89725K4;

    /* renamed from: L4 */
    private String f89726L4;

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_vddx_body_part_dialog, (ViewGroup) null);
        final CompressHelper compressHelper = new CompressHelper(m15366r());
        final ListView listView = (ListView) viewInflate.findViewById(C5562R.id.list_view);
        this.f89720F4 = (RelativeLayout) viewInflate.findViewById(C5562R.id.body_parts);
        RadioButton radioButton = (RadioButton) viewInflate.findViewById(C5562R.id.button2);
        this.f89723I4 = (SegmentedGroup) viewInflate.findViewById(C5562R.id.segment);
        SegmentedGroup segmentedGroup = (SegmentedGroup) viewInflate.findViewById(C5562R.id.segmentgender);
        this.f89721G4 = m15387y().getBundle("db");
        final Bundle bundle2 = m15387y().getBundle("allFindings");
        this.f89724J4 = m15387y().getParcelableArrayList("distribution");
        this.f89725K4 = m15387y().getParcelableArrayList("location");
        final String string = m15387y().getString("bodyFolder");
        this.f89726L4 = CompressHelper.m71754h1(this.f89721G4, string, "BodyLocation");
        String str = this.f89726L4 + "/info.js";
        this.f89723I4.setSelected(true);
        try {
            JSONObject jSONObject = new JSONObject(CompressHelper.m71750e2(new File(str)));
            m72866o3(this.f89726L4 + "/homunculus.gif");
            Bundle bundleM71784G = compressHelper.m71784G(jSONObject);
            this.f89722H4 = bundleM71784G;
            if (bundleM71784G.getParcelableArrayList("bodyDistribution").size() == 0) {
                radioButton.setVisibility(8);
            }
            if (!bundle2.keySet().contains("20200")) {
                radioButton.setVisibility(8);
            }
            FragmentActivity fragmentActivityM15366r = m15366r();
            int i2 = C5562R.layout.list_view_item_simple_text_check;
            final ArrayAdapter<Bundle> arrayAdapter = new ArrayAdapter<Bundle>(fragmentActivityM15366r, i2) { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBodyPartDialog.1
                @Override // android.widget.ArrayAdapter, android.widget.Adapter
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public Bundle getItem(int i3) {
                    return (Bundle) VDDxBodyPartDialog.this.f89722H4.getParcelableArrayList("bodyLocation").get(i3);
                }

                @Override // android.widget.ArrayAdapter, android.widget.Adapter
                public int getCount() {
                    return VDDxBodyPartDialog.this.f89722H4.getParcelableArrayList("bodyLocation").size();
                }

                @Override // android.widget.ArrayAdapter, android.widget.Adapter
                public View getView(int i3, View view, ViewGroup viewGroup) {
                    if (view == null) {
                        view = LayoutInflater.from(VDDxBodyPartDialog.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_check, viewGroup, false);
                    }
                    Bundle item = getItem(i3);
                    TextView textView = (TextView) view.findViewById(C5562R.id.text);
                    ImageView imageView = (ImageView) view.findViewById(C5562R.id.check_icon);
                    String string2 = item.getString("title");
                    if (string2.length() == 0) {
                        string2 = bundle2.getString(item.getStringArrayList("findingIds").get(0));
                    }
                    textView.setText(string2);
                    if (CompressHelper.m71745b(VDDxBodyPartDialog.this.f89725K4, item, "imageName") > -1 || CompressHelper.m71745b(VDDxBodyPartDialog.this.f89724J4, item, "imageName") > -1) {
                        imageView.setVisibility(0);
                    } else {
                        imageView.setVisibility(4);
                    }
                    return view;
                }
            };
            final ArrayAdapter<Bundle> arrayAdapter2 = new ArrayAdapter<Bundle>(m15366r(), i2) { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBodyPartDialog.2
                @Override // android.widget.ArrayAdapter, android.widget.Adapter
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public Bundle getItem(int i3) {
                    return (Bundle) VDDxBodyPartDialog.this.f89722H4.getParcelableArrayList("bodyDistribution").get(i3);
                }

                @Override // android.widget.ArrayAdapter, android.widget.Adapter
                public int getCount() {
                    return VDDxBodyPartDialog.this.f89722H4.getParcelableArrayList("bodyDistribution").size();
                }

                @Override // android.widget.ArrayAdapter, android.widget.Adapter
                public View getView(int i3, View view, ViewGroup viewGroup) {
                    if (view == null) {
                        view = LayoutInflater.from(VDDxBodyPartDialog.this.m15366r()).inflate(C5562R.layout.list_view_item_simple_text_check, viewGroup, false);
                    }
                    Bundle item = getItem(i3);
                    TextView textView = (TextView) view.findViewById(C5562R.id.text);
                    ImageView imageView = (ImageView) view.findViewById(C5562R.id.check_icon);
                    String string2 = item.getString("title");
                    if (string2.length() == 0) {
                        string2 = bundle2.getString(item.getStringArrayList("findingIds").get(0));
                    }
                    textView.setText(string2);
                    if (CompressHelper.m71745b(VDDxBodyPartDialog.this.f89725K4, item, "imageName") > -1 || CompressHelper.m71745b(VDDxBodyPartDialog.this.f89724J4, item, "imageName") > -1) {
                        imageView.setVisibility(0);
                    } else {
                        imageView.setVisibility(4);
                    }
                    return view;
                }
            };
            listView.setAdapter((ListAdapter) arrayAdapter);
            this.f89723I4.check(C5562R.id.button1);
            segmentedGroup.check(C5562R.id.buttongender1);
            this.f89723I4.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBodyPartDialog.3
                @Override // android.widget.RadioGroup.OnCheckedChangeListener
                public void onCheckedChanged(RadioGroup radioGroup, int i3) {
                    ListView listView2;
                    ArrayAdapter arrayAdapter3;
                    if (i3 == C5562R.id.button1) {
                        listView2 = listView;
                        arrayAdapter3 = arrayAdapter;
                    } else {
                        listView2 = listView;
                        arrayAdapter3 = arrayAdapter2;
                    }
                    listView2.setAdapter((ListAdapter) arrayAdapter3);
                    VDDxBodyPartDialog.this.m72867p3();
                }
            });
            segmentedGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBodyPartDialog.4
                @Override // android.widget.RadioGroup.OnCheckedChangeListener
                public void onCheckedChanged(RadioGroup radioGroup, int i3) {
                    VDDxBodyPartDialog vDDxBodyPartDialog;
                    Bundle bundle3;
                    String strReplace;
                    if (i3 == C5562R.id.buttongender1) {
                        vDDxBodyPartDialog = VDDxBodyPartDialog.this;
                        bundle3 = vDDxBodyPartDialog.f89721G4;
                        strReplace = string.replace("Female", "Male");
                    } else {
                        vDDxBodyPartDialog = VDDxBodyPartDialog.this;
                        bundle3 = vDDxBodyPartDialog.f89721G4;
                        strReplace = string.replace("Male", "Female");
                    }
                    vDDxBodyPartDialog.f89726L4 = CompressHelper.m71754h1(bundle3, strReplace, "BodyLocation");
                    try {
                        VDDxBodyPartDialog.this.f89722H4 = compressHelper.m71784G(new JSONObject(CompressHelper.m71750e2(new File(VDDxBodyPartDialog.this.f89726L4 + "/info.js"))));
                        VDDxBodyPartDialog.this.m72866o3(VDDxBodyPartDialog.this.f89726L4 + "/homunculus.gif");
                        VDDxBodyPartDialog.this.f89720F4.removeAllViews();
                        VDDxBodyPartDialog.this.m72866o3(VDDxBodyPartDialog.this.f89726L4 + "/homunculus.gif");
                        ListView listView2 = listView;
                        listView2.setAdapter(listView2.getAdapter());
                        VDDxBodyPartDialog.this.m72867p3();
                    } catch (Exception unused) {
                    }
                }
            });
            listView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxBodyPartDialog.5
                @Override // android.widget.AdapterView.OnItemClickListener
                public void onItemClick(AdapterView<?> adapterView, View view, int i3, long j2) {
                    Bundle bundle3;
                    int iM71745b;
                    ArrayList arrayList;
                    ArrayList arrayList2;
                    VDDxBuilderActivity.VDDXBuilderFragment vDDXBuilderFragment = (VDDxBuilderActivity.VDDXBuilderFragment) VDDxBodyPartDialog.this.m15351l0();
                    if (adapterView.getAdapter() == arrayAdapter) {
                        bundle3 = (Bundle) adapterView.getAdapter().getItem(i3);
                        iM71745b = CompressHelper.m71745b(VDDxBodyPartDialog.this.f89725K4, bundle3, "imageName");
                        if (iM71745b > -1) {
                            arrayList2 = VDDxBodyPartDialog.this.f89725K4;
                            arrayList2.remove(iM71745b);
                        } else {
                            arrayList = VDDxBodyPartDialog.this.f89725K4;
                            arrayList.add(bundle3);
                        }
                    } else {
                        bundle3 = (Bundle) adapterView.getAdapter().getItem(i3);
                        iM71745b = CompressHelper.m71745b(VDDxBodyPartDialog.this.f89724J4, bundle3, "imageName");
                        if (iM71745b > -1) {
                            arrayList2 = VDDxBodyPartDialog.this.f89724J4;
                            arrayList2.remove(iM71745b);
                        } else {
                            arrayList = VDDxBodyPartDialog.this.f89724J4;
                            arrayList.add(bundle3);
                        }
                    }
                    vDDXBuilderFragment.m72885J4();
                    ((ArrayAdapter) adapterView.getAdapter()).notifyDataSetChanged();
                    VDDxBodyPartDialog.this.m72867p3();
                }
            });
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("VDDxBodyDialog", "Error in dialog : " + e2);
        }
        builder.setView(viewInflate);
        return builder.create();
    }

    /* renamed from: n3 */
    public void m72865n3(String str) {
        String str2 = this.f89726L4 + "/" + str + "_highlight.png";
        if (!new File(str2).exists()) {
            str2 = this.f89726L4 + "/" + str + ".png";
        }
        View viewFindViewWithTag = this.f89720F4.findViewWithTag(str2);
        if (viewFindViewWithTag != null) {
            viewFindViewWithTag.setVisibility(0);
            return;
        }
        ImageView imageView = new ImageView(m15366r());
        Glide.m30041G(m15366r()).mo30124i(new File(str2)).m30165B2(imageView);
        imageView.setTag(str2);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(-1, -1);
        imageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
        imageView.setLayoutParams(layoutParams);
        this.f89720F4.addView(imageView);
    }

    /* renamed from: o3 */
    public void m72866o3(String str) {
        if (this.f89720F4.findViewWithTag(str) != null) {
            return;
        }
        ImageView imageView = new ImageView(m15366r());
        Glide.m30041G(m15366r()).mo30124i(new File(str)).m30165B2(imageView);
        imageView.setTag(str);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(-1, -1);
        imageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
        imageView.setLayoutParams(layoutParams);
        this.f89720F4.addView(imageView);
    }

    /* renamed from: p3 */
    public void m72867p3() {
        ArrayList<Bundle> arrayList = this.f89723I4.getCheckedRadioButtonId() == C5562R.id.button1 ? this.f89725K4 : this.f89724J4;
        Iterator it2 = this.f89722H4.getParcelableArrayList("bodyLocation").iterator();
        while (it2.hasNext()) {
            Bundle bundle = (Bundle) it2.next();
            int iM71745b = CompressHelper.m71745b(arrayList, bundle, "imageName");
            String string = bundle.getString("imageName");
            if (iM71745b > -1) {
                m72865n3(string);
            } else {
                m72868q3(string);
            }
        }
        Iterator it3 = this.f89722H4.getParcelableArrayList("bodyDistribution").iterator();
        while (it3.hasNext()) {
            Bundle bundle2 = (Bundle) it3.next();
            int iM71745b2 = CompressHelper.m71745b(arrayList, bundle2, "imageName");
            String string2 = bundle2.getString("imageName");
            if (iM71745b2 > -1) {
                m72865n3(string2);
            } else {
                m72868q3(string2);
            }
        }
    }

    /* renamed from: q3 */
    public void m72868q3(String str) {
        String str2 = this.f89726L4 + "/" + str + "_highlight.png";
        if (!new File(str2).exists()) {
            str2 = this.f89726L4 + "/" + str + ".png";
        }
        View viewFindViewWithTag = this.f89720F4.findViewWithTag(str2);
        if (viewFindViewWithTag != null) {
            viewFindViewWithTag.setVisibility(4);
        }
    }
}

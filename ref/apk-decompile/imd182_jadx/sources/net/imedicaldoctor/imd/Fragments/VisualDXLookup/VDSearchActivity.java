package net.imedicaldoctor.imd.Fragments.VisualDXLookup;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class VDSearchActivity extends iMDActivity {

    public static class VDSearchFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        public SpellSearchAdapter f89899A4;

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            mo71990Q2();
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
            this.f88797q4 = viewInflate;
            m72469W2(bundle);
            m72465S2();
            this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
            mo71990Q2();
            this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
            ((RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout)).setVisibility(0);
            this.f88794n4 = this.f88791k4.m71817V(this.f88788h4, "Select id as _id,* from DiagnosesSearch order by dName collate nocase asc");
            this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "dName") { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDSearchActivity.VDSearchFragment.1
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: d0 */
                public String mo72422d0(String str) {
                    return str.replace("&#039;", "'");
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: f0 */
                public void mo71975f0(Bundle bundle2, int i2) {
                    VDSearchFragment.this.m72468V2();
                    VDSearchFragment vDSearchFragment = VDSearchFragment.this;
                    vDSearchFragment.f88791k4.m71772A1(vDSearchFragment.f88788h4, bundle2.getString("id"), null, null);
                }
            };
            this.f89899A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "dName", null) { // from class: net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDSearchActivity.VDSearchFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: d0 */
                public String mo72930d0(String str) {
                    return str.replace("&#039;", "'");
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: g0 */
                public void mo71976g0(Bundle bundle2, int i2) {
                    VDSearchFragment.this.m72468V2();
                    VDSearchFragment vDSearchFragment = VDSearchFragment.this;
                    vDSearchFragment.f88791k4.m71772A1(vDSearchFragment.f88788h4, bundle2.getString("id"), null, null);
                }

                @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
                /* renamed from: h0 */
                public void mo71977h0(Bundle bundle2) {
                    VDSearchFragment.this.m72468V2();
                    VDSearchFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
                }
            };
            this.f88803w4.setAdapter(this.f88792l4);
            m72461N2();
            m15358o2(true);
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: X2 */
        public void mo71973X2() {
            this.f89899A4.m73478i0(this.f88795o4, this.f88796p4);
            this.f88803w4.setAdapter(this.f89899A4);
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,* from DiagnosesSearch where dNameSearch match '" + str + "*' order by dName asc");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71817V(this.f88788h4, "Select rowid as _id,word from DiagnosesSpell where word match '" + str + "*'");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new VDSearchFragment());
    }
}

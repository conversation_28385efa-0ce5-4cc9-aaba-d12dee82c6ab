package net.imedicaldoctor.imd.Fragments.NEJM;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import com.itextpdf.text.xml.xmp.DublinCoreProperties;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class NEJMViewerActivity extends ViewerHelperActivity {

    public static class NEJMViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private String f88645X4;

        /* renamed from: Y4 */
        private MenuItem f88646Y4;

        /* renamed from: Z4 */
        public ArrayList<String> f88647Z4;

        /* renamed from: a5 */
        public Bundle f88648a5;

        /* renamed from: b5 */
        public String f88649b5;

        /* renamed from: c5 */
        public String f88650c5;

        /* renamed from: d5 */
        public boolean f88651d5;

        /* renamed from: e5 */
        public Bundle f88652e5;

        /* renamed from: K4 */
        private void m72416K4(String str) {
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", str);
            bundle.putString("isVideo", IcyHeaders.f28171a3);
            ArrayList arrayList = new ArrayList();
            arrayList.add(bundle);
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList);
            intent.putExtra("Start", 0);
            mo15256D2(intent);
        }

        /* renamed from: M4 */
        private void m72417M4(String str) {
            ArrayList<String> arrayList = this.f88647Z4;
            if (arrayList == null || arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
                return;
            }
            ArrayList arrayList2 = new ArrayList();
            Iterator<String> it2 = this.f88647Z4.iterator();
            while (it2.hasNext()) {
                String next = it2.next();
                Bundle bundle = new Bundle();
                bundle.putString("ImagePath", next);
                try {
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(next, "/");
                    String str2 = strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1];
                    bundle.putString("Description", this.f88648a5.containsKey(str2) ? this.f88648a5.getString(str2) : "");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
                bundle.putString("id", next);
                if (new File(next).length() > 5000) {
                    arrayList2.add(bundle);
                }
            }
            int i2 = 0;
            for (int i3 = 0; i3 < arrayList2.size(); i3++) {
                if (str.contains(((Bundle) arrayList2.get(i3)).getString("id"))) {
                    i2 = i3;
                }
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList2);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: C3 */
        public void mo71967C3(String str) {
            this.f89569G4.m73433g("document.getElementById(\"" + str + "\").scrollIntoView(true);");
            this.f89569G4.m73433g("document.getElementsByName(\"" + str + "\")[0].scrollIntoView(true);");
        }

        /* renamed from: I4 */
        public void m72418I4(String str, String str2) {
            ArrayList arrayList = new ArrayList();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", m72419J4(str));
            bundle.putString("Description", str2);
            arrayList.add(bundle);
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList);
            intent.putExtra("Start", 0);
            mo15256D2(intent);
        }

        /* renamed from: J4 */
        public String m72419J4(String str) {
            String strM71755j2 = CompressHelper.m71755j2(CompressHelper.m71754h1(this.f89566D4, this.f88652e5.getString("purl"), this.f88652e5.getString("issueName")));
            while (strM71755j2.contains("../")) {
                strM71755j2 = CompressHelper.m71755j2(strM71755j2);
                str = str.substring(3);
            }
            return strM71755j2 + "/" + str;
        }

        /* renamed from: L4 */
        public String m72420L4(String str) {
            ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
            arrayList.remove(arrayList.size() - 1);
            return StringUtils.join(arrayList, "/");
        }

        /* renamed from: N4 */
        public void m72421N4(String str) {
            ArrayList arrayList = new ArrayList();
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", str);
            bundle.putString("isVideo", IcyHeaders.f28171a3);
            arrayList.add(bundle);
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList);
            intent.putExtra("Start", 0);
            mo15256D2(intent);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            ArrayList<String> arrayList = this.f88647Z4;
            if (arrayList == null || arrayList.size() <= 0) {
                return null;
            }
            return m72840w3(this.f88647Z4);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_epubviewer, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            this.f88648a5 = new Bundle();
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f88647Z4 = bundle.getStringArrayList("mImages");
                this.f88649b5 = bundle.getString("mBasePath");
                this.f88650c5 = bundle.getString("mPath");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            iMDLogger.m73554j("NEJMViewerActivity", "Loading NEJM Document with mDocAddress = " + this.f89567E4);
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.NEJM.NEJMViewerActivity.NEJMViewerFragment.1
                /* JADX WARN: Removed duplicated region for block: B:25:0x0111  */
                /* JADX WARN: Removed duplicated region for block: B:28:0x016c A[Catch: Exception -> 0x0022, TryCatch #1 {Exception -> 0x0022, blocks: (B:3:0x000a, B:5:0x001b, B:30:0x0178, B:32:0x017e, B:10:0x0025, B:12:0x0044, B:14:0x004b, B:23:0x00e3, B:26:0x0112, B:28:0x016c, B:29:0x0170, B:22:0x00c5), top: B:38:0x000a }] */
                /* JADX WARN: Removed duplicated region for block: B:32:0x017e A[Catch: Exception -> 0x0022, TRY_LEAVE, TryCatch #1 {Exception -> 0x0022, blocks: (B:3:0x000a, B:5:0x001b, B:30:0x0178, B:32:0x017e, B:10:0x0025, B:12:0x0044, B:14:0x004b, B:23:0x00e3, B:26:0x0112, B:28:0x016c, B:29:0x0170, B:22:0x00c5), top: B:38:0x000a }] */
                /* JADX WARN: Removed duplicated region for block: B:41:? A[RETURN, SYNTHETIC] */
                @Override // java.lang.Runnable
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public void run() {
                    /*
                        Method dump skipped, instructions count: 409
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.NEJM.NEJMViewerActivity.NEJMViewerFragment.RunnableC49581.run():void");
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.NEJM.NEJMViewerActivity.NEJMViewerFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    NEJMViewerFragment.this.m72836s4();
                    String str = NEJMViewerFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        NEJMViewerFragment nEJMViewerFragment = NEJMViewerFragment.this;
                        nEJMViewerFragment.m72780C4(nEJMViewerFragment.f89595p4);
                        return;
                    }
                    NEJMViewerFragment nEJMViewerFragment2 = NEJMViewerFragment.this;
                    nEJMViewerFragment2.m72795O3(nEJMViewerFragment2.f89563A4, nEJMViewerFragment2.f88649b5);
                    NEJMViewerFragment.this.m72831p4();
                    NEJMViewerFragment.this.mo72642f3(C5562R.menu.menu_epubviewer);
                    NEJMViewerFragment.this.m15358o2(false);
                    NEJMViewerFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: W3 */
        public boolean mo71969W3(ConsoleMessage consoleMessage) {
            String[] strArrSplit = consoleMessage.message().split(",,,,,");
            if (strArrSplit[0].equals("images")) {
                if (strArrSplit.length < 2) {
                    return true;
                }
                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(strArrSplit[1], "|");
                ArrayList<String> arrayList = new ArrayList<>();
                for (String str : strArrSplitByWholeSeparator) {
                    String strReplace = this.f88649b5.replace("file://", "");
                    if (strReplace.endsWith("/")) {
                        strReplace = strReplace.substring(0, strReplace.length() - 1);
                    }
                    String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str, "/");
                    for (String str2 : strArrSplitByWholeSeparator2) {
                        strReplace = str2.equals("..") ? m72420L4(strReplace) : strReplace + "/" + str2;
                    }
                    try {
                        if (this.f88651d5 && strArrSplitByWholeSeparator2.length > 0) {
                            String str3 = strArrSplitByWholeSeparator2[strArrSplitByWholeSeparator2.length - 1];
                            CompressHelper compressHelper = this.f89579Q4;
                            Bundle bundleM71907z = compressHelper.m71907z(compressHelper.m71817V(this.f89566D4, "Select * from images where imageName='" + str3 + "'"));
                            if (bundleM71907z != null) {
                                String string = bundleM71907z.getString("desc");
                                if (!this.f88648a5.containsKey(str3)) {
                                    this.f88648a5.putString(str3, string);
                                }
                            }
                        }
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                    }
                    File file = new File(strReplace);
                    file.length();
                    if (file.length() > ExoPlayer.f21773a1) {
                        arrayList.add(strReplace);
                    }
                    iMDLogger.m73554j("EPUB Images", "Imagepath = : " + strReplace);
                }
                this.f88647Z4 = arrayList;
                mo71972o4();
            }
            return super.mo71969W3(consoleMessage);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: Z3 */
        public void mo71956Z3(WebView webView, String str) {
            super.mo71956Z3(webView, str);
            this.f89569G4.m73433g("IgnoreSmallImages();");
            this.f89569G4.m73433g("fixAllImages2();");
            this.f89569G4.m73433g("fixAllTables();");
            this.f89569G4.m73433g("console.log(\"images,,,,,\" + getImageList());");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            if (menuItem.getItemId() == C5562R.id.action_gallery) {
                m72417M4("soheilvb");
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            this.f88646Y4 = menu.findItem(C5562R.id.action_gallery);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) throws JSONException, UnsupportedEncodingException {
            String str4;
            String str5;
            String str6;
            String strSubstring = str3;
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + strSubstring);
            if (strSubstring.startsWith("//")) {
                strSubstring = strSubstring.substring(2);
            }
            String strReplace = strSubstring;
            if (str2.equals("image")) {
                m72417M4(strReplace);
                return true;
            }
            try {
                strReplace = URLDecoder.decode(strReplace, "UTF-8");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
            if (str2.equals("svv")) {
                m72421N4(this.f89566D4.getString("Path") + strReplace.replace("//", ""));
                return true;
            }
            if (str2.equals(Annotation.f68285k3) || (str2.equals("http") && strReplace.contains("localhost:"))) {
                CompressHelper compressHelper = new CompressHelper(m15366r());
                String strM71752f1 = CompressHelper.m71752f1(this.f89566D4);
                str4 = "NEJM";
                str5 = "image";
                if (str.contains("#")) {
                    str6 = StringUtils.splitByWholeSeparator(str, "#")[1];
                    iMDLogger.m73550f("Testing", "BasePath : " + strM71752f1 + ", Resource : " + strReplace + ", mPath : " + this.f88650c5);
                    StringBuilder sb = new StringBuilder();
                    sb.append(strM71752f1);
                    sb.append("/");
                    strReplace = strReplace.replace(sb.toString(), "").replace(this.f88652e5.getString("issueName") + "/", "");
                    if (this.f88650c5.equalsIgnoreCase(strReplace)) {
                        mo71967C3(str6);
                        return true;
                    }
                    if (strReplace.endsWith("/")) {
                        mo71967C3(str6);
                        return true;
                    }
                } else {
                    str6 = "";
                }
                iMDLogger.m73550f("Testing", "BasePath : " + strM71752f1 + ", Resource : " + strReplace + ", mPath : " + this.f88650c5);
                StringBuilder sb2 = new StringBuilder();
                sb2.append(strM71752f1);
                sb2.append("/");
                strReplace = strReplace.replace(sb2.toString(), "").replace(this.f88652e5.getString("issueName") + "/", "");
                ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(this.f89566D4, "Select * from contents where purl = '" + strReplace + "'");
                if (arrayListM71817V == null || arrayListM71817V.size() == 0) {
                    CompressHelper.m71767x2(m15366r(), "Sorry, Document not available", 1);
                } else {
                    compressHelper.m71772A1(this.f89566D4, arrayListM71817V.get(0).getString("pid"), null, str6);
                }
            } else {
                str4 = "NEJM";
                str5 = "image";
            }
            if (str2.equals("opennejmimage")) {
                try {
                    Log.e("Opennejmimage", "Opennejmimage " + strReplace);
                    JSONObject jSONObject = new JSONObject(strReplace);
                    m72418I4(jSONObject.getString(str5), jSONObject.getString(DublinCoreProperties.f73851e));
                } catch (Exception e3) {
                    FirebaseCrashlytics.m48010d().m48016g(e3);
                }
            }
            if (str2.equals("resourceproxy")) {
                Log.e("Resourceproxy", "Resourceproxy " + strReplace);
                if (strReplace.startsWith("video/")) {
                    strReplace = strReplace.replace("video/", "");
                    try {
                        String string = new JSONObject(strReplace).getString("video");
                        String str7 = str4;
                        Log.e(str7, "Video  " + string);
                        if (string == null) {
                            return false;
                        }
                        String strM71754h1 = CompressHelper.m71754h1(this.f89566D4, string, "resources");
                        if (!new File(strM71754h1).exists()) {
                            String str8 = this.f89579Q4.m71790J() + "/nejm/videos-E/" + string;
                            Log.e(str7, "VideoURL  " + str8 + " Path " + strM71754h1);
                            m72809Z2(str8, strM71754h1);
                            return true;
                        }
                        byte[] bArrM71902x = this.f89579Q4.m71902x(CompressHelper.m71748d2(new File(strM71754h1)), string.toLowerCase(), "127");
                        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "video.mp4");
                        if (new File(strM71753g1).exists()) {
                            new File(strM71753g1).delete();
                        }
                        CompressHelper.m71728D2(new File(strM71753g1), bArrM71902x);
                        m72416K4(strM71753g1);
                    } catch (Exception e4) {
                        FirebaseCrashlytics.m48010d().m48016g(e4);
                    }
                }
                if (strReplace.startsWith("audio/")) {
                    strReplace = strReplace.replace("audio/", "");
                    try {
                        String string2 = new JSONObject(strReplace).getString("href");
                        if (string2 == null) {
                            string2 = CompressHelper.m71736K1(strReplace);
                        }
                        String strM71754h12 = CompressHelper.m71754h1(this.f89566D4, string2, "resources");
                        if (!new File(strM71754h12).exists()) {
                            m72809Z2(this.f89579Q4.m71790J() + "/nejm/audios-E/" + string2, strM71754h12);
                        }
                        byte[] bArrM71902x2 = this.f89579Q4.m71902x(CompressHelper.m71748d2(new File(strM71754h12)), string2.toLowerCase(), "127");
                        String strM71753g12 = CompressHelper.m71753g1(this.f89566D4, "audio.mp3");
                        if (new File(strM71753g12).exists()) {
                            new File(strM71753g12).delete();
                        }
                        CompressHelper.m71728D2(new File(strM71753g12), bArrM71902x2);
                        m72416K4(strM71753g12);
                    } catch (Exception e5) {
                        FirebaseCrashlytics.m48010d().m48016g(e5);
                    }
                }
                if (strReplace.startsWith("doi/")) {
                    String strM71736K1 = CompressHelper.m71736K1(strReplace);
                    ArrayList<Bundle> arrayListM71817V2 = this.f89579Q4.m71817V(this.f89566D4, "Select * from contents where purl like '%" + strM71736K1 + "%'");
                    if (arrayListM71817V2 == null || arrayListM71817V2.size() == 0) {
                        CompressHelper.m71767x2(m15366r(), "Sorry, Document not available", 1);
                        return true;
                    }
                    this.f89579Q4.m71772A1(this.f89566D4, arrayListM71817V2.get(0).getString("pid"), null, null);
                }
            }
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new NEJMViewerFragment(), bundle);
    }
}

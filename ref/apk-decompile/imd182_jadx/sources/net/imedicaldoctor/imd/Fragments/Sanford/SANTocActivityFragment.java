package net.imedicaldoctor.imd.Fragments.Sanford;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.SearchView;
import androidx.media3.extractor.text.ttml.TtmlNode;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class SANTocActivityFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88725A4;

    /* renamed from: B4 */
    public String f88726B4;

    /* renamed from: C4 */
    public JSONArray f88727C4;

    /* renamed from: D4 */
    public JSONObject f88728D4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws JSONException, Resources.NotFoundException {
        String string;
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        final RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        if (m15387y() == null || !m15387y().containsKey("ParentId")) {
            appBarLayout.m35746D(true, false);
            relativeLayout.setVisibility(0);
            string = null;
        } else {
            if (m15387y().getString("ParentId").equals("0")) {
                appBarLayout.m35746D(true, false);
                relativeLayout.setVisibility(0);
            } else {
                appBarLayout.m35746D(false, false);
                appBarLayout.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Sanford.SANTocActivityFragment.1
                    @Override // java.lang.Runnable
                    public void run() {
                        relativeLayout.setVisibility(0);
                    }
                }, 800L);
            }
            string = m15387y().getString("ParentId");
        }
        this.f88726B4 = string;
        try {
            JSONArray jSONArray = new JSONObject(this.f88791k4.m71846f2(CompressHelper.m71753g1(this.f88788h4, "BT_config.txt"))).getJSONObject("BT_appConfig").getJSONArray("BT_items").getJSONObject(0).getJSONArray("BT_screens");
            this.f88727C4 = jSONArray;
            String str = this.f88726B4;
            this.f88728D4 = str == null ? jSONArray.getJSONObject(0) : this.f88791k4.m71886r1(jSONArray, "itemId", str);
            if (this.f88728D4.has(TtmlNode.f29697H)) {
                this.f88728D4.getString(TtmlNode.f29697H);
                this.f88728D4.getString("listHeaderTextFontColor");
            }
            this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88791k4.m71782F(this.f88728D4.getJSONArray("childItems")), "titleText", C5562R.layout.list_view_item_ripple_text_arrow) { // from class: net.imedicaldoctor.imd.Fragments.Sanford.SANTocActivityFragment.2
                @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
                /* renamed from: f0 */
                public void mo71975f0(Bundle bundle2, int i2) throws JSONException {
                    SANTocActivityFragment.this.m72437i3(bundle2, i2);
                }
            };
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
        }
        this.f88725A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", null, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.Sanford.SANTocActivityFragment.3
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                TextView textView;
                int i3;
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("text"));
                rippleTextFullViewHolder.f101500J.setText(bundle2.getString("subText"));
                if (bundle2.getString("subText").length() == 0) {
                    textView = rippleTextFullViewHolder.f101500J;
                    i3 = 8;
                } else {
                    textView = rippleTextFullViewHolder.f101500J;
                    i3 = 0;
                }
                textView.setVisibility(i3);
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Sanford.SANTocActivityFragment.3.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        SANTocActivityFragment.this.m72468V2();
                        String strM71893t1 = SANTocActivityFragment.this.f88791k4.m71893t1(StringUtils.splitByWholeSeparator(bundle2.getString("contentId"), "/"));
                        SANTocActivityFragment sANTocActivityFragment = SANTocActivityFragment.this;
                        sANTocActivityFragment.f88791k4.m71772A1(sANTocActivityFragment.f88788h4, strM71893t1 + ".html", null, null);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                SANTocActivityFragment.this.m72468V2();
                SANTocActivityFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m72465S2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88725A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88725A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71819W(this.f88788h4, "Select title as text, subject as subText,path as contentId from search_base where search_base match 'title:" + str + "* OR subject:" + str + "*'", "FTS.db");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71819W(this.f88788h4, "Select word from spell where word match '" + str + "*'", "spell.db");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: h3 */
    public String mo72066h3() {
        try {
            return this.f88728D4.getString("itemNickname");
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return this.f88788h4.getString("Title");
        }
    }

    /* renamed from: i3 */
    public void m72437i3(Bundle bundle, int i2) throws JSONException {
        Bundle bundleM71784G;
        m72468V2();
        String string = bundle.getString("loadScreenWithItemId");
        try {
            bundleM71784G = this.f88791k4.m71784G(this.f88791k4.m71886r1(this.f88727C4, "itemId", string));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            bundleM71784G = null;
        }
        if (bundleM71784G.getString("itemType").equals("BT_screen_webView")) {
            this.f88791k4.m71772A1(this.f88788h4, bundleM71784G.getString("localFileName"), null, null);
            return;
        }
        Bundle bundle2 = new Bundle();
        bundle2.putBundle("DB", this.f88788h4);
        bundle2.putString("ParentId", string);
        this.f88791k4.m71798N(SANTocActivity.class, SANTocActivityFragment.class, bundle2);
    }
}

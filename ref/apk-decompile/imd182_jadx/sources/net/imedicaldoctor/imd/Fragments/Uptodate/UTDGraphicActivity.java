package net.imedicaldoctor.imd.Fragments.Uptodate;

import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import androidx.appcompat.app.AlertDialog;
import androidx.exifinterface.media.ExifInterface;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.common.MimeTypes;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import fi.iki.elonen.NanoHTTPD;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Random;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.iMDLogger;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class UTDGraphicActivity extends ViewerHelperActivity {

    public static class UTDGraphicFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        public boolean f89467X4;

        /* renamed from: Y4 */
        public ArrayList<String> f89468Y4;

        /* renamed from: Z4 */
        public int f89469Z4;

        /* renamed from: a5 */
        private String[] f89470a5;

        /* renamed from: b5 */
        private ArrayList<String> f89471b5;

        /* renamed from: c5 */
        private ArrayList<String> f89472c5;

        /* renamed from: d5 */
        private ArrayList<String> f89473d5;

        /* renamed from: e5 */
        private ArrayList<Bundle> f89474e5;

        /* renamed from: f5 */
        private float f89475f5;

        /* renamed from: g5 */
        private boolean f89476g5;

        /* renamed from: h5 */
        private String f89477h5;

        /* renamed from: i5 */
        private String f89478i5;

        /* renamed from: j5 */
        private String f89479j5;

        /* renamed from: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity$UTDGraphicFragment$3 */
        class RunnableC51543 implements Runnable {
            RunnableC51543() {
            }

            @Override // java.lang.Runnable
            public void run() {
                AlertDialog.Builder builderMo1109s;
                DialogInterface.OnClickListener onClickListener;
                String str = UTDGraphicFragment.this.f89595p4;
                if (str == null || str.length() <= 0) {
                    UTDGraphicFragment uTDGraphicFragment = UTDGraphicFragment.this;
                    uTDGraphicFragment.f89569G4.loadDataWithBaseURL("file:///android_asset/", uTDGraphicFragment.f89563A4, NanoHTTPD.f77082p, "utf-8", null);
                    UTDGraphicFragment.this.m72836s4();
                    UTDGraphicFragment.this.m72831p4();
                    UTDGraphicFragment.this.mo72642f3(C5562R.menu.menu_utdgraphic);
                    UTDGraphicFragment.this.m15358o2(false);
                    UTDGraphicFragment.this.m72786G3();
                    UTDGraphicFragment.this.m72724j5();
                    return;
                }
                if (UTDGraphicFragment.this.f89595p4.equals(IcyHeaders.f28171a3)) {
                    builderMo1109s = new AlertDialog.Builder(UTDGraphicFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("The database is corrupt . it may happen after delta update or as a result of bad installation or a cleaner app in your device . you must delete and redownload this database. what do you want to do ?").mo1106p("Delete", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.3
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            new AlertDialog.Builder(UTDGraphicFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you sure ? this will delete uptodate database ...").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.3.2
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                    UTDGraphicFragment.this.m72798Q2(new File(UTDGraphicFragment.this.f89566D4.getString("Path")));
                                    LocalBroadcastManager.m16410b(UTDGraphicFragment.this.m15366r()).m16413d(new Intent("reload"));
                                    UTDGraphicFragment.this.f89579Q4.m71830Z1(false);
                                    UTDGraphicFragment.this.f89579Q4.m71830Z1(true);
                                }
                            }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.3.1
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                }
                            }).m1090I();
                        }
                    }).mo1109s("More Info", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            UTDGraphicFragment.this.m72811a4("http://imedicaldoctor.net/faq#null");
                            UTDGraphicFragment.this.f89579Q4.m71821W1(false);
                        }
                    });
                    onClickListener = new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                        }
                    };
                } else if (!UTDGraphicFragment.this.f89595p4.equals(ExifInterface.f16317Y4)) {
                    UTDGraphicFragment uTDGraphicFragment2 = UTDGraphicFragment.this;
                    uTDGraphicFragment2.m72780C4(uTDGraphicFragment2.f89595p4);
                    return;
                } else {
                    builderMo1109s = new AlertDialog.Builder(UTDGraphicFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Document can't be found . if this happens a lot your database is corrupted . it may happen after delta update . you must delete and redownload this database. what do you want to do ?").mo1106p("Delete", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.6
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            new AlertDialog.Builder(UTDGraphicFragment.this.m15366r(), C5562R.style.alertDialogTheme).mo1102l("Are you sure ? this will delete uptodate database ...").mo1115y("Yes", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.6.2
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                    UTDGraphicFragment.this.m72798Q2(new File(UTDGraphicFragment.this.f89566D4.getString("Path")));
                                    LocalBroadcastManager.m16410b(UTDGraphicFragment.this.m15366r()).m16413d(new Intent("reload"));
                                    UTDGraphicFragment.this.f89579Q4.m71830Z1(true);
                                    UTDGraphicFragment.this.f89579Q4.m71830Z1(false);
                                }
                            }).mo1106p("No", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.6.1
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialogInterface2, int i3) {
                                }
                            }).m1090I();
                        }
                    }).mo1109s("More Info", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.5
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            UTDGraphicFragment.this.m72811a4("http://imedicaldoctor.net/faq#null");
                            UTDGraphicFragment.this.f89579Q4.m71821W1(false);
                        }
                    });
                    onClickListener = new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.3.4
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                        }
                    };
                }
                builderMo1109s.mo1115y("OK", onClickListener).m1090I();
            }
        }

        /* renamed from: c5 */
        public static void m72712c5(String str, Context context) {
            ContentValues contentValues = new ContentValues();
            contentValues.put("datetaken", Long.valueOf(System.currentTimeMillis()));
            contentValues.put("mime_type", MimeTypes.f19865Q0);
            contentValues.put("_data", str);
            context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
        }

        /* renamed from: d5 */
        public static void m72713d5(String str, Context context) {
            ContentValues contentValues = new ContentValues();
            contentValues.put("datetaken", Long.valueOf(System.currentTimeMillis()));
            contentValues.put("mime_type", MimeTypes.f19892f);
            contentValues.put("_data", str);
            context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
        }

        /* renamed from: g5 */
        private void m72714g5(String str) {
            Bundle bundle = new Bundle();
            bundle.putString("ImagePath", str);
            bundle.putString("isVideo", IcyHeaders.f28171a3);
            ArrayList arrayList = new ArrayList();
            arrayList.add(bundle);
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList);
            intent.putExtra("Start", 0);
            mo15256D2(intent);
        }

        /* renamed from: i5 */
        private void m72715i5(String str) {
            ArrayList<Bundle> arrayList = this.f89474e5;
            if (arrayList == null || arrayList.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no images in this document", 1);
                return;
            }
            int i2 = 0;
            for (int i3 = 0; i3 < this.f89474e5.size(); i3++) {
                if (this.f89474e5.get(i3).getString("name").equals(str)) {
                    i2 = i3;
                }
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", this.f89474e5);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        /* renamed from: I4 */
        public void m72716I4() {
            this.f89598s4.findItem(C5562R.id.action_previous).setEnabled(false);
            this.f89598s4.findItem(C5562R.id.action_previous).setIcon(C5562R.drawable.ic_action_previous_item_disabled);
        }

        /* renamed from: J4 */
        public void m72717J4() {
            this.f89598s4.findItem(C5562R.id.action_next).setEnabled(false);
            this.f89598s4.findItem(C5562R.id.action_next).setIcon(C5562R.drawable.ic_action_next_item_disabled);
        }

        /* renamed from: K4 */
        public void m72718K4() {
            this.f89598s4.findItem(C5562R.id.action_previous).setEnabled(true);
            this.f89598s4.findItem(C5562R.id.action_previous).setIcon(C5562R.drawable.ic_action_previous_item);
        }

        /* renamed from: L4 */
        public void m72719L4() {
            this.f89598s4.findItem(C5562R.id.action_next).setEnabled(true);
            this.f89598s4.findItem(C5562R.id.action_next).setIcon(C5562R.drawable.ic_action_next_item);
        }

        /* renamed from: M4 */
        public void m72720M4() {
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.2
                /* JADX WARN: Removed duplicated region for block: B:100:0x02f5  */
                /* JADX WARN: Removed duplicated region for block: B:155:0x042b  */
                /* JADX WARN: Removed duplicated region for block: B:156:0x042e A[Catch: Exception -> 0x0454, TRY_LEAVE, TryCatch #18 {Exception -> 0x0454, blocks: (B:153:0x0425, B:156:0x042e), top: B:231:0x0425 }] */
                /* JADX WARN: Removed duplicated region for block: B:210:0x0230 A[EXC_TOP_SPLITTER, SYNTHETIC] */
                /* JADX WARN: Removed duplicated region for block: B:227:0x01d9 A[EXC_TOP_SPLITTER, SYNTHETIC] */
                /* JADX WARN: Removed duplicated region for block: B:250:0x016e A[SYNTHETIC] */
                /* JADX WARN: Removed duplicated region for block: B:69:0x0210  */
                @Override // java.lang.Runnable
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public void run() throws org.json.JSONException, java.io.IOException, java.lang.NumberFormatException {
                    /*
                        Method dump skipped, instructions count: 1433
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.RunnableC51532.run():void");
                }
            }, new RunnableC51543());
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            try {
                ArrayList<String> arrayList = this.f89472c5;
                if (arrayList != null && arrayList.size() != 0) {
                    int iNextInt = new Random().nextInt(this.f89472c5.size());
                    if (this.f89472c5.size() - 1 < 0) {
                        return null;
                    }
                    String str = this.f89472c5.get(iNextInt);
                    String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, "background.png");
                    File file = new File(strM71753g1);
                    if (file.exists()) {
                        file.delete();
                        Log.e("Deleted", "deleted");
                    }
                    BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
                    try {
                        bufferedSinkM75768d.write(Base64.decode(str, 0));
                        bufferedSinkM75768d.flush();
                        bufferedSinkM75768d.close();
                        return strM71753g1;
                    } finally {
                    }
                }
                return null;
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                return null;
            }
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_utdgraphic, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            this.f89474e5 = new ArrayList<>();
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            this.f89565C4 = viewInflate;
            m72835r4(viewInflate, bundle);
            this.f89582T4 = new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.Uptodate.UTDGraphicActivity.UTDGraphicFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    CompressHelper.m71767x2(UTDGraphicFragment.this.m15366r(), "Download Completed", 1);
                    try {
                        File file = new File(UTDGraphicFragment.this.f89479j5);
                        if (file.exists()) {
                            BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file));
                            try {
                                byte[] bArrMo75462b0 = bufferedSourceM75769e.mo75462b0();
                                bufferedSourceM75769e.close();
                                String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(UTDGraphicFragment.this.f89478i5, "/");
                                byte[] bArrM71902x = UTDGraphicFragment.this.f89579Q4.m71902x(bArrMo75462b0, strArrSplitByWholeSeparator[strArrSplitByWholeSeparator.length - 1], "127");
                                BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(UTDGraphicFragment.this.f89478i5)));
                                try {
                                    bufferedSinkM75768d.write(bArrM71902x);
                                    bufferedSinkM75768d.close();
                                    new File(UTDGraphicFragment.this.f89478i5).deleteOnExit();
                                    UTDGraphicFragment.this.f89476g5 = true;
                                    UTDGraphicFragment uTDGraphicFragment = UTDGraphicFragment.this;
                                    uTDGraphicFragment.f89477h5 = uTDGraphicFragment.f89478i5;
                                } finally {
                                }
                            } finally {
                            }
                        }
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                    UTDGraphicFragment.this.m72784F3();
                }
            };
            if (m15387y() == null) {
                return this.f89565C4;
            }
            if (bundle != null) {
                this.f89472c5 = bundle.getStringArrayList("mBase64Images");
                this.f89473d5 = bundle.getStringArrayList("mBase64ImageNames");
                this.f89471b5 = bundle.getStringArrayList("mRelatedTopics");
                this.f89476g5 = bundle.getBoolean("mIsVideo");
                this.f89477h5 = bundle.getString("mVideoPath");
                this.f89478i5 = bundle.getString("mVideoSavePath");
            }
            String[] stringArray = m15387y().getStringArray("IDS");
            this.f89470a5 = stringArray;
            this.f89567E4 = "Graphic-" + TextUtils.join(";", stringArray);
            if (m15387y().containsKey("AllGraphics")) {
                this.f89468Y4 = m15387y().getStringArrayList("AllGraphics");
            }
            if (m15387y().containsKey("GraphicIndex")) {
                this.f89469Z4 = Integer.valueOf(m15387y().getString("GraphicIndex")).intValue();
            }
            m72814c3();
            m72720M4();
            return this.f89565C4;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_save_gallery) {
                if (this.f89476g5) {
                    new File(this.f89477h5);
                    try {
                        BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(new File(this.f89477h5)));
                        try {
                            BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(this.f89478i5)));
                            try {
                                bufferedSinkM75768d.mo75508y1(bufferedSourceM75769e);
                                bufferedSinkM75768d.close();
                                if (bufferedSourceM75769e != null) {
                                    bufferedSourceM75769e.close();
                                }
                            } finally {
                            }
                        } finally {
                        }
                    } catch (IOException e2) {
                        iMDLogger.m73550f(getClass().toString(), "Error in copying " + this.f89477h5 + " to " + this.f89478i5);
                        e2.printStackTrace();
                    }
                    MediaScannerConnection.scanFile(m15366r(), new String[]{this.f89478i5}, null, null);
                    m72713d5(this.f89478i5, m15366r());
                } else {
                    for (int i2 = 0; i2 < this.f89472c5.size(); i2++) {
                        String str = this.f89472c5.get(i2);
                        String[] strArrSplit = this.f89473d5.get(i2).split("/");
                        String strM71753g1 = CompressHelper.m71753g1(this.f89566D4, strArrSplit[strArrSplit.length - 1]);
                        try {
                            BufferedSink bufferedSinkM75768d2 = Okio.m75768d(Okio.m75778n(new File(strM71753g1)));
                            try {
                                bufferedSinkM75768d2.write(Base64.decode(str, 0));
                                bufferedSinkM75768d2.flush();
                                bufferedSinkM75768d2.close();
                            } catch (Throwable th) {
                                if (bufferedSinkM75768d2 != null) {
                                    try {
                                        bufferedSinkM75768d2.close();
                                    } catch (Throwable th2) {
                                        th.addSuppressed(th2);
                                    }
                                }
                                throw th;
                            }
                        } catch (IOException e3) {
                            iMDLogger.m73550f(getClass().toString(), "Error in writing to " + strM71753g1);
                            e3.printStackTrace();
                        }
                        MediaScannerConnection.scanFile(m15366r(), new String[]{strM71753g1}, null, null);
                        m72712c5(strM71753g1, m15366r());
                    }
                }
            }
            if (itemId != C5562R.id.action_related_topics) {
                if (itemId == C5562R.id.action_previous) {
                    m72723h5();
                }
                if (itemId == C5562R.id.action_next) {
                    m72722f5();
                }
                return super.mo15329e1(menuItem);
            }
            String strJoin = TextUtils.join(",", this.f89471b5);
            UTDRelatedTopics2Fragment uTDRelatedTopics2Fragment = new UTDRelatedTopics2Fragment();
            Bundle bundle = new Bundle();
            bundle.putString("RELATED", strJoin);
            bundle.putBundle("db", this.f89566D4);
            uTDRelatedTopics2Fragment.m15342i2(bundle);
            uTDRelatedTopics2Fragment.m15245A2(this, 0);
            uTDRelatedTopics2Fragment.mo15222e3(m15283M(), "related");
            return true;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            MenuItem menuItemFindItem = menu.findItem(C5562R.id.action_related_topics);
            if (this.f89471b5.size() == 0) {
                menuItemFindItem.setVisible(false);
            }
        }

        /* renamed from: e5 */
        public void m72721e5(String str) {
            new CompressHelper(m15366r()).m71772A1(this.f89566D4, "Topic-" + str, null, null);
        }

        /* renamed from: f5 */
        public void m72722f5() {
            int i2 = this.f89469Z4 + 1;
            if (i2 <= this.f89468Y4.size() - 1) {
                this.f89469Z4 = i2;
                String[] strArr = {this.f89468Y4.get(i2)};
                this.f89470a5 = strArr;
                this.f89567E4 = "Graphic-" + TextUtils.join(";", strArr);
                this.f89467X4 = true;
                m72720M4();
            }
            m72724j5();
        }

        /* renamed from: h5 */
        public void m72723h5() {
            int i2 = this.f89469Z4 - 1;
            if (i2 >= 0) {
                this.f89469Z4 = i2;
                String[] strArr = {this.f89468Y4.get(i2)};
                this.f89470a5 = strArr;
                this.f89567E4 = "Graphic-" + TextUtils.join(";", strArr);
                this.f89467X4 = true;
                m72720M4();
            }
            m72724j5();
        }

        /* renamed from: j5 */
        public void m72724j5() {
            if (this.f89468Y4 == null) {
                m72716I4();
                m72717J4();
                return;
            }
            m72718K4();
            m72719L4();
            if (this.f89469Z4 <= 0) {
                m72716I4();
            }
            if (this.f89469Z4 >= this.f89468Y4.size() - 1) {
                m72717J4();
            }
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
            iMDLogger.m73554j("URL Requested", str);
            Uri.parse(str);
            if (str2.equals("image")) {
                m72715i5(str3.substring(2));
                return true;
            }
            if (str2.equals("video")) {
                if (this.f89477h5.equals("")) {
                    String[] strArrSplit = StringUtils.split(this.f89478i5, "/");
                    String str4 = strArrSplit[strArrSplit.length - 1];
                    m72809Z2(this.f89579Q4.m71790J() + "/videos-E/" + str4, this.f89566D4.getString("Path") + "/videos-E/" + str4);
                } else {
                    iMDLogger.m73550f("Video Path ", this.f89477h5);
                    m72714g5(this.f89477h5);
                }
            }
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new UTDGraphicFragment(), bundle);
    }
}

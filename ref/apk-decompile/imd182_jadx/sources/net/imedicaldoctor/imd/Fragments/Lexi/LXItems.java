package net.imedicaldoctor.imd.Fragments.Lexi;

import android.os.Bundle;
import android.view.Menu;
import android.view.MenuInflater;
import androidx.appcompat.widget.SearchView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class LXItems extends iMDActivity {

    public static class LXItemsFragment extends SearchHelperFragment {

        /* renamed from: A4 */
        private int f88337A4;

        /* renamed from: B4 */
        private String f88338B4;

        /* renamed from: C4 */
        private String f88339C4;

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.search, menu);
            this.f88799s4 = (SearchView) menu.findItem(C5562R.id.action_search).getActionView();
            m72462O2();
        }

        /* JADX WARN: Removed duplicated region for block: B:27:0x0101  */
        /* JADX WARN: Removed duplicated region for block: B:28:0x0108  */
        /* JADX WARN: Removed duplicated region for block: B:35:0x0122  */
        /* JADX WARN: Removed duplicated region for block: B:38:0x0134  */
        /* JADX WARN: Removed duplicated region for block: B:40:0x0139  */
        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: U0 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public android.view.View mo15303U0(android.view.LayoutInflater r12, android.view.ViewGroup r13, android.os.Bundle r14) throws android.content.res.Resources.NotFoundException {
            /*
                Method dump skipped, instructions count: 352
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.Lexi.LXItems.LXItemsFragment.mo15303U0(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: a3 */
        public ArrayList<Bundle> mo71950a3(String str) {
            StringBuilder sb;
            String str2;
            int i2 = this.f88337A4;
            if (i2 == 0) {
                sb = new StringBuilder();
                sb.append("Select rowid as _id,* from search where name match '");
                sb.append(str);
                str2 = "*'";
            } else {
                if (i2 != 1) {
                    if (i2 != 2) {
                        return null;
                    }
                    return this.f88791k4.m71817V(this.f88788h4, "select id as _id,* from indexitem_document inner join document on (indexitem_document.document_id=document.id) where indexitem_id=" + this.f88338B4 + " AND title like '" + str + "%'");
                }
                sb = new StringBuilder();
                sb.append("Select rowid as _id,* from search where search match 'name:");
                sb.append(str);
                sb.append("* AND type:");
                sb.append(this.f88339C4);
                str2 = "'";
            }
            sb.append(str2);
            return this.f88791k4.m71819W(this.f88788h4, sb.toString(), "fsearch.db");
        }

        @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
        /* renamed from: g3 */
        public ArrayList<Bundle> mo71951g3(String str) {
            return this.f88791k4.m71819W(this.f88788h4, "Select rowid as _id,word from spell where word match '" + str + "*'", "fsearch.db");
        }
    }

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        m73544a1(bundle, new LXItemsFragment());
    }
}

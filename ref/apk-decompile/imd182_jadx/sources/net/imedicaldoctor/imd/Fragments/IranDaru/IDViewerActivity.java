package net.imedicaldoctor.imd.Fragments.IranDaru;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASSectionViewer;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes3.dex */
public class IDViewerActivity extends ViewerHelperActivity {

    public static class IDViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private String f88301X4;

        /* renamed from: Y4 */
        private String f88302Y4;

        /* renamed from: Z4 */
        private ArrayList<Bundle> f88303Z4;

        /* renamed from: M4 */
        private String m72290M4(String str, String str2, String str3, String str4, String str5) {
            return "<div class=\"content\" DIR=\"" + str4 + "\" id=\"f" + str5 + "\" style=\"font-family:" + str2 + "; " + str3 + "\">" + str + "</div>";
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: N4 */
        public String m72291N4(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8) {
            return "<a name=\"f" + str8 + "\"><div id=\"h" + str8 + "\" class=\"headerExpanded\"  DIR=\"" + str3 + "\" text-align=\"center\" onclick=\"collapse(f" + str8 + ");toggleHeaderExpanded(h" + str8 + ");\"><span class=\"fieldname\" style=\"font-family:" + str2 + ";\">" + str + "</span></div></a><div class=\"content\" DIR=\"" + str7 + "\" id=\"f" + str8 + "\" style=\"font-family:" + str5 + "; " + str6 + "\">" + str4 + "</div>";
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* renamed from: O4 */
        public void m72292O4(XmlPullParser xmlPullParser) throws XmlPullParserException, IOException {
            int eventType = xmlPullParser.getEventType();
            while (eventType != 1) {
                if (eventType == 0) {
                    this.f88303Z4 = new ArrayList<>();
                } else if (eventType == 2) {
                    Bundle bundleM72293P4 = m72293P4(xmlPullParser);
                    this.f88301X4 = bundleM72293P4.containsKey("TitlePe") ? bundleM72293P4.getString("TitlePe") : null;
                    this.f88302Y4 = "";
                } else if (eventType != 3) {
                    if (eventType == 4) {
                        this.f88302Y4 += xmlPullParser.getText();
                    }
                } else if (this.f88301X4 != null && this.f88302Y4.length() > 0) {
                    Bundle bundle = new Bundle();
                    bundle.putString("title", this.f88301X4);
                    bundle.putString("value", this.f88302Y4);
                    bundle.putString("element", xmlPullParser.getName());
                    this.f88303Z4.add(bundle);
                }
                eventType = xmlPullParser.next();
            }
        }

        /* renamed from: P4 */
        private Bundle m72293P4(XmlPullParser xmlPullParser) {
            Bundle bundle = new Bundle();
            for (int i2 = 0; i2 < xmlPullParser.getAttributeCount(); i2++) {
                bundle.putString(xmlPullParser.getAttributeName(i2), xmlPullParser.getAttributeValue(i2));
            }
            return bundle;
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.elsviewer, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f88303Z4 = bundle.getParcelableArrayList("mFields");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.IranDaru.IDViewerActivity.IDViewerFragment.1
                /* JADX WARN: Removed duplicated region for block: B:134:0x06ad  */
                @Override // java.lang.Runnable
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public void run() throws java.io.IOException {
                    /*
                        Method dump skipped, instructions count: 1839
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.IranDaru.IDViewerActivity.IDViewerFragment.RunnableC48691.run():void");
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.IranDaru.IDViewerActivity.IDViewerFragment.2
                @Override // java.lang.Runnable
                public void run() {
                    String str = IDViewerFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        IDViewerFragment iDViewerFragment = IDViewerFragment.this;
                        iDViewerFragment.m72780C4(iDViewerFragment.f89595p4);
                        return;
                    }
                    String strM71753g1 = CompressHelper.m71753g1(IDViewerFragment.this.f89566D4, "base");
                    IDViewerFragment iDViewerFragment2 = IDViewerFragment.this;
                    iDViewerFragment2.m72795O3(iDViewerFragment2.f89563A4, strM71753g1);
                    IDViewerFragment.this.m72836s4();
                    IDViewerFragment.this.m72831p4();
                    IDViewerFragment.this.mo72642f3(C5562R.menu.elsviewer);
                    IDViewerFragment.this.m15358o2(false);
                    IDViewerFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            menuItem.getItemId();
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            menu.removeItem(C5562R.id.action_menu);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        public boolean mo71960y4(WebView webView, String str, String str2, String str3) {
            CompressHelper compressHelper;
            Bundle bundle;
            String str4;
            iMDLogger.m73554j("Override", "Url : " + str + ", Scheme : " + str2 + ", Resource : " + str3);
            if (str2.equals(Annotation.f68285k3) || (str2.equals("http") & str3.contains("localhost:"))) {
                try {
                    if (!str3.contains("@\"")) {
                        return true;
                    }
                    new CompressHelper(m15366r());
                    String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(URLDecoder.decode(CompressHelper.m71751f(str3, "@\"", "\""), "UTF-8"), "://");
                    String str5 = "";
                    if (strArrSplitByWholeSeparator[0].equals("medCategory")) {
                        str5 = "medical";
                    } else if (strArrSplitByWholeSeparator[0].equals("pharmCategory")) {
                        str5 = "pharm";
                    }
                    String str6 = strArrSplitByWholeSeparator[1];
                    URLDecoder.decode(strArrSplitByWholeSeparator[2]);
                    new ArrayList();
                    if (str5.equals("medical")) {
                        compressHelper = this.f89579Q4;
                        bundle = this.f89566D4;
                        str4 = "Select  tDrugGenerics.fDrugGenericId as _id,tDrugGenerics.fDrugGenericId, fDrugGenericName from tMedicalGroupGenerics,tDrugGenerics where tMedicalGroupGenerics.fMedicalGroupId=" + str6 + " AND tDrugGenerics.fDrugGenericId=tMedicalGroupGenerics.fDrugGenericId";
                    } else {
                        compressHelper = this.f89579Q4;
                        bundle = this.f89566D4;
                        str4 = "Select tDrugGenerics.fDrugGenericId as _id,tDrugGenerics.fDrugGenericId, fDrugGenericName from tPharmGroupGenerics,tDrugGenerics where tPharmGroupGenerics.fPharmGroupId=" + str6 + " AND tDrugGenerics.fDrugGenericId=tPharmGroupGenerics.fDrugGenericId";
                    }
                    ArrayList<Bundle> arrayListM71817V = compressHelper.m71817V(bundle, str4);
                    ASSectionViewer aSSectionViewer = new ASSectionViewer();
                    Bundle bundle2 = new Bundle();
                    bundle2.putParcelableArrayList("Items", arrayListM71817V);
                    bundle2.putString("TitleProperty", "fDrugGenericName");
                    aSSectionViewer.m15245A2(this, 0);
                    aSSectionViewer.m15342i2(bundle2);
                    aSSectionViewer.mo15218Z2(true);
                    aSSectionViewer.mo15222e3(m15283M(), "asdfasdfasdf");
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("IDViewer ShouldOverride", "Error " + e2);
                }
            }
            return true;
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new IDViewerFragment(), bundle);
    }
}

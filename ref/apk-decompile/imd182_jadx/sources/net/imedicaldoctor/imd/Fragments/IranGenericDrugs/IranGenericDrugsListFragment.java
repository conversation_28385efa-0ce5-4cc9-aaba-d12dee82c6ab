package net.imedicaldoctor.imd.Fragments.IranGenericDrugs;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.AppBarLayout;
import com.itextpdf.text.Annotation;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Fragments.SearchHelperFragment;
import net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;
import net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class IranGenericDrugsListFragment extends SearchHelperFragment {

    /* renamed from: A4 */
    public SpellSearchAdapter f88306A4;

    /* renamed from: B4 */
    public String f88307B4;

    /* renamed from: C4 */
    public ArrayList<Bundle> f88308C4;

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment, androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f88797q4 = layoutInflater.inflate(C5562R.layout.fragment_new_list, viewGroup, false);
        m72469W2(bundle);
        m72465S2();
        this.f88799s4 = (SearchView) this.f88797q4.findViewById(C5562R.id.search_view);
        mo71990Q2();
        this.f88803w4 = (RecyclerView) this.f88797q4.findViewById(C5562R.id.recycler_view);
        this.f88308C4 = new ArrayList<>();
        AppBarLayout appBarLayout = (AppBarLayout) this.f88797q4.findViewById(C5562R.id.appbar);
        RelativeLayout relativeLayout = (RelativeLayout) this.f88797q4.findViewById(C5562R.id.background_layout);
        appBarLayout.m35746D(true, false);
        relativeLayout.setVisibility(0);
        this.f88792l4 = new ChaptersAdapter(m15366r(), this.f88794n4, "name", C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.IranGenericDrugs.IranGenericDrugsListFragment.1
            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: e0 */
            public void mo71985e0(RecyclerView.ViewHolder viewHolder, Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("shortForm") + StringUtils.SPACE + bundle2.getString("name") + StringUtils.SPACE + bundle2.getString("dose"));
                rippleTextFullViewHolder.f101500J.setVisibility(8);
                if (bundle2.getString("genName").length() > 0) {
                    rippleTextFullViewHolder.f101500J.setText(bundle2.getString("genForm") + StringUtils.SPACE + bundle2.getString("genName") + StringUtils.SPACE + bundle2.getString("genDose"));
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                }
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter
            /* renamed from: h0 */
            public RecyclerView.ViewHolder mo71986h0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                rippleTextFullViewHolder.f101502L.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88306A4 = new SpellSearchAdapter(m15366r(), this.f88795o4, "text", Annotation.f68283i3, C5562R.layout.list_view_item_ripple_text_full) { // from class: net.imedicaldoctor.imd.Fragments.IranGenericDrugs.IranGenericDrugsListFragment.2
            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: e0 */
            public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle2, int i2) {
                RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                rippleTextFullViewHolder.f101499I.setText(bundle2.getString("shortForm") + StringUtils.SPACE + bundle2.getString("name") + StringUtils.SPACE + bundle2.getString("dose"));
                rippleTextFullViewHolder.f101500J.setVisibility(8);
                if (bundle2.getString("genName").length() > 0) {
                    rippleTextFullViewHolder.f101500J.setText(bundle2.getString("genForm") + StringUtils.SPACE + bundle2.getString("genName") + StringUtils.SPACE + bundle2.getString("genDose"));
                    rippleTextFullViewHolder.f101500J.setVisibility(0);
                }
                rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Fragments.IranGenericDrugs.IranGenericDrugsListFragment.2.1
                    @Override // android.view.View.OnClickListener
                    public void onClick(View view) {
                        IranGenericDrugsListFragment.this.m72468V2();
                        IranGenericDrugsListFragment.this.f88308C4.add(bundle2);
                        IranGenericDrugsListFragment.this.f88799s4.m2508k0("", false);
                        IranGenericDrugsListFragment iranGenericDrugsListFragment = IranGenericDrugsListFragment.this;
                        ((ChaptersAdapter) iranGenericDrugsListFragment.f88792l4).m73465g0(iranGenericDrugsListFragment.f88308C4);
                        IranGenericDrugsListFragment.this.f88792l4.m27491G();
                        IranGenericDrugsListFragment iranGenericDrugsListFragment2 = IranGenericDrugsListFragment.this;
                        iranGenericDrugsListFragment2.f88803w4.setAdapter(iranGenericDrugsListFragment2.f88792l4);
                    }
                });
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: h0 */
            public void mo71977h0(Bundle bundle2) {
                IranGenericDrugsListFragment.this.m72468V2();
                IranGenericDrugsListFragment.this.f88799s4.m2508k0(bundle2.getString("word"), true);
            }

            @Override // net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter
            /* renamed from: j0 */
            public RecyclerView.ViewHolder mo72196j0(View view) {
                RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(view);
                rippleTextFullViewHolder.f101501K.setVisibility(8);
                return rippleTextFullViewHolder;
            }
        };
        this.f88803w4.setAdapter(this.f88792l4);
        m72461N2();
        m15358o2(false);
        return this.f88797q4;
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: X2 */
    public void mo71973X2() {
        this.f88306A4.m73478i0(this.f88795o4, this.f88796p4);
        this.f88803w4.setAdapter(this.f88306A4);
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: a3 */
    public ArrayList<Bundle> mo71950a3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select * from search where search match 'name:" + str + "*'");
    }

    @Override // net.imedicaldoctor.imd.Fragments.SearchHelperFragment
    /* renamed from: g3 */
    public ArrayList<Bundle> mo71951g3(String str) {
        return this.f88791k4.m71817V(this.f88788h4, "Select word from spell where word match '" + str + "*'");
    }
}

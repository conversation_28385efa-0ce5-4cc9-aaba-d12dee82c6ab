package net.imedicaldoctor.imd.Fragments.AccessMedicine;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.WebView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.IOException;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.ViewerHelperActivity;
import net.imedicaldoctor.imd.Fragments.ViewerHelperFragment;
import net.imedicaldoctor.imd.Gallery.GalleryActivity;
import net.imedicaldoctor.imd.GeneralDialogFragment;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class AMViewerActivity extends ViewerHelperActivity {

    public static class AMViewerFragment extends ViewerHelperFragment {

        /* renamed from: X4 */
        private GeneralDialogFragment f87501X4;

        /* renamed from: Y4 */
        private String f87502Y4;

        /* renamed from: Z4 */
        private MenuItem f87503Z4;

        /* renamed from: a5 */
        public ArrayList<Bundle> f87504a5;

        /* renamed from: b5 */
        public ArrayList<Bundle> f87505b5;

        /* renamed from: c5 */
        public ArrayList<Bundle> f87506c5;

        /* renamed from: d5 */
        public ArrayList<Bundle> f87507d5;

        /* JADX INFO: Access modifiers changed from: private */
        /* JADX WARN: Removed duplicated region for block: B:53:0x0237 A[PHI: r10
          0x0237: PHI (r10v21 java.lang.String) = (r10v20 java.lang.String), (r10v24 java.lang.String) binds: [B:49:0x021b, B:51:0x0234] A[DONT_GENERATE, DONT_INLINE]] */
        /* renamed from: I4 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public void m71962I4() {
            /*
                Method dump skipped, instructions count: 679
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.AccessMedicine.AMViewerActivity.AMViewerFragment.m71962I4():void");
        }

        /* renamed from: N4 */
        private void m71966N4(String str) {
            if (this.f87504a5.size() + this.f87505b5.size() == 0) {
                CompressHelper.m71767x2(m15366r(), "There is no media in this document", 1);
                return;
            }
            ArrayList arrayList = new ArrayList();
            arrayList.addAll(this.f87504a5);
            arrayList.addAll(this.f87505b5);
            Boolean bool = Boolean.FALSE;
            int i2 = 0;
            for (int i3 = 0; i3 < arrayList.size(); i3++) {
                if (((Bundle) arrayList.get(i3)).getString("id").startsWith(str)) {
                    bool = Boolean.TRUE;
                    i2 = i3;
                }
            }
            if (!bool.booleanValue()) {
                for (int i4 = 0; i4 < arrayList.size(); i4++) {
                    if (((Bundle) arrayList.get(i4)).getString("id").replace("m_", "").startsWith(str.replace("m_", ""))) {
                        i2 = i4;
                    }
                }
            }
            Intent intent = new Intent(m15366r(), (Class<?>) GalleryActivity.class);
            intent.putExtra("Images", arrayList);
            intent.putExtra("Start", i2);
            mo15256D2(intent);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: C3 */
        public void mo71967C3(String str) {
            this.f89569G4.m73433g("showSection('" + str + "');");
        }

        /* renamed from: M4 */
        public void m71968M4() {
            this.f89569G4.m73433g("$(\".contentJump\").each(function(){justShowSection($(this).find(\"a[name]\").first().attr(\"name\"))});");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: R2 */
        public String mo71955R2() {
            ArrayList<Bundle> arrayList;
            Bundle bundleM72839v3;
            if (this.f87504a5.size() <= 0 || (arrayList = this.f87504a5) == null || arrayList.size() <= 0 || (bundleM72839v3 = m72839v3(this.f87504a5)) == null) {
                return null;
            }
            return bundleM72839v3.getString("ImagePath");
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: T0 */
        public void mo15301T0(Menu menu, MenuInflater menuInflater) {
            menuInflater.inflate(C5562R.menu.menu_amviewer, menu);
            m72833q4(menu);
            mo71957e3(menu);
        }

        @Override // androidx.fragment.app.Fragment
        /* renamed from: U0 */
        public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
            View view = this.f89565C4;
            if (view != null) {
                return view;
            }
            View viewInflate = layoutInflater.inflate(C5562R.layout.fragment_new_viewer, viewGroup, false);
            m72835r4(viewInflate, bundle);
            if (bundle != null) {
                this.f87502Y4 = bundle.getString("mResources");
                this.f87504a5 = bundle.getParcelableArrayList("mImages");
                this.f87505b5 = bundle.getParcelableArrayList("mVideos");
                this.f87506c5 = bundle.getParcelableArrayList("mOtherImages");
                this.f87507d5 = bundle.getParcelableArrayList("mSections");
            }
            if (m15387y() == null) {
                return viewInflate;
            }
            this.f89595p4 = null;
            iMDLogger.m73554j("AMViewer", "Loading AM Document with mDocAddress = " + this.f89567E4);
            m72803T2(new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.AccessMedicine.AMViewerActivity.AMViewerFragment.1
                @Override // java.lang.Runnable
                public void run() {
                    Bundle bundle2;
                    String str;
                    try {
                        CompressHelper compressHelper = new CompressHelper(AMViewerFragment.this.m15366r());
                        String str2 = AMViewerFragment.this.f89563A4;
                        if (str2 != null && str2.length() != 0) {
                            return;
                        }
                        Bundle bundleM71844e0 = compressHelper.m71844e0(AMViewerFragment.this.f89566D4, "Select * from Docs where id=" + AMViewerFragment.this.f89567E4);
                        if (bundleM71844e0 == null) {
                            bundleM71844e0 = compressHelper.m71844e0(AMViewerFragment.this.f89566D4, "Select * from Docs where aid=" + AMViewerFragment.this.f89567E4);
                            if (bundleM71844e0 == null) {
                                Bundle bundleM71844e02 = compressHelper.m71844e0(AMViewerFragment.this.f89566D4, "Select * from Sections Where id = " + AMViewerFragment.this.f89567E4);
                                if (bundleM71844e02 == null) {
                                    Bundle bundleM71844e03 = compressHelper.m71844e0(AMViewerFragment.this.f89566D4, "Select * from AllSections Where section like '" + AMViewerFragment.this.f89567E4 + "%'");
                                    if (bundleM71844e03 == null) {
                                        AMViewerFragment.this.f89595p4 = "Document doesn't exist";
                                        return;
                                    }
                                    AMViewerFragment.this.f89567E4 = bundleM71844e03.getString("sectionId");
                                    bundle2 = AMViewerFragment.this.f89566D4;
                                    str = "Select * from Docs where id=" + AMViewerFragment.this.f89567E4;
                                } else {
                                    AMViewerFragment.this.f89567E4 = bundleM71844e02.getString("sectionId");
                                    bundle2 = AMViewerFragment.this.f89566D4;
                                    str = "Select * from Docs where id=" + AMViewerFragment.this.f89567E4;
                                }
                                bundleM71844e0 = compressHelper.m71844e0(bundle2, str);
                            } else {
                                AMViewerFragment.this.f89567E4 = bundleM71844e0.getString("id");
                            }
                        }
                        String string = bundleM71844e0.getString("mainContent");
                        AMViewerFragment.this.f87502Y4 = bundleM71844e0.getString("resources");
                        AMViewerFragment.this.f89568F4 = bundleM71844e0.getString("name");
                        String strReplace = new String(compressHelper.m71897v(string, bundleM71844e0.getString("id"), "127")).replace(">Print Section<", "><").replace(">Favorite Table<", "><").replace(">Download (.pdf)<", "><").replace(">|<", "><").replace(">Favorite Figure<", "><").replace(">Download Slide (.ppt)<", "><");
                        AMViewerFragment aMViewerFragment = AMViewerFragment.this;
                        String strM72817d4 = aMViewerFragment.m72817d4(aMViewerFragment.m15366r(), "AMHeader.css");
                        AMViewerFragment aMViewerFragment2 = AMViewerFragment.this;
                        String strM72817d42 = aMViewerFragment2.m72817d4(aMViewerFragment2.m15366r(), "AMFooter.css");
                        String str3 = strM72817d4.replace("[size]", "200").replace("[title]", AMViewerFragment.this.f89568F4) + strReplace.replace("<img data-original=\"s_audio_intext.jpeg\" src=\"spinnerLarge.gif\" alt=\"Image not available.\" class=\"contentFigures\" />", "<img src=\"s_audio_intext.jpeg\">").replace(">Download Section PDF</", "></") + strM72817d42;
                        AMViewerFragment.this.m72824l3("mgh.Popup.Dialog.js");
                        AMViewerFragment.this.m72826m3();
                        AMViewerFragment aMViewerFragment3 = AMViewerFragment.this;
                        aMViewerFragment3.f89563A4 = str3;
                        aMViewerFragment3.m71962I4();
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        AMViewerFragment.this.f89595p4 = e2.getLocalizedMessage();
                    }
                }
            }, new Runnable() { // from class: net.imedicaldoctor.imd.Fragments.AccessMedicine.AMViewerActivity.AMViewerFragment.2
                @Override // java.lang.Runnable
                public void run() throws IOException {
                    String str = AMViewerFragment.this.f89595p4;
                    if (str != null && str.length() > 0) {
                        AMViewerFragment aMViewerFragment = AMViewerFragment.this;
                        aMViewerFragment.m72780C4(aMViewerFragment.f89595p4);
                        return;
                    }
                    if (AMViewerFragment.this.f87503Z4 != null) {
                        AMViewerFragment.this.f87503Z4.setVisible(AMViewerFragment.this.f87504a5.size() + AMViewerFragment.this.f87505b5.size() != 0);
                    }
                    if (!AMViewerFragment.this.f89579Q4.m71903x1()) {
                        AMViewerFragment.this.m72827m4("Chapter");
                    }
                    String strM71753g1 = CompressHelper.m71753g1(AMViewerFragment.this.f89566D4, "base");
                    AMViewerFragment aMViewerFragment2 = AMViewerFragment.this;
                    aMViewerFragment2.f89563A4 = aMViewerFragment2.f89563A4.replace("href=\"#", "href=\"svbfile://somewhere#");
                    AMViewerFragment.this.m72820h3(strM71753g1);
                    AMViewerFragment aMViewerFragment3 = AMViewerFragment.this;
                    aMViewerFragment3.m72795O3(aMViewerFragment3.f89563A4, strM71753g1);
                    AMViewerFragment.this.m72836s4();
                    AMViewerFragment.this.m72831p4();
                    AMViewerFragment.this.mo72642f3(C5562R.menu.menu_amviewer);
                    AMViewerFragment.this.m15358o2(false);
                    AMViewerFragment.this.m72786G3();
                }
            });
            return viewInflate;
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: W3 */
        public boolean mo71969W3(ConsoleMessage consoleMessage) {
            String string;
            iMDLogger.m73550f("Javascript Console", consoleMessage.message());
            String[] strArrSplit = consoleMessage.message().split(",,,,,");
            if (strArrSplit[0].equals("tableAction")) {
                String strReplace = strArrSplit[1].replace(" rs_skip_always", "");
                String strM71751f = "Caption";
                try {
                    CompressHelper.m71751f(strReplace, "class=\"tableLabel\">", "</span>");
                    strM71751f = CompressHelper.m71751f(strReplace, "class=\"tableCaption\">", "</span>");
                    string = Html.fromHtml(strM71751f).toString();
                } catch (Exception unused) {
                    string = strM71751f;
                }
                if (string == null) {
                    string = "Table";
                }
                m72783E4(strReplace, string);
            }
            return super.mo71969W3(consoleMessage);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: Z3 */
        public void mo71956Z3(WebView webView, String str) {
            this.f89569G4.m73433g("adjustTableLinks()");
            this.f89569G4.m73433g("$(\"img[data-original]\").each(function(){if (this.style.display==\"none\"){$(this).attr(\"src\",$(this).attr(\"data-original\"));$(this).show();}});");
            super.mo71956Z3(webView, str);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: e1 */
        public boolean mo15329e1(MenuItem menuItem) {
            int itemId = menuItem.getItemId();
            if (itemId == C5562R.id.action_gallery) {
                m71966N4("soheilvb");
            }
            if (itemId == C5562R.id.action_menu) {
                AMSectionsViewer aMSectionsViewer = new AMSectionsViewer();
                Bundle bundle = new Bundle();
                bundle.putBundle("db", this.f89566D4);
                bundle.putString("docId", this.f89567E4);
                bundle.putString("parentId", "0");
                aMSectionsViewer.m15342i2(bundle);
                aMSectionsViewer.mo15218Z2(true);
                aMSectionsViewer.m15245A2(this, 0);
                aMSectionsViewer.mo15222e3(m15283M(), "AMSectionsViewer");
            }
            return super.mo15329e1(menuItem);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: e3 */
        public void mo71957e3(Menu menu) {
            this.f87503Z4 = menu.findItem(C5562R.id.action_gallery);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment, androidx.fragment.app.Fragment
        /* renamed from: m1 */
        public void mo15225m1(Bundle bundle) {
            super.mo15225m1(bundle);
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: t3 */
        public void mo71958t3(String str) {
            m71968M4();
            super.mo71958t3(str);
        }

        /* JADX WARN: Removed duplicated region for block: B:59:0x01db A[PHI: r8
          0x01db: PHI (r8v16 java.lang.String) = (r8v15 java.lang.String), (r8v18 java.lang.String) binds: [B:58:0x01d9, B:49:0x01a0] A[DONT_GENERATE, DONT_INLINE]] */
        /* JADX WARN: Removed duplicated region for block: B:60:0x01df A[PHI: r8 r9
          0x01df: PHI (r8v17 java.lang.String) = (r8v15 java.lang.String), (r8v18 java.lang.String) binds: [B:58:0x01d9, B:49:0x01a0] A[DONT_GENERATE, DONT_INLINE]
          0x01df: PHI (r9v3 android.os.Bundle) = (r9v2 android.os.Bundle), (r9v6 android.os.Bundle) binds: [B:58:0x01d9, B:49:0x01a0] A[DONT_GENERATE, DONT_INLINE]] */
        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: y4 */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public boolean mo71960y4(android.webkit.WebView r8, java.lang.String r9, java.lang.String r10, java.lang.String r11) {
            /*
                Method dump skipped, instructions count: 491
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Fragments.AccessMedicine.AMViewerActivity.AMViewerFragment.mo71960y4(android.webkit.WebView, java.lang.String, java.lang.String, java.lang.String):boolean");
        }

        @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperFragment
        /* renamed from: z3 */
        public void mo71970z3() {
            if (m15307V1().getSharedPreferences("default_preferences", 0).getBoolean("lastred", false)) {
                String str = "select save from highlight where dbName='" + this.f89566D4.getString("Name").replace("'", "''") + "' AND dbAddress='" + this.f89579Q4.m71833a1(this.f89567E4) + "' AND save like '%$highlightRed$%'";
                CompressHelper compressHelper = this.f89579Q4;
                Bundle bundleM71890s1 = compressHelper.m71890s1(compressHelper.m71825Y(m72790I3(), str));
                if (bundleM71890s1 == null) {
                    return;
                }
                m71968M4();
                this.f89569G4.m73433g("gotoHighlight('" + bundleM71890s1.getString("save") + "');");
            }
        }
    }

    @Override // net.imedicaldoctor.imd.Fragments.ViewerHelperActivity, net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_general_viewer);
        m73543Y0(new AMViewerFragment(), bundle);
    }
}

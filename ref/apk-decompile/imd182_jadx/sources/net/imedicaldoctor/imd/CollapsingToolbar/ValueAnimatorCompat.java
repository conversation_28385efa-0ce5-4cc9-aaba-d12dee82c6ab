package net.imedicaldoctor.imd.CollapsingToolbar;

import android.view.animation.Interpolator;
import androidx.annotation.NonNull;

/* loaded from: classes3.dex */
class ValueAnimatorCompat {

    /* renamed from: a */
    private final Impl f87299a;

    interface AnimatorListener {
        /* renamed from: a */
        void mo71679a(ValueAnimatorCompat valueAnimatorCompat);

        /* renamed from: b */
        void mo71680b(ValueAnimatorCompat valueAnimatorCompat);

        /* renamed from: c */
        void mo71681c(ValueAnimatorCompat valueAnimatorCompat);
    }

    static class AnimatorListenerAdapter implements AnimatorListener {
        AnimatorListenerAdapter() {
        }

        @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.AnimatorListener
        /* renamed from: a */
        public void mo71679a(ValueAnimatorCompat valueAnimatorCompat) {
        }

        @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.AnimatorListener
        /* renamed from: b */
        public void mo71680b(ValueAnimatorCompat valueAnimatorCompat) {
        }

        @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.AnimatorListener
        /* renamed from: c */
        public void mo71681c(ValueAnimatorCompat valueAnimatorCompat) {
        }
    }

    interface AnimatorUpdateListener {
        /* renamed from: a */
        void mo71653a(ValueAnimatorCompat valueAnimatorCompat);
    }

    interface Creator {
        @NonNull
        /* renamed from: c */
        ValueAnimatorCompat mo71682c();
    }

    static abstract class Impl {

        interface AnimatorListenerProxy {
            /* renamed from: a */
            void mo71676a();

            /* renamed from: b */
            void mo71677b();

            /* renamed from: c */
            void mo71678c();
        }

        interface AnimatorUpdateListenerProxy {
            /* renamed from: a */
            void mo71675a();
        }

        Impl() {
        }

        /* renamed from: a */
        abstract void mo71683a(AnimatorListenerProxy animatorListenerProxy);

        /* renamed from: b */
        abstract void mo71684b(AnimatorUpdateListenerProxy animatorUpdateListenerProxy);

        /* renamed from: c */
        abstract void mo71685c();

        /* renamed from: d */
        abstract void mo71686d();

        /* renamed from: e */
        abstract float mo71687e();

        /* renamed from: f */
        abstract float mo71688f();

        /* renamed from: g */
        abstract int mo71689g();

        /* renamed from: h */
        abstract long mo71690h();

        /* renamed from: i */
        abstract boolean mo71691i();

        /* renamed from: j */
        abstract void mo71692j(long j2);

        /* renamed from: k */
        abstract void mo71693k(float f2, float f3);

        /* renamed from: l */
        abstract void mo71694l(int i2, int i3);

        /* renamed from: m */
        abstract void mo71695m(Interpolator interpolator);

        /* renamed from: n */
        abstract void mo71696n();
    }

    ValueAnimatorCompat(Impl impl) {
        this.f87299a = impl;
    }

    /* renamed from: a */
    public void m71661a(final AnimatorListener animatorListener) {
        if (animatorListener != null) {
            this.f87299a.mo71683a(new Impl.AnimatorListenerProxy() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.2
                @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl.AnimatorListenerProxy
                /* renamed from: a */
                public void mo71676a() {
                    animatorListener.mo71679a(ValueAnimatorCompat.this);
                }

                @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl.AnimatorListenerProxy
                /* renamed from: b */
                public void mo71677b() {
                    animatorListener.mo71680b(ValueAnimatorCompat.this);
                }

                @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl.AnimatorListenerProxy
                /* renamed from: c */
                public void mo71678c() {
                    animatorListener.mo71681c(ValueAnimatorCompat.this);
                }
            });
        } else {
            this.f87299a.mo71683a(null);
        }
    }

    /* renamed from: b */
    public void m71662b(final AnimatorUpdateListener animatorUpdateListener) {
        if (animatorUpdateListener != null) {
            this.f87299a.mo71684b(new Impl.AnimatorUpdateListenerProxy() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.1
                @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl.AnimatorUpdateListenerProxy
                /* renamed from: a */
                public void mo71675a() {
                    animatorUpdateListener.mo71653a(ValueAnimatorCompat.this);
                }
            });
        } else {
            this.f87299a.mo71684b(null);
        }
    }

    /* renamed from: c */
    public void m71663c() {
        this.f87299a.mo71685c();
    }

    /* renamed from: d */
    public void m71664d() {
        this.f87299a.mo71686d();
    }

    /* renamed from: e */
    public float m71665e() {
        return this.f87299a.mo71687e();
    }

    /* renamed from: f */
    public float m71666f() {
        return this.f87299a.mo71688f();
    }

    /* renamed from: g */
    public int m71667g() {
        return this.f87299a.mo71689g();
    }

    /* renamed from: h */
    public long m71668h() {
        return this.f87299a.mo71690h();
    }

    /* renamed from: i */
    public boolean m71669i() {
        return this.f87299a.mo71691i();
    }

    /* renamed from: j */
    public void m71670j(long j2) {
        this.f87299a.mo71692j(j2);
    }

    /* renamed from: k */
    public void m71671k(float f2, float f3) {
        this.f87299a.mo71693k(f2, f3);
    }

    /* renamed from: l */
    public void m71672l(int i2, int i3) {
        this.f87299a.mo71694l(i2, i3);
    }

    /* renamed from: m */
    public void m71673m(Interpolator interpolator) {
        this.f87299a.mo71695m(interpolator);
    }

    /* renamed from: n */
    public void m71674n() {
        this.f87299a.mo71696n();
    }
}

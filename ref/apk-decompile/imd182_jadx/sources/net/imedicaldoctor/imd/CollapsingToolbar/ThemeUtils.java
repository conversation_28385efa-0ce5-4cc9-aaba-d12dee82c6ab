package net.imedicaldoctor.imd.CollapsingToolbar;

import android.content.Context;
import android.content.res.TypedArray;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
class ThemeUtils {

    /* renamed from: a */
    private static final int[] f87298a = {C5562R.attr.colorPrimary};

    ThemeUtils() {
    }

    /* renamed from: a */
    static void m71660a(Context context) {
        TypedArray typedArrayObtainStyledAttributes = context.obtainStyledAttributes(f87298a);
        boolean z = !typedArrayObtainStyledAttributes.hasValue(0);
        typedArrayObtainStyledAttributes.recycle();
        if (z) {
            throw new IllegalArgumentException("You need to use a Theme.AppCompat theme (or descendant) with the design library.");
        }
    }
}

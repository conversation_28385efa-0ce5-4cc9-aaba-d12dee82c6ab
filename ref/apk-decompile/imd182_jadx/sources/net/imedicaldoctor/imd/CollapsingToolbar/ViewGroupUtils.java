package net.imedicaldoctor.imd.CollapsingToolbar;

import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;

/* loaded from: classes3.dex */
class ViewGroupUtils {

    /* renamed from: a */
    private static final ViewGroupUtilsImpl f87323a = new ViewGroupUtilsImplHoneycomb();

    private interface ViewGroupUtilsImpl {
        /* renamed from: a */
        void mo71705a(ViewGroup viewGroup, View view, Rect rect);
    }

    private static class ViewGroupUtilsImplBase implements ViewGroupUtilsImpl {
        ViewGroupUtilsImplBase() {
        }

        @Override // net.imedicaldoctor.imd.CollapsingToolbar.ViewGroupUtils.ViewGroupUtilsImpl
        /* renamed from: a */
        public void mo71705a(ViewGroup viewGroup, View view, Rect rect) {
            viewGroup.offsetDescendantRectToMyCoords(view, rect);
            rect.offset(view.getScrollX(), view.getScrollY());
        }
    }

    private static class ViewGroupUtilsImplHoneycomb implements ViewGroupUtilsImpl {
        ViewGroupUtilsImplHoneycomb() {
        }

        @Override // net.imedicaldoctor.imd.CollapsingToolbar.ViewGroupUtils.ViewGroupUtilsImpl
        /* renamed from: a */
        public void mo71705a(ViewGroup viewGroup, View view, Rect rect) {
            ViewGroupUtilsHoneycomb.m71707b(viewGroup, view, rect);
        }
    }

    ViewGroupUtils() {
    }

    /* renamed from: a */
    static void m71703a(ViewGroup viewGroup, View view, Rect rect) {
        rect.set(0, 0, view.getWidth(), view.getHeight());
        m71704b(viewGroup, view, rect);
    }

    /* renamed from: b */
    static void m71704b(ViewGroup viewGroup, View view, Rect rect) {
        f87323a.mo71705a(viewGroup, view, rect);
    }
}

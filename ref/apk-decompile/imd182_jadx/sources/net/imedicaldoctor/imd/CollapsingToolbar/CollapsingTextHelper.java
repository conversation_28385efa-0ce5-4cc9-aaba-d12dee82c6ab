package net.imedicaldoctor.imd.CollapsingToolbar;

import android.R;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Interpolator;
import androidx.annotation.ColorInt;
import androidx.appcompat.C0082R;
import androidx.core.text.TextDirectionHeuristicsCompat;
import androidx.core.view.GravityCompat;
import androidx.core.view.ViewCompat;
import androidx.media3.extractor.p003ts.TsExtractor;

/* loaded from: classes3.dex */
final class CollapsingTextHelper {

    /* renamed from: Z */
    private static final boolean f87212Z = false;

    /* renamed from: A */
    private Paint f87214A;

    /* renamed from: B */
    private float f87215B;

    /* renamed from: C */
    private float f87216C;

    /* renamed from: D */
    private int[] f87217D;

    /* renamed from: E */
    private boolean f87218E;

    /* renamed from: G */
    private Interpolator f87220G;

    /* renamed from: H */
    private Interpolator f87221H;

    /* renamed from: I */
    private float f87222I;

    /* renamed from: J */
    private float f87223J;

    /* renamed from: K */
    private float f87224K;

    /* renamed from: L */
    private int f87225L;

    /* renamed from: M */
    private float f87226M;

    /* renamed from: N */
    private float f87227N;

    /* renamed from: O */
    private float f87228O;

    /* renamed from: P */
    private int f87229P;

    /* renamed from: Q */
    private CharSequence f87230Q;

    /* renamed from: R */
    private Bitmap f87231R;

    /* renamed from: S */
    private Bitmap f87232S;

    /* renamed from: T */
    private StaticLayout f87233T;

    /* renamed from: U */
    private float f87234U;

    /* renamed from: V */
    private float f87235V;

    /* renamed from: W */
    private float f87236W;

    /* renamed from: a */
    private final View f87238a;

    /* renamed from: b */
    private boolean f87239b;

    /* renamed from: c */
    private float f87240c;

    /* renamed from: k */
    private ColorStateList f87248k;

    /* renamed from: l */
    private ColorStateList f87249l;

    /* renamed from: m */
    private float f87250m;

    /* renamed from: n */
    private float f87251n;

    /* renamed from: o */
    private float f87252o;

    /* renamed from: p */
    private float f87253p;

    /* renamed from: q */
    private float f87254q;

    /* renamed from: r */
    private float f87255r;

    /* renamed from: s */
    private Typeface f87256s;

    /* renamed from: t */
    private Typeface f87257t;

    /* renamed from: u */
    private Typeface f87258u;

    /* renamed from: v */
    private CharSequence f87259v;

    /* renamed from: w */
    private CharSequence f87260w;

    /* renamed from: x */
    private boolean f87261x;

    /* renamed from: y */
    private boolean f87262y;

    /* renamed from: z */
    private Bitmap f87263z;

    /* renamed from: Y */
    private static final boolean f87211Y = false;

    /* renamed from: a0 */
    private static final Paint f87213a0 = null;

    /* renamed from: g */
    private int f87244g = 16;

    /* renamed from: h */
    private int f87245h = 16;

    /* renamed from: i */
    private float f87246i = 8.0f;

    /* renamed from: j */
    private float f87247j = 8.0f;

    /* renamed from: X */
    private int f87237X = 3;

    /* renamed from: F */
    private final TextPaint f87219F = new TextPaint(TsExtractor.f30464J);

    /* renamed from: e */
    private final Rect f87242e = new Rect();

    /* renamed from: d */
    private final Rect f87241d = new Rect();

    /* renamed from: f */
    private final RectF f87243f = new RectF();

    public CollapsingTextHelper(View view) {
        this.f87238a = view;
    }

    /* renamed from: B */
    private static float m71583B(float f2, float f3, float f4, Interpolator interpolator) {
        if (interpolator != null) {
            f4 = interpolator.getInterpolation(f4);
        }
        return AnimationUtils.m71581a(f2, f3, f4);
    }

    /* renamed from: D */
    private Typeface m71584D(int i2) throws Resources.NotFoundException {
        TypedArray typedArrayObtainStyledAttributes = this.f87238a.getContext().obtainStyledAttributes(i2, new int[]{R.attr.fontFamily});
        try {
            String string = typedArrayObtainStyledAttributes.getString(0);
            if (string != null) {
                return Typeface.create(string, 0);
            }
            typedArrayObtainStyledAttributes.recycle();
            return null;
        } finally {
            typedArrayObtainStyledAttributes.recycle();
        }
    }

    /* renamed from: F */
    private static boolean m71585F(Rect rect, int i2, int i3, int i4, int i5) {
        return rect.left == i2 && rect.top == i3 && rect.right == i4 && rect.bottom == i5;
    }

    /* renamed from: I */
    private void m71586I(float f2) {
        this.f87234U = f2;
        ViewCompat.m9204t1(this.f87238a);
    }

    /* renamed from: P */
    private void m71587P(float f2) {
        this.f87235V = f2;
        ViewCompat.m9204t1(this.f87238a);
    }

    /* renamed from: V */
    private void m71588V(float f2) {
        m71594f(f2);
        boolean z = f87211Y && this.f87215B != 1.0f;
        this.f87262y = z;
        if (z) {
            m71598k();
            m71596i();
            m71597j();
        }
        ViewCompat.m9204t1(this.f87238a);
    }

    /* renamed from: a */
    private static int m71589a(int i2, int i3, float f2) {
        float f3 = 1.0f - f2;
        return Color.argb((int) ((Color.alpha(i2) * f3) + (Color.alpha(i3) * f2)), (int) ((Color.red(i2) * f3) + (Color.red(i3) * f2)), (int) ((Color.green(i2) * f3) + (Color.green(i3) * f2)), (int) ((Color.blue(i2) * f3) + (Color.blue(i3) * f2)));
    }

    /* JADX WARN: Removed duplicated region for block: B:26:0x007f  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0084  */
    /* JADX WARN: Removed duplicated region for block: B:30:0x0089  */
    /* JADX WARN: Removed duplicated region for block: B:31:0x008e  */
    /* JADX WARN: Removed duplicated region for block: B:34:0x009d  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x00a6  */
    /* JADX WARN: Removed duplicated region for block: B:41:0x00b9  */
    /* JADX WARN: Removed duplicated region for block: B:44:0x00c3  */
    /* JADX WARN: Removed duplicated region for block: B:48:0x00d6  */
    /* renamed from: b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void m71590b() {
        /*
            Method dump skipped, instructions count: 230
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.CollapsingToolbar.CollapsingTextHelper.m71590b():void");
    }

    /* renamed from: c */
    private void m71591c() {
        m71593e(this.f87240c);
    }

    /* renamed from: d */
    private boolean m71592d(CharSequence charSequence) {
        return (ViewCompat.m9135c0(this.f87238a) == 1 ? TextDirectionHeuristicsCompat.f13061d : TextDirectionHeuristicsCompat.f13060c).mo8379b(charSequence, 0, charSequence.length());
    }

    /* renamed from: e */
    private void m71593e(float f2) {
        TextPaint textPaint;
        int iM71599p;
        m71601y(f2);
        this.f87254q = m71583B(this.f87252o, this.f87253p, f2, this.f87220G);
        this.f87255r = m71583B(this.f87250m, this.f87251n, f2, this.f87220G);
        m71588V(m71583B(this.f87246i, this.f87247j, f2, this.f87221H));
        Interpolator interpolator = AnimationUtils.f87207b;
        m71586I(1.0f - m71583B(0.0f, 1.0f, 1.0f - f2, interpolator));
        m71587P(m71583B(1.0f, 0.0f, f2, interpolator));
        if (this.f87249l != this.f87248k) {
            textPaint = this.f87219F;
            iM71599p = m71589a(m71600q(), m71599p(), f2);
        } else {
            textPaint = this.f87219F;
            iM71599p = m71599p();
        }
        textPaint.setColor(iM71599p);
        this.f87219F.setShadowLayer(m71583B(this.f87226M, this.f87222I, f2, null), m71583B(this.f87227N, this.f87223J, f2, null), m71583B(this.f87228O, this.f87224K, f2, null), m71589a(this.f87229P, this.f87225L, f2));
        ViewCompat.m9204t1(this.f87238a);
    }

    /* renamed from: f */
    private void m71594f(float f2) {
        boolean z;
        int i2;
        float f3;
        CharSequence charSequenceConcat;
        Layout.Alignment alignment;
        Layout.Alignment alignment2;
        boolean z2;
        if (this.f87259v == null) {
            return;
        }
        float fWidth = this.f87242e.width();
        float fWidth2 = this.f87241d.width();
        if (m71602z(f2, this.f87247j)) {
            f3 = (this.f87247j / 3.0f) * 2.0f;
            this.f87215B = 1.0f;
            Typeface typeface = this.f87258u;
            Typeface typeface2 = this.f87256s;
            if (typeface != typeface2) {
                this.f87258u = typeface2;
                z2 = true;
            } else {
                z2 = false;
            }
            z = z2;
            fWidth2 = fWidth;
            i2 = 1;
        } else {
            float f4 = this.f87247j;
            Typeface typeface3 = this.f87258u;
            Typeface typeface4 = this.f87257t;
            if (typeface3 != typeface4) {
                this.f87258u = typeface4;
                z = true;
            } else {
                z = false;
            }
            if (m71602z(f2, this.f87246i)) {
                this.f87215B = 1.0f;
            } else {
                this.f87215B = f2 / this.f87246i;
            }
            i2 = this.f87237X;
            f3 = f4;
        }
        if (fWidth2 > 0.0f) {
            z = this.f87216C != f3 || this.f87218E || z;
            this.f87216C = f3;
            this.f87218E = false;
        }
        if (this.f87260w == null || z) {
            this.f87219F.setTextSize(this.f87216C);
            this.f87219F.setTypeface(this.f87258u);
            CharSequence charSequence = this.f87259v;
            TextPaint textPaint = this.f87219F;
            int i3 = (int) fWidth2;
            Layout.Alignment alignment3 = Layout.Alignment.ALIGN_NORMAL;
            StaticLayout staticLayout = new StaticLayout(charSequence, textPaint, i3, alignment3, 1.0f, 0.0f, false);
            if (staticLayout.getLineCount() > i2) {
                int i4 = i2 - 1;
                CharSequence charSequenceSubSequence = "";
                String strSubSequence = i4 > 0 ? this.f87259v.subSequence(0, staticLayout.getLineEnd(i2 - 2)) : "";
                CharSequence charSequenceSubSequence2 = this.f87259v.subSequence(staticLayout.getLineStart(i4), staticLayout.getLineEnd(i4));
                if (charSequenceSubSequence2.charAt(charSequenceSubSequence2.length() - 1) == ' ') {
                    charSequenceSubSequence = charSequenceSubSequence2.subSequence(charSequenceSubSequence2.length() - 1, charSequenceSubSequence2.length());
                    charSequenceSubSequence2 = charSequenceSubSequence2.subSequence(0, charSequenceSubSequence2.length() - 1);
                }
                charSequenceConcat = TextUtils.concat(strSubSequence, TextUtils.ellipsize(TextUtils.concat(charSequenceSubSequence2, "…", charSequenceSubSequence), this.f87219F, fWidth2, TextUtils.TruncateAt.END));
            } else {
                charSequenceConcat = this.f87259v;
            }
            if (!TextUtils.equals(charSequenceConcat, this.f87260w)) {
                this.f87260w = charSequenceConcat;
                this.f87261x = m71592d(charSequenceConcat);
            }
            int i5 = this.f87244g & GravityCompat.f13299d;
            if (i5 == 1) {
                alignment = Layout.Alignment.ALIGN_CENTER;
            } else {
                if (i5 != 5 && i5 != 8388613) {
                    alignment2 = alignment3;
                    this.f87233T = new StaticLayout(this.f87260w, this.f87219F, i3, alignment2, 1.0f, 0.0f, false);
                }
                alignment = Layout.Alignment.ALIGN_OPPOSITE;
            }
            alignment2 = alignment;
            this.f87233T = new StaticLayout(this.f87260w, this.f87219F, i3, alignment2, 1.0f, 0.0f, false);
        }
    }

    /* renamed from: g */
    private void m71595g() {
        Bitmap bitmap = this.f87263z;
        if (bitmap != null) {
            bitmap.recycle();
            this.f87263z = null;
        }
        Bitmap bitmap2 = this.f87231R;
        if (bitmap2 != null) {
            bitmap2.recycle();
            this.f87231R = null;
        }
        Bitmap bitmap3 = this.f87232S;
        if (bitmap3 != null) {
            bitmap3.recycle();
            this.f87232S = null;
        }
    }

    /* renamed from: i */
    private void m71596i() {
        if (this.f87231R != null || this.f87242e.isEmpty() || TextUtils.isEmpty(this.f87260w)) {
            return;
        }
        m71593e(0.0f);
        TextPaint textPaint = this.f87219F;
        CharSequence charSequence = this.f87260w;
        int iRound = Math.round(textPaint.measureText(charSequence, 0, charSequence.length()));
        int iRound2 = Math.round(this.f87219F.descent() - this.f87219F.ascent());
        if (iRound > 0 || iRound2 > 0) {
            this.f87231R = Bitmap.createBitmap(iRound, iRound2, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(this.f87231R);
            CharSequence charSequence2 = this.f87230Q;
            canvas.drawText(charSequence2, 0, charSequence2.length(), 0.0f, (-this.f87219F.ascent()) / this.f87215B, this.f87219F);
            if (this.f87214A == null) {
                this.f87214A = new Paint(3);
            }
        }
    }

    /* renamed from: j */
    private void m71597j() {
        if (this.f87232S != null || this.f87242e.isEmpty() || TextUtils.isEmpty(this.f87260w)) {
            return;
        }
        m71593e(0.0f);
        int iRound = Math.round(this.f87219F.measureText(this.f87260w, this.f87233T.getLineStart(0), this.f87233T.getLineEnd(0)));
        int iRound2 = Math.round(this.f87219F.descent() - this.f87219F.ascent());
        if (iRound > 0 || iRound2 > 0) {
            this.f87232S = Bitmap.createBitmap(iRound, iRound2, Bitmap.Config.ARGB_8888);
            new Canvas(this.f87232S).drawText(this.f87260w, this.f87233T.getLineStart(0), this.f87233T.getLineEnd(0), 0.0f, (-this.f87219F.ascent()) / this.f87215B, this.f87219F);
            if (this.f87214A == null) {
                this.f87214A = new Paint(3);
            }
        }
    }

    /* renamed from: k */
    private void m71598k() {
        if (this.f87263z != null || this.f87241d.isEmpty() || TextUtils.isEmpty(this.f87260w)) {
            return;
        }
        m71593e(0.0f);
        int width = this.f87233T.getWidth();
        int height = this.f87233T.getHeight();
        if (width <= 0 || height <= 0) {
            return;
        }
        this.f87263z = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        this.f87233T.draw(new Canvas(this.f87263z));
        if (this.f87214A == null) {
            this.f87214A = new Paint(3);
        }
    }

    @ColorInt
    /* renamed from: p */
    private int m71599p() {
        int[] iArr = this.f87217D;
        return iArr != null ? this.f87249l.getColorForState(iArr, 0) : this.f87249l.getDefaultColor();
    }

    @ColorInt
    /* renamed from: q */
    private int m71600q() {
        int[] iArr = this.f87217D;
        return iArr != null ? this.f87248k.getColorForState(iArr, 0) : this.f87248k.getDefaultColor();
    }

    /* renamed from: y */
    private void m71601y(float f2) {
        this.f87243f.left = m71583B(this.f87241d.left, this.f87242e.left, f2, this.f87220G);
        this.f87243f.top = m71583B(this.f87250m, this.f87251n, f2, this.f87220G);
        this.f87243f.right = m71583B(this.f87241d.right, this.f87242e.right, f2, this.f87220G);
        this.f87243f.bottom = m71583B(this.f87241d.bottom, this.f87242e.bottom, f2, this.f87220G);
    }

    /* renamed from: z */
    private static boolean m71602z(float f2, float f3) {
        return Math.abs(f2 - f3) < 0.001f;
    }

    /* renamed from: A */
    boolean m71603A() {
        ColorStateList colorStateList;
        ColorStateList colorStateList2 = this.f87249l;
        return (colorStateList2 != null && colorStateList2.isStateful()) || ((colorStateList = this.f87248k) != null && colorStateList.isStateful());
    }

    /* renamed from: C */
    void m71604C() {
        this.f87239b = this.f87242e.width() > 0 && this.f87242e.height() > 0 && this.f87241d.width() > 0 && this.f87241d.height() > 0;
    }

    /* renamed from: E */
    public void m71605E() {
        if (this.f87238a.getHeight() <= 0 || this.f87238a.getWidth() <= 0) {
            return;
        }
        m71590b();
        m71591c();
    }

    /* renamed from: G */
    void m71606G(int i2, int i3, int i4, int i5) {
        if (m71585F(this.f87242e, i2, i3, i4, i5)) {
            return;
        }
        this.f87242e.set(i2, i3, i4, i5);
        this.f87218E = true;
        m71604C();
    }

    /* renamed from: H */
    void m71607H(int i2) throws Resources.NotFoundException {
        TypedArray typedArrayObtainStyledAttributes = this.f87238a.getContext().obtainStyledAttributes(i2, C0082R.styleable.f2100a6);
        if (typedArrayObtainStyledAttributes.hasValue(3)) {
            this.f87249l = typedArrayObtainStyledAttributes.getColorStateList(3);
        }
        if (typedArrayObtainStyledAttributes.hasValue(0)) {
            this.f87247j = typedArrayObtainStyledAttributes.getDimensionPixelSize(0, (int) this.f87247j);
        }
        this.f87225L = typedArrayObtainStyledAttributes.getInt(6, 0);
        this.f87223J = typedArrayObtainStyledAttributes.getFloat(7, 0.0f);
        this.f87224K = typedArrayObtainStyledAttributes.getFloat(8, 0.0f);
        this.f87222I = typedArrayObtainStyledAttributes.getFloat(9, 0.0f);
        typedArrayObtainStyledAttributes.recycle();
        this.f87256s = m71584D(i2);
        m71605E();
    }

    /* renamed from: J */
    void m71608J(ColorStateList colorStateList) {
        if (this.f87249l != colorStateList) {
            this.f87249l = colorStateList;
            m71605E();
        }
    }

    /* renamed from: K */
    void m71609K(int i2) {
        if (this.f87245h != i2) {
            this.f87245h = i2;
            m71605E();
        }
    }

    /* renamed from: L */
    void m71610L(float f2) {
        if (this.f87247j != f2) {
            this.f87247j = f2;
            m71605E();
        }
    }

    /* renamed from: M */
    void m71611M(Typeface typeface) {
        if (this.f87256s != typeface) {
            this.f87256s = typeface;
            m71605E();
        }
    }

    /* renamed from: N */
    void m71612N(int i2, int i3, int i4, int i5) {
        if (m71585F(this.f87241d, i2, i3, i4, i5)) {
            return;
        }
        this.f87241d.set(i2, i3, i4, i5);
        this.f87218E = true;
        m71604C();
    }

    /* renamed from: O */
    void m71613O(int i2) throws Resources.NotFoundException {
        TypedArray typedArrayObtainStyledAttributes = this.f87238a.getContext().obtainStyledAttributes(i2, C0082R.styleable.f2100a6);
        if (typedArrayObtainStyledAttributes.hasValue(3)) {
            this.f87248k = typedArrayObtainStyledAttributes.getColorStateList(3);
        }
        if (typedArrayObtainStyledAttributes.hasValue(0)) {
            this.f87246i = typedArrayObtainStyledAttributes.getDimensionPixelSize(0, (int) this.f87246i);
        }
        this.f87229P = typedArrayObtainStyledAttributes.getInt(6, 0);
        this.f87227N = typedArrayObtainStyledAttributes.getFloat(7, 0.0f);
        this.f87228O = typedArrayObtainStyledAttributes.getFloat(8, 0.0f);
        this.f87226M = typedArrayObtainStyledAttributes.getFloat(9, 0.0f);
        typedArrayObtainStyledAttributes.recycle();
        this.f87257t = m71584D(i2);
        m71605E();
    }

    /* renamed from: Q */
    void m71614Q(ColorStateList colorStateList) {
        if (this.f87248k != colorStateList) {
            this.f87248k = colorStateList;
            m71605E();
        }
    }

    /* renamed from: R */
    void m71615R(int i2) {
        if (this.f87244g != i2) {
            this.f87244g = i2;
            m71605E();
        }
    }

    /* renamed from: S */
    void m71616S(float f2) {
        if (this.f87246i != f2) {
            this.f87246i = f2;
            m71605E();
        }
    }

    /* renamed from: T */
    void m71617T(Typeface typeface) {
        if (this.f87257t != typeface) {
            this.f87257t = typeface;
            m71605E();
        }
    }

    /* renamed from: U */
    void m71618U(float f2) {
        float fM71658a = MathUtils.m71658a(f2, 0.0f, 1.0f);
        if (fM71658a != this.f87240c) {
            this.f87240c = fM71658a;
            m71591c();
        }
    }

    /* renamed from: W */
    void m71619W(int i2) {
        if (i2 != this.f87237X) {
            this.f87237X = i2;
            m71595g();
            m71605E();
        }
    }

    /* renamed from: X */
    void m71620X(Interpolator interpolator) {
        this.f87220G = interpolator;
        m71605E();
    }

    /* renamed from: Y */
    boolean m71621Y(int[] iArr) {
        this.f87217D = iArr;
        if (!m71603A()) {
            return false;
        }
        m71605E();
        return true;
    }

    /* renamed from: Z */
    void m71622Z(CharSequence charSequence) {
        if (charSequence == null || !charSequence.equals(this.f87259v)) {
            this.f87259v = charSequence;
            this.f87260w = null;
            m71595g();
            m71605E();
        }
    }

    /* renamed from: a0 */
    void m71623a0(Interpolator interpolator) {
        this.f87221H = interpolator;
        m71605E();
    }

    /* renamed from: b0 */
    void m71624b0(Typeface typeface) {
        this.f87257t = typeface;
        this.f87256s = typeface;
        m71605E();
    }

    /* renamed from: h */
    public void m71625h(Canvas canvas) {
        int iSave = canvas.save();
        if (this.f87260w != null && this.f87239b) {
            float f2 = this.f87254q;
            float f3 = this.f87255r;
            boolean z = this.f87262y && this.f87263z != null;
            this.f87219F.setTextSize(this.f87216C);
            float fAscent = z ? 0.0f : this.f87219F.ascent() * this.f87215B;
            float f4 = this.f87215B;
            if (f4 != 1.0f) {
                canvas.scale(f4, f4, f2, f3);
            }
            float lineLeft = (this.f87254q + this.f87233T.getLineLeft(0)) - (this.f87236W * 2.0f);
            if (z) {
                this.f87214A.setAlpha((int) (this.f87235V * 255.0f));
                canvas.drawBitmap(this.f87263z, lineLeft, f3, this.f87214A);
                this.f87214A.setAlpha((int) (this.f87234U * 255.0f));
                canvas.drawBitmap(this.f87231R, f2, f3, this.f87214A);
                this.f87214A.setAlpha(255);
                canvas.drawBitmap(this.f87232S, f2, f3, this.f87214A);
            } else {
                canvas.translate(lineLeft, f3);
                this.f87219F.setAlpha((int) (this.f87235V * 255.0f));
                this.f87233T.draw(canvas);
                canvas.translate(f2 - lineLeft, 0.0f);
                this.f87219F.setAlpha((int) (this.f87234U * 255.0f));
                CharSequence charSequence = this.f87230Q;
                float f5 = -fAscent;
                canvas.drawText(charSequence, 0, charSequence.length(), 0.0f, f5 / this.f87215B, this.f87219F);
                this.f87219F.setAlpha(255);
                canvas.drawText(this.f87260w, this.f87233T.getLineStart(0), this.f87233T.getLineEnd(0), 0.0f, f5 / this.f87215B, this.f87219F);
            }
        }
        canvas.restoreToCount(iSave);
    }

    /* renamed from: l */
    ColorStateList m71626l() {
        return this.f87249l;
    }

    /* renamed from: m */
    int m71627m() {
        return this.f87245h;
    }

    /* renamed from: n */
    float m71628n() {
        return this.f87247j;
    }

    /* renamed from: o */
    Typeface m71629o() {
        Typeface typeface = this.f87256s;
        return typeface != null ? typeface : Typeface.DEFAULT;
    }

    /* renamed from: r */
    ColorStateList m71630r() {
        return this.f87248k;
    }

    /* renamed from: s */
    int m71631s() {
        return this.f87244g;
    }

    /* renamed from: t */
    float m71632t() {
        return this.f87246i;
    }

    /* renamed from: u */
    Typeface m71633u() {
        Typeface typeface = this.f87257t;
        return typeface != null ? typeface : Typeface.DEFAULT;
    }

    /* renamed from: v */
    float m71634v() {
        return this.f87240c;
    }

    /* renamed from: w */
    int m71635w() {
        return this.f87237X;
    }

    /* renamed from: x */
    CharSequence m71636x() {
        return this.f87259v;
    }
}

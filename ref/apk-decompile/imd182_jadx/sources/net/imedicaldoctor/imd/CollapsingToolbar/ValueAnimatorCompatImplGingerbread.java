package net.imedicaldoctor.imd.CollapsingToolbar;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.Interpolator;
import java.util.ArrayList;
import net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat;

/* loaded from: classes3.dex */
class ValueAnimatorCompatImplGingerbread extends ValueAnimatorCompat.Impl {

    /* renamed from: k */
    private static final int f87304k = 10;

    /* renamed from: l */
    private static final int f87305l = 200;

    /* renamed from: m */
    private static final Handler f87306m = new Handler(Looper.getMainLooper());

    /* renamed from: a */
    private long f87307a;

    /* renamed from: b */
    private boolean f87308b;

    /* renamed from: c */
    private float f87309c;

    /* renamed from: g */
    private Interpolator f87313g;

    /* renamed from: h */
    private ArrayList<ValueAnimatorCompat.Impl.AnimatorListenerProxy> f87314h;

    /* renamed from: i */
    private ArrayList<ValueAnimatorCompat.Impl.AnimatorUpdateListenerProxy> f87315i;

    /* renamed from: d */
    private final int[] f87310d = new int[2];

    /* renamed from: e */
    private final float[] f87311e = new float[2];

    /* renamed from: f */
    private long f87312f = 200;

    /* renamed from: j */
    private final Runnable f87316j = new Runnable() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompatImplGingerbread.1
        @Override // java.lang.Runnable
        public void run() {
            ValueAnimatorCompatImplGingerbread.this.m71702t();
        }
    };

    ValueAnimatorCompatImplGingerbread() {
    }

    /* renamed from: o */
    private void m71697o() {
        ArrayList<ValueAnimatorCompat.Impl.AnimatorListenerProxy> arrayList = this.f87314h;
        if (arrayList != null) {
            int size = arrayList.size();
            for (int i2 = 0; i2 < size; i2++) {
                this.f87314h.get(i2).mo71677b();
            }
        }
    }

    /* renamed from: p */
    private void m71698p() {
        ArrayList<ValueAnimatorCompat.Impl.AnimatorListenerProxy> arrayList = this.f87314h;
        if (arrayList != null) {
            int size = arrayList.size();
            for (int i2 = 0; i2 < size; i2++) {
                this.f87314h.get(i2).mo71676a();
            }
        }
    }

    /* renamed from: q */
    private void m71699q() {
        ArrayList<ValueAnimatorCompat.Impl.AnimatorListenerProxy> arrayList = this.f87314h;
        if (arrayList != null) {
            int size = arrayList.size();
            for (int i2 = 0; i2 < size; i2++) {
                this.f87314h.get(i2).mo71678c();
            }
        }
    }

    /* renamed from: r */
    private void m71700r() {
        ArrayList<ValueAnimatorCompat.Impl.AnimatorUpdateListenerProxy> arrayList = this.f87315i;
        if (arrayList != null) {
            int size = arrayList.size();
            for (int i2 = 0; i2 < size; i2++) {
                this.f87315i.get(i2).mo71675a();
            }
        }
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: a */
    public void mo71683a(ValueAnimatorCompat.Impl.AnimatorListenerProxy animatorListenerProxy) {
        if (this.f87314h == null) {
            this.f87314h = new ArrayList<>();
        }
        this.f87314h.add(animatorListenerProxy);
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: b */
    public void mo71684b(ValueAnimatorCompat.Impl.AnimatorUpdateListenerProxy animatorUpdateListenerProxy) {
        if (this.f87315i == null) {
            this.f87315i = new ArrayList<>();
        }
        this.f87315i.add(animatorUpdateListenerProxy);
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: c */
    public void mo71685c() {
        this.f87308b = false;
        f87306m.removeCallbacks(this.f87316j);
        m71697o();
        m71698p();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: d */
    public void mo71686d() {
        if (this.f87308b) {
            this.f87308b = false;
            f87306m.removeCallbacks(this.f87316j);
            this.f87309c = 1.0f;
            m71700r();
            m71698p();
        }
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: e */
    public float mo71687e() {
        float[] fArr = this.f87311e;
        return AnimationUtils.m71581a(fArr[0], fArr[1], mo71688f());
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: f */
    public float mo71688f() {
        return this.f87309c;
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: g */
    public int mo71689g() {
        int[] iArr = this.f87310d;
        return AnimationUtils.m71582b(iArr[0], iArr[1], mo71688f());
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: h */
    public long mo71690h() {
        return this.f87312f;
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: i */
    public boolean mo71691i() {
        return this.f87308b;
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: j */
    public void mo71692j(long j2) {
        this.f87312f = j2;
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: k */
    public void mo71693k(float f2, float f3) {
        float[] fArr = this.f87311e;
        fArr[0] = f2;
        fArr[1] = f3;
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: l */
    public void mo71694l(int i2, int i3) {
        int[] iArr = this.f87310d;
        iArr[0] = i2;
        iArr[1] = i3;
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: m */
    public void mo71695m(Interpolator interpolator) {
        this.f87313g = interpolator;
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: n */
    public void mo71696n() {
        if (this.f87308b) {
            return;
        }
        if (this.f87313g == null) {
            this.f87313g = new AccelerateDecelerateInterpolator();
        }
        this.f87308b = true;
        this.f87309c = 0.0f;
        m71701s();
    }

    /* renamed from: s */
    final void m71701s() {
        this.f87307a = SystemClock.uptimeMillis();
        m71700r();
        m71699q();
        f87306m.postDelayed(this.f87316j, 10L);
    }

    /* renamed from: t */
    final void m71702t() {
        if (this.f87308b) {
            float fM71658a = MathUtils.m71658a((SystemClock.uptimeMillis() - this.f87307a) / this.f87312f, 0.0f, 1.0f);
            Interpolator interpolator = this.f87313g;
            if (interpolator != null) {
                fM71658a = interpolator.getInterpolation(fM71658a);
            }
            this.f87309c = fM71658a;
            m71700r();
            if (SystemClock.uptimeMillis() >= this.f87307a + this.f87312f) {
                this.f87308b = false;
                m71698p();
            }
        }
        if (this.f87308b) {
            f87306m.postDelayed(this.f87316j, 10L);
        }
    }
}

package net.imedicaldoctor.imd.CollapsingToolbar;

import android.view.View;
import androidx.core.view.ViewCompat;

/* loaded from: classes3.dex */
class ViewOffsetHelper {

    /* renamed from: a */
    private final View f87326a;

    /* renamed from: b */
    private int f87327b;

    /* renamed from: c */
    private int f87328c;

    /* renamed from: d */
    private int f87329d;

    /* renamed from: e */
    private int f87330e;

    public ViewOffsetHelper(View view) {
        this.f87326a = view;
    }

    /* renamed from: h */
    private void m71708h() {
        View view = this.f87326a;
        ViewCompat.m9164j1(view, this.f87329d - (view.getTop() - this.f87327b));
        View view2 = this.f87326a;
        ViewCompat.m9160i1(view2, this.f87330e - (view2.getLeft() - this.f87328c));
    }

    /* renamed from: a */
    public int m71709a() {
        return this.f87328c;
    }

    /* renamed from: b */
    public int m71710b() {
        return this.f87327b;
    }

    /* renamed from: c */
    public int m71711c() {
        return this.f87330e;
    }

    /* renamed from: d */
    public int m71712d() {
        return this.f87329d;
    }

    /* renamed from: e */
    public void m71713e() {
        this.f87327b = this.f87326a.getTop();
        this.f87328c = this.f87326a.getLeft();
        m71708h();
    }

    /* renamed from: f */
    public boolean m71714f(int i2) {
        if (this.f87330e == i2) {
            return false;
        }
        this.f87330e = i2;
        m71708h();
        return true;
    }

    /* renamed from: g */
    public boolean m71715g(int i2) {
        if (this.f87329d == i2) {
            return false;
        }
        this.f87329d = i2;
        m71708h();
        return true;
    }
}

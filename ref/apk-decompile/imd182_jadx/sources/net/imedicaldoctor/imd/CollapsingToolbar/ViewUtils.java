package net.imedicaldoctor.imd.CollapsingToolbar;

import android.graphics.PorterDuff;
import java.util.Objects;
import net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat;

/* loaded from: classes3.dex */
class ViewUtils {

    /* renamed from: a */
    static final ValueAnimatorCompat.Creator f87331a = new ValueAnimatorCompat.Creator() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.ViewUtils.1
        @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Creator
        /* renamed from: c */
        public ValueAnimatorCompat mo71682c() {
            return new ValueAnimatorCompat(new ValueAnimatorCompatImplHoneycombMr1());
        }
    };

    ViewUtils() {
    }

    /* renamed from: a */
    static ValueAnimatorCompat m71716a() {
        return f87331a.mo71682c();
    }

    /* renamed from: b */
    static boolean m71717b(Object obj, Object obj2) {
        return Objects.equals(obj, obj2);
    }

    /* renamed from: c */
    static PorterDuff.Mode m71718c(int i2, PorterDuff.Mode mode) {
        return i2 != 3 ? i2 != 5 ? i2 != 9 ? i2 != 14 ? i2 != 15 ? mode : PorterDuff.Mode.SCREEN : PorterDuff.Mode.MULTIPLY : PorterDuff.Mode.SRC_ATOP : PorterDuff.Mode.SRC_IN : PorterDuff.Mode.SRC_OVER;
    }
}

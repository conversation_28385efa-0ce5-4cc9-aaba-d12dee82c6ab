package net.imedicaldoctor.imd.CollapsingToolbar;

import android.view.animation.Animation;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import android.view.animation.LinearInterpolator;
import androidx.interpolator.view.animation.FastOutLinearInInterpolator;
import androidx.interpolator.view.animation.FastOutSlowInInterpolator;
import androidx.interpolator.view.animation.LinearOutSlowInInterpolator;

/* loaded from: classes3.dex */
class AnimationUtils {

    /* renamed from: a */
    static final Interpolator f87206a = new LinearInterpolator();

    /* renamed from: b */
    static final Interpolator f87207b = new FastOutSlowInInterpolator();

    /* renamed from: c */
    static final Interpolator f87208c = new FastOutLinearInInterpolator();

    /* renamed from: d */
    static final Interpolator f87209d = new LinearOutSlowInInterpolator();

    /* renamed from: e */
    static final Interpolator f87210e = new DecelerateInterpolator();

    static class AnimationListenerAdapter implements Animation.AnimationListener {
        AnimationListenerAdapter() {
        }

        @Override // android.view.animation.Animation.AnimationListener
        public void onAnimationEnd(Animation animation) {
        }

        @Override // android.view.animation.Animation.AnimationListener
        public void onAnimationRepeat(Animation animation) {
        }

        @Override // android.view.animation.Animation.AnimationListener
        public void onAnimationStart(Animation animation) {
        }
    }

    AnimationUtils() {
    }

    /* renamed from: a */
    static float m71581a(float f2, float f3, float f4) {
        return f2 + (f4 * (f3 - f2));
    }

    /* renamed from: b */
    static int m71582b(int i2, int i3, float f2) {
        return i2 + Math.round(f2 * (i3 - i2));
    }
}

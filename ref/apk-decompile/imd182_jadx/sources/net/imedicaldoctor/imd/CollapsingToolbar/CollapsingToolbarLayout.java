package net.imedicaldoctor.imd.CollapsingToolbar;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;
import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.StyleRes;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import com.google.android.material.C2555R;
import com.google.android.material.appbar.AppBarLayout;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.reflect.InvocationTargetException;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat;

/* loaded from: classes3.dex */
public class CollapsingToolbarLayout extends FrameLayout {

    /* renamed from: u3 */
    private static final int f87264u3 = 600;

    /* renamed from: X2 */
    private final int f87265X2;

    /* renamed from: Y2 */
    private Toolbar f87266Y2;

    /* renamed from: Z2 */
    private View f87267Z2;

    /* renamed from: a3 */
    private View f87268a3;

    /* renamed from: b3 */
    private int f87269b3;

    /* renamed from: c3 */
    private int f87270c3;

    /* renamed from: d3 */
    private int f87271d3;

    /* renamed from: e3 */
    private int f87272e3;

    /* renamed from: f3 */
    private int f87273f3;

    /* renamed from: g3 */
    private final Rect f87274g3;

    /* renamed from: h3 */
    private final CollapsingTextHelper f87275h3;

    /* renamed from: i3 */
    private boolean f87276i3;

    /* renamed from: j3 */
    private boolean f87277j3;

    /* renamed from: k3 */
    private Drawable f87278k3;

    /* renamed from: l3 */
    Drawable f87279l3;

    /* renamed from: m3 */
    private int f87280m3;

    /* renamed from: n3 */
    private boolean f87281n3;

    /* renamed from: o3 */
    private ValueAnimatorCompat f87282o3;

    /* renamed from: p3 */
    private long f87283p3;

    /* renamed from: q3 */
    private int f87284q3;

    /* renamed from: r3 */
    private AppBarLayout.OnOffsetChangedListener f87285r3;

    /* renamed from: s */
    private boolean f87286s;

    /* renamed from: s3 */
    int f87287s3;

    /* renamed from: t3 */
    WindowInsetsCompat f87288t3;

    public static class LayoutParams extends FrameLayout.LayoutParams {

        /* renamed from: c */
        private static final float f87291c = 0.5f;

        /* renamed from: d */
        public static final int f87292d = 0;

        /* renamed from: e */
        public static final int f87293e = 1;

        /* renamed from: f */
        public static final int f87294f = 2;

        /* renamed from: a */
        int f87295a;

        /* renamed from: b */
        float f87296b;

        @Retention(RetentionPolicy.SOURCE)
        @RestrictTo({RestrictTo.Scope.GROUP_ID})
        @interface CollapseMode {
        }

        public LayoutParams(int i2, int i3) {
            super(i2, i3);
            this.f87295a = 0;
            this.f87296b = 0.5f;
        }

        /* renamed from: a */
        public int m71654a() {
            return this.f87295a;
        }

        /* renamed from: b */
        public float m71655b() {
            return this.f87296b;
        }

        /* renamed from: c */
        public void m71656c(int i2) {
            this.f87295a = i2;
        }

        /* renamed from: d */
        public void m71657d(float f2) {
            this.f87296b = f2;
        }

        public LayoutParams(int i2, int i3, int i4) {
            super(i2, i3, i4);
            this.f87295a = 0;
            this.f87296b = 0.5f;
        }

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f87295a = 0;
            this.f87296b = 0.5f;
            TypedArray typedArrayObtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C2555R.styleable.f55112b8);
            this.f87295a = typedArrayObtainStyledAttributes.getInt(0, 0);
            m71657d(typedArrayObtainStyledAttributes.getFloat(1, 0.5f));
            typedArrayObtainStyledAttributes.recycle();
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f87295a = 0;
            this.f87296b = 0.5f;
        }

        public LayoutParams(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
            this.f87295a = 0;
            this.f87296b = 0.5f;
        }

        public LayoutParams(FrameLayout.LayoutParams layoutParams) {
            super(layoutParams);
            this.f87295a = 0;
            this.f87296b = 0.5f;
        }
    }

    private class OffsetUpdateListener implements AppBarLayout.OnOffsetChangedListener {
        OffsetUpdateListener() {
        }

        @Override // com.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener, com.google.android.material.appbar.AppBarLayout.BaseOnOffsetChangedListener
        /* renamed from: a */
        public void mo35811a(AppBarLayout appBarLayout, int i2) {
            int iM71659b;
            CollapsingToolbarLayout collapsingToolbarLayout = CollapsingToolbarLayout.this;
            collapsingToolbarLayout.f87287s3 = i2;
            WindowInsetsCompat windowInsetsCompat = collapsingToolbarLayout.f87288t3;
            int iM9611r = windowInsetsCompat != null ? windowInsetsCompat.m9611r() : 0;
            int childCount = CollapsingToolbarLayout.this.getChildCount();
            for (int i3 = 0; i3 < childCount; i3++) {
                View childAt = CollapsingToolbarLayout.this.getChildAt(i3);
                LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
                ViewOffsetHelper viewOffsetHelperM71642i = CollapsingToolbarLayout.m71642i(childAt);
                int i4 = layoutParams.f87295a;
                if (i4 == 1) {
                    iM71659b = MathUtils.m71659b(-i2, 0, CollapsingToolbarLayout.this.m71647h(childAt));
                } else if (i4 == 2) {
                    iM71659b = Math.round((-i2) * layoutParams.f87296b);
                }
                viewOffsetHelperM71642i.m71715g(iM71659b);
            }
            CollapsingToolbarLayout.this.m71652p();
            CollapsingToolbarLayout collapsingToolbarLayout2 = CollapsingToolbarLayout.this;
            if (collapsingToolbarLayout2.f87279l3 != null && iM9611r > 0) {
                ViewCompat.m9204t1(collapsingToolbarLayout2);
            }
            CollapsingToolbarLayout.this.f87275h3.m71618U(Math.abs(i2) / ((CollapsingToolbarLayout.this.getHeight() - ViewCompat.m9155h0(CollapsingToolbarLayout.this)) - iM9611r));
        }
    }

    public CollapsingToolbarLayout(Context context) {
        this(context, null);
    }

    /* renamed from: b */
    private void m71638b(int i2) {
        m71639c();
        ValueAnimatorCompat valueAnimatorCompat = this.f87282o3;
        if (valueAnimatorCompat == null) {
            ValueAnimatorCompat valueAnimatorCompatM71716a = ViewUtils.m71716a();
            this.f87282o3 = valueAnimatorCompatM71716a;
            valueAnimatorCompatM71716a.m71670j(this.f87283p3);
            this.f87282o3.m71673m(i2 > this.f87280m3 ? AnimationUtils.f87208c : AnimationUtils.f87209d);
            this.f87282o3.m71662b(new ValueAnimatorCompat.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.CollapsingToolbarLayout.2
                @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.AnimatorUpdateListener
                /* renamed from: a */
                public void mo71653a(ValueAnimatorCompat valueAnimatorCompat2) {
                    CollapsingToolbarLayout.this.setScrimAlpha(valueAnimatorCompat2.m71667g());
                }
            });
        } else if (valueAnimatorCompat.m71669i()) {
            this.f87282o3.m71663c();
        }
        this.f87282o3.m71672l(this.f87280m3, i2);
        this.f87282o3.m71674n();
    }

    /* renamed from: c */
    private void m71639c() {
        if (this.f87286s) {
            Toolbar toolbar = null;
            this.f87266Y2 = null;
            this.f87267Z2 = null;
            int i2 = this.f87265X2;
            if (i2 != -1) {
                Toolbar toolbar2 = (Toolbar) findViewById(i2);
                this.f87266Y2 = toolbar2;
                if (toolbar2 != null) {
                    this.f87267Z2 = m71640d(toolbar2);
                }
            }
            if (this.f87266Y2 == null) {
                int childCount = getChildCount();
                int i3 = 0;
                while (true) {
                    if (i3 >= childCount) {
                        break;
                    }
                    View childAt = getChildAt(i3);
                    if (childAt instanceof Toolbar) {
                        toolbar = (Toolbar) childAt;
                        break;
                    }
                    i3++;
                }
                this.f87266Y2 = toolbar;
            }
            m71644o();
            this.f87286s = false;
        }
    }

    /* renamed from: d */
    private View m71640d(View view) {
        for (ViewParent parent = view.getParent(); parent != this && parent != null; parent = parent.getParent()) {
            if (parent instanceof View) {
                view = parent;
            }
        }
        return view;
    }

    /* renamed from: g */
    private static int m71641g(@NonNull View view) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (!(layoutParams instanceof ViewGroup.MarginLayoutParams)) {
            return view.getHeight();
        }
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) layoutParams;
        return view.getHeight() + marginLayoutParams.topMargin + marginLayoutParams.bottomMargin;
    }

    /* renamed from: i */
    static ViewOffsetHelper m71642i(View view) {
        ViewOffsetHelper viewOffsetHelper = (ViewOffsetHelper) view.getTag(C5562R.id.view_offset_helper);
        if (viewOffsetHelper != null) {
            return viewOffsetHelper;
        }
        ViewOffsetHelper viewOffsetHelper2 = new ViewOffsetHelper(view);
        view.setTag(C5562R.id.view_offset_helper, viewOffsetHelper2);
        return viewOffsetHelper2;
    }

    /* renamed from: k */
    private boolean m71643k(View view) {
        int i2 = this.f87269b3;
        return i2 >= 0 && i2 == indexOfChild(view) + 1;
    }

    /* renamed from: o */
    private void m71644o() {
        View view;
        if (!this.f87276i3 && (view = this.f87268a3) != null) {
            ViewParent parent = view.getParent();
            if (parent instanceof ViewGroup) {
                ((ViewGroup) parent).removeView(this.f87268a3);
            }
        }
        if (!this.f87276i3 || this.f87266Y2 == null) {
            return;
        }
        if (this.f87268a3 == null) {
            this.f87268a3 = new View(getContext());
        }
        if (this.f87268a3.getParent() == null) {
            this.f87266Y2.addView(this.f87268a3, -1, -1);
        }
    }

    @Override // android.widget.FrameLayout, android.view.ViewGroup
    protected boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof LayoutParams;
    }

    @Override // android.view.View
    public void draw(Canvas canvas) {
        Drawable drawable;
        super.draw(canvas);
        m71639c();
        if (this.f87266Y2 == null && (drawable = this.f87278k3) != null && this.f87280m3 > 0) {
            drawable.mutate().setAlpha(this.f87280m3);
            this.f87278k3.draw(canvas);
        }
        if (this.f87276i3 && this.f87277j3) {
            this.f87275h3.m71625h(canvas);
        }
        if (this.f87279l3 == null || this.f87280m3 <= 0) {
            return;
        }
        WindowInsetsCompat windowInsetsCompat = this.f87288t3;
        int iM9611r = windowInsetsCompat != null ? windowInsetsCompat.m9611r() : 0;
        if (iM9611r > 0) {
            this.f87279l3.setBounds(0, -this.f87287s3, getWidth(), iM9611r - this.f87287s3);
            this.f87279l3.mutate().setAlpha(this.f87280m3);
            this.f87279l3.draw(canvas);
        }
    }

    @Override // android.view.ViewGroup
    protected boolean drawChild(Canvas canvas, View view, long j2) {
        boolean zDrawChild = super.drawChild(canvas, view, j2);
        if (this.f87278k3 == null || this.f87280m3 <= 0 || !m71643k(view)) {
            return zDrawChild;
        }
        this.f87278k3.mutate().setAlpha(this.f87280m3);
        this.f87278k3.draw(canvas);
        return true;
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void drawableStateChanged() {
        super.drawableStateChanged();
        int[] drawableState = getDrawableState();
        Drawable drawable = this.f87279l3;
        boolean state = (drawable == null || !drawable.isStateful()) ? false : drawable.setState(drawableState);
        Drawable drawable2 = this.f87278k3;
        if (drawable2 != null && drawable2.isStateful()) {
            state |= drawable2.setState(drawableState);
        }
        CollapsingTextHelper collapsingTextHelper = this.f87275h3;
        if (collapsingTextHelper != null) {
            state |= collapsingTextHelper.m71621Y(drawableState);
        }
        if (state) {
            invalidate();
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // android.widget.FrameLayout, android.view.ViewGroup
    /* renamed from: e, reason: merged with bridge method [inline-methods] */
    public LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(-1, -1);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // android.widget.FrameLayout, android.view.ViewGroup
    /* renamed from: f, reason: merged with bridge method [inline-methods] */
    public FrameLayout.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new LayoutParams(layoutParams);
    }

    public int getCollapsedTitleGravity() {
        return this.f87275h3.m71627m();
    }

    @NonNull
    public Typeface getCollapsedTitleTypeface() {
        return this.f87275h3.m71629o();
    }

    @Nullable
    public Drawable getContentScrim() {
        return this.f87278k3;
    }

    public int getExpandedTitleGravity() {
        return this.f87275h3.m71631s();
    }

    public int getExpandedTitleMarginBottom() {
        return this.f87273f3;
    }

    public int getExpandedTitleMarginEnd() {
        return this.f87272e3;
    }

    public int getExpandedTitleMarginStart() {
        return this.f87270c3;
    }

    public int getExpandedTitleMarginTop() {
        return this.f87271d3;
    }

    @NonNull
    public Typeface getExpandedTitleTypeface() {
        return this.f87275h3.m71633u();
    }

    public int getMaxLines() {
        return this.f87275h3.m71635w();
    }

    public long getScrimAnimationDuration() {
        return this.f87283p3;
    }

    public int getScrimVisibleHeightTrigger() {
        int i2 = this.f87284q3;
        if (i2 >= 0) {
            return i2;
        }
        WindowInsetsCompat windowInsetsCompat = this.f87288t3;
        int iM9611r = windowInsetsCompat != null ? windowInsetsCompat.m9611r() : 0;
        int iM9155h0 = ViewCompat.m9155h0(this);
        return iM9155h0 > 0 ? Math.min((iM9155h0 * 2) + iM9611r, getHeight()) : getHeight() / 3;
    }

    @Nullable
    public Drawable getStatusBarScrim() {
        return this.f87279l3;
    }

    @Nullable
    public CharSequence getTitle() {
        if (this.f87276i3) {
            return this.f87275h3.m71636x();
        }
        return null;
    }

    /* renamed from: h */
    final int m71647h(View view) {
        return ((getHeight() - m71642i(view).m71710b()) - view.getHeight()) - ((FrameLayout.LayoutParams) ((LayoutParams) view.getLayoutParams())).bottomMargin;
    }

    /* renamed from: j */
    public boolean m71648j() {
        return this.f87276i3;
    }

    /* renamed from: l */
    WindowInsetsCompat m71649l(WindowInsetsCompat windowInsetsCompat) {
        WindowInsetsCompat windowInsetsCompat2 = ViewCompat.m9114W(this) ? windowInsetsCompat : null;
        if (!ViewUtils.m71717b(this.f87288t3, windowInsetsCompat2)) {
            this.f87288t3 = windowInsetsCompat2;
            requestLayout();
        }
        return windowInsetsCompat.m9596c();
    }

    /* renamed from: m */
    public void m71650m(int i2, int i3, int i4, int i5) {
        this.f87270c3 = i2;
        this.f87271d3 = i3;
        this.f87272e3 = i4;
        this.f87273f3 = i5;
        requestLayout();
    }

    /* renamed from: n */
    public void m71651n(boolean z, boolean z2) {
        if (this.f87281n3 != z) {
            if (z2) {
                m71638b(z ? 255 : 0);
            } else {
                setScrimAlpha(z ? 255 : 0);
            }
            this.f87281n3 = z;
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        Object parent = getParent();
        if (parent instanceof AppBarLayout) {
            ViewCompat.m9116W1(this, ViewCompat.m9114W((View) parent));
            if (this.f87285r3 == null) {
                this.f87285r3 = new OffsetUpdateListener();
            }
            ((AppBarLayout) parent).m35754e(this.f87285r3);
            ViewCompat.m9035B1(this);
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void onDetachedFromWindow() {
        ViewParent parent = getParent();
        AppBarLayout.OnOffsetChangedListener onOffsetChangedListener = this.f87285r3;
        if (onOffsetChangedListener != null && (parent instanceof AppBarLayout)) {
            ((AppBarLayout) parent).m35744B(onOffsetChangedListener);
        }
        super.onDetachedFromWindow();
    }

    @Override // android.widget.FrameLayout, android.view.ViewGroup, android.view.View
    protected void onLayout(boolean z, int i2, int i3, int i4, int i5) {
        int iIndexOfChild;
        View view;
        View view2;
        super.onLayout(z, i2, i3, i4, i5);
        WindowInsetsCompat windowInsetsCompat = this.f87288t3;
        if (windowInsetsCompat != null) {
            int iM9611r = windowInsetsCompat.m9611r();
            int childCount = getChildCount();
            for (int i6 = 0; i6 < childCount; i6++) {
                View childAt = getChildAt(i6);
                if (!ViewCompat.m9114W(childAt) && childAt.getTop() < iM9611r) {
                    ViewCompat.m9164j1(childAt, iM9611r);
                }
            }
        }
        if (this.f87276i3 && (view2 = this.f87268a3) != null) {
            boolean z2 = ViewCompat.m9098R0(view2) && this.f87268a3.getVisibility() == 0;
            this.f87277j3 = z2;
            if (z2) {
                boolean z3 = ViewCompat.m9135c0(this) == 1;
                View view3 = this.f87267Z2;
                if (view3 == null) {
                    view3 = this.f87266Y2;
                }
                int iM71647h = m71647h(view3);
                ViewGroupUtils.m71703a(this, this.f87268a3, this.f87274g3);
                CollapsingTextHelper collapsingTextHelper = this.f87275h3;
                int i7 = this.f87274g3.left;
                Toolbar toolbar = this.f87266Y2;
                int titleMarginEnd = i7 + (z3 ? toolbar.getTitleMarginEnd() : toolbar.getTitleMarginStart());
                int titleMarginTop = this.f87274g3.top + iM71647h + this.f87266Y2.getTitleMarginTop();
                int i8 = this.f87274g3.right;
                Toolbar toolbar2 = this.f87266Y2;
                collapsingTextHelper.m71606G(titleMarginEnd, titleMarginTop, i8 + (z3 ? toolbar2.getTitleMarginStart() : toolbar2.getTitleMarginEnd()), (this.f87274g3.bottom + iM71647h) - this.f87266Y2.getTitleMarginBottom());
                this.f87275h3.m71612N(z3 ? this.f87272e3 : this.f87270c3, this.f87274g3.top + this.f87271d3, (i4 - i2) - (z3 ? this.f87270c3 : this.f87272e3), (i5 - i3) - this.f87273f3);
                this.f87275h3.m71605E();
            }
        }
        int childCount2 = getChildCount();
        for (int i9 = 0; i9 < childCount2; i9++) {
            m71642i(getChildAt(i9)).m71713e();
        }
        if (this.f87266Y2 != null) {
            if (this.f87276i3 && TextUtils.isEmpty(this.f87275h3.m71636x())) {
                this.f87275h3.m71622Z(this.f87266Y2.getTitle());
            }
            View view4 = this.f87267Z2;
            if (view4 == null || view4 == this) {
                setMinimumHeight(m71641g(this.f87266Y2));
                view = this.f87266Y2;
            } else {
                setMinimumHeight(m71641g(view4));
                view = this.f87267Z2;
            }
            iIndexOfChild = indexOfChild(view);
        } else {
            iIndexOfChild = -1;
        }
        this.f87269b3 = iIndexOfChild;
        m71652p();
    }

    @Override // android.widget.FrameLayout, android.view.View
    protected void onMeasure(int i2, int i3) {
        m71639c();
        super.onMeasure(i2, i3);
    }

    @Override // android.view.View
    protected void onSizeChanged(int i2, int i3, int i4, int i5) {
        super.onSizeChanged(i2, i3, i4, i5);
        Drawable drawable = this.f87278k3;
        if (drawable != null) {
            drawable.setBounds(0, 0, i2, i3);
        }
    }

    /* renamed from: p */
    final void m71652p() {
        if (this.f87278k3 == null && this.f87279l3 == null) {
            return;
        }
        setScrimsShown(getHeight() + this.f87287s3 < getScrimVisibleHeightTrigger());
    }

    public void setCollapsedTitleGravity(int i2) {
        this.f87275h3.m71609K(i2);
    }

    public void setCollapsedTitleTextAppearance(@StyleRes int i2) throws Resources.NotFoundException {
        this.f87275h3.m71607H(i2);
    }

    public void setCollapsedTitleTextColor(@ColorInt int i2) {
        setCollapsedTitleTextColor(ColorStateList.valueOf(i2));
    }

    public void setCollapsedTitleTypeface(@Nullable Typeface typeface) {
        this.f87275h3.m71611M(typeface);
    }

    public void setContentScrim(@Nullable Drawable drawable) {
        Drawable drawable2 = this.f87278k3;
        if (drawable2 != drawable) {
            if (drawable2 != null) {
                drawable2.setCallback(null);
            }
            Drawable drawableMutate = drawable != null ? drawable.mutate() : null;
            this.f87278k3 = drawableMutate;
            if (drawableMutate != null) {
                drawableMutate.setBounds(0, 0, getWidth(), getHeight());
                this.f87278k3.setCallback(this);
                this.f87278k3.setAlpha(this.f87280m3);
            }
            ViewCompat.m9204t1(this);
        }
    }

    public void setContentScrimColor(@ColorInt int i2) {
        setContentScrim(new ColorDrawable(i2));
    }

    public void setContentScrimResource(@DrawableRes int i2) {
        setContentScrim(ContextCompat.m6692l(getContext(), i2));
    }

    public void setExpandedTitleColor(@ColorInt int i2) {
        setExpandedTitleTextColor(ColorStateList.valueOf(i2));
    }

    public void setExpandedTitleGravity(int i2) {
        this.f87275h3.m71615R(i2);
    }

    public void setExpandedTitleMarginBottom(int i2) {
        this.f87273f3 = i2;
        requestLayout();
    }

    public void setExpandedTitleMarginEnd(int i2) {
        this.f87272e3 = i2;
        requestLayout();
    }

    public void setExpandedTitleMarginStart(int i2) {
        this.f87270c3 = i2;
        requestLayout();
    }

    public void setExpandedTitleMarginTop(int i2) {
        this.f87271d3 = i2;
        requestLayout();
    }

    public void setExpandedTitleTextAppearance(@StyleRes int i2) throws Resources.NotFoundException {
        this.f87275h3.m71613O(i2);
    }

    public void setExpandedTitleTextColor(@NonNull ColorStateList colorStateList) {
        this.f87275h3.m71614Q(colorStateList);
    }

    public void setExpandedTitleTypeface(@Nullable Typeface typeface) {
        this.f87275h3.m71617T(typeface);
    }

    public void setMaxLines(int i2) {
        this.f87275h3.m71619W(i2);
    }

    void setScrimAlpha(int i2) {
        Toolbar toolbar;
        if (i2 != this.f87280m3) {
            if (this.f87278k3 != null && (toolbar = this.f87266Y2) != null) {
                ViewCompat.m9204t1(toolbar);
            }
            this.f87280m3 = i2;
            ViewCompat.m9204t1(this);
        }
    }

    public void setScrimAnimationDuration(@IntRange(from = 0) long j2) {
        this.f87283p3 = j2;
    }

    public void setScrimVisibleHeightTrigger(@IntRange(from = 0) int i2) {
        if (this.f87284q3 != i2) {
            this.f87284q3 = i2;
            m71652p();
        }
    }

    public void setScrimsShown(boolean z) {
        m71651n(z, ViewCompat.m9121Y0(this) && !isInEditMode());
    }

    public void setStatusBarScrim(@Nullable Drawable drawable) throws IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        Drawable drawable2 = this.f87279l3;
        if (drawable2 != drawable) {
            if (drawable2 != null) {
                drawable2.setCallback(null);
            }
            Drawable drawableMutate = drawable != null ? drawable.mutate() : null;
            this.f87279l3 = drawableMutate;
            if (drawableMutate != null) {
                if (drawableMutate.isStateful()) {
                    this.f87279l3.setState(getDrawableState());
                }
                DrawableCompat.m7600m(this.f87279l3, ViewCompat.m9135c0(this));
                this.f87279l3.setVisible(getVisibility() == 0, false);
                this.f87279l3.setCallback(this);
                this.f87279l3.setAlpha(this.f87280m3);
            }
            ViewCompat.m9204t1(this);
        }
    }

    public void setStatusBarScrimColor(@ColorInt int i2) throws IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        setStatusBarScrim(new ColorDrawable(i2));
    }

    public void setStatusBarScrimResource(@DrawableRes int i2) throws IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        setStatusBarScrim(ContextCompat.m6692l(getContext(), i2));
    }

    public void setTitle(@Nullable CharSequence charSequence) {
        this.f87275h3.m71622Z(charSequence);
    }

    public void setTitleEnabled(boolean z) {
        if (z != this.f87276i3) {
            this.f87276i3 = z;
            m71644o();
            requestLayout();
        }
    }

    @Override // android.view.View
    public void setVisibility(int i2) {
        super.setVisibility(i2);
        boolean z = i2 == 0;
        Drawable drawable = this.f87279l3;
        if (drawable != null && drawable.isVisible() != z) {
            this.f87279l3.setVisible(z, false);
        }
        Drawable drawable2 = this.f87278k3;
        if (drawable2 == null || drawable2.isVisible() == z) {
            return;
        }
        this.f87278k3.setVisible(z, false);
    }

    @Override // android.view.View
    protected boolean verifyDrawable(Drawable drawable) {
        return super.verifyDrawable(drawable) || drawable == this.f87278k3 || drawable == this.f87279l3;
    }

    public CollapsingToolbarLayout(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public void setCollapsedTitleTextColor(@NonNull ColorStateList colorStateList) {
        this.f87275h3.m71608J(colorStateList);
    }

    public CollapsingToolbarLayout(Context context, AttributeSet attributeSet, int i2) throws IllegalAccessException, Resources.NotFoundException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        super(context, attributeSet, i2);
        this.f87286s = true;
        this.f87274g3 = new Rect();
        this.f87284q3 = -1;
        ThemeUtils.m71660a(context);
        CollapsingTextHelper collapsingTextHelper = new CollapsingTextHelper(this);
        this.f87275h3 = collapsingTextHelper;
        collapsingTextHelper.m71623a0(AnimationUtils.f87210e);
        TypedArray typedArrayObtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C2555R.styleable.f54208C7, i2, **********);
        collapsingTextHelper.m71615R(typedArrayObtainStyledAttributes.getInt(4, 8388691));
        collapsingTextHelper.m71609K(typedArrayObtainStyledAttributes.getInt(0, 8388627));
        int dimensionPixelSize = typedArrayObtainStyledAttributes.getDimensionPixelSize(5, 0);
        this.f87273f3 = dimensionPixelSize;
        this.f87272e3 = dimensionPixelSize;
        this.f87271d3 = dimensionPixelSize;
        this.f87270c3 = dimensionPixelSize;
        if (typedArrayObtainStyledAttributes.hasValue(8)) {
            this.f87270c3 = typedArrayObtainStyledAttributes.getDimensionPixelSize(8, 0);
        }
        if (typedArrayObtainStyledAttributes.hasValue(7)) {
            this.f87272e3 = typedArrayObtainStyledAttributes.getDimensionPixelSize(7, 0);
        }
        if (typedArrayObtainStyledAttributes.hasValue(9)) {
            this.f87271d3 = typedArrayObtainStyledAttributes.getDimensionPixelSize(9, 0);
        }
        if (typedArrayObtainStyledAttributes.hasValue(6)) {
            this.f87273f3 = typedArrayObtainStyledAttributes.getDimensionPixelSize(6, 0);
        }
        this.f87276i3 = typedArrayObtainStyledAttributes.getBoolean(20, true);
        setTitle(typedArrayObtainStyledAttributes.getText(18));
        collapsingTextHelper.m71613O(**********);
        collapsingTextHelper.m71607H(**********);
        if (typedArrayObtainStyledAttributes.hasValue(10)) {
            collapsingTextHelper.m71613O(typedArrayObtainStyledAttributes.getResourceId(10, 0));
        }
        if (typedArrayObtainStyledAttributes.hasValue(1)) {
            collapsingTextHelper.m71607H(typedArrayObtainStyledAttributes.getResourceId(1, 0));
        }
        this.f87284q3 = typedArrayObtainStyledAttributes.getDimensionPixelSize(16, -1);
        this.f87283p3 = typedArrayObtainStyledAttributes.getInt(15, 600);
        setContentScrim(typedArrayObtainStyledAttributes.getDrawable(3));
        setStatusBarScrim(typedArrayObtainStyledAttributes.getDrawable(17));
        this.f87265X2 = typedArrayObtainStyledAttributes.getResourceId(23, -1);
        typedArrayObtainStyledAttributes.recycle();
        setWillNotDraw(false);
        ViewCompat.m9169k2(this, new OnApplyWindowInsetsListener() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.CollapsingToolbarLayout.1
            @Override // androidx.core.view.OnApplyWindowInsetsListener
            /* renamed from: a */
            public WindowInsetsCompat mo1280a(View view, WindowInsetsCompat windowInsetsCompat) {
                return CollapsingToolbarLayout.this.m71649l(windowInsetsCompat);
            }
        });
        collapsingTextHelper.m71619W(context.obtainStyledAttributes(attributeSet, C5562R.styleable.f101238y9, i2, 0).getInteger(0, 3));
    }

    @Override // android.widget.FrameLayout, android.view.ViewGroup
    public FrameLayout.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }
}

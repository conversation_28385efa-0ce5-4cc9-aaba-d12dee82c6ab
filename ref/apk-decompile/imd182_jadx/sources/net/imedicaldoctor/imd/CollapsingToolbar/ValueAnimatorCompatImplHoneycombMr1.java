package net.imedicaldoctor.imd.CollapsingToolbar;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.annotation.TargetApi;
import android.view.animation.Interpolator;
import net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat;

@TargetApi(12)
/* loaded from: classes3.dex */
class ValueAnimatorCompatImplHoneycombMr1 extends ValueAnimatorCompat.Impl {

    /* renamed from: a */
    private final ValueAnimator f87318a = new ValueAnimator();

    ValueAnimatorCompatImplHoneycombMr1() {
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: a */
    public void mo71683a(final ValueAnimatorCompat.Impl.AnimatorListenerProxy animatorListenerProxy) {
        this.f87318a.addListener(new AnimatorListenerAdapter() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompatImplHoneycombMr1.2
            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationCancel(Animator animator) {
                animatorListenerProxy.mo71677b();
            }

            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationEnd(Animator animator) {
                animatorListenerProxy.mo71676a();
            }

            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationStart(Animator animator) {
                animatorListenerProxy.mo71678c();
            }
        });
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: b */
    public void mo71684b(final ValueAnimatorCompat.Impl.AnimatorUpdateListenerProxy animatorUpdateListenerProxy) {
        this.f87318a.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompatImplHoneycombMr1.1
            @Override // android.animation.ValueAnimator.AnimatorUpdateListener
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                animatorUpdateListenerProxy.mo71675a();
            }
        });
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: c */
    public void mo71685c() {
        this.f87318a.cancel();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: d */
    public void mo71686d() {
        this.f87318a.end();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: e */
    public float mo71687e() {
        return ((Float) this.f87318a.getAnimatedValue()).floatValue();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: f */
    public float mo71688f() {
        return this.f87318a.getAnimatedFraction();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: g */
    public int mo71689g() {
        return ((Integer) this.f87318a.getAnimatedValue()).intValue();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: h */
    public long mo71690h() {
        return this.f87318a.getDuration();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: i */
    public boolean mo71691i() {
        return this.f87318a.isRunning();
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: j */
    public void mo71692j(long j2) {
        this.f87318a.setDuration(j2);
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: k */
    public void mo71693k(float f2, float f3) {
        this.f87318a.setFloatValues(f2, f3);
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: l */
    public void mo71694l(int i2, int i3) {
        this.f87318a.setIntValues(i2, i3);
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: m */
    public void mo71695m(Interpolator interpolator) {
        this.f87318a.setInterpolator(interpolator);
    }

    @Override // net.imedicaldoctor.imd.CollapsingToolbar.ValueAnimatorCompat.Impl
    /* renamed from: n */
    public void mo71696n() {
        this.f87318a.start();
    }
}

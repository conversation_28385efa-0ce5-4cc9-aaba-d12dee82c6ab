package net.imedicaldoctor.imd.Utils;

import android.content.Context;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.GestureDetector;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.webkit.WebBackForwardList;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.tool.xml.css.CSS;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.NestedScrollWebView;
import net.imedicaldoctor.imd.iMDLogger;

/* loaded from: classes3.dex */
public class iMDWebView extends NestedScrollWebView {

    /* renamed from: c3 */
    public int f101409c3;

    /* renamed from: d3 */
    public int f101410d3;

    /* renamed from: e3 */
    public ScaleGestureDetector f101411e3;

    /* renamed from: f3 */
    public GestureDetector f101412f3;

    /* renamed from: g3 */
    public ActionModeResponse f101413g3;

    /* renamed from: h3 */
    final int f101414h3;

    /* renamed from: i3 */
    public Context f101415i3;

    /* renamed from: j3 */
    public String f101416j3;

    /* renamed from: k3 */
    private ActionMode f101417k3;

    /* renamed from: l3 */
    private ActionMode.Callback f101418l3;

    /* renamed from: m3 */
    private ActionMode.Callback f101419m3;

    public iMDWebView(Context context) {
        super(context);
        this.f101414h3 = 15;
    }

    /* renamed from: g */
    public void m73433g(String str) {
        evaluateJavascript(str, null);
    }

    /* renamed from: i */
    public void m73434i() {
        int i2;
        Context context;
        String str;
        iMDLogger.m73548d("UTDWebView", "Zoom in : " + (getSettings().getTextZoom() + 15));
        if (getSettings().getTextZoom() >= 300) {
            context = this.f101415i3;
            str = "Can't Zoom In Anymore";
            i2 = 1;
        } else {
            m73438m();
            getSettings().setTextZoom(getSettings().getTextZoom() + 15);
            m73437l();
            i2 = 0;
            this.f101415i3.getSharedPreferences("default_preferences", 0).edit().putInt(this.f101416j3 + "zoom", getSettings().getTextZoom()).commit();
            context = this.f101415i3;
            str = getSettings().getTextZoom() + CSS.Value.f74136n0;
        }
        CompressHelper.m71767x2(context, str, i2);
    }

    @Override // android.webkit.WebView
    public void invokeZoomPicker() {
        iMDLogger.m73548d("UTDWebView", "Invoke zoom picker");
        super.invokeZoomPicker();
    }

    /* renamed from: j */
    public void m73435j() {
        int i2;
        Context context;
        String str;
        StringBuilder sb = new StringBuilder();
        sb.append("Zoom Out : ");
        sb.append(getSettings().getTextZoom() - 15);
        iMDLogger.m73548d("UTDWebView", sb.toString());
        if (getSettings().getTextZoom() <= 25) {
            context = this.f101415i3;
            str = "Can't Zoom Out Anymore";
            i2 = 1;
        } else {
            m73438m();
            getSettings().setTextZoom(getSettings().getTextZoom() - 15);
            m73437l();
            i2 = 0;
            this.f101415i3.getSharedPreferences("default_preferences", 0).edit().putInt(this.f101416j3 + "zoom", getSettings().getTextZoom()).commit();
            context = this.f101415i3;
            str = getSettings().getTextZoom() + CSS.Value.f74136n0;
        }
        CompressHelper.m71767x2(context, str, i2);
    }

    /* renamed from: k */
    public void m73436k() {
        ActionMode actionMode = this.f101417k3;
        if (actionMode == null) {
            return;
        }
        actionMode.finish();
    }

    /* renamed from: l */
    public void m73437l() {
        m73433g("document.getElementById('orientation').scrollIntoView(true);");
        m73433g("element=document.getElementById('orientation');element.parentNode.removeChild(element);");
    }

    /* renamed from: m */
    public void m73438m() {
        m73433g("e = document.createElement('span'); e.setAttribute('id','orientation');document.caretRangeFromPoint(0, 0).insertNode(e);");
    }

    @Override // android.webkit.WebView, android.view.View
    protected void onScrollChanged(int i2, int i3, int i4, int i5) {
        super.onScrollChanged(i2, i3, i4, i5);
        this.f101409c3 = i2;
        this.f101410d3 = i3;
    }

    @Override // net.imedicaldoctor.imd.NestedScrollWebView, android.webkit.WebView, android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent) {
        try {
            this.f101412f3.onTouchEvent(motionEvent);
            this.f101411e3.onTouchEvent(motionEvent);
            return super.onTouchEvent(motionEvent);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return true;
        }
    }

    @Override // android.webkit.WebView
    public WebBackForwardList saveState(Bundle bundle) {
        iMDLogger.m73548d("UTDWebView", "SaveState");
        return super.saveState(bundle);
    }

    @Override // android.view.View
    public ActionMode startActionMode(ActionMode.Callback callback) {
        ActionMode actionMode = new ActionMode() { // from class: net.imedicaldoctor.imd.Utils.iMDWebView.3
            @Override // android.view.ActionMode
            public void finish() {
                ActionModeResponse actionModeResponse = iMDWebView.this.f101413g3;
                if (actionModeResponse != null) {
                    actionModeResponse.mo72853a();
                    iMDWebView.this.clearFocus();
                }
            }

            @Override // android.view.ActionMode
            public View getCustomView() {
                return null;
            }

            @Override // android.view.ActionMode
            public Menu getMenu() {
                return null;
            }

            @Override // android.view.ActionMode
            public MenuInflater getMenuInflater() {
                return null;
            }

            @Override // android.view.ActionMode
            public CharSequence getSubtitle() {
                return null;
            }

            @Override // android.view.ActionMode
            public CharSequence getTitle() {
                return null;
            }

            @Override // android.view.ActionMode
            public void invalidate() {
            }

            @Override // android.view.ActionMode
            public void setCustomView(View view) {
            }

            @Override // android.view.ActionMode
            public void setSubtitle(int i2) {
            }

            @Override // android.view.ActionMode
            public void setTitle(int i2) {
            }

            @Override // android.view.ActionMode
            public void setSubtitle(CharSequence charSequence) {
            }

            @Override // android.view.ActionMode
            public void setTitle(CharSequence charSequence) {
            }
        };
        ActionModeResponse actionModeResponse = this.f101413g3;
        if (actionModeResponse != null) {
            actionModeResponse.mo72854b();
        }
        this.f101417k3 = actionMode;
        return actionMode;
    }

    @Override // android.webkit.WebView
    public boolean zoomIn() {
        iMDLogger.m73548d("UTDWebView", "ZoomIn");
        return super.zoomIn();
    }

    public iMDWebView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f101414h3 = 15;
        this.f101415i3 = context;
        this.f101412f3 = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() { // from class: net.imedicaldoctor.imd.Utils.iMDWebView.1
            @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
            public boolean onSingleTapUp(MotionEvent motionEvent) {
                return false;
            }
        });
        this.f101411e3 = new ScaleGestureDetector(context, new ScaleGestureDetector.SimpleOnScaleGestureListener() { // from class: net.imedicaldoctor.imd.Utils.iMDWebView.2
            @Override // android.view.ScaleGestureDetector.SimpleOnScaleGestureListener, android.view.ScaleGestureDetector.OnScaleGestureListener
            public void onScaleEnd(ScaleGestureDetector scaleGestureDetector) {
                iMDLogger.m73550f("iMDScale", scaleGestureDetector.getScaleFactor() + "");
                if (scaleGestureDetector.getScaleFactor() > 1.2d) {
                    iMDWebView.this.m73434i();
                } else if (scaleGestureDetector.getScaleFactor() < 0.8d) {
                    iMDWebView.this.m73435j();
                }
            }
        });
    }

    public iMDWebView(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        this.f101414h3 = 15;
    }
}

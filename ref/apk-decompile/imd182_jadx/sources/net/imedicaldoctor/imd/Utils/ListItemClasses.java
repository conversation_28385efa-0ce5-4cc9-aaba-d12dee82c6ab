package net.imedicaldoctor.imd.Utils;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class ListItemClasses {

    public class DatabaseViewHolder {

        /* renamed from: a */
        public final TextView f101336a;

        /* renamed from: b */
        public final ImageView f101337b;

        public DatabaseViewHolder(View view) {
            this.f101336a = (TextView) view.findViewById(C5562R.id.database_title);
            this.f101337b = (ImageView) view.findViewById(C5562R.id.database_image);
        }
    }

    public class HeaderViewHolder {

        /* renamed from: a */
        public final TextView f101339a;

        public HeaderViewHolder(View view) {
            this.f101339a = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    public class InteractionViewHolder {

        /* renamed from: a */
        public final TextView f101341a;

        /* renamed from: b */
        public final ImageView f101342b;

        /* renamed from: c */
        public final TextView f101343c;

        public InteractionViewHolder(View view) {
            this.f101343c = (TextView) view.findViewById(C5562R.id.subtitle_text);
            this.f101341a = (TextView) view.findViewById(C5562R.id.title_text);
            this.f101342b = (ImageView) view.findViewById(C5562R.id.image);
        }
    }
}

package net.imedicaldoctor.imd.Utils;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/* loaded from: classes3.dex */
public class AutoSpanRecyclerView extends RecyclerView {

    /* renamed from: m5 */
    private int f101330m5;

    /* renamed from: n5 */
    private int f101331n5;

    /* renamed from: o5 */
    private final LayoutRequester f101332o5;

    private class LayoutRequester implements Runnable {
        private LayoutRequester() {
        }

        @Override // java.lang.Runnable
        public void run() {
            AutoSpanRecyclerView.this.requestLayout();
        }
    }

    public AutoSpanRecyclerView(Context context) {
        super(context);
        this.f101332o5 = new LayoutRequester();
    }

    /* renamed from: f2 */
    public void m73391f2(int i2, int i3, int i4) {
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 2, i2, false);
        this.f101331n5 = i3;
        this.f101330m5 = i4;
        setLayoutManager(gridLayoutManager);
    }

    @Override // androidx.recyclerview.widget.RecyclerView, android.view.ViewGroup, android.view.View
    protected void onLayout(boolean z, int i2, int i3, int i4, int i5) {
        super.onLayout(z, i2, i3, i4, i5);
        if (z) {
            RecyclerView.LayoutManager layoutManager = getLayoutManager();
            if (layoutManager instanceof GridLayoutManager) {
                View viewInflate = LayoutInflater.from(getContext()).inflate(this.f101331n5, (ViewGroup) this, false);
                int iMakeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
                viewInflate.measure(iMakeMeasureSpec, iMakeMeasureSpec);
                int measuredWidth = viewInflate.getMeasuredWidth();
                ((GridLayoutManager) layoutManager).m27029Q3(Math.max(this.f101330m5, getMeasuredWidth() / measuredWidth));
                post(this.f101332o5);
            }
        }
    }
}

package net.imedicaldoctor.imd.Utils;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Property;
import android.util.TypedValue;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import net.imedicaldoctor.imd.C5562R;

@SuppressLint({"ClickableViewAccessibility"})
/* loaded from: classes3.dex */
public class MaterialRippleLayout extends FrameLayout {

    /* renamed from: A3 */
    private static final int f101345A3 = 75;

    /* renamed from: B3 */
    private static final float f101346B3 = 35.0f;

    /* renamed from: C3 */
    private static final float f101347C3 = 0.2f;

    /* renamed from: D3 */
    private static final int f101348D3 = -16777216;

    /* renamed from: E3 */
    private static final int f101349E3 = 0;

    /* renamed from: F3 */
    private static final boolean f101350F3 = true;

    /* renamed from: G3 */
    private static final boolean f101351G3 = true;

    /* renamed from: H3 */
    private static final boolean f101352H3 = false;

    /* renamed from: I3 */
    private static final boolean f101353I3 = false;

    /* renamed from: J3 */
    private static final boolean f101354J3 = false;

    /* renamed from: K3 */
    private static final int f101355K3 = 50;

    /* renamed from: L3 */
    private static final long f101356L3 = 2500;

    /* renamed from: z3 */
    private static final int f101357z3 = 550;

    /* renamed from: X2 */
    private final Rect f101358X2;

    /* renamed from: Y2 */
    private int f101359Y2;

    /* renamed from: Z2 */
    private boolean f101360Z2;

    /* renamed from: a3 */
    private boolean f101361a3;

    /* renamed from: b3 */
    private int f101362b3;

    /* renamed from: c3 */
    private int f101363c3;

    /* renamed from: d3 */
    private int f101364d3;

    /* renamed from: e3 */
    private boolean f101365e3;

    /* renamed from: f3 */
    private int f101366f3;

    /* renamed from: g3 */
    private boolean f101367g3;

    /* renamed from: h3 */
    private Drawable f101368h3;

    /* renamed from: i3 */
    private boolean f101369i3;

    /* renamed from: j3 */
    private float f101370j3;

    /* renamed from: k3 */
    private AdapterView<?> f101371k3;

    /* renamed from: l3 */
    private View f101372l3;

    /* renamed from: m3 */
    private AnimatorSet f101373m3;

    /* renamed from: n3 */
    private ObjectAnimator f101374n3;

    /* renamed from: o3 */
    private final Point f101375o3;

    /* renamed from: p3 */
    private Point f101376p3;

    /* renamed from: q3 */
    private boolean f101377q3;

    /* renamed from: r3 */
    private boolean f101378r3;

    /* renamed from: s */
    private final Paint f101379s;

    /* renamed from: s3 */
    private int f101380s3;

    /* renamed from: t3 */
    private final GestureDetector f101381t3;

    /* renamed from: u3 */
    private PerformClickEvent f101382u3;

    /* renamed from: v3 */
    private PressedEvent f101383v3;

    /* renamed from: w3 */
    private final GestureDetector.SimpleOnGestureListener f101384w3;

    /* renamed from: x3 */
    private final Property<MaterialRippleLayout, Float> f101385x3;

    /* renamed from: y3 */
    private final Property<MaterialRippleLayout, Integer> f101386y3;

    private class PerformClickEvent implements Runnable {
        private PerformClickEvent() {
        }

        /* renamed from: a */
        private void m73420a(AdapterView<?> adapterView) {
            int positionForView = adapterView.getPositionForView(MaterialRippleLayout.this);
            long itemId = adapterView.getAdapter() != null ? adapterView.getAdapter().getItemId(positionForView) : 0L;
            if (positionForView != -1) {
                adapterView.performItemClick(MaterialRippleLayout.this, positionForView, itemId);
            }
        }

        @Override // java.lang.Runnable
        public void run() {
            AdapterView<?> adapterViewM73410p;
            if (MaterialRippleLayout.this.getParent() instanceof AdapterView) {
                adapterViewM73410p = (AdapterView) MaterialRippleLayout.this.getParent();
            } else {
                if (!MaterialRippleLayout.this.f101369i3) {
                    MaterialRippleLayout.this.f101372l3.performClick();
                    return;
                }
                adapterViewM73410p = MaterialRippleLayout.this.m73410p();
            }
            m73420a(adapterViewM73410p);
        }
    }

    private final class PressedEvent implements Runnable {

        /* renamed from: s */
        private final MotionEvent f101395s;

        public PressedEvent(MotionEvent motionEvent) {
            this.f101395s = motionEvent;
        }

        @Override // java.lang.Runnable
        public void run() {
            MaterialRippleLayout.this.f101378r3 = false;
            MaterialRippleLayout.this.f101372l3.onTouchEvent(this.f101395s);
            MaterialRippleLayout.this.f101372l3.setPressed(true);
            if (MaterialRippleLayout.this.f101361a3) {
                MaterialRippleLayout.this.m73414t();
            }
        }
    }

    public static class RippleBuilder {

        /* renamed from: a */
        private final Context f101396a;

        /* renamed from: b */
        private final View f101397b;

        /* renamed from: c */
        private int f101398c = -16777216;

        /* renamed from: d */
        private boolean f101399d = false;

        /* renamed from: e */
        private boolean f101400e = true;

        /* renamed from: f */
        private float f101401f = MaterialRippleLayout.f101346B3;

        /* renamed from: g */
        private int f101402g = MaterialRippleLayout.f101357z3;

        /* renamed from: h */
        private float f101403h = 0.2f;

        /* renamed from: i */
        private boolean f101404i = true;

        /* renamed from: j */
        private int f101405j = 75;

        /* renamed from: k */
        private boolean f101406k = false;

        /* renamed from: l */
        private int f101407l = 0;

        /* renamed from: m */
        private final boolean f101408m = false;

        public RippleBuilder(View view) {
            this.f101397b = view;
            this.f101396a = view.getContext();
        }

        /* renamed from: a */
        public MaterialRippleLayout m73421a() {
            MaterialRippleLayout materialRippleLayout = new MaterialRippleLayout(this.f101396a);
            materialRippleLayout.setRippleColor(this.f101398c);
            materialRippleLayout.setDefaultRippleAlpha((int) this.f101403h);
            materialRippleLayout.setRippleDelayClick(this.f101404i);
            materialRippleLayout.setRippleDiameter((int) MaterialRippleLayout.m73408n(this.f101396a.getResources(), this.f101401f));
            materialRippleLayout.setRippleDuration(this.f101402g);
            materialRippleLayout.setRippleFadeDuration(this.f101405j);
            materialRippleLayout.setRippleHover(this.f101400e);
            materialRippleLayout.setRipplePersistent(this.f101406k);
            materialRippleLayout.setRippleOverlay(this.f101399d);
            materialRippleLayout.setRippleBackground(this.f101407l);
            int iIndexOfChild = 0;
            materialRippleLayout.setRippleInAdapter(false);
            ViewGroup.LayoutParams layoutParams = this.f101397b.getLayoutParams();
            ViewGroup viewGroup = (ViewGroup) this.f101397b.getParent();
            if (viewGroup != null && (viewGroup instanceof MaterialRippleLayout)) {
                throw new IllegalStateException("MaterialRippleLayout could not be created: parent of the view already is a MaterialRippleLayout");
            }
            if (viewGroup != null) {
                iIndexOfChild = viewGroup.indexOfChild(this.f101397b);
                viewGroup.removeView(this.f101397b);
            }
            materialRippleLayout.addView(this.f101397b, new ViewGroup.LayoutParams(-1, -1));
            if (viewGroup != null) {
                viewGroup.addView(materialRippleLayout, iIndexOfChild, layoutParams);
            }
            return materialRippleLayout;
        }

        /* renamed from: b */
        public RippleBuilder m73422b(float f2) {
            this.f101403h = f2 * 255.0f;
            return this;
        }

        /* renamed from: c */
        public RippleBuilder m73423c(int i2) {
            this.f101407l = i2;
            return this;
        }

        /* renamed from: d */
        public RippleBuilder m73424d(int i2) {
            this.f101398c = i2;
            return this;
        }

        /* renamed from: e */
        public RippleBuilder m73425e(boolean z) {
            this.f101404i = z;
            return this;
        }

        /* renamed from: f */
        public RippleBuilder m73426f(int i2) {
            this.f101401f = i2;
            return this;
        }

        /* renamed from: g */
        public RippleBuilder m73427g(int i2) {
            this.f101402g = i2;
            return this;
        }

        /* renamed from: h */
        public RippleBuilder m73428h(int i2) {
            this.f101405j = i2;
            return this;
        }

        /* renamed from: i */
        public RippleBuilder m73429i(boolean z) {
            this.f101400e = z;
            return this;
        }

        /* renamed from: j */
        public RippleBuilder m73430j(boolean z) {
            m73430j(z);
            return this;
        }

        /* renamed from: k */
        public RippleBuilder m73431k(boolean z) {
            this.f101399d = z;
            return this;
        }

        /* renamed from: l */
        public RippleBuilder m73432l(boolean z) {
            this.f101406k = z;
            return this;
        }
    }

    public MaterialRippleLayout(Context context) {
        this(context, null, 0);
    }

    private float getEndRadius() {
        int width = getWidth();
        int i2 = width / 2;
        int height = getHeight() / 2;
        Point point = this.f101375o3;
        int i3 = point.x;
        return ((float) Math.sqrt(Math.pow(i2 > i3 ? width - i3 : i3, 2.0d) + Math.pow(height > point.y ? r1 - r2 : r2, 2.0d))) * 1.2f;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public float getRadius() {
        return this.f101370j3;
    }

    /* renamed from: k */
    private boolean m73405k() {
        if (!this.f101369i3) {
            return false;
        }
        int positionForView = m73410p().getPositionForView(this);
        boolean z = positionForView != this.f101380s3;
        this.f101380s3 = positionForView;
        if (z) {
            m73407m();
            m73406l();
            this.f101372l3.setPressed(false);
            setRadius(0.0f);
        }
        return z;
    }

    /* renamed from: l */
    private void m73406l() {
        AnimatorSet animatorSet = this.f101373m3;
        if (animatorSet != null) {
            animatorSet.cancel();
            this.f101373m3.removeAllListeners();
        }
        ObjectAnimator objectAnimator = this.f101374n3;
        if (objectAnimator != null) {
            objectAnimator.cancel();
        }
    }

    /* renamed from: m */
    private void m73407m() {
        PressedEvent pressedEvent = this.f101383v3;
        if (pressedEvent != null) {
            removeCallbacks(pressedEvent);
            this.f101378r3 = false;
        }
    }

    /* renamed from: n */
    static float m73408n(Resources resources, float f2) {
        return TypedValue.applyDimension(1, f2, resources.getDisplayMetrics());
    }

    /* renamed from: o */
    private boolean m73409o(View view, int i2, int i3) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i4 = 0; i4 < viewGroup.getChildCount(); i4++) {
                View childAt = viewGroup.getChildAt(i4);
                Rect rect = new Rect();
                childAt.getHitRect(rect);
                if (rect.contains(i2, i3)) {
                    return m73409o(childAt, i2 - rect.left, i3 - rect.top);
                }
            }
        } else if (view != this.f101372l3) {
            if (view.isEnabled()) {
                return view.isClickable() || view.isLongClickable() || view.isFocusableInTouchMode();
            }
            return false;
        }
        return view.isFocusableInTouchMode();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: p */
    public AdapterView<?> m73410p() {
        AdapterView<?> adapterView = this.f101371k3;
        if (adapterView != null) {
            return adapterView;
        }
        ViewParent parent = getParent();
        while (!(parent instanceof AdapterView)) {
            try {
                parent = parent.getParent();
            } catch (NullPointerException unused) {
                throw new RuntimeException("Could not find a parent AdapterView");
            }
        }
        AdapterView<?> adapterView2 = (AdapterView) parent;
        this.f101371k3 = adapterView2;
        return adapterView2;
    }

    /* renamed from: q */
    private boolean m73411q() {
        for (ViewParent parent = getParent(); parent != null && (parent instanceof ViewGroup); parent = parent.getParent()) {
            if (((ViewGroup) parent).shouldDelayChildPressedState()) {
                return true;
            }
        }
        return false;
    }

    /* renamed from: r */
    public static RippleBuilder m73412r(View view) {
        return new RippleBuilder(view);
    }

    /* renamed from: s */
    private void m73413s() {
        if (this.f101369i3) {
            this.f101380s3 = m73410p().getPositionForView(this);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: t */
    public void m73414t() {
        if (this.f101377q3) {
            return;
        }
        ObjectAnimator objectAnimator = this.f101374n3;
        if (objectAnimator != null) {
            objectAnimator.cancel();
        }
        ObjectAnimator duration = ObjectAnimator.ofFloat(this, this.f101385x3, this.f101362b3, (float) (Math.sqrt(Math.pow(getWidth(), 2.0d) + Math.pow(getHeight(), 2.0d)) * 1.2000000476837158d)).setDuration(f101356L3);
        this.f101374n3 = duration;
        duration.setInterpolator(new LinearInterpolator());
        this.f101374n3.start();
    }

    /* renamed from: u */
    private void m73415u(final Runnable runnable) {
        if (this.f101377q3) {
            return;
        }
        float endRadius = getEndRadius();
        m73406l();
        AnimatorSet animatorSet = new AnimatorSet();
        this.f101373m3 = animatorSet;
        animatorSet.addListener(new AnimatorListenerAdapter() { // from class: net.imedicaldoctor.imd.Utils.MaterialRippleLayout.3
            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationEnd(Animator animator) {
                if (!MaterialRippleLayout.this.f101367g3) {
                    MaterialRippleLayout.this.setRadius(0.0f);
                    MaterialRippleLayout materialRippleLayout = MaterialRippleLayout.this;
                    materialRippleLayout.setRippleAlpha(Integer.valueOf(materialRippleLayout.f101364d3));
                }
                if (runnable != null && MaterialRippleLayout.this.f101365e3) {
                    try {
                        runnable.run();
                    } catch (Exception unused) {
                    }
                }
                MaterialRippleLayout.this.f101372l3.setPressed(false);
            }
        });
        ObjectAnimator objectAnimatorOfFloat = ObjectAnimator.ofFloat(this, this.f101385x3, this.f101370j3, endRadius);
        objectAnimatorOfFloat.setDuration(this.f101363c3);
        objectAnimatorOfFloat.setInterpolator(new DecelerateInterpolator());
        ObjectAnimator objectAnimatorOfInt = ObjectAnimator.ofInt(this, this.f101386y3, this.f101364d3, 0);
        objectAnimatorOfInt.setDuration(this.f101366f3);
        objectAnimatorOfInt.setInterpolator(new AccelerateInterpolator());
        objectAnimatorOfInt.setStartDelay((this.f101363c3 - this.f101366f3) - 50);
        if (this.f101367g3) {
            this.f101373m3.play(objectAnimatorOfFloat);
        } else if (getRadius() > endRadius) {
            objectAnimatorOfInt.setStartDelay(0L);
            this.f101373m3.play(objectAnimatorOfInt);
        } else {
            this.f101373m3.playTogether(objectAnimatorOfFloat, objectAnimatorOfInt);
        }
        this.f101373m3.start();
    }

    @Override // android.view.ViewGroup
    public final void addView(View view, int i2, ViewGroup.LayoutParams layoutParams) {
        if (getChildCount() > 0) {
            throw new IllegalStateException("MaterialRippleLayout can host only one child");
        }
        this.f101372l3 = view;
        super.addView(view, i2, layoutParams);
    }

    @Override // android.view.View
    public void draw(Canvas canvas) {
        boolean zM73405k = m73405k();
        if (!this.f101360Z2) {
            if (!zM73405k) {
                this.f101368h3.draw(canvas);
                Point point = this.f101375o3;
                canvas.drawCircle(point.x, point.y, this.f101370j3, this.f101379s);
            }
            super.draw(canvas);
            return;
        }
        if (!zM73405k) {
            this.f101368h3.draw(canvas);
        }
        super.draw(canvas);
        if (zM73405k) {
            return;
        }
        Point point2 = this.f101375o3;
        canvas.drawCircle(point2.x, point2.y, this.f101370j3, this.f101379s);
    }

    public <T extends View> T getChildView() {
        return (T) this.f101372l3;
    }

    public int getRippleAlpha() {
        return this.f101379s.getAlpha();
    }

    @Override // android.view.View
    public boolean isInEditMode() {
        return true;
    }

    @Override // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        return !m73409o(this.f101372l3, (int) motionEvent.getX(), (int) motionEvent.getY());
    }

    @Override // android.view.View
    protected void onSizeChanged(int i2, int i3, int i4, int i5) {
        super.onSizeChanged(i2, i3, i4, i5);
        this.f101358X2.set(0, 0, i2, i3);
        this.f101368h3.setBounds(this.f101358X2);
    }

    @Override // android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent) {
        boolean zOnTouchEvent = super.onTouchEvent(motionEvent);
        if (!isEnabled() || !this.f101372l3.isEnabled()) {
            return zOnTouchEvent;
        }
        boolean zContains = this.f101358X2.contains((int) motionEvent.getX(), (int) motionEvent.getY());
        if (zContains) {
            Point point = this.f101376p3;
            Point point2 = this.f101375o3;
            point.set(point2.x, point2.y);
            this.f101375o3.set((int) motionEvent.getX(), (int) motionEvent.getY());
        }
        if (this.f101381t3.onTouchEvent(motionEvent)) {
            return true;
        }
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked != 0) {
            if (actionMasked == 1) {
                this.f101382u3 = new PerformClickEvent();
                if (this.f101378r3) {
                    this.f101372l3.setPressed(true);
                    postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Utils.MaterialRippleLayout.1
                        @Override // java.lang.Runnable
                        public void run() {
                            MaterialRippleLayout.this.f101372l3.setPressed(false);
                        }
                    }, ViewConfiguration.getPressedStateDuration());
                }
                if (zContains) {
                    m73415u(this.f101382u3);
                } else if (!this.f101361a3) {
                    setRadius(0.0f);
                }
                if (!this.f101365e3 && zContains) {
                    this.f101382u3.run();
                }
            } else if (actionMasked == 2) {
                if (this.f101361a3) {
                    if (zContains && !this.f101377q3) {
                        invalidate();
                    } else if (!zContains) {
                        m73415u(null);
                    }
                }
                if (!zContains) {
                    m73407m();
                    ObjectAnimator objectAnimator = this.f101374n3;
                    if (objectAnimator != null) {
                        objectAnimator.cancel();
                    }
                    this.f101372l3.onTouchEvent(motionEvent);
                    this.f101377q3 = true;
                }
            } else if (actionMasked == 3) {
                if (this.f101369i3) {
                    Point point3 = this.f101375o3;
                    Point point4 = this.f101376p3;
                    point3.set(point4.x, point4.y);
                    this.f101376p3 = new Point();
                }
                this.f101372l3.onTouchEvent(motionEvent);
                if (!this.f101361a3) {
                    this.f101372l3.setPressed(false);
                } else if (!this.f101378r3) {
                    m73415u(null);
                }
            }
            m73407m();
        } else {
            m73413s();
            this.f101377q3 = false;
            if (m73411q()) {
                m73407m();
                this.f101378r3 = true;
                PressedEvent pressedEvent = new PressedEvent(motionEvent);
                this.f101383v3 = pressedEvent;
                postDelayed(pressedEvent, ViewConfiguration.getTapTimeout());
            } else {
                this.f101372l3.onTouchEvent(motionEvent);
                this.f101372l3.setPressed(true);
                if (this.f101361a3) {
                    m73414t();
                }
            }
        }
        return true;
    }

    public void setDefaultRippleAlpha(int i2) {
        this.f101364d3 = i2;
        this.f101379s.setAlpha(i2);
        invalidate();
    }

    @Override // android.view.View
    public void setOnClickListener(View.OnClickListener onClickListener) {
        View view = this.f101372l3;
        if (view == null) {
            throw new IllegalStateException("MaterialRippleLayout must have a child view to handle clicks");
        }
        view.setOnClickListener(onClickListener);
    }

    public void setRadius(float f2) {
        this.f101370j3 = f2;
        invalidate();
    }

    public void setRippleAlpha(Integer num) {
        this.f101379s.setAlpha(num.intValue());
        invalidate();
    }

    public void setRippleBackground(int i2) {
        ColorDrawable colorDrawable = new ColorDrawable(i2);
        this.f101368h3 = colorDrawable;
        colorDrawable.setBounds(this.f101358X2);
        invalidate();
    }

    public void setRippleColor(int i2) {
        this.f101359Y2 = i2;
        this.f101379s.setColor(i2);
        this.f101379s.setAlpha(this.f101364d3);
        invalidate();
    }

    public void setRippleDelayClick(boolean z) {
        this.f101365e3 = z;
    }

    public void setRippleDiameter(int i2) {
        this.f101362b3 = i2;
    }

    public void setRippleDuration(int i2) {
        this.f101363c3 = i2;
    }

    public void setRippleFadeDuration(int i2) {
        this.f101366f3 = i2;
    }

    public void setRippleHover(boolean z) {
        this.f101361a3 = z;
    }

    public void setRippleInAdapter(boolean z) {
        this.f101369i3 = z;
    }

    public void setRippleOverlay(boolean z) {
        this.f101360Z2 = z;
    }

    public void setRipplePersistent(boolean z) {
        this.f101367g3 = z;
    }

    public MaterialRippleLayout(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public MaterialRippleLayout(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        Paint paint = new Paint(1);
        this.f101379s = paint;
        this.f101358X2 = new Rect();
        this.f101375o3 = new Point();
        this.f101376p3 = new Point();
        GestureDetector.SimpleOnGestureListener simpleOnGestureListener = new GestureDetector.SimpleOnGestureListener() { // from class: net.imedicaldoctor.imd.Utils.MaterialRippleLayout.2
            @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
            public void onLongPress(MotionEvent motionEvent) {
                MaterialRippleLayout.this.f101372l3.performLongClick();
            }
        };
        this.f101384w3 = simpleOnGestureListener;
        this.f101385x3 = new Property<MaterialRippleLayout, Float>(Float.class, "radius") { // from class: net.imedicaldoctor.imd.Utils.MaterialRippleLayout.4
            @Override // android.util.Property
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public Float get(MaterialRippleLayout materialRippleLayout) {
                return Float.valueOf(materialRippleLayout.getRadius());
            }

            @Override // android.util.Property
            /* renamed from: b, reason: merged with bridge method [inline-methods] */
            public void set(MaterialRippleLayout materialRippleLayout, Float f2) {
                materialRippleLayout.setRadius(f2.floatValue());
            }
        };
        this.f101386y3 = new Property<MaterialRippleLayout, Integer>(Integer.class, "rippleAlpha") { // from class: net.imedicaldoctor.imd.Utils.MaterialRippleLayout.5
            @Override // android.util.Property
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public Integer get(MaterialRippleLayout materialRippleLayout) {
                return Integer.valueOf(materialRippleLayout.getRippleAlpha());
            }

            @Override // android.util.Property
            /* renamed from: b, reason: merged with bridge method [inline-methods] */
            public void set(MaterialRippleLayout materialRippleLayout, Integer num) {
                materialRippleLayout.setRippleAlpha(num);
            }
        };
        setWillNotDraw(false);
        this.f101381t3 = new GestureDetector(context, simpleOnGestureListener);
        TypedArray typedArrayObtainStyledAttributes = context.obtainStyledAttributes(attributeSet, C5562R.styleable.f101175wt);
        this.f101359Y2 = typedArrayObtainStyledAttributes.getColor(2, -16777216);
        this.f101362b3 = typedArrayObtainStyledAttributes.getDimensionPixelSize(4, (int) m73408n(getResources(), f101346B3));
        this.f101360Z2 = typedArrayObtainStyledAttributes.getBoolean(9, false);
        this.f101361a3 = typedArrayObtainStyledAttributes.getBoolean(7, true);
        this.f101363c3 = typedArrayObtainStyledAttributes.getInt(5, f101357z3);
        this.f101364d3 = (int) (typedArrayObtainStyledAttributes.getFloat(0, 0.2f) * 255.0f);
        this.f101365e3 = typedArrayObtainStyledAttributes.getBoolean(3, true);
        this.f101366f3 = typedArrayObtainStyledAttributes.getInteger(6, 75);
        this.f101368h3 = new ColorDrawable(typedArrayObtainStyledAttributes.getColor(1, 0));
        this.f101367g3 = typedArrayObtainStyledAttributes.getBoolean(10, false);
        this.f101369i3 = typedArrayObtainStyledAttributes.getBoolean(8, false);
        typedArrayObtainStyledAttributes.recycle();
        paint.setColor(this.f101359Y2);
        paint.setAlpha(this.f101364d3);
    }
}

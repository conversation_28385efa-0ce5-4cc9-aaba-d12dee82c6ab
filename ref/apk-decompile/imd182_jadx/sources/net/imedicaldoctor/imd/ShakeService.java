package net.imedicaldoctor.imd;

import android.app.Service;
import android.content.Intent;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Handler;
import android.os.IBinder;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

/* loaded from: classes3.dex */
public class ShakeService extends Service implements SensorEventListener {

    /* renamed from: X */
    private Sensor f101323X;

    /* renamed from: X2 */
    private float f101324X2;

    /* renamed from: Y */
    private float f101325Y;

    /* renamed from: Z */
    private float f101326Z;

    /* renamed from: s */
    private SensorManager f101327s;

    @Override // android.hardware.SensorEventListener
    public void onAccuracyChanged(Sensor sensor, int i2) {
    }

    @Override // android.app.Service
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override // android.hardware.SensorEventListener
    public void onSensorChanged(SensorEvent sensorEvent) {
        float[] fArr = sensorEvent.values;
        float f2 = fArr[0];
        float f3 = fArr[1];
        float f4 = fArr[2];
        this.f101324X2 = this.f101326Z;
        float fSqrt = (float) Math.sqrt((f2 * f2) + (f3 * f3) + (f4 * f4));
        this.f101326Z = fSqrt;
        float f5 = (this.f101325Y * 0.9f) + (fSqrt - this.f101324X2);
        this.f101325Y = f5;
        if (f5 > 11.0f) {
            LocalBroadcastManager.m16410b(this).m16413d(new Intent("Shake"));
        }
    }

    @Override // android.app.Service
    public int onStartCommand(Intent intent, int i2, int i3) {
        SensorManager sensorManager = (SensorManager) getSystemService("sensor");
        this.f101327s = sensorManager;
        Sensor defaultSensor = sensorManager.getDefaultSensor(1);
        this.f101323X = defaultSensor;
        this.f101327s.registerListener(this, defaultSensor, 2, new Handler());
        return 1;
    }
}

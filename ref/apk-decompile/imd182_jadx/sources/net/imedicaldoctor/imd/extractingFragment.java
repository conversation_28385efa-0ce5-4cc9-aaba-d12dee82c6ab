package net.imedicaldoctor.imd;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import java.util.Timer;
import java.util.TimerTask;

/* loaded from: classes3.dex */
public class extractingFragment extends DialogFragment {

    /* renamed from: F4 */
    private int f101660F4 = 0;

    /* renamed from: i3 */
    static /* synthetic */ int m73533i3(extractingFragment extractingfragment, int i2) {
        int i3 = extractingfragment.f101660F4 + i2;
        extractingfragment.f101660F4 = i3;
        return i3;
    }

    @Override // androidx.fragment.app.DialogFragment
    /* renamed from: U2 */
    public Dialog mo1332U2(Bundle bundle) {
        AlertDialog.Builder builder = new AlertDialog.Builder(m15366r());
        View viewInflate = m15366r().getLayoutInflater().inflate(C5562R.layout.fragment_extracting, (ViewGroup) null);
        m15366r().setFinishOnTouchOutside(false);
        final TextView textView = (TextView) viewInflate.findViewById(C5562R.id.title_text);
        final String string = m15387y().getString("MESSAGE");
        textView.setText(string);
        new Timer().schedule(new TimerTask() { // from class: net.imedicaldoctor.imd.extractingFragment.1
            @Override // java.util.TimerTask, java.lang.Runnable
            public void run() {
                extractingFragment.m73533i3(extractingFragment.this, 1);
                final String str = "";
                for (int i2 = 0; i2 < extractingFragment.this.f101660F4; i2++) {
                    str = str + ".";
                }
                textView.post(new Runnable() { // from class: net.imedicaldoctor.imd.extractingFragment.1.1
                    @Override // java.lang.Runnable
                    public void run() {
                        textView.setText(string + str);
                    }
                });
                if (extractingFragment.this.f101660F4 >= 3) {
                    extractingFragment.this.f101660F4 = 0;
                }
            }
        }, 0L, 1000L);
        builder.setView(viewInflate);
        return builder.create();
    }
}

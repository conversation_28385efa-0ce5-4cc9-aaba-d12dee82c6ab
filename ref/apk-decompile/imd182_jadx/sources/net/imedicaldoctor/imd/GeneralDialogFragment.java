package net.imedicaldoctor.imd;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.Annotation;
import java.io.File;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class GeneralDialogFragment extends DialogFragment {

    /* renamed from: F4 */
    public View f90903F4;

    /* renamed from: G4 */
    public String f90904G4;

    /* renamed from: H4 */
    public Fragment f90905H4;

    public GeneralDialogFragment() {
    }

    @Override // androidx.fragment.app.Fragment
    @Nullable
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        this.f90903F4 = layoutInflater.inflate(C5562R.layout.fragment_container_dialog, (ViewGroup) null);
        try {
            m15208Q2().getWindow().requestFeature(1);
            m15208Q2().getWindow().requestFeature(11);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        String string = this.f90905H4.m15387y().getString("URL");
        this.f90904G4 = string;
        if (string.equals(Annotation.f68285k3)) {
            try {
                this.f90904G4 = CompressHelper.m71750e2(new File(CompressHelper.m71753g1(this.f90905H4.m15387y().getBundle("DB"), "temp.html")));
            } catch (Exception unused) {
            }
        }
        ((Button) this.f90903F4.findViewById(C5562R.id.open_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.GeneralDialogFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                GeneralDialogFragment.this.mo15205N2();
                new CompressHelper(GeneralDialogFragment.this.m15366r()).m71772A1(GeneralDialogFragment.this.f90905H4.m15387y().getBundle("DB"), GeneralDialogFragment.this.f90904G4, null, null);
            }
        });
        ((Button) this.f90903F4.findViewById(C5562R.id.close_button)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.GeneralDialogFragment.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                GeneralDialogFragment.this.mo15205N2();
            }
        });
        this.f90903F4.postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.GeneralDialogFragment.3
            @Override // java.lang.Runnable
            public void run() {
                GeneralDialogFragment.this.m15391z().m15664u().m15803C(C5562R.id.fragment_container, GeneralDialogFragment.this.f90905H4).mo15164r();
            }
        }, 50L);
        return this.f90903F4;
    }

    public GeneralDialogFragment(Fragment fragment) {
        this.f90905H4 = fragment;
    }
}

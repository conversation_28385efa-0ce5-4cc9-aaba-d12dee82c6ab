package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextArrowViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101482I;

    /* renamed from: J */
    public MaterialRippleLayout f101483J;

    /* renamed from: K */
    public ImageView f101484K;

    public RippleTextArrowViewHolder(View view) {
        super(view);
        this.f101482I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101483J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101484K = (ImageView) view.findViewById(C5562R.id.next_icon);
    }
}

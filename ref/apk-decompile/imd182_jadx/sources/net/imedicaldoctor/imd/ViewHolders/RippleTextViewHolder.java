package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101515I;

    /* renamed from: J */
    public MaterialRippleLayout f101516J;

    public RippleTextViewHolder(View view) {
        super(view);
        this.f101515I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101516J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
    }
}

package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class ChaptersSectionAdapter extends RecyclerView.Adapter implements StickyRecyclerHeadersAdapter {

    /* renamed from: d */
    public Context f101438d;

    /* renamed from: e */
    public ArrayList<Bundle> f101439e;

    /* renamed from: f */
    public ArrayList<Bundle> f101440f;

    /* renamed from: g */
    public String f101441g;

    /* renamed from: h */
    public String f101442h;

    public class EmptyViewHolder extends RecyclerView.ViewHolder {
        public EmptyViewHolder(View view) {
            super(view);
        }
    }

    public class SectionHeaderViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f101447I;

        public SectionHeaderViewHolder(Context context, View view) {
            super(view);
            this.f101447I = (TextView) view.findViewById(C5562R.id.text);
        }
    }

    public ChaptersSectionAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2) {
        this.f101438d = context;
        this.f101439e = arrayList;
        this.f101441g = str;
        this.f101442h = str2;
        this.f101440f = new CompressHelper(this.f101438d).m71887r2(this.f101439e, this.f101442h);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
        ArrayList<Bundle> arrayList = this.f101439e;
        if (arrayList == null || arrayList.size() == 0) {
            ((MessageViewHolder) viewHolder).f101463I.setText("Search");
            return;
        }
        RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
        final Bundle bundle = this.f101439e.get(i2);
        rippleTextViewHolder.f101515I.setText(bundle.getString(this.f101441g));
        rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.ChaptersSectionAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                ChaptersSectionAdapter.this.mo72538f0(bundle, i2);
            }
        });
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        ArrayList<Bundle> arrayList = this.f101439e;
        if (arrayList != null && arrayList.size() != 0) {
            return new RippleTextViewHolder(LayoutInflater.from(this.f101438d).inflate(C5562R.layout.list_view_item_ripple_text_arrow, viewGroup, false));
        }
        return new MessageViewHolder(this.f101438d, LayoutInflater.from(this.f101438d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        ArrayList<Bundle> arrayList = this.f101439e;
        if (arrayList == null || arrayList.size() == 0) {
            return 1;
        }
        return this.f101439e.size();
    }

    /* renamed from: d0 */
    public int m73466d0(int i2, ArrayList<Bundle> arrayList) {
        ArrayList<Bundle> arrayList2 = this.f101439e;
        if (arrayList2 != null && arrayList2.size() != 0) {
            int size = 0;
            for (int i3 = 0; i3 < arrayList.size(); i3++) {
                size += arrayList.get(i3).getParcelableArrayList("items").size();
                if (i2 < size) {
                    return i3;
                }
            }
        }
        return 0;
    }

    /* renamed from: e0 */
    public Bundle m73467e0(int i2, ArrayList<Bundle> arrayList) {
        if (arrayList == null) {
            return null;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        int size = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            size += next.getParcelableArrayList("items").size();
            if (i2 < size) {
                int size2 = i2 - (size - next.getParcelableArrayList("items").size());
                Bundle bundle = new Bundle();
                bundle.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                bundle.putString("Title", next.getString("title"));
                return bundle;
            }
        }
        return null;
    }

    /* renamed from: f0 */
    public void mo72538f0(Bundle bundle, int i2) {
    }

    /* renamed from: g0 */
    public void m73468g0(ArrayList<Bundle> arrayList) {
        this.f101439e = arrayList;
        this.f101440f = new CompressHelper(this.f101438d).m71887r2(this.f101439e, this.f101442h);
        m27491G();
    }

    @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
    /* renamed from: o */
    public RecyclerView.ViewHolder mo58201o(ViewGroup viewGroup) {
        ArrayList<Bundle> arrayList = this.f101439e;
        if (arrayList == null || arrayList.size() == 0) {
            return new EmptyViewHolder(LayoutInflater.from(this.f101438d).inflate(C5562R.layout.list_view_item_header_keeper, viewGroup, false));
        }
        return new SectionHeaderViewHolder(this.f101438d, LayoutInflater.from(this.f101438d).inflate(C5562R.layout.list_view_item_section_header, viewGroup, false));
    }

    @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
    /* renamed from: p */
    public void mo58202p(RecyclerView.ViewHolder viewHolder, int i2) {
        ArrayList<Bundle> arrayList = this.f101439e;
        if (arrayList == null || arrayList.size() == 0) {
            return;
        }
        ((SectionHeaderViewHolder) viewHolder).f101447I.setText(m73467e0(i2, this.f101440f).getString("Title"));
    }

    @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
    /* renamed from: r */
    public long mo58203r(int i2) {
        return m73466d0(i2, this.f101440f);
    }
}

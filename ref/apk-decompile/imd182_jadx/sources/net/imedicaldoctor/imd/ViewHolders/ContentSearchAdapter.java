package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class ContentSearchAdapter extends RecyclerView.Adapter {

    /* renamed from: d */
    public Context f101449d;

    /* renamed from: e */
    public ArrayList<Bundle> f101450e;

    /* renamed from: f */
    public String f101451f;

    /* renamed from: g */
    public String f101452g;

    /* renamed from: h */
    public int f101453h;

    public ContentSearchAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2) {
        this.f101449d = context;
        this.f101450e = arrayList;
        this.f101451f = str;
        this.f101452g = str2;
        this.f101453h = C5562R.layout.list_view_item_search_content_ripple;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        ArrayList<Bundle> arrayList = this.f101450e;
        if (arrayList == null || arrayList.size() == 0) {
        } else {
            mo72039d0(viewHolder, i2, this.f101450e.get(i2));
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        ArrayList<Bundle> arrayList = this.f101450e;
        if (arrayList != null && arrayList.size() != 0) {
            return new RippleSearchContentViewHolder(LayoutInflater.from(this.f101449d).inflate(this.f101453h, viewGroup, false));
        }
        return new MessageViewHolder(this.f101449d, LayoutInflater.from(this.f101449d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        ArrayList<Bundle> arrayList = this.f101450e;
        if (arrayList == null || arrayList.size() == 0) {
            return 1;
        }
        return this.f101450e.size();
    }

    /* renamed from: d0 */
    public void mo72039d0(RecyclerView.ViewHolder viewHolder, final int i2, final Bundle bundle) {
        RippleSearchContentViewHolder rippleSearchContentViewHolder = (RippleSearchContentViewHolder) viewHolder;
        rippleSearchContentViewHolder.f101479I.setText(bundle.getString(this.f101451f));
        if (this.f101452g == null) {
            rippleSearchContentViewHolder.f101480J.setVisibility(8);
        } else {
            rippleSearchContentViewHolder.f101480J.setVisibility(0);
            rippleSearchContentViewHolder.f101480J.setText(Html.fromHtml(bundle.getString(this.f101452g)));
        }
        rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.ContentSearchAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                ContentSearchAdapter.this.mo71953e0(bundle, i2);
            }
        });
    }

    /* renamed from: e0 */
    public void mo71953e0(Bundle bundle, int i2) {
    }

    /* renamed from: f0 */
    public void m73469f0(ArrayList<Bundle> arrayList) {
        this.f101450e = arrayList;
        m27491G();
    }

    public ContentSearchAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2, int i2) {
        this.f101449d = context;
        this.f101450e = arrayList;
        this.f101451f = str;
        this.f101452g = str2;
        this.f101453h = i2;
    }
}

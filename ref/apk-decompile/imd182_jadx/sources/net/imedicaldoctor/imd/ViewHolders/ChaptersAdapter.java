package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class ChaptersAdapter extends RecyclerView.Adapter {

    /* renamed from: d */
    public Context f101430d;

    /* renamed from: e */
    public ArrayList<Bundle> f101431e;

    /* renamed from: f */
    public String f101432f;

    /* renamed from: g */
    public int f101433g;

    /* renamed from: h */
    public String f101434h;

    public ChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
        this(context, arrayList, str, C5562R.layout.list_view_item_ripple_text);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        ArrayList<Bundle> arrayList = this.f101431e;
        if (arrayList == null || arrayList.size() == 0) {
            ((MessageViewHolder) viewHolder).f101463I.setText(this.f101434h);
        } else {
            mo71985e0(viewHolder, this.f101431e.get(i2), i2);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        ArrayList<Bundle> arrayList = this.f101431e;
        if (arrayList != null && arrayList.size() != 0) {
            return mo71986h0(LayoutInflater.from(this.f101430d).inflate(this.f101433g, viewGroup, false));
        }
        return new MessageViewHolder(this.f101430d, LayoutInflater.from(this.f101430d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        ArrayList<Bundle> arrayList = this.f101431e;
        if (arrayList == null || arrayList.size() == 0) {
            return 1;
        }
        return this.f101431e.size();
    }

    /* renamed from: d0 */
    public String mo72422d0(String str) {
        return str;
    }

    /* renamed from: e0 */
    public void mo71985e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle, final int i2) {
        RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
        rippleTextViewHolder.f101515I.setText(mo72422d0(bundle.getString(this.f101432f)));
        rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.ChaptersAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                ChaptersAdapter.this.mo71975f0(bundle, i2);
            }
        });
    }

    /* renamed from: f0 */
    public void mo71975f0(Bundle bundle, int i2) {
    }

    /* renamed from: g0 */
    public void m73465g0(ArrayList<Bundle> arrayList) {
        this.f101431e = arrayList;
        m27491G();
    }

    /* renamed from: h0 */
    public RecyclerView.ViewHolder mo71986h0(View view) {
        return new RippleTextViewHolder(view);
    }

    public ChaptersAdapter(Context context, ArrayList<Bundle> arrayList, String str, int i2) {
        this.f101430d = context;
        this.f101431e = arrayList;
        this.f101432f = str;
        this.f101433g = i2;
        this.f101434h = "Nothing";
    }
}

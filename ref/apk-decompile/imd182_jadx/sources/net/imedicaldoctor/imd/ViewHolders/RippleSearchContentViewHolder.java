package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleSearchContentViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101479I;

    /* renamed from: J */
    public TextView f101480J;

    /* renamed from: K */
    public MaterialRippleLayout f101481K;

    public RippleSearchContentViewHolder(View view) {
        super(view);
        this.f101479I = (TextView) view.findViewById(C5562R.id.title_text);
        this.f101480J = (TextView) view.findViewById(C5562R.id.subtitle_text);
        this.f101481K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
    }
}

package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextFullViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101499I;

    /* renamed from: J */
    public TextView f101500J;

    /* renamed from: K */
    public ImageView f101501K;

    /* renamed from: L */
    public ImageView f101502L;

    /* renamed from: M */
    public MaterialRippleLayout f101503M;

    public RippleTextFullViewHolder(View view) {
        super(view);
        this.f101499I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101500J = (TextView) view.findViewById(C5562R.id.sub_text_view);
        this.f101503M = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101501K = (ImageView) view.findViewById(C5562R.id.image_view);
        this.f101502L = (ImageView) view.findViewById(C5562R.id.arrow);
    }
}

package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class StickySectionAdapter extends RecyclerView.Adapter implements StickyRecyclerHeadersAdapter {

    /* renamed from: d */
    public Context f101533d;

    /* renamed from: e */
    public ArrayList<Bundle> f101534e;

    /* renamed from: f */
    public String f101535f;

    /* renamed from: g */
    public int f101536g;

    public StickySectionAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
        this(context, arrayList, str, C5562R.layout.list_view_item_ripple_text);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        m73482g0(viewHolder, m73480e0(i2, this.f101534e).getBundle("Item"), i2);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        return m73485l0(LayoutInflater.from(this.f101533d).inflate(this.f101536g, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        return m73484k0(this.f101534e);
    }

    /* renamed from: d0 */
    public int m73479d0(int i2, ArrayList<Bundle> arrayList) {
        int size = 0;
        for (int i3 = 0; i3 < arrayList.size(); i3++) {
            size += arrayList.get(i3).getParcelableArrayList("items").size();
            if (i2 < size) {
                return i3;
            }
        }
        return 0;
    }

    /* renamed from: e0 */
    public Bundle m73480e0(int i2, ArrayList<Bundle> arrayList) {
        Iterator<Bundle> it2 = arrayList.iterator();
        int size = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            size += next.getParcelableArrayList("items").size();
            if (i2 < size) {
                int size2 = i2 - (size - next.getParcelableArrayList("items").size());
                Bundle bundle = new Bundle();
                bundle.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                bundle.putString("Title", next.getString("title"));
                return bundle;
            }
        }
        return null;
    }

    /* renamed from: f0 */
    public String m73481f0(String str) {
        return str;
    }

    /* renamed from: g0 */
    public void m73482g0(RecyclerView.ViewHolder viewHolder, final Bundle bundle, final int i2) {
        RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
        rippleTextViewHolder.f101515I.setText(m73481f0(bundle.getString(this.f101535f)));
        rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.StickySectionAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                StickySectionAdapter.this.mo72190h0(bundle, i2);
            }
        });
    }

    /* renamed from: h0 */
    public void mo72190h0(Bundle bundle, int i2) {
    }

    /* renamed from: i0 */
    public void m73483i0(ArrayList<Bundle> arrayList) {
        this.f101534e = arrayList;
        m27491G();
    }

    /* renamed from: j0 */
    public String mo72206j0(String str) {
        return str;
    }

    /* renamed from: k0 */
    public int m73484k0(ArrayList<Bundle> arrayList) {
        int size = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            size += it2.next().getParcelableArrayList("items").size();
        }
        return size;
    }

    /* renamed from: l0 */
    public RecyclerView.ViewHolder m73485l0(View view) {
        return new RippleTextViewHolder(view);
    }

    @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
    /* renamed from: o */
    public RecyclerView.ViewHolder mo58201o(ViewGroup viewGroup) {
        return new SpellHeaderViewHolder(LayoutInflater.from(this.f101533d).inflate(C5562R.layout.list_view_item_spell_header, viewGroup, false));
    }

    @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
    /* renamed from: p */
    public void mo58202p(RecyclerView.ViewHolder viewHolder, int i2) {
        SpellHeaderViewHolder spellHeaderViewHolder = (SpellHeaderViewHolder) viewHolder;
        ArrayList<Bundle> arrayList = this.f101534e;
        if (arrayList == null) {
            return;
        }
        spellHeaderViewHolder.f101517I.setText(mo72206j0(m73480e0(i2, arrayList).getString("Title")));
    }

    @Override // com.timehop.stickyheadersrecyclerview.StickyRecyclerHeadersAdapter
    /* renamed from: r */
    public long mo58203r(int i2) {
        return m73479d0(i2, this.f101534e);
    }

    public StickySectionAdapter(Context context, ArrayList<Bundle> arrayList, String str, int i2) {
        this.f101533d = context;
        this.f101534e = arrayList;
        this.f101535f = str;
        this.f101536g = i2;
    }
}

package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextImageArrowViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101508I;

    /* renamed from: J */
    public ImageView f101509J;

    /* renamed from: K */
    public ImageView f101510K;

    /* renamed from: L */
    public MaterialRippleLayout f101511L;

    public RippleTextImageArrowViewHolder(View view) {
        super(view);
        this.f101508I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101511L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101509J = (ImageView) view.findViewById(C5562R.id.image_view);
        this.f101510K = (ImageView) view.findViewById(C5562R.id.arrow);
    }
}

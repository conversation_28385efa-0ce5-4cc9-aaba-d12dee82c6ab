package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Iterator;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class NotStickySectionAdapter extends RecyclerView.Adapter {

    /* renamed from: d */
    public Context f101465d;

    /* renamed from: e */
    public ArrayList<Bundle> f101466e;

    /* renamed from: f */
    public String f101467f;

    /* renamed from: g */
    public int f101468g;

    /* renamed from: h */
    public int f101469h;

    /* renamed from: i */
    public String f101470i;

    public static class HeaderCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f101474I;

        public HeaderCellViewHolder(View view) {
            super(view);
            this.f101474I = (TextView) view.findViewById(C5562R.id.header_text);
        }
    }

    public NotStickySectionAdapter(Context context, ArrayList<Bundle> arrayList, String str) {
        this(context, arrayList, str, C5562R.layout.list_view_item_ripple_text);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: C */
    public int mo26845C(int i2) {
        ArrayList<Bundle> arrayList = this.f101466e;
        if (arrayList == null || arrayList.size() == 0) {
            return 2;
        }
        return m73472d0(i2, this.f101466e).containsKey("Title") ? 1 : 0;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        TextView textView;
        String strMo72201i0;
        int iM27811F = viewHolder.m27811F();
        if (iM27811F == 0) {
            Bundle bundleM73472d0 = m73472d0(i2, this.f101466e);
            int i3 = bundleM73472d0.containsKey("Index") ? bundleM73472d0.getInt("Index") : -2;
            Bundle bundle = bundleM73472d0.getBundle("Item");
            if (i3 > -2) {
                bundle.putInt("Index", i3);
            }
            mo72200f0(viewHolder, bundle, i2);
            return;
        }
        if (iM27811F == 1) {
            Bundle bundleM73472d02 = m73472d0(i2, this.f101466e);
            textView = ((HeaderCellViewHolder) viewHolder).f101474I;
            strMo72201i0 = mo72201i0(bundleM73472d02.getString("Title"));
        } else {
            if (iM27811F != 2) {
                return;
            }
            textView = ((MessageViewHolder) viewHolder).f101463I;
            strMo72201i0 = this.f101470i;
        }
        textView.setText(strMo72201i0);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        if (i2 == 0) {
            return mo72202k0(LayoutInflater.from(this.f101465d).inflate(this.f101468g, viewGroup, false));
        }
        if (i2 == 1) {
            return new HeaderCellViewHolder(LayoutInflater.from(this.f101465d).inflate(this.f101469h, viewGroup, false));
        }
        if (i2 != 2) {
            return null;
        }
        return new MessageViewHolder(this.f101465d, LayoutInflater.from(this.f101465d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        ArrayList<Bundle> arrayList = this.f101466e;
        if (arrayList == null || arrayList.size() == 0) {
            return 1;
        }
        return m73476j0(this.f101466e);
    }

    /* renamed from: d0 */
    public Bundle m73472d0(int i2, ArrayList<Bundle> arrayList) {
        Iterator<Bundle> it2 = arrayList.iterator();
        int i3 = 0;
        int i4 = 0;
        while (it2.hasNext()) {
            Bundle next = it2.next();
            if (i2 == i3) {
                Bundle bundle = new Bundle();
                bundle.putString("Title", next.getString("title"));
                bundle.putInt("Row", 0);
                bundle.putInt("Section", i4);
                bundle.putInt("Row2", 1);
                bundle.putInt("Section2", i4 - 1);
                return bundle;
            }
            int size = i3 + next.getParcelableArrayList("items").size();
            if (i2 <= size) {
                int size2 = (i2 - (size - next.getParcelableArrayList("items").size())) - 1;
                Bundle bundle2 = new Bundle();
                bundle2.putBundle("Item", (Bundle) next.getParcelableArrayList("items").get(size2));
                bundle2.putInt("Row", size2);
                bundle2.putInt("Index", size2);
                bundle2.putInt("Section", i4);
                return bundle2;
            }
            i3 = size + 1;
            i4++;
        }
        return null;
    }

    /* renamed from: e0 */
    public String m73473e0(String str) {
        return str;
    }

    /* renamed from: f0 */
    public void mo72200f0(RecyclerView.ViewHolder viewHolder, final Bundle bundle, final int i2) {
        RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
        rippleTextViewHolder.f101515I.setText(m73473e0(bundle.getString(this.f101467f)));
        rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.NotStickySectionAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                NotStickySectionAdapter.this.m73474g0(bundle, i2);
            }
        });
    }

    /* renamed from: g0 */
    public void m73474g0(Bundle bundle, int i2) {
    }

    /* renamed from: h0 */
    public void m73475h0(ArrayList<Bundle> arrayList) {
        this.f101466e = arrayList;
        m27491G();
    }

    /* renamed from: i0 */
    public String mo72201i0(String str) {
        return str;
    }

    /* renamed from: j0 */
    public int m73476j0(ArrayList<Bundle> arrayList) {
        int size = 0;
        if (arrayList == null) {
            return 0;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            size = size + it2.next().getParcelableArrayList("items").size() + 1;
        }
        return size;
    }

    /* renamed from: k0 */
    public RecyclerView.ViewHolder mo72202k0(View view) {
        return new RippleTextViewHolder(view);
    }

    public NotStickySectionAdapter(Context context, ArrayList<Bundle> arrayList, String str, int i2) {
        this(context, arrayList, str, i2, C5562R.layout.list_view_item_database_card_header);
    }

    public NotStickySectionAdapter(Context context, ArrayList<Bundle> arrayList, String str, int i2, int i3) {
        this.f101465d = context;
        this.f101466e = arrayList;
        this.f101467f = str;
        this.f101468g = i2;
        this.f101469h = i3;
        this.f101470i = "Nothing";
    }
}

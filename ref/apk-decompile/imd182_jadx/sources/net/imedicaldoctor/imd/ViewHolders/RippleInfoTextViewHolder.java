package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleInfoTextViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101475I;

    /* renamed from: J */
    public ImageView f101476J;

    /* renamed from: K */
    public MaterialRippleLayout f101477K;

    /* renamed from: L */
    public ImageView f101478L;

    public RippleInfoTextViewHolder(View view) {
        super(view);
        this.f101475I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101477K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101476J = (ImageView) view.findViewById(C5562R.id.info_button);
        this.f101478L = (ImageView) view.findViewById(C5562R.id.next_icon);
    }
}

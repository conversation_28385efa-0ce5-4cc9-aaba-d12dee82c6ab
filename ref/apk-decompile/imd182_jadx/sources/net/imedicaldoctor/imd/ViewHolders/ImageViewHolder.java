package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class ImageViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public ImageView f101461I;

    /* renamed from: J */
    public MaterialRippleLayout f101462J;

    public ImageViewHolder(View view) {
        super(view);
        this.f101461I = (ImageView) view.findViewById(C5562R.id.image_view);
        this.f101462J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
    }
}

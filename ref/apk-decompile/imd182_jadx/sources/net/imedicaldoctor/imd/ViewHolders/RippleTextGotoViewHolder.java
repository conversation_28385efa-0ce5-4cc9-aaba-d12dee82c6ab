package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextGotoViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101504I;

    /* renamed from: J */
    public MaterialRippleLayout f101505J;

    /* renamed from: K */
    public ImageView f101506K;

    /* renamed from: L */
    public ImageView f101507L;

    public RippleTextGotoViewHolder(View view) {
        super(view);
        this.f101504I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101505J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101506K = (ImageView) view.findViewById(C5562R.id.info_button);
        this.f101507L = (ImageView) view.findViewById(C5562R.id.next_icon);
    }
}

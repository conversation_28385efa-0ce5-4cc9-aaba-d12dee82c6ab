package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextFullDeleteViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101493I;

    /* renamed from: J */
    public TextView f101494J;

    /* renamed from: K */
    public ImageView f101495K;

    /* renamed from: L */
    public ImageView f101496L;

    /* renamed from: M */
    public MaterialRippleLayout f101497M;

    /* renamed from: N */
    public ImageView f101498N;

    public RippleTextFullDeleteViewHolder(View view) {
        super(view);
        this.f101493I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101494J = (TextView) view.findViewById(C5562R.id.sub_text_view);
        this.f101497M = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101495K = (ImageView) view.findViewById(C5562R.id.image_view);
        this.f101496L = (ImageView) view.findViewById(C5562R.id.arrow);
        this.f101498N = (ImageView) view.findViewById(C5562R.id.remove_icon);
    }
}

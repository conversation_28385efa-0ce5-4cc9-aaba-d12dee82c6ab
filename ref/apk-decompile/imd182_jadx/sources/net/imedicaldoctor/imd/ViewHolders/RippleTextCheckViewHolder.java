package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextCheckViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101485I;

    /* renamed from: J */
    public MaterialRippleLayout f101486J;

    /* renamed from: K */
    public ImageView f101487K;

    public RippleTextCheckViewHolder(View view) {
        super(view);
        this.f101485I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101486J = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101487K = (ImageView) view.findViewById(C5562R.id.check_icon);
    }
}

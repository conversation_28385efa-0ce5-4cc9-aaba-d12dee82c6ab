package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextCircleViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101488I;

    /* renamed from: J */
    public TextView f101489J;

    /* renamed from: K */
    public ImageView f101490K;

    /* renamed from: L */
    public ImageView f101491L;

    /* renamed from: M */
    public MaterialRippleLayout f101492M;

    public RippleTextCircleViewHolder(View view) {
        super(view);
        this.f101488I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101492M = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101490K = (ImageView) view.findViewById(C5562R.id.image_view);
        this.f101491L = (ImageView) view.findViewById(C5562R.id.arrow);
        this.f101489J = (TextView) view.findViewById(C5562R.id.text_number);
    }
}

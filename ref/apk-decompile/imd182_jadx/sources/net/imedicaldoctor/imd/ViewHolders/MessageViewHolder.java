package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.graphics.Typeface;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class MessageViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101463I;

    /* renamed from: J */
    public ImageView f101464J;

    public MessageViewHolder(Context context, View view) {
        super(view);
        this.f101463I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101464J = (ImageView) view.findViewById(C5562R.id.image_view);
        this.f101463I.setTypeface(Typeface.createFromAsset(context.getAssets(), "fonts/HelveticaNeue-Light.otf"));
    }
}

package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class StatusAdapter extends RecyclerView.Adapter {

    /* renamed from: d */
    public Context f101530d;

    /* renamed from: e */
    public String f101531e;

    public StatusAdapter(Context context, String str) {
        this.f101530d = context;
        this.f101531e = str;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        MessageViewHolder messageViewHolder = (MessageViewHolder) viewHolder;
        messageViewHolder.f101463I.setText(this.f101531e);
        messageViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.StatusAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                StatusAdapter.this.mo73158d0();
            }
        });
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        return new MessageViewHolder(this.f101530d, LayoutInflater.from(this.f101530d).inflate(C5562R.layout.list_view_item_card_status, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        return 1;
    }

    /* renamed from: d0 */
    public void mo73158d0() {
    }
}

package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.util.TypedValue;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/* loaded from: classes3.dex */
public class GridAutoFitLayoutManager extends GridLayoutManager {

    /* renamed from: a0 */
    private int f101457a0;

    /* renamed from: b0 */
    public int f101458b0;

    /* renamed from: c0 */
    private boolean f101459c0;

    public GridAutoFitLayoutManager(Context context, int i2) {
        super(context, 1);
        this.f101459c0 = true;
        m73471V3(m73470U3(context, i2));
    }

    /* renamed from: U3 */
    private int m73470U3(Context context, int i2) {
        return i2 <= 0 ? (int) TypedValue.applyDimension(1, 48.0f, context.getResources().getDisplayMetrics()) : i2;
    }

    /* renamed from: V3 */
    public void m73471V3(int i2) {
        if (i2 <= 0 || i2 == this.f101457a0) {
            return;
        }
        this.f101457a0 = i2;
        this.f101459c0 = true;
    }

    @Override // androidx.recyclerview.widget.GridLayoutManager, androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.LayoutManager
    /* renamed from: s1 */
    public void mo27050s1(RecyclerView.Recycler recycler, RecyclerView.State state) {
        int iM27640j0;
        int iM27657q0;
        if (this.f101459c0 && this.f101457a0 > 0) {
            if (m27185Q2() == 1) {
                iM27640j0 = m27563D0() - m27662t0();
                iM27657q0 = m27661s0();
            } else {
                iM27640j0 = m27640j0() - m27665v0();
                iM27657q0 = m27657q0();
            }
            int iMax = Math.max(1, (iM27640j0 - iM27657q0) / this.f101457a0);
            m27029Q3(iMax);
            this.f101458b0 = iMax;
            this.f101459c0 = false;
        }
        super.mo27050s1(recycler, state);
    }

    public GridAutoFitLayoutManager(Context context, int i2, int i3, boolean z) {
        super(context, 1, i3, z);
        this.f101459c0 = true;
        m73471V3(m73470U3(context, i2));
    }
}

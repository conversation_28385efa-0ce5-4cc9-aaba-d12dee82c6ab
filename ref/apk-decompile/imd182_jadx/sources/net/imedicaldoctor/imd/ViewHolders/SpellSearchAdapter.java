package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class SpellSearchAdapter extends RecyclerView.Adapter {

    /* renamed from: d */
    public Context f101518d;

    /* renamed from: e */
    public ArrayList<Bundle> f101519e;

    /* renamed from: f */
    public ArrayList<Bundle> f101520f;

    /* renamed from: g */
    public String f101521g;

    /* renamed from: h */
    public String f101522h;

    /* renamed from: i */
    public int f101523i;

    public SpellSearchAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2) {
        this(context, arrayList, str, str2, C5562R.layout.list_view_item_search_content_ripple);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: C */
    public int mo26845C(int i2) {
        if (m73477f0() == 0) {
            return 0;
        }
        ArrayList<Bundle> arrayList = this.f101519e;
        if (arrayList == null || arrayList.size() <= 0) {
            return i2 == 0 ? 3 : 4;
        }
        if (i2 == 0) {
            return 1;
        }
        if (i2 < this.f101519e.size() + 1) {
            return 2;
        }
        return i2 == this.f101519e.size() + 1 ? 3 : 4;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        TextView textView;
        String str;
        if (m73477f0() == 0) {
            return;
        }
        int iM27811F = viewHolder.m27811F();
        if (iM27811F == 1) {
            textView = ((SpellHeaderViewHolder) viewHolder).f101517I;
            str = "Matched";
        } else {
            if (iM27811F == 2) {
                int i3 = i2 - 1;
                mo72195e0(viewHolder, this.f101519e.get(i3), i3);
                return;
            }
            if (iM27811F != 3) {
                if (iM27811F == 4) {
                    SpellTextViewHolder spellTextViewHolder = (SpellTextViewHolder) viewHolder;
                    ArrayList<Bundle> arrayList = this.f101519e;
                    int size = i2 - (((arrayList == null || arrayList.size() <= 0) ? 0 : this.f101519e.size() + 1) + 1);
                    ArrayList<Bundle> arrayList2 = this.f101520f;
                    if (arrayList2 != null && size <= arrayList2.size() - 1) {
                        final Bundle bundle = this.f101520f.get(size);
                        spellTextViewHolder.f101529I.setText(bundle.getString("word"));
                        spellTextViewHolder.f33076a.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter.2
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                SpellSearchAdapter.this.mo71977h0(bundle);
                            }
                        });
                        return;
                    }
                    return;
                }
                return;
            }
            textView = ((SpellHeaderViewHolder) viewHolder).f101517I;
            str = "Did you mean";
        }
        textView.setText(str);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        if (i2 == 0) {
            return new MessageViewHolder(this.f101518d, LayoutInflater.from(this.f101518d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
        }
        if (i2 == 1) {
            return new SpellHeaderViewHolder(LayoutInflater.from(this.f101518d).inflate(C5562R.layout.list_view_item_spell_header, viewGroup, false));
        }
        if (i2 == 2) {
            return mo72196j0(LayoutInflater.from(this.f101518d).inflate(this.f101523i, viewGroup, false));
        }
        if (i2 == 3) {
            return new SpellHeaderViewHolder(LayoutInflater.from(this.f101518d).inflate(C5562R.layout.list_view_item_spell_header, viewGroup, false));
        }
        if (i2 == 4) {
            return new SpellTextViewHolder(LayoutInflater.from(this.f101518d).inflate(C5562R.layout.list_view_item_search_spell, viewGroup, false));
        }
        return null;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        int iM73477f0 = m73477f0();
        if (iM73477f0 == 0) {
            return 1;
        }
        return iM73477f0 + 2;
    }

    /* renamed from: d0 */
    public String mo72930d0(String str) {
        return str;
    }

    /* renamed from: e0 */
    public void mo72195e0(RecyclerView.ViewHolder viewHolder, final Bundle bundle, final int i2) {
        RippleSearchContentViewHolder rippleSearchContentViewHolder = (RippleSearchContentViewHolder) viewHolder;
        rippleSearchContentViewHolder.f101479I.setText(mo72930d0(bundle.getString(this.f101521g)));
        if (this.f101522h == null) {
            rippleSearchContentViewHolder.f101480J.setVisibility(8);
        } else {
            rippleSearchContentViewHolder.f101480J.setVisibility(0);
            rippleSearchContentViewHolder.f101480J.setText(Html.fromHtml(bundle.getString(this.f101522h)));
        }
        rippleSearchContentViewHolder.f101481K.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.SpellSearchAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                SpellSearchAdapter.this.mo71976g0(bundle, i2);
            }
        });
    }

    /* renamed from: f0 */
    public int m73477f0() {
        ArrayList<Bundle> arrayList = this.f101519e;
        int size = arrayList != null ? arrayList.size() : 0;
        ArrayList<Bundle> arrayList2 = this.f101520f;
        return arrayList2 != null ? size + arrayList2.size() : size;
    }

    /* renamed from: g0 */
    public void mo71976g0(Bundle bundle, int i2) {
    }

    /* renamed from: h0 */
    public void mo71977h0(Bundle bundle) {
    }

    /* renamed from: i0 */
    public void m73478i0(ArrayList<Bundle> arrayList, ArrayList<Bundle> arrayList2) {
        this.f101519e = arrayList;
        this.f101520f = arrayList2;
        m27491G();
    }

    /* renamed from: j0 */
    public RecyclerView.ViewHolder mo72196j0(View view) {
        return new RippleSearchContentViewHolder(view);
    }

    public SpellSearchAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2, int i2) {
        this.f101518d = context;
        this.f101519e = arrayList;
        this.f101521g = str;
        this.f101522h = str2;
        this.f101523i = i2;
        this.f101520f = new ArrayList<>();
    }
}

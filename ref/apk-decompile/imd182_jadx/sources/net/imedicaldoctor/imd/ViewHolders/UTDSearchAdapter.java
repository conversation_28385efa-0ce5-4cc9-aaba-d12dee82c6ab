package net.imedicaldoctor.imd.ViewHolders;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class UTDSearchAdapter extends RecyclerView.Adapter {

    /* renamed from: d */
    public Context f101540d;

    /* renamed from: e */
    public ArrayList<Bundle> f101541e;

    /* renamed from: f */
    public String f101542f;

    /* renamed from: g */
    public String f101543g;

    public UTDSearchAdapter(Context context, ArrayList<Bundle> arrayList, String str, String str2) {
        this.f101540d = context;
        this.f101541e = arrayList;
        this.f101542f = str;
        this.f101543g = str2;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, final int i2) {
        ArrayList<Bundle> arrayList = this.f101541e;
        if (arrayList == null || arrayList.size() == 0) {
            return;
        }
        RippleTextViewHolder rippleTextViewHolder = (RippleTextViewHolder) viewHolder;
        final Bundle bundle = this.f101541e.get(i2);
        rippleTextViewHolder.f101515I.setText(bundle.getString(this.f101542f));
        rippleTextViewHolder.f101516J.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.ViewHolders.UTDSearchAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                UTDSearchAdapter.this.mo72733d0(bundle, i2);
            }
        });
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        ArrayList<Bundle> arrayList = this.f101541e;
        if (arrayList != null && arrayList.size() != 0) {
            return new RippleTextViewHolder(LayoutInflater.from(this.f101540d).inflate(C5562R.layout.list_view_item_ripple_text, viewGroup, false));
        }
        return new MessageViewHolder(this.f101540d, LayoutInflater.from(this.f101540d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        ArrayList<Bundle> arrayList = this.f101541e;
        if (arrayList == null || arrayList.size() == 0) {
            return 1;
        }
        return this.f101541e.size();
    }

    /* renamed from: d0 */
    public void mo72733d0(Bundle bundle, int i2) {
    }

    /* renamed from: e0 */
    public void m73486e0(ArrayList<Bundle> arrayList) {
        this.f101541e = arrayList;
        m27491G();
    }
}

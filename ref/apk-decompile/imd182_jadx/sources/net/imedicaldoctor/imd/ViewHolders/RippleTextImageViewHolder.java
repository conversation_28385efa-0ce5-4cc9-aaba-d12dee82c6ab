package net.imedicaldoctor.imd.ViewHolders;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;

/* loaded from: classes3.dex */
public class RippleTextImageViewHolder extends RecyclerView.ViewHolder {

    /* renamed from: I */
    public TextView f101512I;

    /* renamed from: J */
    public ImageView f101513J;

    /* renamed from: K */
    public MaterialRippleLayout f101514K;

    public RippleTextImageViewHolder(View view) {
        super(view);
        this.f101512I = (TextView) view.findViewById(C5562R.id.text_view);
        this.f101514K = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
        this.f101513J = (ImageView) view.findViewById(C5562R.id.image_view);
    }
}

package net.imedicaldoctor.imd;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.webkit.WebView;
import androidx.core.view.MotionEventCompat;
import androidx.core.view.NestedScrollingChild;
import androidx.core.view.NestedScrollingChildHelper;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

/* loaded from: classes3.dex */
public class NestedScrollWebView extends WebView implements NestedScrollingChild {

    /* renamed from: b3 */
    public static final String f90912b3 = "NestedScrollWebView";

    /* renamed from: X2 */
    private final int[] f90913X2;

    /* renamed from: Y2 */
    private final int[] f90914Y2;

    /* renamed from: Z2 */
    private int f90915Z2;

    /* renamed from: a3 */
    private final NestedScrollingChildHelper f90916a3;

    /* renamed from: s */
    private int f90917s;

    public NestedScrollWebView(Context context) {
        this(context, null);
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public boolean dispatchNestedFling(float f2, float f3, boolean z) {
        return this.f90916a3.m8947a(f2, f3, z);
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public boolean dispatchNestedPreFling(float f2, float f3) {
        return this.f90916a3.m8948b(f2, f3);
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public boolean dispatchNestedPreScroll(int i2, int i3, int[] iArr, int[] iArr2) {
        return this.f90916a3.m8949c(i2, i3, iArr, iArr2);
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public boolean dispatchNestedScroll(int i2, int i3, int i4, int i5, int[] iArr) {
        return this.f90916a3.m8952f(i2, i3, i4, i5, iArr);
    }

    /* renamed from: e */
    public boolean m73386e() {
        return getContext().getSharedPreferences("default_preferences", 0).getBoolean("NestedScroll", true);
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public boolean hasNestedScrollingParent() {
        return this.f90916a3.m8954k();
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public boolean isNestedScrollingEnabled() {
        if (m73386e()) {
            return this.f90916a3.m8956m();
        }
        return false;
    }

    @Override // android.webkit.WebView, android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent) {
        if (!m73386e()) {
            return super.onTouchEvent(motionEvent);
        }
        try {
            MotionEvent motionEventObtain = MotionEvent.obtain(motionEvent);
            int iM8925c = MotionEventCompat.m8925c(motionEvent);
            if (iM8925c == 0) {
                this.f90915Z2 = 0;
            }
            int y = (int) motionEvent.getY();
            motionEvent.offsetLocation(0.0f, this.f90915Z2);
            if (iM8925c != 0) {
                if (iM8925c != 1) {
                    if (iM8925c == 2) {
                        int i2 = this.f90917s - y;
                        if (dispatchNestedPreScroll(0, i2, this.f90914Y2, this.f90913X2)) {
                            i2 -= this.f90914Y2[1];
                            motionEventObtain.offsetLocation(0.0f, this.f90913X2[1]);
                            this.f90915Z2 += this.f90913X2[1];
                        }
                        int scrollY = getScrollY();
                        this.f90917s = y - this.f90913X2[1];
                        if (i2 < 0) {
                            int iMax = Math.max(0, scrollY + i2);
                            int i3 = i2 - (iMax - scrollY);
                            if (dispatchNestedScroll(0, iMax - i3, 0, i3, this.f90913X2)) {
                                int i4 = this.f90917s;
                                int i5 = this.f90913X2[1];
                                this.f90917s = i4 - i5;
                                motionEventObtain.offsetLocation(0.0f, i5);
                                this.f90915Z2 += this.f90913X2[1];
                            }
                        }
                        motionEventObtain.recycle();
                        return super.onTouchEvent(motionEventObtain);
                    }
                    if (iM8925c != 3) {
                        return false;
                    }
                }
                stopNestedScroll();
            } else {
                this.f90917s = y;
                startNestedScroll(2);
            }
            return super.onTouchEvent(motionEvent);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            try {
                return super.onTouchEvent(motionEvent);
            } catch (Exception e3) {
                e3.printStackTrace();
                return false;
            }
        }
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public void setNestedScrollingEnabled(boolean z) {
        if (m73386e()) {
            this.f90916a3.m8959p(z);
        }
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public boolean startNestedScroll(int i2) {
        return this.f90916a3.m8960r(i2);
    }

    @Override // android.view.View, androidx.core.view.NestedScrollingChild
    public void stopNestedScroll() {
        this.f90916a3.m8962t();
    }

    public NestedScrollWebView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public NestedScrollWebView(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        this.f90913X2 = new int[2];
        this.f90914Y2 = new int[2];
        this.f90916a3 = new NestedScrollingChildHelper(this);
        setNestedScrollingEnabled(true);
    }
}

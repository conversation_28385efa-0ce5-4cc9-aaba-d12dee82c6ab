package net.imedicaldoctor.imd.Views;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.RelativeLayout;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class HighlightButton extends ButtonFloat {
    public HighlightButton(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f101588r3 = 20;
        this.f101587q3 = 20;
        mo73503c();
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(Utils.m73528a(this.f101587q3, getResources()), Utils.m73528a(this.f101587q3, getResources()));
        layoutParams.addRule(13, -1);
        this.f101589s3.setLayoutParams(layoutParams);
    }

    @Override // net.imedicaldoctor.imd.Views.ButtonFloat, net.imedicaldoctor.imd.Views.Button
    /* renamed from: c */
    protected void mo73503c() {
        this.f101575f3 = Utils.m73528a(2.0f, getResources());
        this.f101576g3 = 10;
        setMinimumHeight(Utils.m73528a(this.f101588r3 * 2, getResources()));
        setMinimumWidth(Utils.m73528a(this.f101588r3 * 2, getResources()));
        mo73500h();
    }

    /* renamed from: h */
    public void mo73500h() {
        setBackgroundResource(C5562R.drawable.background_button_float);
        setRippleColor(getResources().getColor(this.f101577h3.intValue()));
    }
}

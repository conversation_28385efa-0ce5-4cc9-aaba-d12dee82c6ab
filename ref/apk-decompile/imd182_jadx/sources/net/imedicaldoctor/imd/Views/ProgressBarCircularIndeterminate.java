package net.imedicaldoctor.imd.Views;

import android.R;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.util.AttributeSet;
import com.itextpdf.text.pdf.codec.TIFFConstants;
import com.itextpdf.tool.xml.css.CSS;

/* loaded from: classes3.dex */
public class ProgressBarCircularIndeterminate extends CustomView {

    /* renamed from: l3 */
    static final String f101632l3 = "http://schemas.android.com/apk/res/android";

    /* renamed from: c3 */
    int f101633c3;

    /* renamed from: d3 */
    float f101634d3;

    /* renamed from: e3 */
    float f101635e3;

    /* renamed from: f3 */
    int f101636f3;

    /* renamed from: g3 */
    boolean f101637g3;

    /* renamed from: h3 */
    int f101638h3;

    /* renamed from: i3 */
    int f101639i3;

    /* renamed from: j3 */
    float f101640j3;

    /* renamed from: k3 */
    int f101641k3;

    public ProgressBarCircularIndeterminate(Context context, AttributeSet attributeSet) throws Resources.NotFoundException {
        super(context, attributeSet);
        this.f101633c3 = Color.parseColor("#1E88E5");
        this.f101634d3 = 0.0f;
        this.f101635e3 = 0.0f;
        this.f101636f3 = 0;
        this.f101637g3 = false;
        this.f101638h3 = 1;
        this.f101639i3 = 0;
        this.f101640j3 = 0.0f;
        this.f101641k3 = 0;
        setAttributes(attributeSet);
    }

    /* JADX WARN: Removed duplicated region for block: B:14:0x00c5  */
    /* renamed from: a */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void m73514a(android.graphics.Canvas r11) {
        /*
            Method dump skipped, instructions count: 313
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Views.ProgressBarCircularIndeterminate.m73514a(android.graphics.Canvas):void");
    }

    /* renamed from: b */
    private void m73515b(Canvas canvas) {
        int i2 = this.f101639i3;
        int i3 = this.f101641k3;
        if (i2 == i3) {
            this.f101638h3 += 6;
        }
        int i4 = this.f101638h3;
        if (i4 >= 290 || i2 > i3) {
            this.f101639i3 = i2 + 6;
            this.f101638h3 = i4 - 6;
        }
        int i5 = this.f101639i3;
        if (i5 > i3 + TIFFConstants.f72452G0) {
            this.f101641k3 = i5;
            this.f101639i3 = i5;
            this.f101638h3 = 1;
        }
        float f2 = this.f101640j3 + 4.0f;
        this.f101640j3 = f2;
        canvas.rotate(f2, getWidth() / 2, getHeight() / 2);
        Bitmap bitmapCreateBitmap = Bitmap.createBitmap(canvas.getWidth(), canvas.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas2 = new Canvas(bitmapCreateBitmap);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(this.f101633c3);
        canvas2.drawArc(new RectF(0.0f, 0.0f, getWidth(), getHeight()), this.f101639i3, this.f101638h3, true, paint);
        Paint paint2 = new Paint();
        paint2.setAntiAlias(true);
        paint2.setColor(getResources().getColor(R.color.transparent));
        paint2.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
        canvas2.drawCircle(getWidth() / 2, getHeight() / 2, (getWidth() / 2) - Utils.m73528a(4.0f, getResources()), paint2);
        canvas.drawBitmap(bitmapCreateBitmap, 0.0f, 0.0f, new Paint());
    }

    /* renamed from: c */
    protected int m73516c() {
        int i2 = this.f101633c3;
        return Color.argb(128, (i2 >> 16) & 255, (i2 >> 8) & 255, i2 & 255);
    }

    @Override // net.imedicaldoctor.imd.Views.CustomView, android.view.View
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!this.f101637g3) {
            m73514a(canvas);
        }
        if (this.f101636f3 > 0) {
            m73515b(canvas);
        }
        invalidate();
    }

    protected void setAttributes(AttributeSet attributeSet) throws Resources.NotFoundException {
        int attributeIntValue;
        setMinimumHeight(Utils.m73528a(32.0f, getResources()));
        setMinimumWidth(Utils.m73528a(32.0f, getResources()));
        int attributeResourceValue = attributeSet.getAttributeResourceValue(f101632l3, CSS.Property.f74018a, -1);
        if (attributeResourceValue != -1) {
            attributeIntValue = getResources().getColor(attributeResourceValue);
        } else {
            attributeIntValue = attributeSet.getAttributeIntValue(f101632l3, CSS.Property.f74018a, -1);
            if (attributeIntValue == -1) {
                attributeIntValue = Color.parseColor("#1E88E5");
            }
        }
        setBackgroundColor(attributeIntValue);
        setMinimumHeight(Utils.m73528a(3.0f, getResources()));
    }

    @Override // android.view.View
    public void setBackgroundColor(int i2) {
        super.setBackgroundColor(getResources().getColor(R.color.transparent));
        if (isEnabled()) {
            this.f101617X2 = this.f101633c3;
        }
        this.f101633c3 = i2;
    }
}

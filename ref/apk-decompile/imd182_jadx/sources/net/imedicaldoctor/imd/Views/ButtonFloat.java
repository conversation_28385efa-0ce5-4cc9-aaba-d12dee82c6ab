package net.imedicaldoctor.imd.Views;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.animation.BounceInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.nineoldandroids.animation.ObjectAnimator;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class ButtonFloat extends Button {

    /* renamed from: q3 */
    int f101587q3;

    /* renamed from: r3 */
    int f101588r3;

    /* renamed from: s3 */
    ImageView f101589s3;

    /* renamed from: t3 */
    Drawable f101590t3;

    /* renamed from: u3 */
    public boolean f101591u3;

    /* renamed from: v3 */
    float f101592v3;

    /* renamed from: w3 */
    float f101593w3;

    /* renamed from: x3 */
    Integer f101594x3;

    /* renamed from: y3 */
    Integer f101595y3;

    public ButtonFloat(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f101587q3 = 24;
        this.f101588r3 = 28;
        this.f101591u3 = false;
        setBackgroundResource(C5562R.drawable.background_button_float);
        setBackgroundColor(this.f101580k3);
        this.f101588r3 = 28;
        mo73503c();
        ImageView imageView = new ImageView(context);
        this.f101589s3 = imageView;
        imageView.setAdjustViewBounds(true);
        this.f101589s3.setScaleType(ImageView.ScaleType.CENTER_CROP);
        Drawable drawable = this.f101590t3;
        if (drawable != null) {
            this.f101589s3.setImageDrawable(drawable);
        }
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(Utils.m73528a(this.f101587q3, getResources()), Utils.m73528a(this.f101587q3, getResources()));
        layoutParams.addRule(13, -1);
        this.f101589s3.setLayoutParams(layoutParams);
        addView(this.f101589s3);
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    /* renamed from: c */
    protected void mo73503c() {
        this.f101575f3 = Utils.m73528a(2.0f, getResources());
        this.f101576g3 = Utils.m73528a(5.0f, getResources());
        setMinimumWidth(Utils.m73528a(this.f101588r3 * 2, getResources()));
        setMinimumHeight(Utils.m73528a(this.f101588r3 * 2, getResources()));
        this.f101574e3 = C5562R.drawable.background_button_float;
    }

    /* renamed from: d */
    public Bitmap m73504d(Bitmap bitmap) {
        Bitmap bitmapCreateBitmap = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmapCreateBitmap);
        Paint paint = new Paint();
        Rect rect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
        paint.setAntiAlias(true);
        canvas.drawARGB(0, 0, 0, 0);
        paint.setColor(-12434878);
        canvas.drawCircle(bitmap.getWidth() / 2, bitmap.getHeight() / 2, bitmap.getWidth() / 2, paint);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        canvas.drawBitmap(bitmap, rect, rect, paint);
        return bitmapCreateBitmap;
    }

    /* renamed from: e */
    public void m73505e() {
        ObjectAnimator objectAnimatorM57670S0 = ObjectAnimator.m57670S0(this, "y", this.f101593w3);
        objectAnimatorM57670S0.mo57600n(new BounceInterpolator());
        objectAnimatorM57670S0.mo57599m(1500L);
        objectAnimatorM57670S0.mo57605s();
        this.f101591u3 = false;
    }

    /* renamed from: f */
    public boolean m73506f() {
        return this.f101591u3;
    }

    /* renamed from: g */
    public void m73507g() {
        ObjectAnimator objectAnimatorM57670S0 = ObjectAnimator.m57670S0(this, "y", this.f101592v3);
        objectAnimatorM57670S0.mo57600n(new BounceInterpolator());
        objectAnimatorM57670S0.mo57599m(1500L);
        objectAnimatorM57670S0.mo57605s();
        this.f101591u3 = true;
    }

    public Drawable getDrawableIcon() {
        return this.f101590t3;
    }

    public ImageView getIcon() {
        return this.f101589s3;
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    public TextView getTextView() {
        return null;
    }

    @Override // net.imedicaldoctor.imd.Views.CustomView, android.view.View
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (this.f101582m3 != -1.0f) {
            canvas.drawBitmap(m73504d(m73501a()), new Rect(0, 0, getWidth(), getHeight()), new Rect(Utils.m73528a(1.0f, getResources()), Utils.m73528a(2.0f, getResources()), getWidth() - Utils.m73528a(1.0f, getResources()), getHeight() - Utils.m73528a(2.0f, getResources())), (Paint) null);
            invalidate();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:13:0x0036  */
    /* JADX WARN: Removed duplicated region for block: B:19:0x004a  */
    @Override // net.imedicaldoctor.imd.Views.Button
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected void setAttributes(android.util.AttributeSet r5) throws android.content.res.Resources.NotFoundException {
        /*
            r4 = this;
            java.lang.String r0 = "http://schemas.android.com/apk/res/android"
            java.lang.String r1 = "background"
            r2 = -1
            int r3 = r5.getAttributeResourceValue(r0, r1, r2)
            if (r3 == r2) goto L17
            android.content.res.Resources r0 = r4.getResources()
            int r0 = r0.getColor(r3)
        L13:
            r4.setBackgroundColor(r0)
            goto L20
        L17:
            int r0 = r5.getAttributeIntValue(r0, r1, r2)
            r4.f101574e3 = r0
            if (r0 == r2) goto L20
            goto L13
        L20:
            java.lang.String r0 = "http://schemas.android.com/apk/res-auto"
            java.lang.String r1 = "rippleColor"
            int r3 = r5.getAttributeResourceValue(r0, r1, r2)
            if (r3 == r2) goto L36
            android.content.res.Resources r1 = r4.getResources()
            int r1 = r1.getColor(r3)
        L32:
            r4.setRippleColor(r1)
            goto L42
        L36:
            int r1 = r5.getAttributeIntValue(r0, r1, r2)
            if (r1 == r2) goto L3d
            goto L32
        L3d:
            int r1 = r4.mo73502b()
            goto L32
        L42:
            java.lang.String r1 = "iconDrawable"
            int r1 = r5.getAttributeResourceValue(r0, r1, r2)
            if (r1 == r2) goto L54
            android.content.res.Resources r2 = r4.getResources()
            android.graphics.drawable.Drawable r1 = r2.getDrawable(r1)
            r4.f101590t3 = r1
        L54:
            java.lang.String r1 = "animate"
            r2 = 0
            boolean r5 = r5.getAttributeBooleanValue(r0, r1, r2)
            net.imedicaldoctor.imd.Views.ButtonFloat$1 r0 = new net.imedicaldoctor.imd.Views.ButtonFloat$1
            r0.<init>()
            r4.post(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Views.ButtonFloat.setAttributes(android.util.AttributeSet):void");
    }

    public void setDrawableIcon(Drawable drawable) {
        this.f101590t3 = drawable;
        try {
            this.f101589s3.setBackground(drawable);
        } catch (NoSuchMethodError unused) {
            this.f101589s3.setBackgroundDrawable(drawable);
        }
    }

    public void setIcon(ImageView imageView) {
        this.f101589s3 = imageView;
    }

    public void setRippleColor(int i2) {
        this.f101577h3 = Integer.valueOf(i2);
    }
}

package net.imedicaldoctor.imd.Views;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.itextpdf.tool.xml.css.CSS;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class ButtonFlatHelp extends Button {

    /* renamed from: q3 */
    TextView f101586q3;

    public ButtonFlatHelp(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f101580k3 = Color.parseColor("#1d7021");
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    /* renamed from: b */
    protected int mo73502b() {
        return Color.parseColor("#88DDDDDD");
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    /* renamed from: c */
    protected void mo73503c() {
        this.f101573d3 = 36;
        this.f101572c3 = 88;
        this.f101576g3 = 3;
        setMinimumHeight(Utils.m73528a(36, getResources()));
        setMinimumWidth(Utils.m73528a(this.f101572c3, getResources()));
        setBackgroundResource(C5562R.drawable.background_transparent);
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    public String getText() {
        return this.f101586q3.getText().toString();
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    public TextView getTextView() {
        return this.f101586q3;
    }

    @Override // net.imedicaldoctor.imd.Views.CustomView, android.view.View
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (this.f101582m3 != -1.0f) {
            Paint paint = new Paint();
            paint.setAntiAlias(true);
            paint.setColor(mo73502b());
            canvas.drawCircle(this.f101582m3, this.f101583n3, this.f101584o3, paint);
            if (this.f101584o3 > getHeight() / this.f101576g3) {
                this.f101584o3 += this.f101575f3;
            }
            if (this.f101584o3 >= getWidth()) {
                this.f101582m3 = -1.0f;
                this.f101583n3 = -1.0f;
                this.f101584o3 = getHeight() / this.f101576g3;
                View.OnClickListener onClickListener = this.f101578i3;
                if (onClickListener != null && this.f101579j3) {
                    onClickListener.onClick(this);
                }
            }
            invalidate();
        }
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    protected void setAttributes(AttributeSet attributeSet) throws Resources.NotFoundException {
        int attributeIntValue;
        int attributeResourceValue = attributeSet.getAttributeResourceValue("http://schemas.android.com/apk/res/android", "text", -1);
        String string = attributeResourceValue != -1 ? getResources().getString(attributeResourceValue) : attributeSet.getAttributeValue("http://schemas.android.com/apk/res/android", "text");
        if (string != null) {
            TextView textView = new TextView(getContext());
            this.f101586q3 = textView;
            textView.setText(string.toUpperCase());
            this.f101586q3.setTextColor(this.f101580k3);
            this.f101586q3.setTypeface(null, 1);
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(-2, -2);
            layoutParams.addRule(13, -1);
            this.f101586q3.setLayoutParams(layoutParams);
            addView(this.f101586q3);
        }
        int attributeResourceValue2 = attributeSet.getAttributeResourceValue("http://schemas.android.com/apk/res/android", CSS.Property.f74018a, -1);
        if (attributeResourceValue2 != -1) {
            attributeIntValue = getResources().getColor(attributeResourceValue2);
        } else {
            attributeIntValue = attributeSet.getAttributeIntValue("http://schemas.android.com/apk/res/android", CSS.Property.f74018a, -1);
            this.f101574e3 = attributeIntValue;
            if (attributeIntValue == -1) {
                return;
            }
        }
        setBackgroundColor(attributeIntValue);
    }

    @Override // net.imedicaldoctor.imd.Views.Button, android.view.View
    public void setBackgroundColor(int i2) {
        this.f101580k3 = i2;
        if (isEnabled()) {
            this.f101617X2 = this.f101580k3;
        }
        this.f101586q3.setTextColor(i2);
    }

    @Override // net.imedicaldoctor.imd.Views.Button
    public void setText(String str) {
        this.f101586q3.setText(str.toUpperCase());
    }
}

package net.imedicaldoctor.imd.Views;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import com.itextpdf.tool.xml.css.CSS;

/* loaded from: classes3.dex */
public class LayoutRipple extends CustomView {

    /* renamed from: c3 */
    int f101621c3;

    /* renamed from: d3 */
    float f101622d3;

    /* renamed from: e3 */
    int f101623e3;

    /* renamed from: f3 */
    View.OnClickListener f101624f3;

    /* renamed from: g3 */
    int f101625g3;

    /* renamed from: h3 */
    Integer f101626h3;

    /* renamed from: i3 */
    Float f101627i3;

    /* renamed from: j3 */
    Float f101628j3;

    /* renamed from: k3 */
    float f101629k3;

    /* renamed from: l3 */
    float f101630l3;

    /* renamed from: m3 */
    float f101631m3;

    public LayoutRipple(Context context, AttributeSet attributeSet) throws Resources.NotFoundException {
        super(context, attributeSet);
        this.f101622d3 = 10.0f;
        this.f101623e3 = 3;
        this.f101625g3 = Color.parseColor("#FFFFFF");
        this.f101629k3 = -1.0f;
        this.f101630l3 = -1.0f;
        this.f101631m3 = -1.0f;
        setAttributes(attributeSet);
    }

    /* renamed from: a */
    public Bitmap m73512a() {
        Bitmap bitmapCreateBitmap = Bitmap.createBitmap(getWidth(), getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmapCreateBitmap);
        canvas.drawARGB(0, 0, 0, 0);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        if (this.f101626h3 == null) {
            this.f101626h3 = Integer.valueOf(m73513b());
        }
        paint.setColor(this.f101626h3.intValue());
        Float f2 = this.f101627i3;
        this.f101629k3 = f2 == null ? this.f101629k3 : f2.floatValue();
        Float f3 = this.f101628j3;
        float fFloatValue = f3 == null ? this.f101630l3 : f3.floatValue();
        this.f101630l3 = fFloatValue;
        canvas.drawCircle(this.f101629k3, fFloatValue, this.f101631m3, paint);
        if (this.f101631m3 > getHeight() / this.f101623e3) {
            this.f101631m3 += this.f101622d3;
        }
        if (this.f101631m3 >= getWidth()) {
            this.f101629k3 = -1.0f;
            this.f101630l3 = -1.0f;
            this.f101631m3 = getHeight() / this.f101623e3;
            View.OnClickListener onClickListener = this.f101624f3;
            if (onClickListener != null) {
                onClickListener.onClick(this);
            }
        }
        return bitmapCreateBitmap;
    }

    /* renamed from: b */
    protected int m73513b() {
        int i2 = this.f101625g3;
        int i3 = (i2 >> 16) & 255;
        int i4 = (i2 >> 8) & 255;
        int i5 = i2 & 255;
        int i6 = i3 - 30;
        if (i6 < 0) {
            i6 = 0;
        }
        int i7 = i4 - 30;
        if (i7 < 0) {
            i7 = 0;
        }
        int i8 = i5 - 30;
        return Color.rgb(i6, i7, i8 >= 0 ? i8 : 0);
    }

    @Override // net.imedicaldoctor.imd.Views.CustomView, android.view.View
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (this.f101629k3 != -1.0f) {
            canvas.drawBitmap(m73512a(), new Rect(0, 0, getWidth(), getHeight()), new Rect(0, 0, getWidth(), getHeight()), (Paint) null);
            invalidate();
        }
    }

    @Override // android.view.View
    protected void onFocusChanged(boolean z, int i2, Rect rect) {
        if (z) {
            return;
        }
        this.f101629k3 = -1.0f;
        this.f101630l3 = -1.0f;
    }

    @Override // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        return true;
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0075  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean onTouchEvent(android.view.MotionEvent r7) {
        /*
            r6 = this;
            r6.invalidate()
            boolean r0 = r6.isEnabled()
            r1 = 1
            if (r0 == 0) goto Lc0
            r6.f101618Y2 = r1
            int r0 = r7.getAction()
            r2 = 0
            r3 = -1082130432(0xffffffffbf800000, float:-1.0)
            if (r0 != 0) goto L2d
            int r0 = r6.getHeight()
            int r4 = r6.f101623e3
            int r0 = r0 / r4
            float r0 = (float) r0
            r6.f101631m3 = r0
            float r0 = r7.getX()
            r6.f101629k3 = r0
            float r0 = r7.getY()
            r6.f101630l3 = r0
            goto Lb3
        L2d:
            int r0 = r7.getAction()
            r4 = 2
            r5 = 0
            if (r0 != r4) goto L7c
            int r0 = r6.getHeight()
            int r4 = r6.f101623e3
            int r0 = r0 / r4
            float r0 = (float) r0
            r6.f101631m3 = r0
            float r0 = r7.getX()
            r6.f101629k3 = r0
            float r0 = r7.getY()
            r6.f101630l3 = r0
            float r0 = r7.getX()
            int r4 = r6.getWidth()
            float r4 = (float) r4
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            if (r0 > 0) goto L75
            float r0 = r7.getX()
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            if (r0 < 0) goto L75
            float r0 = r7.getY()
            int r4 = r6.getHeight()
            float r4 = (float) r4
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            if (r0 > 0) goto L75
            float r0 = r7.getY()
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            if (r0 >= 0) goto Lb3
        L75:
            r6.f101618Y2 = r2
            r6.f101629k3 = r3
            r6.f101630l3 = r3
            goto Lb3
        L7c:
            int r0 = r7.getAction()
            if (r0 != r1) goto Lb3
            float r0 = r7.getX()
            int r4 = r6.getWidth()
            float r4 = (float) r4
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            if (r0 > 0) goto L75
            float r0 = r7.getX()
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            if (r0 < 0) goto L75
            float r0 = r7.getY()
            int r4 = r6.getHeight()
            float r4 = (float) r4
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            if (r0 > 0) goto L75
            float r0 = r7.getY()
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            if (r0 < 0) goto L75
            float r0 = r6.f101631m3
            r4 = **********(0x3f800000, float:1.0)
            float r0 = r0 + r4
            r6.f101631m3 = r0
        Lb3:
            int r7 = r7.getAction()
            r0 = 3
            if (r7 != r0) goto Lc0
            r6.f101618Y2 = r2
            r6.f101629k3 = r3
            r6.f101630l3 = r3
        Lc0:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Views.LayoutRipple.onTouchEvent(android.view.MotionEvent):boolean");
    }

    protected void setAttributes(AttributeSet attributeSet) throws Resources.NotFoundException {
        int attributeIntValue;
        int attributeIntValue2;
        int attributeResourceValue = attributeSet.getAttributeResourceValue("http://schemas.android.com/apk/res/android", CSS.Property.f74018a, -1);
        if (attributeResourceValue != -1) {
            attributeIntValue = getResources().getColor(attributeResourceValue);
        } else {
            attributeIntValue = attributeSet.getAttributeIntValue("http://schemas.android.com/apk/res/android", CSS.Property.f74018a, -1);
            this.f101621c3 = attributeIntValue;
            if (attributeIntValue == -1) {
                attributeIntValue = this.f101625g3;
            }
        }
        setBackgroundColor(attributeIntValue);
        int attributeResourceValue2 = attributeSet.getAttributeResourceValue("http://schemas.android.com/apk/res-auto", "rippleColor", -1);
        if (attributeResourceValue2 != -1) {
            attributeIntValue2 = getResources().getColor(attributeResourceValue2);
        } else {
            attributeIntValue2 = attributeSet.getAttributeIntValue("http://schemas.android.com/apk/res-auto", "rippleColor", -1);
            if (attributeIntValue2 == -1) {
                attributeIntValue2 = m73513b();
            }
        }
        setRippleColor(attributeIntValue2);
        this.f101622d3 = attributeSet.getAttributeFloatValue("http://schemas.android.com/apk/res-auto", "rippleSpeed", 20.0f);
    }

    @Override // android.view.View
    public void setBackgroundColor(int i2) {
        this.f101625g3 = i2;
        if (isEnabled()) {
            this.f101617X2 = this.f101625g3;
        }
        super.setBackgroundColor(i2);
    }

    @Override // android.view.View
    public void setOnClickListener(View.OnClickListener onClickListener) {
        this.f101624f3 = onClickListener;
    }

    public void setRippleColor(int i2) {
        this.f101626h3 = Integer.valueOf(i2);
    }

    public void setRippleSpeed(int i2) {
        this.f101622d3 = i2;
    }

    public void setxRippleOrigin(Float f2) {
        this.f101627i3 = f2;
    }

    public void setyRippleOrigin(Float f2) {
        this.f101628j3 = f2;
    }
}

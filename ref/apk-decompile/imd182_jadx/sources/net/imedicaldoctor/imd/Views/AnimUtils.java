package net.imedicaldoctor.imd.Views;

import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import com.nineoldandroids.animation.Animator;
import com.nineoldandroids.animation.ValueAnimator;
import com.nineoldandroids.view.ViewHelper;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class AnimUtils {

    /* renamed from: net.imedicaldoctor.imd.Views.AnimUtils$9 */
    static /* synthetic */ class C55899 {

        /* renamed from: a */
        static final /* synthetic */ int[] f101563a;

        static {
            int[] iArr = new int[Style.values().length];
            f101563a = iArr;
            try {
                iArr[Style.Fade.ordinal()] = 1;
            } catch (NoSuchFieldError unused) {
            }
            try {
                f101563a[Style.Pop.ordinal()] = 2;
            } catch (NoSuchFieldError unused2) {
            }
            try {
                f101563a[Style.Fly.ordinal()] = 3;
            } catch (NoSuchFieldError unused3) {
            }
            try {
                f101563a[Style.BrightnessSaturationFade.ordinal()] = 4;
            } catch (NoSuchFieldError unused4) {
            }
        }
    }

    public enum Style {
        None,
        Fade,
        Pop,
        Fly,
        BrightnessSaturationFade,
        ProgressWidth
    }

    private AnimUtils() {
    }

    /* renamed from: a */
    public static ValueAnimator m73487a(View view, Style style, Animator.AnimatorListener animatorListener) {
        int i2 = C55899.f101563a[style.ordinal()];
        if (i2 == 1) {
            return m73491e(view, animatorListener);
        }
        if (i2 == 2) {
            return m73497k(view, animatorListener);
        }
        if (i2 == 3) {
            return m73493g(view, animatorListener);
        }
        if (i2 == 4) {
            return view instanceof ImageView ? m73489c((ImageView) view, animatorListener) : m73491e(view, animatorListener);
        }
        if (animatorListener != null) {
            animatorListener.mo57609d(null);
        }
        return null;
    }

    /* renamed from: b */
    public static ValueAnimator m73488b(View view, Style style, Animator.AnimatorListener animatorListener) {
        int i2 = C55899.f101563a[style.ordinal()];
        if (i2 == 1) {
            return m73492f(view, animatorListener);
        }
        if (i2 == 2) {
            return m73498l(view, animatorListener);
        }
        if (i2 == 3) {
            return m73494h(view, animatorListener);
        }
        if (i2 == 4) {
            return view instanceof ImageView ? m73490d((ImageView) view, animatorListener) : m73492f(view, animatorListener);
        }
        if (animatorListener != null) {
            animatorListener.mo57609d(null);
        }
        return null;
    }

    /* renamed from: c */
    public static ValueAnimator m73489c(final ImageView imageView, Animator.AnimatorListener animatorListener) {
        final ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(0.0f, 1.0f);
        final AccelerateDecelerateInterpolator accelerateDecelerateInterpolator = new AccelerateDecelerateInterpolator();
        valueAnimatorM57766l0.mo57600n(accelerateDecelerateInterpolator);
        valueAnimatorM57766l0.mo57599m(800L);
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.7

            /* renamed from: a */
            final ColorMatrix f101553a = new ColorMatrix();

            /* renamed from: b */
            final ColorMatrix f101554b = new ColorMatrix();

            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                float fM57782S = valueAnimatorM57766l0.m57782S();
                this.f101553a.setSaturation(((Float) valueAnimatorM57766l0.m57783U()).floatValue());
                float interpolation = 2.0f - accelerateDecelerateInterpolator.getInterpolation(Math.min((4.0f * fM57782S) / 3.0f, 1.0f));
                this.f101554b.setScale(interpolation, interpolation, interpolation, accelerateDecelerateInterpolator.getInterpolation(Math.min(fM57782S * 2.0f, 1.0f)));
                this.f101553a.preConcat(this.f101554b);
                imageView.setColorFilter(new ColorMatrixColorFilter(this.f101553a));
                if (imageView.getParent() != null) {
                    ((View) imageView.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }

    /* renamed from: d */
    public static ValueAnimator m73490d(final ImageView imageView, Animator.AnimatorListener animatorListener) {
        final ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(1.0f, 0.0f);
        final AccelerateDecelerateInterpolator accelerateDecelerateInterpolator = new AccelerateDecelerateInterpolator();
        valueAnimatorM57766l0.mo57600n(accelerateDecelerateInterpolator);
        valueAnimatorM57766l0.mo57599m(800L);
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.8

            /* renamed from: a */
            final ColorMatrix f101558a = new ColorMatrix();

            /* renamed from: b */
            final ColorMatrix f101559b = new ColorMatrix();

            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                float fM57782S = valueAnimatorM57766l0.m57782S();
                this.f101558a.setSaturation(((Float) valueAnimatorM57766l0.m57783U()).floatValue());
                float f2 = 1.0f - fM57782S;
                float interpolation = 2.0f - accelerateDecelerateInterpolator.getInterpolation(Math.min((4.0f * f2) / 3.0f, 1.0f));
                this.f101559b.setScale(interpolation, interpolation, interpolation, accelerateDecelerateInterpolator.getInterpolation(Math.min(f2 * 2.0f, 1.0f)));
                this.f101558a.preConcat(this.f101559b);
                imageView.setColorFilter(new ColorMatrixColorFilter(this.f101558a));
                if (imageView.getParent() != null) {
                    ((View) imageView.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }

    /* renamed from: e */
    public static ValueAnimator m73491e(final View view, Animator.AnimatorListener animatorListener) {
        if (view.getVisibility() != 0) {
            ViewHelper.m57825o(view, 0.0f);
        }
        float fM57811a = ViewHelper.m57811a(view);
        ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(fM57811a, 1.0f);
        valueAnimatorM57766l0.mo57599m((long) ((1.0f - fM57811a) * 200.0f));
        valueAnimatorM57766l0.mo57600n(new DecelerateInterpolator());
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.1
            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                ViewHelper.m57825o(view, ((Float) valueAnimator.m57783U()).floatValue());
                if (view.getParent() != null) {
                    ((View) view.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }

    /* renamed from: f */
    public static ValueAnimator m73492f(final View view, Animator.AnimatorListener animatorListener) {
        float fM57811a = ViewHelper.m57811a(view);
        ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(fM57811a, 0.0f);
        valueAnimatorM57766l0.mo57599m((long) (fM57811a * 200.0f));
        valueAnimatorM57766l0.mo57600n(new DecelerateInterpolator());
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.2
            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                ViewHelper.m57825o(view, ((Float) valueAnimator.m57783U()).floatValue());
                if (view.getParent() != null) {
                    ((View) view.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }

    /* renamed from: g */
    public static ValueAnimator m73493g(final View view, Animator.AnimatorListener animatorListener) {
        if (view.getVisibility() != 0) {
            ViewHelper.m57825o(view, 0.0f);
        }
        float fM57811a = ViewHelper.m57811a(view);
        ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(fM57811a, 1.0f);
        valueAnimatorM57766l0.mo57599m((long) ((1.0f - fM57811a) * 200.0f));
        valueAnimatorM57766l0.mo57600n(new DecelerateInterpolator());
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.5
            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                ViewHelper.m57825o(view, ((Float) valueAnimator.m57783U()).floatValue());
                ViewHelper.m57836z(view, Math.min(r0.getHeight() / 2, view.getResources().getDimension(C5562R.dimen.carbon_1dip) * 50.0f) * (1.0f - ((Float) valueAnimator.m57783U()).floatValue()));
                if (view.getParent() != null) {
                    ((View) view.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }

    /* renamed from: h */
    public static ValueAnimator m73494h(final View view, Animator.AnimatorListener animatorListener) {
        float fM57811a = ViewHelper.m57811a(view);
        ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(fM57811a, 0.0f);
        valueAnimatorM57766l0.mo57599m((long) (fM57811a * 200.0f));
        valueAnimatorM57766l0.mo57600n(new DecelerateInterpolator());
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.6
            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                ViewHelper.m57825o(view, ((Float) valueAnimator.m57783U()).floatValue());
                ViewHelper.m57836z(view, Math.min(r0.getHeight() / 2, view.getResources().getDimension(C5562R.dimen.carbon_1dip) * 50.0f) * (1.0f - ((Float) valueAnimator.m57783U()).floatValue()));
                if (view.getParent() != null) {
                    ((View) view.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }

    /* renamed from: i */
    public static float m73495i(float f2, float f3, float f4) {
        return (f3 * (1.0f - f2)) + (f4 * f2);
    }

    /* renamed from: j */
    public static int m73496j(float f2, int i2, int i3) {
        return Color.argb((int) m73495i(f2, i2 >> 24, i3 >> 24), (int) m73495i(f2, (i2 >> 16) & 255, (i3 >> 16) & 255), (int) m73495i(f2, (i2 >> 8) & 255, (i3 >> 8) & 255), (int) m73495i(f2, i2 & 255, i3 & 255));
    }

    /* renamed from: k */
    public static ValueAnimator m73497k(final View view, Animator.AnimatorListener animatorListener) {
        if (view.getVisibility() != 0) {
            ViewHelper.m57825o(view, 0.0f);
        }
        float fM57811a = ViewHelper.m57811a(view);
        ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(fM57811a, 1.0f);
        valueAnimatorM57766l0.mo57599m((long) ((1.0f - fM57811a) * 200.0f));
        valueAnimatorM57766l0.mo57600n(new DecelerateInterpolator());
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.3
            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                ViewHelper.m57825o(view, ((Float) valueAnimator.m57783U()).floatValue());
                ViewHelper.m57831u(view, ((Float) valueAnimator.m57783U()).floatValue());
                ViewHelper.m57832v(view, ((Float) valueAnimator.m57783U()).floatValue());
                if (view.getParent() != null) {
                    ((View) view.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }

    /* renamed from: l */
    public static ValueAnimator m73498l(final View view, Animator.AnimatorListener animatorListener) {
        float fM57811a = ViewHelper.m57811a(view);
        ValueAnimator valueAnimatorM57766l0 = ValueAnimator.m57766l0(fM57811a, 0.0f);
        valueAnimatorM57766l0.mo57599m((long) (fM57811a * 200.0f));
        valueAnimatorM57766l0.mo57600n(new DecelerateInterpolator());
        if (animatorListener != null) {
            valueAnimatorM57766l0.m57589a(animatorListener);
        }
        valueAnimatorM57766l0.m57779F(new ValueAnimator.AnimatorUpdateListener() { // from class: net.imedicaldoctor.imd.Views.AnimUtils.4
            @Override // com.nineoldandroids.animation.ValueAnimator.AnimatorUpdateListener
            /* renamed from: e */
            public void mo57800e(ValueAnimator valueAnimator) {
                ViewHelper.m57825o(view, ((Float) valueAnimator.m57783U()).floatValue());
                ViewHelper.m57831u(view, ((Float) valueAnimator.m57783U()).floatValue());
                ViewHelper.m57832v(view, ((Float) valueAnimator.m57783U()).floatValue());
                if (view.getParent() != null) {
                    ((View) view.getParent()).postInvalidate();
                }
            }
        });
        valueAnimatorM57766l0.mo57605s();
        return valueAnimatorM57766l0;
    }
}

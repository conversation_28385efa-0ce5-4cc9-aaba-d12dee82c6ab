package net.imedicaldoctor.imd.Views;

import android.content.res.Resources;
import android.util.TypedValue;
import android.view.View;

/* loaded from: classes3.dex */
public class Utils {
    /* renamed from: a */
    public static int m73528a(float f2, Resources resources) {
        return (int) TypedValue.applyDimension(1, f2, resources.getDisplayMetrics());
    }

    /* renamed from: b */
    public static int m73529b(View view) {
        return view.getId() == 16908290 ? view.getLeft() : view.getLeft() + m73529b((View) view.getParent());
    }

    /* renamed from: c */
    public static int m73530c(View view) {
        return view.getId() == 16908290 ? view.getTop() : view.getTop() + m73530c((View) view.getParent());
    }
}

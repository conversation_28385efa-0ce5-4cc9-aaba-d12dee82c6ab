package net.imedicaldoctor.imd.Views;

import android.R;
import android.app.Activity;
import android.app.Dialog;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;
import androidx.vectordrawable.graphics.drawable.PathInterpolatorCompat;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class SnackBar extends Dialog {

    /* renamed from: X */
    float f101642X;

    /* renamed from: X2 */
    Activity f101643X2;

    /* renamed from: Y */
    String f101644Y;

    /* renamed from: Y2 */
    View f101645Y2;

    /* renamed from: Z */
    View.OnClickListener f101646Z;

    /* renamed from: Z2 */
    ButtonFlat f101647Z2;

    /* renamed from: a3 */
    int f101648a3;

    /* renamed from: b3 */
    int f101649b3;

    /* renamed from: c3 */
    OnHideListener f101650c3;

    /* renamed from: d3 */
    private boolean f101651d3;

    /* renamed from: e3 */
    private int f101652e3;

    /* renamed from: f3 */
    Thread f101653f3;

    /* renamed from: g3 */
    Handler f101654g3;

    /* renamed from: s */
    String f101655s;

    public interface OnHideListener {
        /* renamed from: a */
        void m73527a();
    }

    public SnackBar(Activity activity, String str) {
        super(activity, R.style.Theme.Translucent);
        this.f101642X = 14.0f;
        this.f101648a3 = Color.parseColor("#333333");
        this.f101649b3 = Color.parseColor("#1E88E5");
        this.f101651d3 = false;
        this.f101652e3 = PathInterpolatorCompat.f34639d;
        this.f101653f3 = new Thread(new Runnable() { // from class: net.imedicaldoctor.imd.Views.SnackBar.2
            @Override // java.lang.Runnable
            public void run() throws InterruptedException {
                try {
                    Thread.sleep(SnackBar.this.f101652e3);
                } catch (InterruptedException e2) {
                    e2.printStackTrace();
                }
                SnackBar.this.f101654g3.sendMessage(new Message());
            }
        });
        this.f101654g3 = new Handler(new Handler.Callback() { // from class: net.imedicaldoctor.imd.Views.SnackBar.3
            @Override // android.os.Handler.Callback
            public boolean handleMessage(Message message) throws Resources.NotFoundException {
                OnHideListener onHideListener = SnackBar.this.f101650c3;
                if (onHideListener != null) {
                    onHideListener.m73527a();
                }
                SnackBar.this.dismiss();
                return false;
            }
        });
        this.f101643X2 = activity;
        this.f101655s = str;
    }

    /* renamed from: c */
    public int m73519c() {
        return this.f101652e3;
    }

    /* renamed from: d */
    public boolean m73520d() {
        return this.f101651d3;
    }

    @Override // android.app.Dialog, android.content.DialogInterface
    public void dismiss() throws Resources.NotFoundException {
        Animation animationLoadAnimation = AnimationUtils.loadAnimation(this.f101643X2, C5562R.anim.snackbar_hide_animation);
        animationLoadAnimation.setAnimationListener(new Animation.AnimationListener() { // from class: net.imedicaldoctor.imd.Views.SnackBar.4
            @Override // android.view.animation.Animation.AnimationListener
            public void onAnimationEnd(Animation animation) {
                SnackBar.super.dismiss();
            }

            @Override // android.view.animation.Animation.AnimationListener
            public void onAnimationRepeat(Animation animation) {
            }

            @Override // android.view.animation.Animation.AnimationListener
            public void onAnimationStart(Animation animation) {
            }
        });
        this.f101645Y2.startAnimation(animationLoadAnimation);
    }

    /* renamed from: e */
    public void m73521e(int i2) {
        this.f101648a3 = i2;
        View view = this.f101645Y2;
        if (view != null) {
            view.setBackgroundColor(i2);
        }
    }

    /* renamed from: f */
    public void m73522f(int i2) {
        this.f101649b3 = i2;
        ButtonFlat buttonFlat = this.f101647Z2;
        if (buttonFlat != null) {
            buttonFlat.setBackgroundColor(i2);
        }
    }

    /* renamed from: g */
    public void m73523g(int i2) {
        this.f101652e3 = i2;
    }

    /* renamed from: h */
    public void m73524h(boolean z) {
        this.f101651d3 = z;
    }

    /* renamed from: i */
    public void m73525i(float f2) {
        this.f101642X = f2;
    }

    /* renamed from: j */
    public void m73526j(OnHideListener onHideListener) {
        this.f101650c3 = onHideListener;
    }

    @Override // android.app.Dialog
    public void onBackPressed() {
    }

    @Override // android.app.Dialog
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        requestWindowFeature(1);
        setContentView(C5562R.layout.snackbar);
        setCanceledOnTouchOutside(false);
        ((TextView) findViewById(C5562R.id.text)).setText(this.f101655s);
        ((TextView) findViewById(C5562R.id.text)).setTextSize(this.f101642X);
        ButtonFlat buttonFlat = (ButtonFlat) findViewById(C5562R.id.buttonflat);
        this.f101647Z2 = buttonFlat;
        if (this.f101655s == null || this.f101646Z == null) {
            buttonFlat.setVisibility(8);
        } else {
            buttonFlat.setText(this.f101644Y);
            this.f101647Z2.setBackgroundColor(this.f101649b3);
            this.f101647Z2.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Views.SnackBar.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view) throws Resources.NotFoundException {
                    SnackBar.this.dismiss();
                    SnackBar.this.f101646Z.onClick(view);
                }
            });
        }
        View viewFindViewById = findViewById(C5562R.id.snackbar);
        this.f101645Y2 = viewFindViewById;
        viewFindViewById.setBackgroundColor(this.f101648a3);
    }

    @Override // android.app.Dialog, android.view.KeyEvent.Callback
    public boolean onKeyDown(int i2, KeyEvent keyEvent) throws Resources.NotFoundException {
        if (i2 == 4) {
            dismiss();
        }
        return super.onKeyDown(i2, keyEvent);
    }

    @Override // android.app.Dialog
    public boolean onTouchEvent(MotionEvent motionEvent) {
        return this.f101643X2.dispatchTouchEvent(motionEvent);
    }

    @Override // android.app.Dialog
    public void show() {
        super.show();
        this.f101645Y2.setVisibility(0);
        this.f101645Y2.startAnimation(AnimationUtils.loadAnimation(this.f101643X2, C5562R.anim.snackbar_show_animation));
        if (this.f101651d3) {
            return;
        }
        this.f101653f3.start();
    }

    public SnackBar(Activity activity, String str, String str2, View.OnClickListener onClickListener) {
        super(activity, R.style.Theme.Translucent);
        this.f101642X = 14.0f;
        this.f101648a3 = Color.parseColor("#333333");
        this.f101649b3 = Color.parseColor("#1E88E5");
        this.f101651d3 = false;
        this.f101652e3 = PathInterpolatorCompat.f34639d;
        this.f101653f3 = new Thread(new Runnable() { // from class: net.imedicaldoctor.imd.Views.SnackBar.2
            @Override // java.lang.Runnable
            public void run() throws InterruptedException {
                try {
                    Thread.sleep(SnackBar.this.f101652e3);
                } catch (InterruptedException e2) {
                    e2.printStackTrace();
                }
                SnackBar.this.f101654g3.sendMessage(new Message());
            }
        });
        this.f101654g3 = new Handler(new Handler.Callback() { // from class: net.imedicaldoctor.imd.Views.SnackBar.3
            @Override // android.os.Handler.Callback
            public boolean handleMessage(Message message) throws Resources.NotFoundException {
                OnHideListener onHideListener = SnackBar.this.f101650c3;
                if (onHideListener != null) {
                    onHideListener.m73527a();
                }
                SnackBar.this.dismiss();
                return false;
            }
        });
        this.f101643X2 = activity;
        this.f101655s = str;
        this.f101644Y = str2;
        this.f101646Z = onClickListener;
    }
}

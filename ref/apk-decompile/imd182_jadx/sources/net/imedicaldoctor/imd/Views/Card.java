package net.imedicaldoctor.imd.Views;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;
import android.util.AttributeSet;
import android.widget.TextView;
import com.itextpdf.tool.xml.css.CSS;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public class Card extends CustomView {

    /* renamed from: c3 */
    TextView f101609c3;

    /* renamed from: d3 */
    int f101610d3;

    /* renamed from: e3 */
    int f101611e3;

    /* renamed from: f3 */
    int f101612f3;

    /* renamed from: g3 */
    int f101613g3;

    /* renamed from: h3 */
    int f101614h3;

    public Card(Context context, AttributeSet attributeSet) throws Resources.NotFoundException {
        super(context, attributeSet);
        this.f101614h3 = Color.parseColor("#FFFFFF");
        setAttributes(attributeSet);
    }

    protected void setAttributes(AttributeSet attributeSet) throws Resources.NotFoundException {
        int color;
        setBackgroundResource(C5562R.drawable.background_button_rectangle);
        int attributeResourceValue = attributeSet.getAttributeResourceValue("http://schemas.android.com/apk/res/android", CSS.Property.f74018a, -1);
        if (attributeResourceValue != -1) {
            color = getResources().getColor(attributeResourceValue);
        } else {
            String attributeValue = attributeSet.getAttributeValue("http://schemas.android.com/apk/res/android", CSS.Property.f74018a);
            color = attributeValue != null ? Color.parseColor(attributeValue) : this.f101614h3;
        }
        setBackgroundColor(color);
    }

    @Override // android.view.View
    public void setBackgroundColor(int i2) {
        this.f101614h3 = i2;
        if (isEnabled()) {
            this.f101617X2 = this.f101614h3;
        }
        ((GradientDrawable) ((LayerDrawable) getBackground()).findDrawableByLayerId(C5562R.id.shape_bacground)).setColor(this.f101614h3);
    }
}

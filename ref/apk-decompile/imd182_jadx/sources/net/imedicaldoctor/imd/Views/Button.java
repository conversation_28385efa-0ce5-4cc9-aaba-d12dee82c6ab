package net.imedicaldoctor.imd.Views;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;
import net.imedicaldoctor.imd.C5562R;

/* loaded from: classes3.dex */
public abstract class Button extends CustomView {

    /* renamed from: p3 */
    static final String f101571p3 = "http://schemas.android.com/apk/res/android";

    /* renamed from: c3 */
    int f101572c3;

    /* renamed from: d3 */
    int f101573d3;

    /* renamed from: e3 */
    int f101574e3;

    /* renamed from: f3 */
    float f101575f3;

    /* renamed from: g3 */
    int f101576g3;

    /* renamed from: h3 */
    Integer f101577h3;

    /* renamed from: i3 */
    View.OnClickListener f101578i3;

    /* renamed from: j3 */
    boolean f101579j3;

    /* renamed from: k3 */
    int f101580k3;

    /* renamed from: l3 */
    TextView f101581l3;

    /* renamed from: m3 */
    float f101582m3;

    /* renamed from: n3 */
    float f101583n3;

    /* renamed from: o3 */
    float f101584o3;

    public Button(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f101575f3 = 12.0f;
        this.f101576g3 = 3;
        this.f101579j3 = true;
        this.f101580k3 = Color.parseColor("#1E88E5");
        this.f101582m3 = -1.0f;
        this.f101583n3 = -1.0f;
        this.f101584o3 = -1.0f;
        mo73503c();
        this.f101579j3 = attributeSet.getAttributeBooleanValue("http://schemas.android.com/apk/res-auto", "animate", true);
        setAttributes(attributeSet);
        this.f101617X2 = this.f101580k3;
        if (this.f101577h3 == null) {
            this.f101577h3 = Integer.valueOf(mo73502b());
        }
    }

    /* renamed from: a */
    public Bitmap m73501a() {
        Bitmap bitmapCreateBitmap = Bitmap.createBitmap(getWidth() - Utils.m73528a(6.0f, getResources()), getHeight() - Utils.m73528a(7.0f, getResources()), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmapCreateBitmap);
        canvas.drawARGB(0, 0, 0, 0);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(this.f101577h3.intValue());
        canvas.drawCircle(this.f101582m3, this.f101583n3, this.f101584o3, paint);
        if (this.f101584o3 > getHeight() / this.f101576g3) {
            this.f101584o3 += this.f101575f3;
        }
        if (this.f101584o3 >= getWidth()) {
            this.f101582m3 = -1.0f;
            this.f101583n3 = -1.0f;
            this.f101584o3 = getHeight() / this.f101576g3;
            View.OnClickListener onClickListener = this.f101578i3;
            if (onClickListener != null && this.f101579j3) {
                onClickListener.onClick(this);
            }
        }
        return bitmapCreateBitmap;
    }

    /* renamed from: b */
    protected int mo73502b() {
        int i2 = this.f101580k3;
        int i3 = (i2 >> 16) & 255;
        int i4 = (i2 >> 8) & 255;
        int i5 = i2 & 255;
        int i6 = i3 - 30;
        if (i6 < 0) {
            i6 = 0;
        }
        int i7 = i4 - 30;
        if (i7 < 0) {
            i7 = 0;
        }
        int i8 = i5 - 30;
        return Color.rgb(i6, i7, i8 >= 0 ? i8 : 0);
    }

    /* renamed from: c */
    protected void mo73503c() {
        setMinimumHeight(Utils.m73528a(this.f101573d3, getResources()));
        setMinimumWidth(Utils.m73528a(this.f101572c3, getResources()));
        setBackgroundResource(this.f101574e3);
        setBackgroundColor(this.f101580k3);
    }

    public float getRippleSpeed() {
        return this.f101575f3;
    }

    public String getText() {
        return this.f101581l3.getText().toString();
    }

    public TextView getTextView() {
        return this.f101581l3;
    }

    @Override // android.view.View
    protected void onFocusChanged(boolean z, int i2, Rect rect) {
        if (z) {
            return;
        }
        this.f101582m3 = -1.0f;
        this.f101583n3 = -1.0f;
    }

    @Override // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        return true;
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0075  */
    @Override // android.view.View
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean onTouchEvent(android.view.MotionEvent r7) {
        /*
            r6 = this;
            r6.invalidate()
            boolean r0 = r6.isEnabled()
            r1 = 1
            if (r0 == 0) goto Lc7
            r6.f101618Y2 = r1
            int r0 = r7.getAction()
            if (r0 != 0) goto L2a
            int r0 = r6.getHeight()
            int r2 = r6.f101576g3
            int r0 = r0 / r2
            float r0 = (float) r0
            r6.f101584o3 = r0
            float r0 = r7.getX()
            r6.f101582m3 = r0
            float r7 = r7.getY()
            r6.f101583n3 = r7
            goto Lc7
        L2a:
            int r0 = r7.getAction()
            r2 = 2
            r3 = 0
            r4 = 0
            r5 = -1082130432(0xffffffffbf800000, float:-1.0)
            if (r0 != r2) goto L7c
            int r0 = r6.getHeight()
            int r2 = r6.f101576g3
            int r0 = r0 / r2
            float r0 = (float) r0
            r6.f101584o3 = r0
            float r0 = r7.getX()
            r6.f101582m3 = r0
            float r0 = r7.getY()
            r6.f101583n3 = r0
            float r0 = r7.getX()
            int r2 = r6.getWidth()
            float r2 = (float) r2
            int r0 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r0 > 0) goto L75
            float r0 = r7.getX()
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            if (r0 < 0) goto L75
            float r0 = r7.getY()
            int r2 = r6.getHeight()
            float r2 = (float) r2
            int r0 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r0 > 0) goto L75
            float r7 = r7.getY()
            int r7 = (r7 > r4 ? 1 : (r7 == r4 ? 0 : -1))
            if (r7 >= 0) goto Lc7
        L75:
            r6.f101618Y2 = r3
            r6.f101582m3 = r5
            r6.f101583n3 = r5
            goto Lc7
        L7c:
            int r0 = r7.getAction()
            if (r0 != r1) goto Lbf
            float r0 = r7.getX()
            int r2 = r6.getWidth()
            float r2 = (float) r2
            int r0 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r0 > 0) goto L75
            float r0 = r7.getX()
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            if (r0 < 0) goto L75
            float r0 = r7.getY()
            int r2 = r6.getHeight()
            float r2 = (float) r2
            int r0 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r0 > 0) goto L75
            float r7 = r7.getY()
            int r7 = (r7 > r4 ? 1 : (r7 == r4 ? 0 : -1))
            if (r7 < 0) goto L75
            float r7 = r6.f101584o3
            r0 = 1065353216(0x3f800000, float:1.0)
            float r7 = r7 + r0
            r6.f101584o3 = r7
            boolean r7 = r6.f101579j3
            if (r7 != 0) goto Lc7
            android.view.View$OnClickListener r7 = r6.f101578i3
            if (r7 == 0) goto Lc7
            r7.onClick(r6)
            goto Lc7
        Lbf:
            int r7 = r7.getAction()
            r0 = 3
            if (r7 != r0) goto Lc7
            goto L75
        Lc7:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Views.Button.onTouchEvent(android.view.MotionEvent):boolean");
    }

    protected abstract void setAttributes(AttributeSet attributeSet);

    @Override // android.view.View
    public void setBackgroundColor(int i2) {
        this.f101580k3 = i2;
        if (isEnabled()) {
            this.f101617X2 = this.f101580k3;
        }
        try {
            ((GradientDrawable) ((LayerDrawable) getBackground()).findDrawableByLayerId(C5562R.id.shape_bacground)).setColor(this.f101580k3);
            this.f101577h3 = Integer.valueOf(mo73502b());
        } catch (Exception unused) {
        }
    }

    @Override // android.view.View
    public void setOnClickListener(View.OnClickListener onClickListener) {
        this.f101578i3 = onClickListener;
    }

    public void setRippleSpeed(float f2) {
        this.f101575f3 = f2;
    }

    public void setText(String str) {
        this.f101581l3.setText(str);
    }

    public void setTextColor(int i2) {
        this.f101581l3.setTextColor(i2);
    }
}

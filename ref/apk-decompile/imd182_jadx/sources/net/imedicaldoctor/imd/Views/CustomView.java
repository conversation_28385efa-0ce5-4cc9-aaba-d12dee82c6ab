package net.imedicaldoctor.imd.Views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.util.AttributeSet;
import android.widget.RelativeLayout;

/* loaded from: classes3.dex */
public class CustomView extends RelativeLayout {

    /* renamed from: a3 */
    static final String f101615a3 = "http://schemas.android.com/apk/res-auto";

    /* renamed from: b3 */
    static final String f101616b3 = "http://schemas.android.com/apk/res/android";

    /* renamed from: X2 */
    int f101617X2;

    /* renamed from: Y2 */
    public boolean f101618Y2;

    /* renamed from: Z2 */
    boolean f101619Z2;

    /* renamed from: s */
    final int f101620s;

    public CustomView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f101620s = Color.parseColor("#E2E2E2");
        this.f101618Y2 = false;
        this.f101619Z2 = false;
    }

    @Override // android.view.View
    protected void onAnimationEnd() {
        super.onAnimationEnd();
        this.f101619Z2 = false;
    }

    @Override // android.view.View
    protected void onAnimationStart() {
        super.onAnimationStart();
        this.f101619Z2 = true;
    }

    @Override // android.view.View
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (this.f101619Z2) {
            invalidate();
        }
    }

    @Override // android.view.View
    public void setEnabled(boolean z) {
        super.setEnabled(z);
        setBackgroundColor(z ? this.f101617X2 : this.f101620s);
        invalidate();
    }
}

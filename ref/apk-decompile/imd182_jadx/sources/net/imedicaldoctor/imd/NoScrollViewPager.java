package net.imedicaldoctor.imd;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import androidx.viewpager.widget.ViewPager;

/* loaded from: classes3.dex */
public class NoScrollViewPager extends ViewPager {

    /* renamed from: v4 */
    private boolean f90918v4;

    public NoScrollViewPager(Context context) {
        super(context);
        this.f90918v4 = true;
    }

    @Override // androidx.viewpager.widget.ViewPager, android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        try {
            if (this.f90918v4) {
                return super.onInterceptTouchEvent(motionEvent);
            }
            return false;
        } catch (Exception unused) {
            return false;
        }
    }

    @Override // androidx.viewpager.widget.ViewPager, android.view.View
    public boolean onTouchEvent(MotionEvent motionEvent) {
        return this.f90918v4 && super.onTouchEvent(motionEvent);
    }

    public void setPagingEnabled(boolean z) {
        this.f90918v4 = z;
    }

    public NoScrollViewPager(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f90918v4 = true;
    }
}

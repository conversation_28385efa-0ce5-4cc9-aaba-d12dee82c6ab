package net.imedicaldoctor.imd;

import android.app.Activity;
import android.content.Context;
import android.widget.Toast;

/* loaded from: classes3.dex */
public class Premissions extends Activity {

    /* renamed from: X */
    private static final int f90930X = 1;

    /* renamed from: s */
    private final Context f90931s;

    public Premissions(Context context) {
        this.f90931s = context;
    }

    @Override // android.app.Activity
    public void onRequestPermissionsResult(int i2, String[] strArr, int[] iArr) {
        Context context;
        String str;
        if (i2 != 1) {
            return;
        }
        if (iArr.length <= 0 || iArr[0] != 0) {
            context = this.f90931s;
            str = "Permission Denied";
        } else {
            context = this.f90931s;
            str = "Permission Granted";
        }
        Toast.makeText(context, str, 0).show();
    }
}

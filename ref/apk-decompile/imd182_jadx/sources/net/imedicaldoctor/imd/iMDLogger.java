package net.imedicaldoctor.imd;

import android.os.Looper;
import android.util.Log;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.requery.android.database.sqlite.SQLiteDatabase;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class iMDLogger {
    /* renamed from: a */
    private static boolean m73545a() {
        return iMD.m73536d().getSharedPreferences("default_preferences", 0).getBoolean("savelogs", false);
    }

    /* renamed from: b */
    private static void m73546b(String str, String str2, String str3, Exception exc) {
        try {
            if (str.equals("e")) {
                Log.e(str2, str3);
            } else if (str.equals("d")) {
                Log.d(str2, str3);
            } else if (str.equals("w")) {
                Log.w(str2, str3);
            } else if (str.equals("v")) {
                Log.v(str2, str3);
            } else if (str.equals("i")) {
                Log.i(str2, str3);
            }
            if (m73545a()) {
                m73558n(str, str2, str3, exc != null ? exc.getLocalizedMessage() : "");
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: c */
    public static String m73547c() {
        try {
            String str = new CompressHelper(iMD.m73535c()).m71797M1() + "/zlogs.db";
            if (!new File(str).exists()) {
                SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("create table logs (id integer primary key autoincrement,cat varchar(10), tag varchar(255), description text, error text, date varchar(50), appsession varchar(20),stacktrace text);");
            }
            return str;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return null;
        }
    }

    /* renamed from: d */
    public static void m73548d(String str, String str2) {
        m73546b("d", str, str2, null);
    }

    /* renamed from: e */
    public static void m73549e(String str, String str2, Exception exc) {
        m73546b("d", str, str2, exc);
    }

    /* renamed from: f */
    public static void m73550f(String str, String str2) {
        m73546b("e", str, str2, null);
    }

    /* renamed from: g */
    public static void m73551g(String str, String str2, Exception exc) {
        m73546b("e", str, str2, exc);
    }

    /* renamed from: h */
    public static void m73552h(String str, String str2) {
        m73546b("i", str, str2, null);
    }

    /* renamed from: i */
    public static void m73553i(String str, String str2, Exception exc) {
        m73546b("i", str, str2, exc);
    }

    /* renamed from: j */
    public static void m73554j(String str, String str2) {
        m73546b("v", str, str2, null);
    }

    /* renamed from: k */
    public static void m73555k(String str, String str2, Exception exc) {
        m73546b("v", str, str2, exc);
    }

    /* renamed from: l */
    public static void m73556l(String str, String str2) {
        m73546b("w", str, str2, null);
    }

    /* renamed from: m */
    public static void m73557m(String str, String str2, Exception exc) {
        m73546b("w", str, str2, exc);
    }

    /* renamed from: n */
    private static void m73558n(String str, String str2, String str3, String str4) {
        try {
            if (m73547c() == null) {
                return;
            }
            CompressHelper compressHelper = new CompressHelper(iMD.m73536d());
            String strM73559o = m73559o(str);
            String strM73559o2 = m73559o(str2);
            String strM73559o3 = m73559o(str4);
            String strM73559o4 = m73559o(str3);
            String str5 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String strValueOf = String.valueOf(Looper.getMainLooper().getThread().getId());
            compressHelper.m71881q(m73547c(), "Insert into logs (cat, tag, description,error, date, appsession, stacktrace) values ('" + strM73559o + "', '" + strM73559o2 + "', '" + strM73559o4 + "', '" + strM73559o3 + "', '" + str5 + "', '" + strValueOf + "', '')");
        } catch (Exception unused) {
        }
    }

    /* renamed from: o */
    private static String m73559o(String str) {
        return str.replace("'", "''");
    }
}

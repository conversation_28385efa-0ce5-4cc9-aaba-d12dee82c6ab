package net.imedicaldoctor.imd;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.fragment.app.Fragment;

/* loaded from: classes3.dex */
public class iMDActivity extends AppCompatActivity {
    /* renamed from: Y0 */
    public void m73543Y0(Fragment fragment, Bundle bundle) {
        fragment.m15342i2(getIntent().getExtras());
        if (getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
            getWindow().setFlags(67108864, 67108864);
            View viewFindViewById = findViewById(C5562R.id.detail_container);
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) viewFindViewById.getLayoutParams();
            layoutParams.setMargins(0, -mo72763Z0(), 0, 0);
            viewFindViewById.setLayoutParams(layoutParams);
        }
        m1121E0().mo1186d0(false);
        m15416k0().m15664u().m15803C(C5562R.id.detail_container, fragment).mo15164r();
    }

    /* renamed from: Z0 */
    public int mo72763Z0() {
        int identifier = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (identifier > 0) {
            return getResources().getDimensionPixelSize(identifier);
        }
        return 0;
    }

    /* renamed from: a1 */
    public void m73544a1(Bundle bundle, Fragment fragment) {
        setContentView(C5562R.layout.activity_general_list);
        if (bundle == null) {
            fragment.m15342i2(getIntent().getExtras());
            if (getSharedPreferences("default_preferences", 0).getBoolean("HideStatusBar", false)) {
                getWindow().setFlags(67108864, 67108864);
                View viewFindViewById = findViewById(C5562R.id.container);
                FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) viewFindViewById.getLayoutParams();
                layoutParams.setMargins(0, -mo72763Z0(), 0, 0);
                viewFindViewById.setLayoutParams(layoutParams);
            }
            m15416k0().m15664u().m15818f(C5562R.id.container, fragment).mo15164r();
        }
    }

    @Override // android.view.ComponentActivity, android.app.Activity
    public void onBackPressed() {
        finish();
        overridePendingTransition(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
    }

    @Override // androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(@Nullable Bundle bundle) {
        super.onCreate(bundle);
        if (getSharedPreferences("default_preferences", 0).getBoolean("dark", false)) {
            AppCompatDelegate.m1154c0(2);
        } else {
            AppCompatDelegate.m1154c0(1);
        }
        if (getSharedPreferences("default_preferences", 0).getBoolean("wakelock", true)) {
            getWindow().addFlags(128);
        }
    }

    @Override // android.app.Activity, android.content.ContextWrapper, android.content.Context
    public void startActivity(Intent intent) {
        super.startActivity(intent);
        overridePendingTransition(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out);
    }
}

package net.imedicaldoctor.imd;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.SparseIntArray;
import android.util.TypedValue;
import androidx.appcompat.widget.AppCompatTextView;

/* loaded from: classes3.dex */
public class AutoResizeTextView extends AppCompatTextView {

    /* renamed from: p3 */
    private static final int f87186p3 = -1;

    /* renamed from: d3 */
    private final RectF f87187d3;

    /* renamed from: e3 */
    private final SparseIntArray f87188e3;

    /* renamed from: f3 */
    private final SizeTester f87189f3;

    /* renamed from: g3 */
    private float f87190g3;

    /* renamed from: h3 */
    private float f87191h3;

    /* renamed from: i3 */
    private float f87192i3;

    /* renamed from: j3 */
    private float f87193j3;

    /* renamed from: k3 */
    private int f87194k3;

    /* renamed from: l3 */
    private int f87195l3;

    /* renamed from: m3 */
    private boolean f87196m3;

    /* renamed from: n3 */
    private boolean f87197n3;

    /* renamed from: o3 */
    private TextPaint f87198o3;

    private interface SizeTester {
        /* renamed from: a */
        int mo71580a(int i2, RectF rectF);
    }

    public AutoResizeTextView(Context context) {
        this(context, null, 0);
    }

    /* renamed from: A */
    private int m71571A(int i2, int i3, SizeTester sizeTester, RectF rectF) {
        int i4 = i3 - 1;
        int i5 = i2;
        while (i2 <= i4) {
            i5 = (i2 + i4) >>> 1;
            int iMo71580a = sizeTester.mo71580a(i5, rectF);
            if (iMo71580a >= 0) {
                if (iMo71580a <= 0) {
                    break;
                }
                i5--;
                i4 = i5;
            } else {
                int i6 = i5 + 1;
                i5 = i2;
                i2 = i6;
            }
        }
        return i5;
    }

    /* renamed from: B */
    private int m71572B(int i2, int i3, SizeTester sizeTester, RectF rectF) {
        if (!this.f87196m3) {
            return m71571A(i2, i3, sizeTester, rectF);
        }
        String string = getText().toString();
        int length = string == null ? 0 : string.length();
        int i4 = this.f87188e3.get(length);
        if (i4 != 0) {
            return i4;
        }
        int iM71571A = m71571A(i2, i3, sizeTester, rectF);
        this.f87188e3.put(length, iM71571A);
        return iM71571A;
    }

    /* renamed from: C */
    private void m71573C() {
        m71579z();
    }

    /* renamed from: D */
    private void m71574D(int i2) {
        super.setTextSize(0, m71572B(i2, (int) this.f87190g3, this.f87189f3, this.f87187d3));
    }

    /* renamed from: z */
    private void m71579z() {
        if (this.f87197n3) {
            int i2 = (int) this.f87193j3;
            int measuredHeight = (getMeasuredHeight() - getCompoundPaddingBottom()) - getCompoundPaddingTop();
            int measuredWidth = (getMeasuredWidth() - getCompoundPaddingLeft()) - getCompoundPaddingRight();
            this.f87194k3 = measuredWidth;
            if (measuredWidth <= 0) {
                return;
            }
            RectF rectF = this.f87187d3;
            rectF.right = measuredWidth;
            rectF.bottom = measuredHeight;
            m71574D(i2);
        }
    }

    @Override // android.widget.TextView
    public int getMaxLines() {
        return this.f87195l3;
    }

    @Override // android.view.View
    protected void onSizeChanged(int i2, int i3, int i4, int i5) {
        this.f87188e3.clear();
        super.onSizeChanged(i2, i3, i4, i5);
        if (i2 == i4 && i3 == i5) {
            return;
        }
        m71573C();
    }

    @Override // androidx.appcompat.widget.AppCompatTextView, android.widget.TextView
    protected void onTextChanged(CharSequence charSequence, int i2, int i3, int i4) {
        super.onTextChanged(charSequence, i2, i3, i4);
        m71573C();
    }

    public void setEnableSizeCache(boolean z) {
        this.f87196m3 = z;
        this.f87188e3.clear();
        m71579z();
    }

    @Override // android.widget.TextView
    public void setLineSpacing(float f2, float f3) {
        super.setLineSpacing(f2, f3);
        this.f87191h3 = f3;
        this.f87192i3 = f2;
    }

    @Override // android.widget.TextView
    public void setLines(int i2) {
        super.setLines(i2);
        this.f87195l3 = i2;
        m71573C();
    }

    @Override // android.widget.TextView
    public void setMaxLines(int i2) {
        super.setMaxLines(i2);
        this.f87195l3 = i2;
        m71573C();
    }

    public void setMinTextSize(float f2) {
        this.f87193j3 = f2;
        m71573C();
    }

    @Override // android.widget.TextView
    public void setSingleLine() {
        super.setSingleLine();
        this.f87195l3 = 1;
        m71573C();
    }

    @Override // android.widget.TextView
    public void setTextSize(float f2) {
        this.f87190g3 = f2;
        this.f87188e3.clear();
        m71579z();
    }

    @Override // android.widget.TextView
    public void setTypeface(Typeface typeface) {
        if (this.f87198o3 == null) {
            this.f87198o3 = new TextPaint(getPaint());
        }
        this.f87198o3.setTypeface(typeface);
        m71579z();
        super.setTypeface(typeface);
    }

    public AutoResizeTextView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    @Override // android.widget.TextView
    public void setSingleLine(boolean z) {
        super.setSingleLine(z);
        this.f87195l3 = z ? 1 : -1;
        m71573C();
    }

    @Override // androidx.appcompat.widget.AppCompatTextView, android.widget.TextView
    public void setTextSize(int i2, float f2) {
        Context context = getContext();
        this.f87190g3 = TypedValue.applyDimension(i2, f2, (context == null ? Resources.getSystem() : context.getResources()).getDisplayMetrics());
        this.f87188e3.clear();
        m71579z();
    }

    public AutoResizeTextView(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        this.f87187d3 = new RectF();
        this.f87188e3 = new SparseIntArray();
        this.f87191h3 = 1.0f;
        this.f87192i3 = 0.0f;
        this.f87196m3 = true;
        this.f87197n3 = false;
        this.f87193j3 = TypedValue.applyDimension(2, 12.0f, getResources().getDisplayMetrics());
        this.f87190g3 = getTextSize();
        if (this.f87195l3 == 0) {
            this.f87195l3 = -1;
        }
        this.f87189f3 = new SizeTester() { // from class: net.imedicaldoctor.imd.AutoResizeTextView.1

            /* renamed from: a */
            final RectF f87199a = new RectF();

            @Override // net.imedicaldoctor.imd.AutoResizeTextView.SizeTester
            @TargetApi(16)
            /* renamed from: a */
            public int mo71580a(int i3, RectF rectF) {
                RectF rectF2;
                float fMeasureText;
                AutoResizeTextView.this.f87198o3.setTextSize(i3);
                String string = AutoResizeTextView.this.getText().toString();
                if (AutoResizeTextView.this.getMaxLines() == 1) {
                    this.f87199a.bottom = AutoResizeTextView.this.f87198o3.getFontSpacing();
                    rectF2 = this.f87199a;
                    fMeasureText = AutoResizeTextView.this.f87198o3.measureText(string);
                } else {
                    StaticLayout staticLayout = new StaticLayout(string, AutoResizeTextView.this.f87198o3, AutoResizeTextView.this.f87194k3, Layout.Alignment.ALIGN_NORMAL, AutoResizeTextView.this.f87191h3, AutoResizeTextView.this.f87192i3, true);
                    if (AutoResizeTextView.this.getMaxLines() != -1 && staticLayout.getLineCount() > AutoResizeTextView.this.getMaxLines()) {
                        return 1;
                    }
                    this.f87199a.bottom = staticLayout.getHeight();
                    int lineRight = -1;
                    for (int i4 = 0; i4 < staticLayout.getLineCount(); i4++) {
                        if (lineRight < staticLayout.getLineRight(i4) - staticLayout.getLineLeft(i4)) {
                            lineRight = ((int) staticLayout.getLineRight(i4)) - ((int) staticLayout.getLineLeft(i4));
                        }
                    }
                    rectF2 = this.f87199a;
                    fMeasureText = lineRight;
                }
                rectF2.right = fMeasureText;
                this.f87199a.offsetTo(0.0f, 0.0f);
                return rectF.contains(this.f87199a) ? -1 : 1;
            }
        };
        this.f87197n3 = true;
    }
}

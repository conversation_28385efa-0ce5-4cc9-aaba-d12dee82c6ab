package net.imedicaldoctor.imd.Gallery;

import android.os.Bundle;
import androidx.viewpager.widget.ViewPager;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.iMDActivity;

/* loaded from: classes3.dex */
public class GalleryActivity extends iMDActivity {

    /* renamed from: z3 */
    private static final String f90862z3 = "STATE_POSITION";

    /* renamed from: y3 */
    private ViewPager f90863y3;

    @Override // net.imedicaldoctor.imd.iMDActivity, androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.activity_gallery);
        ImagePagerFragment imagePagerFragment = new ImagePagerFragment();
        Bundle bundle2 = new Bundle();
        bundle2.putParcelableArrayList("Images", getIntent().getParcelableArrayListExtra("Images"));
        bundle2.putInt("Start", getIntent().getIntExtra("Start", 0));
        if (getIntent().getParcelableArrayListExtra("Images").size() == 0) {
            CompressHelper.m71767x2(this, "There is no image in this document", 1);
            finish();
        }
        imagePagerFragment.m15342i2(bundle2);
        m15416k0().m15664u().m15803C(C5562R.id.detail_container, imagePagerFragment).mo15164r();
    }
}

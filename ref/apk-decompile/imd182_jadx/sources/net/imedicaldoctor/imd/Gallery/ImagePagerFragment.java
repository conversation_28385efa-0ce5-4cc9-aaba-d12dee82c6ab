package net.imedicaldoctor.imd.Gallery;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Parcelable;
import android.provider.MediaStore;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.MediaController;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.VideoView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.media3.common.MimeTypes;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Views.ButtonSmall;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;

/* loaded from: classes3.dex */
public class ImagePagerFragment extends Fragment {

    /* renamed from: m4 */
    public static final String f90864m4 = "com.nostra13.example.universalimageloader.FRAGMENT_INDEX";

    /* renamed from: n4 */
    public static final String f90865n4 = "com.nostra13.example.universalimageloader.IMAGE_POSITION";

    /* renamed from: o4 */
    public static final int f90866o4 = 2;

    /* renamed from: p4 */
    private static int f90867p4;

    /* renamed from: e4 */
    ExoPlayer f90868e4;

    /* renamed from: f4 */
    ArrayList<Bundle> f90869f4;

    /* renamed from: g4 */
    public Toolbar f90870g4;

    /* renamed from: h4 */
    public TextView f90871h4;

    /* renamed from: i4 */
    public MediaController f90872i4;

    /* renamed from: j4 */
    public VideoView f90873j4;

    /* renamed from: k4 */
    public String f90874k4;

    /* renamed from: l4 */
    public Typeface f90875l4;

    private class ImageAdapter extends PagerAdapter {

        /* renamed from: g */
        static final /* synthetic */ boolean f90887g = false;

        /* renamed from: e */
        private final LayoutInflater f90888e;

        ImageAdapter() {
            this.f90888e = LayoutInflater.from(ImagePagerFragment.this.m15366r());
        }

        @Override // androidx.viewpager.widget.PagerAdapter
        /* renamed from: b */
        public void mo15726b(ViewGroup viewGroup, int i2, Object obj) {
            viewGroup.removeView((View) obj);
        }

        @Override // androidx.viewpager.widget.PagerAdapter
        /* renamed from: e */
        public int mo28926e() {
            return ImagePagerFragment.this.f90869f4.size();
        }

        /* JADX WARN: Removed duplicated region for block: B:31:0x0234  */
        /* JADX WARN: Removed duplicated region for block: B:33:0x0246  */
        @Override // androidx.viewpager.widget.PagerAdapter
        @android.annotation.SuppressLint({"UnsafeOptInUsageError"})
        /* renamed from: j */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public java.lang.Object mo15728j(android.view.ViewGroup r21, final int r22) throws java.io.IOException {
            /*
                Method dump skipped, instructions count: 700
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.ImageAdapter.mo15728j(android.view.ViewGroup, int):java.lang.Object");
        }

        @Override // androidx.viewpager.widget.PagerAdapter
        /* renamed from: k */
        public boolean mo15729k(View view, Object obj) {
            return view.equals(obj);
        }

        @Override // androidx.viewpager.widget.PagerAdapter
        /* renamed from: n */
        public void mo15730n(Parcelable parcelable, ClassLoader classLoader) {
        }

        @Override // androidx.viewpager.widget.PagerAdapter
        /* renamed from: o */
        public Parcelable mo15731o() {
            return null;
        }
    }

    /* renamed from: L2 */
    public static void m73381L2(String str, Context context) {
        ContentValues contentValues = new ContentValues();
        contentValues.put("datetaken", Long.valueOf(System.currentTimeMillis()));
        contentValues.put("mime_type", MimeTypes.f19892f);
        contentValues.put("_data", str);
        context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
    }

    /* renamed from: J2 */
    public Boolean m73382J2() {
        try {
            if (Build.VERSION.SDK_INT < 23 || ContextCompat.m6681a(m15366r(), "android.permission.WRITE_EXTERNAL_STORAGE") == 0) {
                return Boolean.TRUE;
            }
            if (ActivityCompat.m5650T(m15366r(), "android.permission.WRITE_EXTERNAL_STORAGE")) {
                Toast.makeText(m15366r(), "", 1).show();
            }
            new AlertDialog.Builder(m15366r(), C5562R.style.alertDialogTheme).mo1102l("Write External Storage permission allows us to save images on your device").mo1115y("Allow it", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.2
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                    ActivityCompat.m5644N(ImagePagerFragment.this.m15366r(), new String[]{"android.permission.WRITE_EXTERNAL_STORAGE"}, 1);
                }
            }).mo1106p("Not now", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.1
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                }
            }).m1090I();
            return Boolean.FALSE;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            Log.e("PremissionGranted", e2.getLocalizedMessage());
            return Boolean.FALSE;
        }
    }

    /* renamed from: K2 */
    public void m73383K2(String str, Context context) {
        try {
            m73384M2(str, context);
        } catch (Exception unused) {
            Toast.makeText(context, "Error in saving image", 0).show();
        }
    }

    /* renamed from: M2 */
    public void m73384M2(String str, Context context) throws IOException {
        OutputStream fileOutputStream;
        String name = new File(str).getName();
        if (Build.VERSION.SDK_INT >= 29) {
            ContentResolver contentResolver = context.getContentResolver();
            ContentValues contentValues = new ContentValues();
            contentValues.put("_display_name", name);
            contentValues.put("mime_type", MimeTypes.f19867R0);
            contentValues.put("relative_path", "DCIM/iMD");
            fileOutputStream = contentResolver.openOutputStream(contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues));
        } else {
            if (!m73382J2().booleanValue()) {
                return;
            }
            String string = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM + "/iMD").toString();
            File file = new File(string);
            if (!file.exists()) {
                file.mkdir();
            }
            fileOutputStream = new FileOutputStream(new File(string, name));
        }
        BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(new File(str)));
        BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75780p(fileOutputStream));
        bufferedSinkM75768d.mo75508y1(bufferedSourceM75769e);
        bufferedSourceM75769e.close();
        bufferedSinkM75768d.close();
        Toast.makeText(context, "Image Saved", 0).show();
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: Q0 */
    public void mo15207Q0(Bundle bundle) {
        super.mo15207Q0(bundle);
        this.f90875l4 = Typeface.createFromAsset(m15366r().getAssets(), "fonts/HelveticaNeue-Light.otf");
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: U0 */
    public View mo15303U0(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) throws Resources.NotFoundException {
        this.f90869f4 = m15387y().getParcelableArrayList("Images");
        View viewInflate = layoutInflater.inflate(C5562R.layout.fr_image_pager, viewGroup, false);
        final ViewPager viewPager = (ViewPager) viewInflate.findViewById(C5562R.id.pager);
        viewPager.setAdapter(new ImageAdapter());
        Toolbar toolbar = (Toolbar) viewInflate.findViewById(C5562R.id.toolbar);
        this.f90870g4 = toolbar;
        this.f90871h4 = (TextView) toolbar.findViewById(C5562R.id.toolbar_title);
        ButtonSmall buttonSmall = (ButtonSmall) viewInflate.findViewById(C5562R.id.back_button);
        this.f90870g4.setNavigationOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                ImagePagerFragment.this.m15366r().finish();
                ImagePagerFragment.this.m15366r().overridePendingTransition(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
            }
        });
        if (buttonSmall != null) {
            buttonSmall.setDrawableIcon(m15366r().getResources().getDrawable(C5562R.drawable.back_icon));
            buttonSmall.setRippleColor(m15366r().getResources().getColor(C5562R.color.toolbar_item_ripple_color));
            buttonSmall.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    ImagePagerFragment.this.m15366r().finish();
                    ImagePagerFragment.this.m15366r().overridePendingTransition(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
                }
            });
        }
        ((TextView) viewInflate.findViewById(C5562R.id.show_in_text)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
            }
        });
        ((ImageButton) viewInflate.findViewById(C5562R.id.action_share)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.6
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                String str;
                FragmentActivity fragmentActivityM15366r;
                String str2;
                int i2;
                String string;
                StringBuilder sb;
                Bundle bundle2 = ImagePagerFragment.this.f90869f4.get(viewPager.getCurrentItem());
                if (bundle2.containsKey("isVideo")) {
                    CompressHelper.m71767x2(ImagePagerFragment.this.m15366r(), "Can't share video", 0);
                    return;
                }
                String string2 = bundle2.containsKey("Description") ? bundle2.getString("Description") : "";
                String string3 = bundle2.containsKey("DescriptionHTML") ? bundle2.getString("DescriptionHTML") : "";
                if (bundle2.containsKey("DescriptionHTML2")) {
                    string3 = bundle2.getString("DescriptionHTML2");
                }
                if (string2.length() == 0) {
                    string2 = string3;
                }
                if (bundle2.containsKey("Encrypted")) {
                    try {
                        File file = new File(bundle2.getString("ImagePath"));
                        byte[] bArrMo75462b0 = Okio.m75769e(Okio.m75784t(file)).mo75462b0();
                        String name = file.getName();
                        byte[] bArrM71899w = new CompressHelper(ImagePagerFragment.this.m15366r()).m71899w(bArrMo75462b0, name, "127");
                        File file2 = new File(new CompressHelper(ImagePagerFragment.this.m15366r()).m71797M1() + "/" + name);
                        if (file2.exists()) {
                            file2.delete();
                        }
                        Okio.m75768d(Okio.m75778n(file2)).write(bArrM71899w).close();
                        Intent intent = new Intent();
                        intent.setAction("android.intent.action.SEND");
                        intent.addFlags(1);
                        intent.putExtra("android.intent.extra.STREAM", FileProvider.m6734h(ImagePagerFragment.this.m15366r(), ImagePagerFragment.this.m15366r().getApplicationContext().getPackageName() + ".provider", file2));
                        if (string2.length() > 0) {
                            intent.putExtra("android.intent.extra.TEXT", string2);
                        }
                        intent.setType("image/*");
                        ImagePagerFragment.this.mo15256D2(Intent.createChooser(intent, "Share Image ..."));
                        return;
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        CompressHelper.m71767x2(ImagePagerFragment.this.m15366r(), "Can't share this photo", 0);
                        return;
                    }
                }
                if (bundle2.containsKey("base64")) {
                    try {
                        string = bundle2.getString("name");
                        sb = new StringBuilder();
                        str = "Can't share this photo";
                    } catch (Exception e3) {
                        e = e3;
                        str = "Can't share this photo";
                    }
                    try {
                        sb.append(new CompressHelper(ImagePagerFragment.this.m15366r()).m71797M1());
                        sb.append("/");
                        sb.append(string);
                        File file3 = new File(sb.toString());
                        if (file3.exists()) {
                            file3.delete();
                        }
                        Okio.m75768d(Okio.m75778n(file3)).write(bundle2.getByteArray("base64")).close();
                        Intent intent2 = new Intent();
                        intent2.setAction("android.intent.action.SEND");
                        intent2.putExtra("android.intent.extra.STREAM", FileProvider.m6734h(ImagePagerFragment.this.m15366r(), ImagePagerFragment.this.m15366r().getApplicationContext().getPackageName() + ".provider", file3));
                        if (string2.length() > 0) {
                            intent2.putExtra("android.intent.extra.TEXT", string2);
                        }
                        intent2.setType("image/*");
                        intent2.addFlags(1);
                        ImagePagerFragment.this.mo15256D2(Intent.createChooser(intent2, "Share Image ..."));
                        return;
                    } catch (Exception e4) {
                        e = e4;
                        FirebaseCrashlytics.m48010d().m48016g(e);
                        e.printStackTrace();
                        fragmentActivityM15366r = ImagePagerFragment.this.m15366r();
                        str2 = str;
                        i2 = 0;
                        CompressHelper.m71767x2(fragmentActivityM15366r, str2, i2);
                    }
                }
                str2 = "Can't share this photo";
                i2 = 0;
                if (!bundle2.getString("ImagePath").contains("http://")) {
                    File file4 = new File(bundle2.getString("ImagePath"));
                    File file5 = new File(new CompressHelper(ImagePagerFragment.this.m15366r()).m71797M1() + "/" + file4.getName());
                    if (file5.exists()) {
                        file5.delete();
                    }
                    try {
                        BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file4));
                        try {
                            BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file5));
                            try {
                                bufferedSinkM75768d.mo75508y1(bufferedSourceM75769e);
                                bufferedSinkM75768d.close();
                                if (bufferedSourceM75769e != null) {
                                    bufferedSourceM75769e.close();
                                }
                            } finally {
                            }
                        } finally {
                        }
                    } catch (Exception e5) {
                        FirebaseCrashlytics.m48010d().m48016g(e5);
                        e5.printStackTrace();
                    }
                    Intent intent3 = new Intent();
                    intent3.setAction("android.intent.action.SEND");
                    intent3.putExtra("android.intent.extra.STREAM", FileProvider.m6734h(ImagePagerFragment.this.m15366r(), ImagePagerFragment.this.m15366r().getApplicationContext().getPackageName() + ".provider", file5));
                    intent3.setType("image/*");
                    intent3.addFlags(1);
                    ImagePagerFragment.this.mo15256D2(Intent.createChooser(intent3, "Share Image ..."));
                    return;
                }
                fragmentActivityM15366r = ImagePagerFragment.this.m15366r();
                CompressHelper.m71767x2(fragmentActivityM15366r, str2, i2);
            }
        });
        ((ImageButton) viewInflate.findViewById(C5562R.id.action_save)).setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.7
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                FragmentActivity fragmentActivityM15366r;
                String str;
                BufferedSource bufferedSourceM75769e;
                BufferedSink bufferedSinkM75768d;
                Bundle bundle2 = ImagePagerFragment.this.f90869f4.get(viewPager.getCurrentItem());
                if (bundle2.containsKey("isVideo")) {
                    CompressHelper.m71767x2(ImagePagerFragment.this.m15366r(), "Can't save video", 0);
                    return;
                }
                if (bundle2.containsKey("Encrypted")) {
                    try {
                        File file = new File(bundle2.getString("ImagePath"));
                        bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file));
                        try {
                            byte[] bArrMo75462b0 = bufferedSourceM75769e.mo75462b0();
                            bufferedSourceM75769e.close();
                            String name = file.getName();
                            byte[] bArrM71899w = new CompressHelper(ImagePagerFragment.this.m15366r()).m71899w(bArrMo75462b0, name, "127");
                            File file2 = new File(new CompressHelper(ImagePagerFragment.this.m15366r()).m71797M1() + "/" + name);
                            if (file2.exists()) {
                                file2.delete();
                            }
                            bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file2));
                            try {
                                bufferedSinkM75768d.write(bArrM71899w);
                                bufferedSinkM75768d.close();
                                ImagePagerFragment.this.m73383K2(file2.getPath(), ImagePagerFragment.this.m15366r());
                                return;
                            } finally {
                            }
                        } finally {
                            if (bufferedSourceM75769e != null) {
                                try {
                                    bufferedSourceM75769e.close();
                                } catch (Throwable th) {
                                    th.addSuppressed(th);
                                }
                            }
                        }
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                        e2.printStackTrace();
                        fragmentActivityM15366r = ImagePagerFragment.this.m15366r();
                        str = "Can't save this media";
                    }
                } else if (bundle2.containsKey("base64")) {
                    try {
                        File file3 = new File(new CompressHelper(ImagePagerFragment.this.m15366r()).m71797M1() + "/" + bundle2.getString("name"));
                        if (file3.exists()) {
                            file3.delete();
                        }
                        byte[] byteArray = bundle2.getByteArray("base64");
                        bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file3));
                        try {
                            bufferedSinkM75768d.write(byteArray);
                            bufferedSinkM75768d.close();
                            ImagePagerFragment.this.m73383K2(file3.getPath(), ImagePagerFragment.this.m15366r());
                            return;
                        } finally {
                        }
                    } catch (Exception e3) {
                        FirebaseCrashlytics.m48010d().m48016g(e3);
                        e3.printStackTrace();
                        fragmentActivityM15366r = ImagePagerFragment.this.m15366r();
                        str = "Can't save this photo";
                    }
                } else {
                    if (!bundle2.getString("ImagePath").contains("http://")) {
                        File file4 = new File(bundle2.getString("ImagePath"));
                        File file5 = new File(new CompressHelper(ImagePagerFragment.this.m15366r()).m71797M1() + "/" + file4.getName());
                        if (file5.exists()) {
                            file5.delete();
                        }
                        try {
                            bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file4));
                        } catch (Exception e4) {
                            FirebaseCrashlytics.m48010d().m48016g(e4);
                            e4.printStackTrace();
                        }
                        try {
                            BufferedSink bufferedSinkM75768d2 = Okio.m75768d(Okio.m75778n(file5));
                            try {
                                bufferedSinkM75768d2.mo75508y1(bufferedSourceM75769e);
                                bufferedSinkM75768d2.close();
                                if (bufferedSourceM75769e != null) {
                                    bufferedSourceM75769e.close();
                                }
                                ImagePagerFragment.this.m73383K2(file5.getPath(), ImagePagerFragment.this.m15366r());
                                return;
                            } finally {
                            }
                        } finally {
                        }
                    }
                    fragmentActivityM15366r = ImagePagerFragment.this.m15366r();
                    str = "Can't share this media";
                }
                CompressHelper.m71767x2(fragmentActivityM15366r, str, 0);
            }
        });
        final int iMo28926e = viewPager.getAdapter().mo28926e();
        viewPager.setCurrentItem(m15387y() != null ? m15387y().getInt("Start", 0) : 0);
        if (bundle != null) {
            f90867p4 = bundle.getInt("Start");
        }
        int i2 = f90867p4;
        if (i2 > 0) {
            viewPager.setCurrentItem(i2);
        }
        this.f90871h4.setText((viewPager.getCurrentItem() + 1) + " of " + iMo28926e);
        viewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() { // from class: net.imedicaldoctor.imd.Gallery.ImagePagerFragment.8
            @Override // androidx.viewpager.widget.ViewPager.OnPageChangeListener
            /* renamed from: a */
            public void mo28941a(int i3, float f2, int i4) {
            }

            @Override // androidx.viewpager.widget.ViewPager.OnPageChangeListener
            /* renamed from: c */
            public void mo28943c(int i3) {
                MediaController mediaController = ImagePagerFragment.this.f90872i4;
                if (mediaController != null) {
                    mediaController.hide();
                    ImagePagerFragment.this.f90873j4.pause();
                }
            }

            @Override // androidx.viewpager.widget.ViewPager.OnPageChangeListener
            /* renamed from: d */
            public void mo28944d(int i3) {
                ImagePagerFragment.this.f90871h4.setText((i3 + 1) + " of " + iMo28926e);
            }
        });
        return viewInflate;
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: X0 */
    public void mo15214X0() {
        super.mo15214X0();
        ExoPlayer exoPlayer = this.f90868e4;
        if (exoPlayer != null) {
            exoPlayer.mo16953i1(false);
            this.f90868e4.stop();
            this.f90868e4.mo16940a();
            this.f90868e4 = null;
        }
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: m1 */
    public void mo15225m1(Bundle bundle) {
        super.mo15225m1(bundle);
    }

    @Override // androidx.fragment.app.Fragment
    /* renamed from: o1 */
    public void mo15227o1() {
        super.mo15227o1();
        ExoPlayer exoPlayer = this.f90868e4;
        if (exoPlayer != null) {
            exoPlayer.mo16953i1(false);
            this.f90868e4.stop();
            this.f90868e4.mo16940a();
            this.f90868e4 = null;
        }
    }
}

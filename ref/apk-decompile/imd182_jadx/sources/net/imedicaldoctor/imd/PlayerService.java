package net.imedicaldoctor.imd;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.media3.common.util.UnstableApi;
import androidx.media3.p004ui.PlayerNotificationManager;
import net.imedicaldoctor.imd.Fragments.CMEInfo.Player;

@UnstableApi
/* loaded from: classes3.dex */
public class PlayerService extends Service {

    /* renamed from: X */
    public static final String f90927X = "imd_channel";

    /* renamed from: Y */
    public static final int f90928Y = 2;

    /* renamed from: s */
    private PlayerNotificationManager f90929s;

    @Override // android.app.Service
    @Nullable
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override // android.app.Service
    public void onDestroy() {
        PlayerNotificationManager playerNotificationManager = this.f90929s;
        if (playerNotificationManager != null) {
            playerNotificationManager.m26297z(null);
            this.f90929s = null;
        }
        super.onDestroy();
    }

    @Override // android.app.Service
    public int onStartCommand(Intent intent, int i2, int i3) {
        PlayerNotificationManager playerNotificationManager = this.f90929s;
        if (playerNotificationManager != null) {
            return 1;
        }
        playerNotificationManager.m26297z(Player.f87654I3);
        return 1;
    }
}

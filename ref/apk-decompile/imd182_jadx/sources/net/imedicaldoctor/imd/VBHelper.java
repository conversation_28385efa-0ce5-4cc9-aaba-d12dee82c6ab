package net.imedicaldoctor.imd;

import android.content.Context;
import android.database.SQLException;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.media3.extractor.text.ttml.TtmlNode;
import com.google.common.base.Ascii;
import com.google.common.primitives.SignedBytes;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.itextpdf.text.DocWriter;
import com.itextpdf.text.pdf.PdfWriter;
import com.p008dd.plist.ASCIIPropertyListParser;
import com.p008dd.plist.NSDictionary;
import com.p008dd.plist.PropertyListParser;
import java.io.File;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.UUID;
import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import net.imedicaldoctor.imd.Data.CompressHelper;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class VBHelper {

    /* renamed from: d */
    static String[] f101423d;

    /* renamed from: e */
    static String f101424e;

    /* renamed from: f */
    static String f101425f;

    /* renamed from: g */
    static String f101426g;

    /* renamed from: a */
    Context f101427a;

    /* renamed from: b */
    CompressHelper f101428b;

    /* renamed from: c */
    Bundle f101429c;

    public VBHelper(Context context) {
        this.f101427a = context;
    }

    /* renamed from: a */
    public byte[] m73439a(String str) {
        if (str == null || str.length() == 0) {
            return null;
        }
        return m73459u(str);
    }

    /* renamed from: b */
    public String m73440b(String str, String str2) {
        byte[] bArr = {117, 115, 111, 102, 103, 104, 111, 111, 108, 122, 120, 119, 111, 91, 110, 109};
        try {
            return new String(m73447i(str2.toCharArray(), new byte[]{122, 12, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, bArr, m73439a(str)));
        } catch (Exception unused) {
            return null;
        }
    }

    /* renamed from: c */
    public Bundle m73441c(byte[] bArr) {
        return m73443e(bArr, null);
    }

    /* renamed from: d */
    public Bundle m73442d(File file) {
        try {
            return m73443e(CompressHelper.m71748d2(file), file);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("analyzeVBE vbeFile", "Error in reading vbe file " + e2);
            e2.printStackTrace();
            return null;
        }
    }

    /* renamed from: e */
    public Bundle m73443e(byte[] bArr, File file) {
        String strM73445g;
        Bundle bundleM73450l;
        boolean zM73463y = true;
        if (this.f101428b == null) {
            this.f101428b = new CompressHelper(this.f101427a);
        }
        if (file != null) {
            try {
                file.length();
                file.lastModified();
                strM73445g = m73445g(file.getAbsolutePath(), (file.length() + file.lastModified()) + "");
            } catch (Exception e2) {
                iMDLogger.m73550f("AnalyzeVBE", "Error in decrypting " + e2.getMessage());
                e2.printStackTrace();
                return null;
            }
        } else {
            strM73445g = null;
        }
        if (strM73445g == null) {
            char[] charArray = TextUtils.split(m73462x(m73451m()).replace("||", "::"), "::")[1].toCharArray();
            Charset charset = StandardCharsets.UTF_8;
            NSDictionary nSDictionary = (NSDictionary) PropertyListParser.m31891h(new String(m73447i(charArray, "info.vb ".getBytes(charset), new byte[]{17, 115, 105, 102, 103, 104, 111, 107, 108, 122, 120, 119, 118, 98, 110, 109}, bArr)).replace("&", "&amp;").getBytes(charset));
            bundleM73450l = new Bundle();
            for (String str : nSDictionary.m31841y()) {
                bundleM73450l.putString(str, nSDictionary.m31828N(str).toString().replace("soheilvb", "&"));
            }
            if (file != null) {
                this.f101428b.m71908z0(file.getAbsolutePath(), m73461w(bundleM73450l), (file.length() + file.lastModified()) + "");
            }
        } else {
            bundleM73450l = m73450l(strM73445g);
        }
        StringBuilder sb = new StringBuilder();
        if (bundleM73450l.containsKey("Version")) {
            zM73463y = m73463y(bundleM73450l.getString("Name"), bundleM73450l.getString("Version"), sb);
            if (sb.toString().length() > 0) {
                bundleM73450l.putString("ExpDate", sb.toString());
            }
        }
        if (!zM73463y) {
            bundleM73450l.putString("Inactive", IcyHeaders.f28171a3);
            if (m73460v(bundleM73450l.getString("Type"))) {
                bundleM73450l.putString("Demo", IcyHeaders.f28171a3);
            }
        }
        return bundleM73450l;
    }

    /* renamed from: f */
    public String m73444f(byte[] bArr) {
        char[] cArr = {'0', '1', PdfWriter.f71685p4, PdfWriter.f71687q4, PdfWriter.f71689r4, PdfWriter.f71691s4, PdfWriter.f71693t4, PdfWriter.f71695u4, '8', '9', 'A', ASCIIPropertyListParser.f44476u, 'C', ASCIIPropertyListParser.f44475t, 'E', 'F'};
        char[] cArr2 = new char[bArr.length * 2];
        for (int i2 = 0; i2 < bArr.length; i2++) {
            byte b2 = bArr[i2];
            int i3 = i2 * 2;
            cArr2[i3] = cArr[(b2 & 255) >>> 4];
            cArr2[i3 + 1] = cArr[b2 & 15];
        }
        return new String(cArr2);
    }

    /* renamed from: g */
    public String m73445g(String str, String str2) throws SQLException {
        Bundle bundleM73458t = m73458t();
        if (bundleM73458t.containsKey(str)) {
            Bundle bundle = bundleM73458t.getBundle(str);
            if (bundle.getString("cachevalidation").equals(str2)) {
                return bundle.getString("cachecontent");
            }
            CompressHelper compressHelper = this.f101428b;
            compressHelper.m71881q(compressHelper.m71778D0(), "Delete from cache where cachekey = '" + str + "'");
        }
        return null;
    }

    /* renamed from: h */
    public byte[] m73446h(char[] cArr, byte[] bArr, byte[] bArr2, byte[] bArr3) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1").generateSecret(new PBEKeySpec(cArr, bArr, 19, 128)).getEncoded(), "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(bArr2);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        cipher.init(1, secretKeySpec, ivParameterSpec);
        return cipher.doFinal(bArr3);
    }

    /* renamed from: i */
    public byte[] m73447i(char[] cArr, byte[] bArr, byte[] bArr2, byte[] bArr3) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1").generateSecret(new PBEKeySpec(cArr, bArr, 19, 128)).getEncoded(), "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(bArr2);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        cipher.init(2, secretKeySpec, ivParameterSpec);
        return cipher.doFinal(bArr3);
    }

    /* renamed from: j */
    public String m73448j(String str, String str2) {
        if (str == null || str.equals("")) {
            return "";
        }
        if (!str2.equals("127")) {
            return null;
        }
        byte[] bArr = {Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36, 37, 38, 39, Ascii.f60218H, Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36};
        try {
            return new String(m73447i("hs;d,hghdk[;ak".toCharArray(), new byte[]{122, 12, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, bArr, m73459u(str)));
        } catch (Exception unused) {
            return null;
        }
    }

    /* renamed from: k */
    public String m73449k(String str, String str2, String str3) {
        if (str == null) {
            return "";
        }
        if (!str2.equals("127")) {
            return null;
        }
        byte[] bArr = {Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36, 37, 38, 39, Ascii.f60218H, Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36};
        try {
            return new String(m73447i(str3.toCharArray(), new byte[]{122, 12, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, bArr, m73459u(str)));
        } catch (Exception unused) {
            return null;
        }
    }

    /* renamed from: l */
    public Bundle m73450l(String str) {
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "$$$");
        Bundle bundle = new Bundle();
        for (String str2 : strArrSplitByWholeSeparator) {
            String[] strArrSplitByWholeSeparator2 = StringUtils.splitByWholeSeparator(str2, ":::");
            bundle.putString(strArrSplitByWholeSeparator2[0], strArrSplitByWholeSeparator2[1]);
        }
        return bundle;
    }

    /* renamed from: m */
    public String m73451m() {
        int i2;
        try {
            i2 = this.f101427a.getPackageManager().getPackageInfo(this.f101427a.getPackageName(), 0).versionCode;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            i2 = 0;
        }
        String string = Settings.Secure.getString(this.f101427a.getContentResolver(), "android_id");
        if (string == null) {
            string = "soheilvb";
        }
        if (this.f101427a.getSharedPreferences("default_preferences", 0).contains("DS")) {
            String strM73449k = m73449k(this.f101427a.getSharedPreferences("default_preferences", 0).getString("DS", ""), "127", string + "30");
            if (strM73449k != null) {
                return strM73449k;
            }
        }
        byte[] bArr = {Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36, 37, 38, 39, Ascii.f60218H, Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36};
        try {
            String strReplace = m73444f(m73446h("hs;d,hghdk[;".toCharArray(), new byte[]{122, 13, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, bArr, (UUID.randomUUID().toString() + ",,,,," + i2).getBytes(StandardCharsets.UTF_8))).replace(StringUtils.f103471LF, "XX");
            String strM73453o = m73453o(strReplace, "127", string + "30");
            if (this.f101427a.getSharedPreferences("default_preferences", 0).contains("DS")) {
                this.f101427a.getSharedPreferences("default_preferences", 0).edit().remove("DS").commit();
            }
            this.f101427a.getSharedPreferences("default_preferences", 0).edit().putString("DS", strM73453o).commit();
            return strReplace;
        } catch (Exception e3) {
            FirebaseCrashlytics.m48010d().m48016g(e3);
            return null;
        }
    }

    /* renamed from: n */
    public String m73452n(String str, String str2) {
        if (!str2.equals("127")) {
            return null;
        }
        byte[] bArr = {Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36, 37, 38, 39, Ascii.f60218H, Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36};
        try {
            return m73444f(m73446h("hs;d,hghdk[;ak".toCharArray(), new byte[]{122, 12, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, bArr, str.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception unused) {
            return null;
        }
    }

    /* renamed from: o */
    public String m73453o(String str, String str2, String str3) {
        if (!str2.equals("127")) {
            return null;
        }
        byte[] bArr = {Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36, 37, 38, 39, Ascii.f60218H, Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36};
        try {
            return m73444f(m73446h(str3.toCharArray(), new byte[]{122, 12, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, bArr, str.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception unused) {
            return null;
        }
    }

    /* renamed from: p */
    public String m73454p(byte[] bArr, String str) {
        if (!str.equals("127")) {
            return null;
        }
        byte[] bArr2 = {Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36, 37, 38, 39, Ascii.f60218H, Ascii.f60219I, 32, 33, DocWriter.f68365e3, 35, 36};
        try {
            return m73444f(m73446h("hs;d,hghdk[;ak".toCharArray(), new byte[]{122, 12, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, bArr2, bArr));
        } catch (Exception unused) {
            return null;
        }
    }

    /* renamed from: q */
    public String m73455q(String str, String str2) {
        for (int length = str2.length(); length < 8; length++) {
            str2 = str2 + StringUtils.SPACE;
        }
        byte[] bArr = {117, 115, 111, 102, 103, 104, 111, 111, 108, 122, 120, 119, 111, 91, 110, 109};
        try {
            char[] charArray = "soheilvb'ghndhj,v".toCharArray();
            Charset charset = StandardCharsets.UTF_8;
            return m73444f(m73446h(charArray, str2.getBytes(charset), bArr, str.getBytes(charset)));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return null;
        }
    }

    /* renamed from: r */
    public String m73456r() {
        return this.f101427a.getSharedPreferences("default_preferences", 0).getString("ActivationCode", "");
    }

    /* renamed from: s */
    public void m73457s() {
        String strM73456r = m73456r();
        if (strM73456r == f101424e) {
            return;
        }
        String[] strArrSplit = TextUtils.split(m73462x(m73451m()).replace("||", "::"), "::");
        String str = strArrSplit[1];
        String[] strArrSplit2 = TextUtils.split(strArrSplit[3], ",");
        f101424e = strM73456r;
        f101423d = strArrSplit2;
    }

    /* renamed from: t */
    public Bundle m73458t() {
        if (this.f101429c == null) {
            Bundle bundle = new Bundle();
            CompressHelper compressHelper = this.f101428b;
            ArrayList<Bundle> arrayListM71825Y = compressHelper.m71825Y(compressHelper.m71778D0(), "Select * from cache");
            if (arrayListM71825Y == null) {
                return bundle;
            }
            Iterator<Bundle> it2 = arrayListM71825Y.iterator();
            while (it2.hasNext()) {
                Bundle next = it2.next();
                bundle.putBundle(next.getString("cachekey"), next);
            }
            this.f101429c = bundle;
        }
        return this.f101429c;
    }

    /* renamed from: u */
    public byte[] m73459u(String str) {
        String strTrim = str.trim();
        int length = strTrim.length();
        byte[] bArr = new byte[length / 2];
        for (int i2 = 0; i2 < length; i2 += 2) {
            bArr[i2 / 2] = (byte) ((Character.digit(strTrim.charAt(i2), 16) << 4) + Character.digit(strTrim.charAt(i2 + 1), 16));
        }
        return bArr;
    }

    /* renamed from: v */
    public boolean m73460v(String str) {
        return TextUtils.split(m73462x(m73451m()).replace("||", "::"), "::")[6].equals("0");
    }

    /* renamed from: w */
    public String m73461w(Bundle bundle) {
        ArrayList arrayList = new ArrayList();
        for (String str : bundle.keySet()) {
            arrayList.add(str + ":::" + bundle.getString(str));
        }
        return StringUtils.join(arrayList, "$$$");
    }

    /* renamed from: x */
    public String m73462x(String str) {
        String str2;
        String strM73456r = m73456r();
        if (f101426g == strM73456r) {
            return f101425f;
        }
        try {
            str2 = new String(m73447i(str.toCharArray(), new byte[]{122, 12, 11, 120, 32, DocWriter.f68365e3, 56, 78, Ascii.f60250y, Ascii.f60212B, 76, Ascii.f60250y, 65, 32, 76, Ascii.f60219I, Ascii.f60212B, SignedBytes.f63269a, Ascii.f60211A}, new byte[]{117, 115, 111, 102, 103, 104, 111, 111, 108, 122, 120, 119, 111, 91, 110, 109}, m73439a(strM73456r)));
        } catch (Exception unused) {
            str2 = null;
        }
        if (str2 == null) {
            this.f101427a.getSharedPreferences("default_preferences", 0).edit().remove("ActivationCode").commit();
            return null;
        }
        FirebaseCrashlytics.m48010d().m48027r(TextUtils.split(str2.replace("||", "::"), "::")[9]);
        f101425f = str2;
        f101426g = strM73456r;
        return str2;
    }

    /* renamed from: y */
    public boolean m73463y(String str, String str2, StringBuilder sb) {
        ArrayList arrayList = new ArrayList();
        Bundle bundle = new Bundle();
        m73457s();
        for (String str3 : f101423d) {
            if (str3.contains("$$$")) {
                String strReplace = StringUtils.splitByWholeSeparator(str3, "$$$")[0];
                if (strReplace.contains("-expired")) {
                    strReplace = strReplace.replace("-expired", "");
                }
                arrayList.add(strReplace);
                bundle.putString(strReplace, StringUtils.splitByWholeSeparator(str3, "$$$")[1]);
            } else {
                arrayList.add(str3);
            }
        }
        if (!arrayList.contains(TtmlNode.f29738r0)) {
            if (!arrayList.contains(str)) {
                return false;
            }
            if (bundle.containsKey(str)) {
                String string = bundle.getString(str);
                String strSubstring = new SimpleDateFormat("yyyyMMdd").format(new Date());
                sb.append(string);
                if (str2.length() == 6) {
                    string = string.substring(0, 6);
                    strSubstring = strSubstring.substring(0, 6);
                }
                if (string.compareTo(str2) < 0) {
                    return false;
                }
                if (str.equals("uptodateonline") && string.compareTo(strSubstring) < 0) {
                    return false;
                }
            }
        }
        return true;
    }

    /* renamed from: z */
    public Bundle m73464z() {
        m73457s();
        Bundle bundle = new Bundle();
        for (String str : f101423d) {
            if (str.contains("$$$")) {
                String strReplace = StringUtils.splitByWholeSeparator(str, "$$$")[0];
                if (strReplace.contains("-expired")) {
                    strReplace = strReplace.replace("-expired", "");
                }
                bundle.putString(strReplace, StringUtils.splitByWholeSeparator(str, "$$$")[1]);
            } else {
                bundle.putString(str, "0");
            }
        }
        return bundle;
    }
}

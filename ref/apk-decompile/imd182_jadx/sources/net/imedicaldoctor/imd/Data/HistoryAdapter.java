package net.imedicaldoctor.imd.Data;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatButton;
import androidx.core.content.ContextCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import java.io.File;
import java.util.ArrayList;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.ViewHolders.HeaderCellViewHolder;
import net.imedicaldoctor.imd.ViewHolders.MessageViewHolder;
import net.imedicaldoctor.imd.ViewHolders.RippleTextFullViewHolder;

/* loaded from: classes3.dex */
public class HistoryAdapter extends RecyclerView.Adapter {

    /* renamed from: l */
    private static final int f87428l = 0;

    /* renamed from: m */
    private static final int f87429m = 1;

    /* renamed from: n */
    private static final int f87430n = 2;

    /* renamed from: o */
    private static final int f87431o = 3;

    /* renamed from: d */
    public Context f87432d;

    /* renamed from: e */
    public String f87433e;

    /* renamed from: f */
    public int f87434f;

    /* renamed from: g */
    public String f87435g;

    /* renamed from: h */
    public ArrayList<Bundle> f87436h;

    /* renamed from: i */
    public ArrayList<Bundle> f87437i;

    /* renamed from: j */
    public DrawerLayout f87438j;

    /* renamed from: k */
    public CompressHelper f87439k;

    public static class DatabaseCellViewHolder extends RecyclerView.ViewHolder {

        /* renamed from: I */
        public TextView f87449I;

        /* renamed from: J */
        public TextView f87450J;

        /* renamed from: K */
        public ImageView f87451K;

        /* renamed from: L */
        public MaterialRippleLayout f87452L;

        /* renamed from: M */
        public AppCompatButton f87453M;

        public DatabaseCellViewHolder(View view) {
            super(view);
            this.f87449I = (TextView) view.findViewById(C5562R.id.database_title);
            this.f87451K = (ImageView) view.findViewById(C5562R.id.database_image);
            this.f87452L = (MaterialRippleLayout) view.findViewById(C5562R.id.ripple_layout);
            this.f87453M = (AppCompatButton) view.findViewById(C5562R.id.buy_button);
            this.f87450J = (TextView) view.findViewById(C5562R.id.database_subtitle);
        }
    }

    public HistoryAdapter(Context context, DrawerLayout drawerLayout) {
        this.f87439k = new CompressHelper(context);
        this.f87432d = context;
        m71926d0();
        CompressHelper compressHelper = this.f87439k;
        this.f87437i = compressHelper.m71825Y(compressHelper.m71852h2(), "SELECT m1.* FROM recent m1 LEFT JOIN recent m2 ON (m1.dbAddress = m2.dbAddress AND m1.dbName = m2.dbName AND m1.id < m2.id) WHERE m2.id IS NULL order by id desc limit 30");
        if (this.f87436h == null) {
            this.f87436h = new ArrayList<>();
        }
        if (this.f87437i == null) {
            this.f87437i = new ArrayList<>();
        }
        this.f87438j = drawerLayout;
        this.f87435g = "No History";
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: C */
    public int mo26845C(int i2) {
        if (this.f87436h.size() == 0 && this.f87437i.size() == 0) {
            return 0;
        }
        if (i2 == 0) {
            return 1;
        }
        if (i2 < this.f87436h.size() + 1) {
            return 2;
        }
        if (i2 == this.f87436h.size() + 1) {
            return 1;
        }
        return i2 > this.f87436h.size() + 1 ? 3 : -1;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: R */
    public void mo26169R(RecyclerView.ViewHolder viewHolder, int i2) {
        MaterialRippleLayout materialRippleLayout;
        View.OnLongClickListener onLongClickListener;
        TextView textView;
        String str;
        int iM27811F = viewHolder.m27811F();
        if (iM27811F == 0) {
            textView = ((MessageViewHolder) viewHolder).f101463I;
            str = this.f87435g;
        } else {
            if (iM27811F != 1) {
                if (iM27811F == 2) {
                    final Bundle bundle = this.f87436h.get(i2 - 1);
                    DatabaseCellViewHolder databaseCellViewHolder = (DatabaseCellViewHolder) viewHolder;
                    databaseCellViewHolder.f87453M.setVisibility(8);
                    databaseCellViewHolder.f87450J.setVisibility(8);
                    if (bundle.containsKey("home")) {
                        databaseCellViewHolder.f87451K.setImageDrawable(ContextCompat.m6692l(this.f87432d, C5562R.drawable.home_24px));
                        databaseCellViewHolder.f87449I.setText("Home");
                        databaseCellViewHolder.f87452L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Data.HistoryAdapter.1
                            @Override // android.view.View.OnClickListener
                            public void onClick(View view) {
                                HistoryAdapter.this.m71927e0();
                                HistoryAdapter.this.f87438j.m14280h();
                                HistoryAdapter.this.f87439k.m71830Z1(true);
                                HistoryAdapter.this.f87439k.m71830Z1(false);
                            }
                        });
                        return;
                    }
                    databaseCellViewHolder.f87449I.setText(bundle.getString("dbTitle"));
                    String strM71726D = CompressHelper.m71726D(bundle);
                    if (strM71726D.contains("file:///android_asset/") || new File(strM71726D).exists()) {
                        Glide.m30038D(this.f87432d).mo30129t(strM71726D).m30165B2(databaseCellViewHolder.f87451K);
                    } else {
                        databaseCellViewHolder.f87451K.setImageDrawable(this.f87432d.getResources().getDrawable(C5562R.drawable.placeholder));
                    }
                    databaseCellViewHolder.f87452L.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Data.HistoryAdapter.2
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            HistoryAdapter.this.m71927e0();
                            HistoryAdapter.this.f87438j.m14280h();
                            Bundle bundleM71826Y0 = HistoryAdapter.this.f87439k.m71826Y0("Name", bundle.getString("dbName"));
                            if (bundleM71826Y0 == null) {
                                CompressHelper.m71767x2(HistoryAdapter.this.f87432d, "This database doesn't exist anymore", 0);
                            } else {
                                HistoryAdapter.this.f87439k.m71909z1(bundleM71826Y0);
                            }
                        }
                    });
                    materialRippleLayout = databaseCellViewHolder.f87452L;
                    onLongClickListener = new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Data.HistoryAdapter.3
                        @Override // android.view.View.OnLongClickListener
                        public boolean onLongClick(View view) {
                            CompressHelper compressHelper = HistoryAdapter.this.f87439k;
                            compressHelper.m71881q(compressHelper.m71852h2(), "delete from dbrecent where dbName='" + bundle.getString("dbName") + "'");
                            HistoryAdapter.this.m71926d0();
                            HistoryAdapter.this.m27491G();
                            return true;
                        }
                    };
                } else {
                    if (iM27811F != 3) {
                        return;
                    }
                    final Bundle bundle2 = this.f87437i.get((i2 - this.f87436h.size()) - 2);
                    RippleTextFullViewHolder rippleTextFullViewHolder = (RippleTextFullViewHolder) viewHolder;
                    rippleTextFullViewHolder.f101499I.setText(bundle2.getString("dbDocName").trim());
                    rippleTextFullViewHolder.f101500J.setText(bundle2.getString("dbTitle").trim());
                    rippleTextFullViewHolder.f101503M.setOnClickListener(new View.OnClickListener() { // from class: net.imedicaldoctor.imd.Data.HistoryAdapter.4
                        @Override // android.view.View.OnClickListener
                        public void onClick(View view) {
                            HistoryAdapter.this.m71927e0();
                            HistoryAdapter.this.f87438j.m14280h();
                            Bundle bundleM71826Y0 = HistoryAdapter.this.f87439k.m71826Y0("Name", bundle2.getString("dbName"));
                            if (bundleM71826Y0 == null) {
                                CompressHelper.m71767x2(HistoryAdapter.this.f87432d, "This database doesn't exist anymore", 0);
                            } else {
                                HistoryAdapter.this.f87439k.m71772A1(bundleM71826Y0, bundle2.getString("dbAddress"), null, null);
                            }
                        }
                    });
                    materialRippleLayout = rippleTextFullViewHolder.f101503M;
                    onLongClickListener = new View.OnLongClickListener() { // from class: net.imedicaldoctor.imd.Data.HistoryAdapter.5
                        @Override // android.view.View.OnLongClickListener
                        public boolean onLongClick(View view) {
                            CompressHelper compressHelper = HistoryAdapter.this.f87439k;
                            compressHelper.m71881q(compressHelper.m71852h2(), "delete from recent where dbName='" + bundle2.getString("dbName") + "' AND dbAddress='" + bundle2.getString("dbAddress") + "'");
                            HistoryAdapter historyAdapter = HistoryAdapter.this;
                            CompressHelper compressHelper2 = historyAdapter.f87439k;
                            historyAdapter.f87437i = compressHelper2.m71825Y(compressHelper2.m71852h2(), "SELECT m1.* FROM recent m1 LEFT JOIN recent m2 ON (m1.dbAddress = m2.dbAddress AND m1.dbName = m2.dbName AND m1.id < m2.id) WHERE m2.id IS NULL order by id desc limit 30");
                            HistoryAdapter.this.m27491G();
                            return true;
                        }
                    };
                }
                materialRippleLayout.setOnLongClickListener(onLongClickListener);
                return;
            }
            textView = ((HeaderCellViewHolder) viewHolder).f101460I;
            str = i2 == 0 ? "Recent Databases" : "Recent Documents";
        }
        textView.setText(str);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: T */
    public RecyclerView.ViewHolder mo26170T(ViewGroup viewGroup, int i2) {
        if (i2 == 0) {
            return new MessageViewHolder(this.f87432d, LayoutInflater.from(this.f87432d).inflate(C5562R.layout.list_view_item_card_notfound, viewGroup, false));
        }
        if (i2 == 1) {
            return new HeaderCellViewHolder(LayoutInflater.from(this.f87432d).inflate(C5562R.layout.list_view_item_database_header, viewGroup, false));
        }
        if (i2 == 2) {
            return new DatabaseCellViewHolder(LayoutInflater.from(this.f87432d).inflate(C5562R.layout.list_view_item_database_ripple, viewGroup, false));
        }
        if (i2 != 3) {
            return null;
        }
        RippleTextFullViewHolder rippleTextFullViewHolder = new RippleTextFullViewHolder(LayoutInflater.from(this.f87432d).inflate(C5562R.layout.list_view_item_ripple_recent, viewGroup, false));
        rippleTextFullViewHolder.f101501K.setVisibility(8);
        return rippleTextFullViewHolder;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    /* renamed from: b */
    public int mo26171b() {
        try {
            if (this.f87436h.size() == 0 && this.f87437i.size() == 0) {
                return 1;
            }
            return this.f87436h.size() + this.f87437i.size() + 2;
        } catch (Exception unused) {
            return 0;
        }
    }

    /* renamed from: d0 */
    public void m71926d0() {
        CompressHelper compressHelper = this.f87439k;
        ArrayList<Bundle> arrayListM71825Y = compressHelper.m71825Y(compressHelper.m71852h2(), "SELECT distinct(dbName), dbTitle, dbIcon FROM dbrecent order by id desc limit 2");
        this.f87436h = arrayListM71825Y;
        if (arrayListM71825Y == null) {
            this.f87436h = new ArrayList<>();
        }
        Bundle bundle = new Bundle();
        bundle.putString("home", "");
        this.f87436h.add(bundle);
    }

    /* renamed from: e0 */
    public void m71927e0() {
        try {
            ((InputMethodManager) this.f87432d.getSystemService("input_method")).hideSoftInputFromWindow(((Activity) this.f87432d).getCurrentFocus().getWindowToken(), 0);
            if (((Activity) this.f87432d).getCurrentFocus() != null) {
                ((Activity) this.f87432d).getCurrentFocus().clearFocus();
            }
        } catch (Exception unused) {
        }
    }
}

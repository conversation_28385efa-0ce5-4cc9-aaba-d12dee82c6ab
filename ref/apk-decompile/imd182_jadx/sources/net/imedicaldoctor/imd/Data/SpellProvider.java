package net.imedicaldoctor.imd.Data;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.requery.android.database.sqlite.SQLiteDatabase;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class SpellProvider extends ContentProvider {

    /* renamed from: X2 */
    private static final String f87454X2 = "net.imedicaldoctor.spell";

    /* renamed from: Z */
    private static final UriMatcher f87455Z = m71928a();

    /* renamed from: X */
    private String f87456X;

    /* renamed from: Y */
    private final String f87457Y = null;

    /* renamed from: s */
    private SQLiteDatabase f87458s;

    /* renamed from: a */
    private static UriMatcher m71928a() {
        UriMatcher uriMatcher = new UriMatcher(-1);
        uriMatcher.addURI(f87454X2, "spell/*/*", 101);
        uriMatcher.addURI(f87454X2, "cspell/*/*", 102);
        return uriMatcher;
    }

    @Override // android.content.ContentProvider
    public int delete(Uri uri, String str, String[] strArr) {
        return this.f87457Y == null ? 0 : 1;
    }

    @Override // android.content.ContentProvider
    public String getType(Uri uri) {
        if (f87455Z.match(uri) != 401) {
            return null;
        }
        return this.f87456X;
    }

    @Override // android.content.ContentProvider
    public Uri insert(Uri uri, ContentValues contentValues) {
        return null;
    }

    @Override // android.content.ContentProvider
    public boolean onCreate() {
        try {
            this.f87458s = SQLiteDatabase.openDatabase(new CompressHelper(getContext()).m71770A(), (SQLiteDatabase.CursorFactory) null, 1);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            Log.e("SpellProvider", "Can't open spell Database");
        }
        this.f87456X = "0";
        return false;
    }

    @Override // android.content.ContentProvider, android.content.ComponentCallbacks
    public void onLowMemory() {
        iMDLogger.m73550f("UTDProvider", "OnLowMemory");
    }

    @Override // android.content.ContentProvider
    public Cursor query(Uri uri, String[] strArr, String str, String[] strArr2, String str2) {
        SQLiteDatabase sQLiteDatabase;
        String str3;
        int iMatch = f87455Z.match(uri);
        Cursor cursorRawQuery = null;
        try {
            if (iMatch == 101) {
                String[] strArrSplit = uri.getLastPathSegment().trim().split(StringUtils.SPACE);
                String str4 = strArrSplit[strArrSplit.length - 1];
                String str5 = "";
                for (int i2 = 0; i2 < strArrSplit.length - 1; i2++) {
                    str5 = str5 + StringUtils.SPACE + strArrSplit[i2];
                }
                String strTrim = str5.trim();
                sQLiteDatabase = this.f87458s;
                str3 = "Select rowid as _id, word as suggest_text_1,\"" + strTrim + " \" || word from spell where word match '" + str4 + "*'";
            } else {
                if (iMatch != 102) {
                    return null;
                }
                sQLiteDatabase = this.f87458s;
                str3 = "Select rowid as _id, word as suggest_text_1 from contentspell where word match '" + uri.getLastPathSegment() + "*'";
            }
            cursorRawQuery = sQLiteDatabase.rawQuery(str3, null);
            return cursorRawQuery;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f(SpellProvider.class.toString(), "Error in UTDProvider query , " + e2);
            return cursorRawQuery;
        }
    }

    @Override // android.content.ContentProvider
    public int update(Uri uri, ContentValues contentValues, String str, String[] strArr) {
        onCreate();
        return 1;
    }
}

package net.imedicaldoctor.imd.Data;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.SQLException;
import android.net.Uri;
import android.os.Bundle;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.requery.android.database.sqlite.SQLiteCursor;
import io.requery.android.database.sqlite.SQLiteCursorDriver;
import io.requery.android.database.sqlite.SQLiteDatabase;
import io.requery.android.database.sqlite.SQLiteQuery;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import net.imedicaldoctor.imd.iMDLogger;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class iMDProvider extends ContentProvider {

    /* renamed from: X */
    private static final String f87459X = "net.imedicaldoctor.imd";

    /* renamed from: X2 */
    public static Bundle f87460X2;

    /* renamed from: Y */
    private static HashMap<String, SQLiteDatabase> f87461Y;

    /* renamed from: Z */
    private static HashMap<String, Integer> f87462Z;

    /* renamed from: s */
    private static final UriMatcher f87463s = m71935e();

    public class LeaklessCursor extends SQLiteCursor {

        /* renamed from: Y */
        static final String f87464Y = "LeaklessCursor";

        /* renamed from: s */
        final SQLiteDatabase f87466s;

        public LeaklessCursor(SQLiteDatabase sQLiteDatabase, SQLiteCursorDriver sQLiteCursorDriver, String str, SQLiteQuery sQLiteQuery) {
            super(sQLiteCursorDriver, str, sQLiteQuery);
            this.f87466s = sQLiteDatabase;
        }

        @Override // io.requery.android.database.sqlite.SQLiteCursor, io.requery.android.database.AbstractCursor, android.database.Cursor, java.io.Closeable, java.lang.AutoCloseable
        public void close() {
            iMDLogger.m73548d(f87464Y, "Closing LeaklessCursor: " + this.f87466s.getPath());
            super.close();
            SQLiteDatabase sQLiteDatabase = this.f87466s;
            if (sQLiteDatabase != null) {
                sQLiteDatabase.close();
            }
        }
    }

    public class LeaklessCursorFactory implements SQLiteDatabase.CursorFactory {
        public LeaklessCursorFactory() {
        }

        @Override // io.requery.android.database.sqlite.SQLiteDatabase.CursorFactory
        public Cursor newCursor(SQLiteDatabase sQLiteDatabase, SQLiteCursorDriver sQLiteCursorDriver, String str, SQLiteQuery sQLiteQuery) {
            return iMDProvider.this.new LeaklessCursor(sQLiteDatabase, sQLiteCursorDriver, str, sQLiteQuery);
        }
    }

    /* renamed from: a */
    private SQLiteDatabase m71931a(String str) throws SQLException {
        SQLiteDatabase sQLiteDatabaseOpenDatabase;
        if (f87461Y == null) {
            f87461Y = new HashMap<>();
        }
        if (f87462Z == null) {
            f87462Z = new HashMap<>();
        }
        if (f87461Y.containsKey(str)) {
            sQLiteDatabaseOpenDatabase = f87461Y.get(str);
        } else {
            sQLiteDatabaseOpenDatabase = SQLiteDatabase.openDatabase(str, (SQLiteDatabase.CursorFactory) null, str.contains("/DBs.db") ? 1 : 2);
            sQLiteDatabaseOpenDatabase.disableWriteAheadLogging();
            sQLiteDatabaseOpenDatabase.execSQL("PRAGMA temp_store = MEMORY");
            f87461Y.put(str, sQLiteDatabaseOpenDatabase);
        }
        m71932b(str);
        return sQLiteDatabaseOpenDatabase;
    }

    /* renamed from: b */
    private void m71932b(String str) {
        int iIntValue;
        if (f87462Z.containsKey(str)) {
            iIntValue = f87462Z.get(str).intValue();
            f87462Z.remove(str);
        } else {
            iIntValue = 0;
        }
        f87462Z.put(str, Integer.valueOf(iIntValue + 1));
    }

    /* renamed from: c */
    private void m71933c(String str) {
        if (f87461Y == null) {
            f87461Y = new HashMap<>();
        }
        if (f87462Z == null) {
            f87462Z = new HashMap<>();
        }
        if (f87461Y.containsKey(str)) {
            m71934d(str);
            if ((f87462Z.containsKey(str) ? f87462Z.get(str).intValue() : 0) <= 0) {
                SQLiteDatabase sQLiteDatabase = f87461Y.get(str);
                try {
                    if (sQLiteDatabase.isOpen()) {
                        sQLiteDatabase.close();
                        iMDLogger.m73550f("iMDProvider", "Closed database");
                    }
                } catch (Exception unused) {
                }
                f87461Y.remove(str);
            }
        }
    }

    /* renamed from: d */
    private void m71934d(String str) {
        if (f87462Z.containsKey(str)) {
            try {
                int iIntValue = f87462Z.get(str).intValue() - 1;
                f87462Z.remove(str);
                f87462Z.put(str, Integer.valueOf(iIntValue));
            } catch (Exception unused) {
            }
        }
    }

    /* renamed from: e */
    private static UriMatcher m71935e() {
        UriMatcher uriMatcher = new UriMatcher(-1);
        uriMatcher.addURI("net.imedicaldoctor.imd", "*", 101);
        return uriMatcher;
    }

    @Override // android.content.ContentProvider
    public int delete(Uri uri, String str, String[] strArr) {
        return 0;
    }

    @Override // android.content.ContentProvider
    public String getType(Uri uri) {
        return null;
    }

    @Override // android.content.ContentProvider
    public Uri insert(Uri uri, ContentValues contentValues) {
        return null;
    }

    @Override // android.content.ContentProvider
    public boolean onCreate() {
        return false;
    }

    @Override // android.content.ContentProvider
    public Cursor query(Uri uri, String[] strArr, String str, String[] strArr2, String str2) {
        if (str2 != null) {
            iMDLogger.m73550f("iMDProvider", "Query " + str + " - " + str2);
        }
        int iMatch = f87463s.match(uri);
        if (str2 == null) {
            try {
                m71933c(str);
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
            return null;
        }
        if (iMatch != 101) {
            return null;
        }
        try {
            return m71931a(str).rawQuery(str2, null);
        } catch (Exception e3) {
            iMDLogger.m73550f(iMDProvider.class.toString(), "Error in iMDProvider query , " + e3 + " in " + e3.getStackTrace());
            return null;
        }
    }

    @Override // android.content.ContentProvider
    public int update(Uri uri, ContentValues contentValues, String str, String[] strArr) throws IOException, SQLException {
        int i2 = 0;
        String str2 = strArr[0];
        SQLiteDatabase sQLiteDatabaseOpenDatabase = SQLiteDatabase.openDatabase(str, (SQLiteDatabase.CursorFactory) null, 2);
        sQLiteDatabaseOpenDatabase.disableWriteAheadLogging();
        sQLiteDatabaseOpenDatabase.execSQL("PRAGMA temp_store = MEMORY");
        if (strArr.length <= 1) {
            sQLiteDatabaseOpenDatabase.execSQL(str2);
        } else if (strArr[0].equals("SQLFile")) {
            String str3 = strArr[1];
            String[] strArrSplit = StringUtils.split(str3, "/");
            String str4 = strArrSplit[strArrSplit.length - 1];
            int iIntValue = Integer.valueOf(strArr[2]).intValue();
            try {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(new File(str3))), 131072);
                for (int i3 = 0; i3 < iIntValue; i3++) {
                    bufferedReader.readLine();
                }
                while (true) {
                    try {
                        String line = bufferedReader.readLine();
                        if (line == null) {
                            break;
                        }
                        i2++;
                        try {
                            iMDLogger.m73548d("SQL Line - " + str4, i2 + "");
                            sQLiteDatabaseOpenDatabase.execSQL(line);
                        } catch (Exception e2) {
                            FirebaseCrashlytics.m48010d().m48016g(e2);
                            e2.printStackTrace();
                        }
                    } catch (Exception e3) {
                        FirebaseCrashlytics.m48010d().m48016g(e3);
                        e3.printStackTrace();
                    }
                }
                sQLiteDatabaseOpenDatabase.close();
            } catch (Exception e4) {
                FirebaseCrashlytics.m48010d().m48016g(e4);
                iMDLogger.m73550f("ExecuteDB", "Error in reading and executing " + str3 + " With error : " + e4.getMessage());
                e4.printStackTrace();
            }
        } else {
            sQLiteDatabaseOpenDatabase.beginTransaction();
            int length = strArr.length;
            while (i2 < length) {
                sQLiteDatabaseOpenDatabase.execSQL(strArr[i2]);
                i2++;
            }
            sQLiteDatabaseOpenDatabase.setTransactionSuccessful();
            sQLiteDatabaseOpenDatabase.endTransaction();
        }
        try {
            if (sQLiteDatabaseOpenDatabase.isOpen()) {
                sQLiteDatabaseOpenDatabase.close();
            }
        } catch (Exception e5) {
            FirebaseCrashlytics.m48010d().m48016g(e5);
            iMDLogger.m73551g("iMD Provider", "Closing database", e5);
        }
        return 1;
    }
}

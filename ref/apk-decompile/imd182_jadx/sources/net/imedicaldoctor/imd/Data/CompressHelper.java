package net.imedicaldoctor.imd.Data;

import android.R;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ResolveInfo;
import android.content.res.AssetFileDescriptor;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.database.SQLException;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.os.Process;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.content.FileProvider;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import androidx.media3.extractor.text.ttml.TtmlNode;
import androidx.slidingpanelayout.widget.SlidingPaneLayout;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.FluentIterable;
import com.google.common.collect.Lists;
import com.google.common.net.HttpHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.messaging.FirebaseMessaging;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.observers.DisposableObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.requery.android.database.sqlite.SQLiteDatabase;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Locale;
import java.util.Random;
import java.util.Stack;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.zip.CRC32;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.Inflater;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import kotlinx.coroutines.scheduling.WorkQueueKt;
import net.imedicaldoctor.imd.BuildConfig;
import net.imedicaldoctor.imd.C5562R;
import net.imedicaldoctor.imd.Decompress;
import net.imedicaldoctor.imd.Fragments.AccessMedicine.AMChaptersActivity;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASListActivity;
import net.imedicaldoctor.imd.Fragments.Amirsys.ASListActivityFragment;
import net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOC;
import net.imedicaldoctor.imd.Fragments.CMEInfo.CMETOCFragment;
import net.imedicaldoctor.imd.Fragments.DRE.DREMainActivity;
import net.imedicaldoctor.imd.Fragments.DRE.DREMainActivityFragment;
import net.imedicaldoctor.imd.Fragments.Dictionary.CDicSearchActivity;
import net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivity;
import net.imedicaldoctor.imd.Fragments.EPUB.EPUBChaptersActivityFragment;
import net.imedicaldoctor.imd.Fragments.Elsevier.ELSChaptersActivity;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPOLabListActivity;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPOLabListActivityFragment;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivity;
import net.imedicaldoctor.imd.Fragments.Epocrate.EPOMainActivityFragment;
import net.imedicaldoctor.imd.Fragments.Facts.FTListActivity;
import net.imedicaldoctor.imd.Fragments.Facts.FTListActivityFragment;
import net.imedicaldoctor.imd.Fragments.IranDaru.IDSearchActivity;
import net.imedicaldoctor.imd.Fragments.IranGenericDrugs.IranGenericDrugsList;
import net.imedicaldoctor.imd.Fragments.IranGenericDrugs.IranGenericDrugsListFragment;
import net.imedicaldoctor.imd.Fragments.LWW.LWWChapters;
import net.imedicaldoctor.imd.Fragments.LWW.LWWChaptersFragment;
import net.imedicaldoctor.imd.Fragments.Lexi.LXItems;
import net.imedicaldoctor.imd.Fragments.LexiInteract.LXInteractList;
import net.imedicaldoctor.imd.Fragments.LexiInteract.LXIvInteract;
import net.imedicaldoctor.imd.Fragments.Martindale.MDListActivity;
import net.imedicaldoctor.imd.Fragments.Martindale.MDListActivityFragment;
import net.imedicaldoctor.imd.Fragments.Medhand.MHSearchActivity;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMIVSelectActivity;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMIVSelectActivityFragment;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivity;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMInteractSelectActivityFragment;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMListActivity;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMListActivityFragment;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivity;
import net.imedicaldoctor.imd.Fragments.Micromedex.MMNeoListActivityFragment;
import net.imedicaldoctor.imd.Fragments.NEJM.NEJMTOCActivity;
import net.imedicaldoctor.imd.Fragments.Noskheha.NOSListActivity;
import net.imedicaldoctor.imd.Fragments.Noskheha.NOSListActivityFragment;
import net.imedicaldoctor.imd.Fragments.OVID.OvidChaptersActivity;
import net.imedicaldoctor.imd.Fragments.Sanford.SANTocActivity;
import net.imedicaldoctor.imd.Fragments.Sanford.SANTocActivityFragment;
import net.imedicaldoctor.imd.Fragments.Skyscape.SSSearchActivity;
import net.imedicaldoctor.imd.Fragments.Statdx.SDListActivity;
import net.imedicaldoctor.imd.Fragments.Statdx.SDListActivityFragment;
import net.imedicaldoctor.imd.Fragments.Stockley.STListActivity;
import net.imedicaldoctor.imd.Fragments.Stockley.STListActivityFragment;
import net.imedicaldoctor.imd.Fragments.TOL.PsychoListActivity;
import net.imedicaldoctor.imd.Fragments.TOL.PsychoListActivityFragment;
import net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDASearchActivity;
import net.imedicaldoctor.imd.Fragments.UTDAdvanced.UTDASearchActivityFragment;
import net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivity;
import net.imedicaldoctor.imd.Fragments.UWorld.UWMainActivityFragment;
import net.imedicaldoctor.imd.Fragments.Uptodate.UTDSearchActivity;
import net.imedicaldoctor.imd.Fragments.UptodateDDX.UTDDSearchActivity;
import net.imedicaldoctor.imd.Fragments.VisualDDX.VDDxScenarioActivity;
import net.imedicaldoctor.imd.Fragments.VisualDXLookup.VDSearchActivity;
import net.imedicaldoctor.imd.Fragments.mainActivity;
import net.imedicaldoctor.imd.Utils.MaterialRippleLayout;
import net.imedicaldoctor.imd.VBHelper;
import net.imedicaldoctor.imd.iMD;
import net.imedicaldoctor.imd.iMDLogger;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;
import okio.Sink;
import okio.Source;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes3.dex */
public class CompressHelper {

    /* renamed from: h */
    public static final String f87333h = "fileSize";

    /* renamed from: i */
    public static final String f87334i = "MD5";

    /* renamed from: j */
    public static final String f87335j = "bytesDownloaded";

    /* renamed from: k */
    public static final String f87336k = "bytesTotal";

    /* renamed from: l */
    public static final String f87337l = "avgSpeed";

    /* renamed from: m */
    public static final String f87338m = "remaining";

    /* renamed from: n */
    public static final String f87339n = "downloader";

    /* renamed from: o */
    public static MediaPlayer f87340o = null;

    /* renamed from: p */
    public static ArrayList<String> f87341p = null;

    /* renamed from: q */
    public static final String f87342q = ",visualdx.png,uptodate.png,irandarou.png,";

    /* renamed from: r */
    public static Fragment f87343r = null;

    /* renamed from: s */
    public static HashMap<String, Stack<Fragment>> f87344s = null;

    /* renamed from: t */
    public static ArrayList<Bundle> f87345t = null;

    /* renamed from: u */
    private static final Logger f87346u = Logger.getLogger(CompressHelper.class.toString());

    /* renamed from: v */
    public static final String f87347v = "master";

    /* renamed from: w */
    public static final String f87348w = "detail";

    /* renamed from: x */
    public static boolean f87349x;

    /* renamed from: a */
    public Bundle f87350a;

    /* renamed from: b */
    public ArrayList<File> f87351b;

    /* renamed from: c */
    public String f87352c;

    /* renamed from: d */
    public Context f87353d;

    /* renamed from: e */
    public Bundle f87354e;

    /* renamed from: f */
    public VBHelper f87355f;

    /* renamed from: g */
    private final OkHttpClient f87356g = new OkHttpClient();

    /* renamed from: net.imedicaldoctor.imd.Data.CompressHelper$10 */
    class C463310 extends DisposableObserver<String> {
        C463310() {
        }

        @Override // io.reactivex.rxjava3.core.Observer
        /* renamed from: c, reason: merged with bridge method [inline-methods] */
        public void onNext(String str) {
            StringUtils.splitByWholeSeparator(str, "|||||");
        }

        @Override // io.reactivex.rxjava3.core.Observer
        public void onComplete() {
        }

        @Override // io.reactivex.rxjava3.core.Observer
        public void onError(Throwable th) {
        }
    }

    /* renamed from: net.imedicaldoctor.imd.Data.CompressHelper$9 */
    class C46619 extends DisposableObserver<String> {
        C46619() {
        }

        @Override // io.reactivex.rxjava3.core.Observer
        /* renamed from: c, reason: merged with bridge method [inline-methods] */
        public void onNext(@NonNull String str) {
            StringUtils.splitByWholeSeparator(str, "|||||");
        }

        @Override // io.reactivex.rxjava3.core.Observer
        public void onComplete() {
        }

        @Override // io.reactivex.rxjava3.core.Observer
        public void onError(@NonNull Throwable th) {
        }
    }

    public CompressHelper(Context context) {
        if (f87344s == null) {
            HashMap<String, Stack<Fragment>> map = new HashMap<>();
            f87344s = map;
            map.put(f87347v, new Stack<>());
            f87344s.put(f87348w, new Stack<>());
        }
        this.f87353d = context;
        this.f87355f = new VBHelper(context);
        this.f87350a = new Bundle();
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: A2 */
    public boolean m71722A2(String str, String str2, Bundle bundle) throws IOException {
        File file;
        if (!new File(str).exists()) {
            return false;
        }
        long jLongValue = Long.valueOf(bundle.getString("fileSize", "0")).longValue();
        if (jLongValue == 0 && this.f87350a.containsKey(str)) {
            jLongValue = this.f87350a.getLong(str);
        }
        if (new File(str).length() == jLongValue || jLongValue == 0) {
            String string = bundle.getString("MD5", "");
            if (string.isEmpty() && new File(str2).exists()) {
                string = m71846f2(str2);
            }
            if (string.equalsIgnoreCase(m71804P1(new File(str)).replace(StringUtils.f103471LF, "")) || string.isEmpty()) {
                return true;
            }
            file = new File(str);
        } else {
            file = new File(str);
        }
        file.delete();
        bundle.putDouble("bytesDownloaded", 0.0d);
        return false;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: B2 */
    public void m71723B2(String str, String str2, String str3, Bundle bundle) throws IOException {
        BufferedSource bufferedSourceM75769e;
        BufferedSink bufferedSinkM75768d;
        File file;
        String string = bundle.getString("MD5", "");
        if (string.isEmpty() && new File(str3).exists()) {
            string = m71846f2(str3);
        }
        if (string.isEmpty()) {
            bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(new File(str)));
            try {
                bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(str2)));
                try {
                    bufferedSinkM75768d.mo75508y1(bufferedSourceM75769e);
                    bufferedSinkM75768d.close();
                    if (bufferedSourceM75769e != null) {
                        bufferedSourceM75769e.close();
                    }
                    file = new File(str);
                } finally {
                    if (bufferedSinkM75768d != null) {
                        try {
                            bufferedSinkM75768d.close();
                        } catch (Throwable th) {
                            th.addSuppressed(th);
                        }
                    }
                }
            } finally {
            }
        } else {
            if (!string.equalsIgnoreCase(m71804P1(new File(str)).replace(StringUtils.f103471LF, ""))) {
                new File(str).delete();
                bundle.putDouble("bytesDownloaded", 0.0d);
                throw new IOException("MD5 checksum mismatch. Downloading again");
            }
            bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(new File(str)));
            try {
                bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(str2)));
                try {
                    bufferedSinkM75768d.mo75508y1(bufferedSourceM75769e);
                    bufferedSinkM75768d.close();
                    if (bufferedSourceM75769e != null) {
                        bufferedSourceM75769e.close();
                    }
                    file = new File(str);
                } finally {
                }
            } finally {
            }
        }
        file.delete();
    }

    /* renamed from: C */
    public static String m71724C(Bundle bundle) {
        try {
            String string = bundle.getString("IconName");
            if (f87342q.contains("," + string + ",")) {
                return "file:///android_asset/" + string;
            }
            return bundle.getString("Path") + "/" + string;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return "file:///android_asset/placeholder.png";
        }
    }

    /* renamed from: C1 */
    public static boolean m71725C1(View view) {
        if (view instanceof CardView) {
            return true;
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i2 = 0; i2 < viewGroup.getChildCount(); i2++) {
                if (m71725C1(viewGroup.getChildAt(i2))) {
                    return true;
                }
            }
        }
        return false;
    }

    /* renamed from: D */
    public static String m71726D(Bundle bundle) {
        String str = StringUtils.splitByWholeSeparator(bundle.getString("dbIcon"), "/")[r1.length - 1];
        if (!f87342q.contains("," + str + ",")) {
            return bundle.getString("dbIcon");
        }
        return "file:///android_asset/" + str;
    }

    /* renamed from: D1 */
    public static boolean m71727D1(View view) {
        if (view instanceof MaterialRippleLayout) {
            return true;
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i2 = 0; i2 < viewGroup.getChildCount(); i2++) {
                if (m71727D1(viewGroup.getChildAt(i2))) {
                    return true;
                }
            }
        }
        return false;
    }

    /* renamed from: D2 */
    public static void m71728D2(File file, byte[] bArr) throws IOException {
        BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
        try {
            bufferedSinkM75768d.write(bArr);
            bufferedSinkM75768d.close();
        } catch (Throwable th) {
            if (bufferedSinkM75768d != null) {
                try {
                    bufferedSinkM75768d.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    /* renamed from: E2 */
    public static void m71729E2(File file, String str) throws IOException {
        BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
        try {
            bufferedSinkM75768d.mo75454W0(str);
            bufferedSinkM75768d.flush();
            bufferedSinkM75768d.close();
        } catch (Throwable th) {
            if (bufferedSinkM75768d != null) {
                try {
                    bufferedSinkM75768d.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    /* renamed from: F0 */
    public static byte[] m71730F0(byte[] bArr) throws IOException {
        Deflater deflater = new Deflater();
        deflater.setLevel(-1);
        deflater.setInput(bArr);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(bArr.length);
        deflater.finish();
        byte[] bArr2 = new byte[5120];
        while (!deflater.finished()) {
            byteArrayOutputStream.write(bArr2, 0, deflater.deflate(bArr2));
        }
        byteArrayOutputStream.close();
        return byteArrayOutputStream.toByteArray();
    }

    /* renamed from: F1 */
    public static String m71731F1(String str) throws NoSuchAlgorithmException {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes());
            byte[] bArrDigest = messageDigest.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b2 : bArrDigest) {
                String hexString = Integer.toHexString(b2 & 255);
                while (hexString.length() < 2) {
                    hexString = "0" + hexString;
                }
                sb.append(hexString);
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e2) {
            e2.printStackTrace();
            return "";
        }
    }

    /* renamed from: G0 */
    public static String m71732G0(String str) throws IOException {
        if (str == null || str.length() == 0) {
            return str;
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        GZIPOutputStream gZIPOutputStream = new GZIPOutputStream(byteArrayOutputStream);
        gZIPOutputStream.write(str.getBytes("UTF-8"));
        gZIPOutputStream.close();
        return Base64.encodeToString(byteArrayOutputStream.toByteArray(), 0);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: H1 */
    public boolean m71733H1(Bundle bundle) {
        return bundle.containsKey("downloader") && bundle.getBundle("downloader").containsKey("Go");
    }

    /* renamed from: I1 */
    public static String m71734I1(ArrayList<Bundle> arrayList, String str) {
        return m71735J1(arrayList, str, ",", "", "");
    }

    /* renamed from: J1 */
    public static String m71735J1(ArrayList<Bundle> arrayList, String str, String str2, String str3, String str4) {
        ArrayList arrayList2 = new ArrayList();
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            arrayList2.add(str3 + it2.next().getString(str) + str4);
        }
        return TextUtils.join(str2, arrayList2);
    }

    /* renamed from: K1 */
    public static String m71736K1(String str) {
        return (String) new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/"))).get(r0.size() - 1);
    }

    /* renamed from: L0 */
    public static byte[] m71737L0(byte[] bArr) throws DataFormatException, IOException {
        Inflater inflater = new Inflater();
        inflater.setInput(bArr);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(bArr.length);
        byte[] bArr2 = new byte[1024];
        while (!inflater.finished()) {
            byteArrayOutputStream.write(bArr2, 0, inflater.inflate(bArr2));
        }
        byteArrayOutputStream.close();
        return byteArrayOutputStream.toByteArray();
    }

    /* renamed from: M0 */
    public static String m71738M0(String str) throws IOException {
        if (str == null || str.length() == 0) {
            return str;
        }
        GZIPInputStream gZIPInputStream = new GZIPInputStream(new ByteArrayInputStream(Base64.decode(str, 0)));
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bArr = new byte[1024];
        while (true) {
            int i2 = gZIPInputStream.read(bArr);
            if (i2 == -1) {
                return byteArrayOutputStream.toString("UTF-8");
            }
            byteArrayOutputStream.write(bArr, 0, i2);
        }
    }

    /* renamed from: O1 */
    public static String m71739O1(String str, int i2) {
        if (str.length() <= i2) {
            return str;
        }
        return str.substring(0, i2 - 1) + "...";
    }

    /* renamed from: P0 */
    public static boolean m71740P0(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        File[] fileArrListFiles = file.listFiles();
        if (fileArrListFiles != null) {
            for (File file2 : fileArrListFiles) {
                if (file2.isDirectory()) {
                    m71740P0(file2);
                } else {
                    file2.delete();
                }
            }
        }
        return file.delete();
    }

    /* renamed from: Q0 */
    public static void m71741Q0(Context context, String str, String str2, String str3) {
        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            builder.mo1092b(false);
            builder.mo1115y("اطلاعات بیشتر", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.5
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                }
            });
            builder.mo1106p("باشه", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.6
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialogInterface, int i2) {
                }
            });
            builder.setTitle(str);
            builder.mo1102l(str2);
            AlertDialog alertDialogCreate = builder.create();
            ((TextView) alertDialogCreate.findViewById(R.id.message)).setTypeface(ResourcesCompat.m7158j(context, C5562R.font.iransans));
            alertDialogCreate.show();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* renamed from: Q1 */
    public static boolean m71742Q1(File file, File file2) {
        if (!file.exists()) {
            return false;
        }
        file2.getParentFile().mkdirs();
        try {
            Source sourceM75784t = Okio.m75784t(file);
            try {
                Sink sinkM75778n = Okio.m75778n(file2);
                try {
                    Okio.m75768d(sinkM75778n).mo75508y1(sourceM75784t);
                    if (sinkM75778n != null) {
                        sinkM75778n.close();
                    }
                    if (sourceM75784t != null) {
                        sourceM75784t.close();
                    }
                    return file.delete();
                } finally {
                }
            } finally {
            }
        } catch (IOException e2) {
            e2.printStackTrace();
            return false;
        }
    }

    /* renamed from: R1 */
    public static boolean m71743R1(String str, String str2) {
        File file = new File(str);
        File file2 = new File(str2);
        if (!file.exists()) {
            return false;
        }
        file2.getParentFile().mkdirs();
        try {
            Source sourceM75784t = Okio.m75784t(file);
            try {
                Sink sinkM75778n = Okio.m75778n(file2);
                try {
                    Okio.m75768d(sinkM75778n).mo75508y1(sourceM75784t);
                    if (sinkM75778n != null) {
                        sinkM75778n.close();
                    }
                    if (sourceM75784t != null) {
                        sourceM75784t.close();
                    }
                    return file.delete();
                } finally {
                }
            } finally {
            }
        } catch (IOException e2) {
            e2.printStackTrace();
            return false;
        }
    }

    /* renamed from: V1 */
    public static void m71744V1(Context context, String str) throws IllegalStateException, IOException, IllegalArgumentException {
        try {
            MediaPlayer mediaPlayer = f87340o;
            if (mediaPlayer != null) {
                mediaPlayer.release();
                f87340o = null;
            }
            if (f87340o == null) {
                f87340o = new MediaPlayer();
            }
            if (f87340o.isPlaying()) {
                f87340o.stop();
            }
            AssetFileDescriptor assetFileDescriptorOpenFd = context.getAssets().openFd(str);
            f87340o.setDataSource(assetFileDescriptorOpenFd.getFileDescriptor(), assetFileDescriptorOpenFd.getStartOffset(), assetFileDescriptorOpenFd.getLength());
            assetFileDescriptorOpenFd.close();
            f87340o.prepare();
            f87340o.setVolume(1.0f, 1.0f);
            f87340o.setLooping(false);
            f87340o.start();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* renamed from: b */
    public static int m71745b(ArrayList<Bundle> arrayList, Bundle bundle, String str) {
        for (int i2 = 0; i2 < arrayList.size(); i2++) {
            if (arrayList.get(i2).getString(str).equals(bundle.getString(str))) {
                return i2;
            }
        }
        return -1;
    }

    /* renamed from: c1 */
    public static String m71746c1(String str) throws ParseException {
        try {
            return (str.length() == 6 ? new SimpleDateFormat("MMM, yyyy") : new SimpleDateFormat("MMM d, yyyy")).format((str.length() == 6 ? new SimpleDateFormat("yyyyMM") : new SimpleDateFormat("yyyyMMdd")).parse(str));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return str;
        }
    }

    /* renamed from: d1 */
    public static byte[] m71747d1(byte[] bArr) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(bArr.length);
        GZIPOutputStream gZIPOutputStream = new GZIPOutputStream(byteArrayOutputStream);
        gZIPOutputStream.write(bArr);
        gZIPOutputStream.close();
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        byteArrayOutputStream.close();
        return byteArray;
    }

    /* renamed from: d2 */
    public static byte[] m71748d2(File file) throws IOException {
        BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file));
        try {
            byte[] bArrMo75462b0 = bufferedSourceM75769e.mo75462b0();
            bufferedSourceM75769e.close();
            return bArrMo75462b0;
        } catch (Throwable th) {
            if (bufferedSourceM75769e != null) {
                try {
                    bufferedSourceM75769e.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    /* renamed from: e1 */
    public static byte[] m71749e1(byte[] bArr) throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bArr);
        GZIPInputStream gZIPInputStream = new GZIPInputStream(byteArrayInputStream, 32);
        byte[] bArr2 = new byte[32];
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(bArr.length);
        while (true) {
            int i2 = gZIPInputStream.read(bArr2);
            if (i2 == -1) {
                gZIPInputStream.close();
                byteArrayInputStream.close();
                return byteArrayOutputStream.toByteArray();
            }
            byteArrayOutputStream.write(bArr2, 0, i2);
        }
    }

    /* renamed from: e2 */
    public static String m71750e2(File file) throws IOException {
        BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(file));
        try {
            String strMo75460a2 = bufferedSourceM75769e.mo75460a2();
            bufferedSourceM75769e.close();
            return strMo75460a2;
        } catch (Throwable th) {
            if (bufferedSourceM75769e != null) {
                try {
                    bufferedSourceM75769e.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    /* renamed from: f */
    public static String m71751f(String str, String str2, String str3) {
        if (!str.contains(str2) || !str.contains(str3)) {
            return null;
        }
        return StringUtils.splitByWholeSeparator(StringUtils.splitByWholeSeparator(str, str2)[r2.length - 1], str3)[0];
    }

    /* renamed from: f1 */
    public static String m71752f1(Bundle bundle) {
        return bundle.getString("Path") + "/" + bundle.getString("Name");
    }

    /* renamed from: g1 */
    public static String m71753g1(Bundle bundle, String str) {
        return bundle.getString("Path") + "/" + str;
    }

    /* renamed from: h1 */
    public static String m71754h1(Bundle bundle, String str, String str2) {
        return bundle.getString("Path") + "/" + str2 + "/" + str;
    }

    /* renamed from: j2 */
    public static String m71755j2(String str) {
        ArrayList arrayList = new ArrayList(Arrays.asList(StringUtils.splitByWholeSeparator(str, "/")));
        arrayList.remove(arrayList.size() - 1);
        return StringUtils.join(arrayList, "/");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: k1 */
    public long m71756k1(String str) {
        File file = new File(str);
        if (file.exists()) {
            return file.length();
        }
        return 0L;
    }

    /* renamed from: l1 */
    public static HashSet<String> m71757l1() throws InterruptedException, IOException {
        HashSet<String> hashSet = new HashSet<>();
        String str = "";
        try {
            Process processStart = new ProcessBuilder(new String[0]).command("mount").redirectErrorStream(true).start();
            processStart.waitFor();
            InputStream inputStream = processStart.getInputStream();
            byte[] bArr = new byte[1024];
            while (inputStream.read(bArr) != -1) {
                str = str + new String(bArr);
            }
            inputStream.close();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
        for (String str2 : str.split(StringUtils.f103471LF)) {
            if (!str2.toLowerCase(Locale.US).contains("asec") && str2.matches("(?i).*vold.*(vfat|ntfs|exfat|fat32|ext3|ext4).*rw.*")) {
                for (String str3 : str2.split(StringUtils.SPACE)) {
                    if (str3.startsWith("/") && !str3.toLowerCase(Locale.US).contains("vold")) {
                        hashSet.add(str3);
                    }
                }
            }
        }
        return hashSet;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: m1 */
    public long m71758m1(String str) throws IOException {
        Response responseExecute = new OkHttpClient().mo74340a(new Request.Builder().m74760q(str).m74750g().m74745b()).execute();
        try {
            if (!responseExecute.m74781r()) {
                responseExecute.close();
                return -1L;
            }
            long j2 = Long.parseLong(responseExecute.m74776i(HttpHeaders.f62912b));
            responseExecute.close();
            return j2;
        } catch (Throwable th) {
            if (responseExecute != null) {
                try {
                    responseExecute.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    /* renamed from: q1 */
    public static Bundle m71759q1(ArrayList<Bundle> arrayList, final String str, final String str2) {
        ArrayList arrayList2 = new ArrayList(Collections2.m42365d(arrayList, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.13
            @Override // com.google.common.base.Predicate
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public boolean apply(Bundle bundle) {
                try {
                    return bundle.getString(str).equals(str2);
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("Error in filtering", e2.getLocalizedMessage());
                    return false;
                }
            }
        }));
        if (arrayList2.size() == 0) {
            return null;
        }
        return (Bundle) arrayList2.get(0);
    }

    /* renamed from: u1 */
    public static String m71762u1(Bundle bundle, String str) {
        return bundle.getString("Path") + "/" + str;
    }

    /* renamed from: v1 */
    public static String m71764v1() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        int iNextInt = random.nextInt(20);
        for (int i2 = 0; i2 < iNextInt; i2++) {
            sb.append((char) (random.nextInt(96) + 32));
        }
        return sb.toString();
    }

    /* renamed from: x2 */
    public static void m71767x2(Context context, String str, int i2) {
        if (context == null) {
            return;
        }
        try {
            Toast.makeText(context, str, i2).show();
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error", "Error in showtoast");
            e2.printStackTrace();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* renamed from: z2 */
    public String m71769z2(String str) throws MalformedURLException {
        if (str.startsWith("http://") || str.startsWith("https://")) {
            return str;
        }
        throw new MalformedURLException("Invalid URL scheme. URL should start with 'http://' or 'https://'");
    }

    /* renamed from: A */
    public String m71770A() throws IOException {
        String str = "";
        try {
            str = m71816U1() + "/spell.db";
            if (!new File(str).exists()) {
                InputStream inputStreamOpen = this.f87353d.getAssets().open("spell.db");
                FileOutputStream fileOutputStream = new FileOutputStream(str);
                byte[] bArr = new byte[1048576];
                while (true) {
                    int i2 = inputStreamOpen.read(bArr);
                    if (i2 <= 0) {
                        break;
                    }
                    fileOutputStream.write(bArr, 0, i2);
                }
                fileOutputStream.flush();
                fileOutputStream.close();
                inputStreamOpen.close();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f(CompressHelper.class.toString(), e2.getMessage());
        }
        return str;
    }

    /* renamed from: A0 */
    public void m71771A0(String str, String str2, String str3, String str4) {
        String strM71833a1 = m71833a1(str);
        String strM71833a12 = m71833a1(str2);
        String strM71833a13 = m71833a1(str4);
        String str5 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        m71881q(m71852h2(), "Insert into recent (dbName, dbTitle, dbAddress, dbDate, dbDocName) values ('" + strM71833a1.replace("'", "''") + "', '" + strM71833a12.replace("'", "''") + "', '" + str3.replace("'", "''") + "', '" + str5 + "', '" + strM71833a13.replace("'", "''") + "')");
    }

    /* renamed from: A1 */
    public void m71772A1(Bundle bundle, String str, String[] strArr, String str2) {
        m71775B1(bundle, str, strArr, str2, null);
    }

    /* renamed from: B */
    public String m71773B(String str, String str2, String str3) {
        String str4;
        if (!str3.equals("127")) {
            return null;
        }
        VBHelper vBHelper = this.f87355f;
        String str5 = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::")[1];
        byte[] bArrDecode = Base64.decode(str, 0);
        for (int length = str2.length(); length < 8; length++) {
            str2 = str2 + StringUtils.SPACE;
        }
        try {
        } catch (Exception e2) {
            e = e2;
            str4 = "CompressHelper _ GetString Decryption";
        }
        try {
            return new String(m71749e1(m71799N0(str5.toCharArray(), str2.getBytes(StandardCharsets.UTF_8), new byte[]{17, 115, 105, 102, 103, 104, 111, 107, 108, 122, 120, 119, 118, 98, 110, 109}, bArrDecode)));
        } catch (Exception e3) {
            e = e3;
            str4 = "CompressHelper _ GetString Decompressing";
            iMDLogger.m73550f(str4, e.toString());
            return null;
        }
    }

    /* renamed from: B0 */
    public void m71774B0(String str, String str2, String str3) {
        String strM71833a1 = m71833a1(str);
        String strM71833a12 = m71833a1(str2);
        String strM71833a13 = m71833a1(str3);
        String str4 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        m71881q(m71852h2(), "Insert into dbrecent (dbName, dbTitle, dbDate, dbIcon) values ('" + strM71833a1 + "', '" + strM71833a12 + "', '" + str4 + "', '" + strM71833a13 + "')");
    }

    /* JADX WARN: Removed duplicated region for block: B:232:0x0623  */
    /* JADX WARN: Removed duplicated region for block: B:235:0x062c  */
    /* JADX WARN: Removed duplicated region for block: B:238:0x063c  */
    /* JADX WARN: Removed duplicated region for block: B:360:0x0928  */
    /* JADX WARN: Removed duplicated region for block: B:476:0x0bc6  */
    /* JADX WARN: Removed duplicated region for block: B:479:0x0bcf  */
    /* JADX WARN: Removed duplicated region for block: B:482:0x0bdf  */
    /* JADX WARN: Removed duplicated region for block: B:485:0x0c01  */
    /* JADX WARN: Removed duplicated region for block: B:490:? A[ADDED_TO_REGION, RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:91:0x028d  */
    /* renamed from: B1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void m71775B1(android.os.Bundle r25, java.lang.String r26, java.lang.String[] r27, java.lang.String r28, android.os.Bundle r29) {
        /*
            Method dump skipped, instructions count: 3092
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Data.CompressHelper.m71775B1(android.os.Bundle, java.lang.String, java.lang.String[], java.lang.String, android.os.Bundle):void");
    }

    /* renamed from: C0 */
    public String m71776C0(String str, String str2) throws SQLException {
        String strM71833a1 = m71833a1(str);
        Bundle bundleM71871n1 = m71871n1(m71825Y(m71778D0(), "Select * from cache where cachekey='" + strM71833a1 + "'"));
        if (bundleM71871n1 == null) {
            return null;
        }
        if (bundleM71871n1.getString("cachevalidation").equals(str2)) {
            return bundleM71871n1.getString("cachecontent");
        }
        m71881q(m71778D0(), "Delete form cache where cachekey = '" + strM71833a1 + "'");
        return null;
    }

    /* renamed from: C2 */
    public void m71777C2(String str) {
        String str2 = m71797M1() + "/exp.txt";
        try {
            String strM73452n = new VBHelper(this.f87353d).m73452n(str, "127");
            BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(str2)));
            try {
                bufferedSinkM75768d.mo75454W0(strM73452n);
                bufferedSinkM75768d.close();
            } finally {
            }
        } catch (Exception unused) {
        }
    }

    /* renamed from: D0 */
    public String m71778D0() throws SQLException {
        String str = m71797M1() + "/cache.db";
        if (!new File(str).exists()) {
            SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("create table cache (id integer primary key autoincrement, cachekey varchar(255) unique, cachecontent text, cachevalidation text);");
        }
        return str;
    }

    /* renamed from: E */
    public ArrayList<Object> m71779E(JSONArray jSONArray) throws JSONException {
        ArrayList<Object> arrayList = new ArrayList<>();
        for (int i2 = 0; i2 < jSONArray.length(); i2++) {
            new Bundle();
            try {
                if (!jSONArray.isNull(i2)) {
                    Object obj = jSONArray.get(i2);
                    if (obj.getClass() != JSONArray.class) {
                        arrayList.add(obj.getClass() == JSONObject.class ? m71784G((JSONObject) obj) : jSONArray.getString(i2));
                    }
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                e2.getStackTrace()[0].getLineNumber();
                ArrayList arrayList2 = new ArrayList();
                for (int i3 = 0; i3 < e2.getStackTrace().length; i3++) {
                    String str = e2.getStackTrace()[i3].getClassName() + " - " + e2.getStackTrace()[i3].getLineNumber();
                    arrayList2.add(str);
                    iMDLogger.m73550f("JSONArrayTo", str);
                }
                e2.printStackTrace();
                iMDLogger.m73550f("JSONArrayToBundle", "Error in parsing");
                return null;
            }
        }
        return arrayList;
    }

    /* renamed from: E0 */
    public String m71780E0(String str) {
        if (str.contains(") order by RANDOM() limit 3")) {
            return str;
        }
        return "Select * from (" + str + ") order by RANDOM() limit 3";
    }

    /* renamed from: E1 */
    public String m71781E1(File file) throws IOException {
        String string = Long.toString(file.length());
        byte[] bArr = new byte[1048576];
        try {
            FileInputStream fileInputStream = new FileInputStream(file);
            try {
                BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75785u(fileInputStream));
                try {
                    bufferedSourceM75769e.read(bArr, 0, 1048576);
                    bufferedSourceM75769e.close();
                    fileInputStream.close();
                    CRC32 crc32 = new CRC32();
                    crc32.update(bArr, 0, 1048576);
                    return Long.toString(crc32.getValue()) + string;
                } finally {
                }
            } finally {
            }
        } catch (IOException unused) {
            return null;
        }
    }

    /* renamed from: F */
    public ArrayList<Bundle> m71782F(JSONArray jSONArray) throws JSONException {
        ArrayList arrayList = new ArrayList();
        for (int i2 = 0; i2 < jSONArray.length(); i2++) {
            new Bundle();
            try {
                Object obj = jSONArray.get(i2);
                if (obj.getClass() != JSONArray.class) {
                    arrayList.add(obj.getClass() == JSONObject.class ? m71784G((JSONObject) obj) : jSONArray.getString(i2));
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("JSONArrayToBundle", "Error in parsing");
                return null;
            }
        }
        ArrayList<Bundle> arrayList2 = new ArrayList<>();
        Iterator it2 = arrayList.iterator();
        while (it2.hasNext()) {
            arrayList2.add((Bundle) it2.next());
        }
        return arrayList2;
    }

    /* renamed from: F2 */
    public String m71783F2(Bundle bundle, String str) {
        try {
            BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(new File(m71753g1(bundle, "temp.html"))));
            try {
                bufferedSinkM75768d.mo75411C1(str, StandardCharsets.UTF_8);
                bufferedSinkM75768d.close();
                return Annotation.f68285k3;
            } finally {
            }
        } catch (Exception unused) {
            return Annotation.f68285k3;
        }
    }

    /* renamed from: G */
    public Bundle m71784G(JSONObject jSONObject) throws Exception {
        Bundle bundle = new Bundle();
        Iterator it2 = Lists.m43698s(jSONObject.keys()).iterator();
        while (it2.hasNext()) {
            String str = (String) it2.next();
            Object obj = jSONObject.get(str);
            if (obj.getClass() == JSONArray.class) {
                ArrayList<Object> arrayListM71779E = m71779E((JSONArray) obj);
                if (arrayListM71779E.size() == 0) {
                    bundle.putParcelableArrayList(str, new ArrayList<>());
                } else if (arrayListM71779E.get(0).getClass() == Bundle.class) {
                    ArrayList<? extends Parcelable> arrayList = new ArrayList<>();
                    Iterator<Object> it3 = arrayListM71779E.iterator();
                    while (it3.hasNext()) {
                        arrayList.add((Bundle) it3.next());
                    }
                    bundle.putParcelableArrayList(str, arrayList);
                } else {
                    ArrayList<String> arrayList2 = new ArrayList<>();
                    Iterator<Object> it4 = arrayListM71779E.iterator();
                    while (it4.hasNext()) {
                        arrayList2.add((String) it4.next());
                    }
                    bundle.putStringArrayList(str, arrayList2);
                }
            } else if (obj.getClass() == JSONObject.class) {
                bundle.putBundle(str, m71784G((JSONObject) obj));
            } else {
                bundle.putString(str, jSONObject.getString(str));
            }
        }
        return bundle;
    }

    /* renamed from: G1 */
    public void m71785G1() {
        Bundle bundleM71890s1 = m71890s1(m71825Y(m71852h2(), "Select c from r where id=1"));
        if (bundleM71890s1 == null) {
            m71881q(m71852h2(), "INSERT OR IGNORE INTO r values (1,0)");
            return;
        }
        int iIntValue = Integer.valueOf(bundleM71890s1.getString("c")).intValue() + 1;
        m71881q(m71852h2(), "update r set c=" + iIntValue + " where id=1");
    }

    /* renamed from: H */
    public void m71786H(String str, String str2, String str3) {
    }

    /* renamed from: H0 */
    public Observable<String> m71787H0(Activity activity, Observable<String> observable) {
        return observable.m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e());
    }

    /* renamed from: I */
    public void m71788I(String str, String str2) {
    }

    /* renamed from: I0 */
    public Observable<String> m71789I0(Fragment fragment, Observable<String> observable) {
        return observable.m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e());
    }

    /* renamed from: J */
    public String m71790J() {
        return "http://" + (this.f87353d.getSharedPreferences("default_preferences", 0).getString("MainServer", "Iran").equals("Iran") ? "si.imedicaldoctor.net" : "sg.imedicaldoctor.net");
    }

    /* renamed from: J0 */
    public byte[] m71791J0(char[] cArr, byte[] bArr, byte[] bArr2, byte[] bArr3) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1").generateSecret(new PBEKeySpec(cArr, bArr, 19, 128)).getEncoded(), "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(bArr2);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        cipher.init(1, secretKeySpec, ivParameterSpec);
        return cipher.doFinal(bArr3);
    }

    /* renamed from: K */
    public String m71792K() {
        String absolutePath = Environment.getExternalStoragePublicDirectory("Documents").getAbsolutePath();
        new File(absolutePath).mkdirs();
        return absolutePath + "/.iMD";
    }

    /* renamed from: K0 */
    public ArrayList<String> m71793K0() {
        ArrayList<Bundle> arrayList = ((iMD) this.f87353d.getApplicationContext()).f101678s;
        ArrayList<String> arrayList2 = new ArrayList<>();
        if ((arrayList.size() == 0) || (arrayList == null)) {
            return arrayList2;
        }
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            arrayList2.add(it2.next().getString("Name"));
        }
        return arrayList2;
    }

    /* renamed from: L */
    public String m71794L() {
        String absolutePath = Environment.getExternalStoragePublicDirectory("Documents").getAbsolutePath();
        new File(absolutePath).mkdirs();
        return absolutePath + "/iMD";
    }

    /* renamed from: L1 */
    public ArrayList<Bundle> m71795L1() throws JSONException {
        String str = m71797M1() + "/databases.json";
        if (((iMD) this.f87353d.getApplicationContext()).f101678s == null) {
            m71861k0();
            if (((iMD) this.f87353d.getApplicationContext()).f101678s == null) {
                ((iMD) this.f87353d.getApplicationContext()).f101678s = new ArrayList<>();
            }
        }
        ArrayList<Bundle> arrayList = new ArrayList<>();
        Iterator<Bundle> it2 = ((iMD) this.f87353d.getApplicationContext()).f101678s.iterator();
        while (it2.hasNext()) {
            arrayList.add(it2.next());
        }
        Collections.sort(arrayList, new Comparator<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.14
            @Override // java.util.Comparator
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public int compare(Bundle bundle, Bundle bundle2) {
                return bundle.getString("Section").compareTo(bundle.getString("Section"));
            }
        });
        if (!new File(str).exists()) {
            return m71883q2(arrayList);
        }
        try {
            JSONArray jSONArray = new JSONArray(new String(m71840c2(str)));
            ArrayList<Bundle> arrayList2 = new ArrayList<>();
            int i2 = 0;
            while (i2 < jSONArray.length()) {
                JSONObject jSONObject = jSONArray.getJSONObject(i2);
                JSONArray jSONArray2 = jSONObject.getJSONArray("items");
                ArrayList<? extends Parcelable> arrayList3 = new ArrayList<>();
                int i3 = 0;
                while (i3 < jSONArray2.length()) {
                    final JSONObject jSONObject2 = jSONArray2.getJSONObject(i3);
                    ArrayList arrayList4 = new ArrayList(Collections2.m42365d(arrayList, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.15
                        @Override // com.google.common.base.Predicate
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public boolean apply(Bundle bundle) {
                            try {
                                return bundle.getString("Name").equals(jSONObject2.getString("Name"));
                            } catch (Exception e2) {
                                FirebaseCrashlytics.m48010d().m48016g(e2);
                                iMDLogger.m73550f("Error in filtering", e2.getLocalizedMessage());
                                return false;
                            }
                        }
                    }));
                    JSONArray jSONArray3 = jSONArray;
                    if (arrayList4.size() == 1) {
                        Bundle bundle = (Bundle) arrayList4.get(0);
                        arrayList.remove(bundle);
                        if (jSONObject2.has("dontSearch") && jSONObject2.getString("dontSearch").equals(IcyHeaders.f28171a3)) {
                            bundle.putString("dontSearch", IcyHeaders.f28171a3);
                        } else {
                            bundle.putString("dontSearch", "0");
                        }
                        arrayList3.add(bundle);
                    }
                    i3++;
                    jSONArray = jSONArray3;
                }
                JSONArray jSONArray4 = jSONArray;
                Bundle bundle2 = new Bundle();
                bundle2.putString("title", jSONObject.getString("title"));
                bundle2.putParcelableArrayList("items", arrayList3);
                arrayList2.add(bundle2);
                i2++;
                jSONArray = jSONArray4;
            }
            if (arrayList.size() > 0) {
                ArrayList<Bundle> arrayListM71883q2 = m71883q2(arrayList);
                for (int i4 = 0; i4 < arrayListM71883q2.size(); i4++) {
                    final Bundle bundle3 = arrayListM71883q2.get(i4);
                    ArrayList arrayList5 = new ArrayList(Collections2.m42365d(arrayList2, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.16
                        @Override // com.google.common.base.Predicate
                        /* renamed from: a, reason: merged with bridge method [inline-methods] */
                        public boolean apply(Bundle bundle4) {
                            return bundle4.getString("title").equals(bundle3.getString("title"));
                        }
                    }));
                    if (arrayList5.size() == 0) {
                        Bundle bundle4 = new Bundle();
                        bundle4.putString("title", bundle3.getString("title"));
                        bundle4.putParcelableArrayList("items", bundle3.getParcelableArrayList("items"));
                        arrayList2.add(bundle4);
                    } else {
                        Bundle bundle5 = (Bundle) arrayList5.get(0);
                        ArrayList<? extends Parcelable> parcelableArrayList = bundle5.getParcelableArrayList("items");
                        parcelableArrayList.addAll(bundle3.getParcelableArrayList("items"));
                        int iIndexOf = arrayList2.indexOf(bundle5);
                        Bundle bundle6 = new Bundle();
                        bundle6.putString("title", bundle5.getString("title"));
                        bundle6.putParcelableArrayList("items", parcelableArrayList);
                        arrayList2.remove(iIndexOf);
                        arrayList2.add(iIndexOf, bundle6);
                    }
                }
            }
            return arrayList2;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in Reading Json", e2.getLocalizedMessage());
            return m71883q2(arrayList);
        }
    }

    /* renamed from: M */
    public void m71796M(Class<?> cls, Class<?> cls2, Bundle bundle, int i2) {
        if (!m71903x1()) {
            Intent intent = new Intent(this.f87353d, cls);
            intent.putExtras(bundle);
            this.f87353d.startActivity(intent);
            ((Activity) this.f87353d).overridePendingTransition(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out);
            return;
        }
        try {
            Fragment fragment = (Fragment) cls2.getConstructor(null).newInstance(null);
            fragment.m15342i2(bundle);
            if (i2 == C5562R.id.container) {
                m71834a2(f87347v, fragment, C5562R.id.rootcontainer, true, true);
            } else {
                m71834a2(f87348w, fragment, i2, false, true);
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("OpenFragment", "Error in creating fragment : " + e2);
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
        }
    }

    /* renamed from: M1 */
    public String m71797M1() {
        return m71816U1();
    }

    /* renamed from: N */
    public void m71798N(Class<?> cls, Class<?> cls2, Bundle bundle) {
        m71796M(cls, cls2, bundle, C5562R.id.container);
    }

    /* renamed from: N0 */
    public byte[] m71799N0(char[] cArr, byte[] bArr, byte[] bArr2, byte[] bArr3) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1").generateSecret(new PBEKeySpec(cArr, bArr, 19, 128)).getEncoded(), "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(bArr2);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        cipher.init(2, secretKeySpec, ivParameterSpec);
        return cipher.doFinal(bArr3);
    }

    /* renamed from: N1 */
    public boolean m71800N1(Bundle bundle, String str) {
        String strDecode = URLDecoder.decode(str);
        if (strDecode.contains("rx/monograph/")) {
            m71772A1(bundle, "rx-" + String.valueOf(Integer.valueOf(m71893t1(StringUtils.splitByWholeSeparator(strDecode, "rx/monograph/"))).intValue() + 1), null, null);
            return true;
        }
        if (strDecode.contains("dx/monograph/")) {
            m71772A1(bundle, "dx-" + m71893t1(StringUtils.splitByWholeSeparator(strDecode, "dx/monograph/")), null, null);
            return true;
        }
        if (strDecode.contains("lab/monograph/")) {
            m71772A1(bundle, "lab-" + m71893t1(StringUtils.splitByWholeSeparator(strDecode, "lab/monograph/")), null, null);
            return true;
        }
        if (strDecode.contains("lab/list/panel/")) {
            Bundle bundleM71890s1 = m71890s1(m71817V(bundle, "Select * from lab_panel where id2=" + m71893t1(StringUtils.splitByWholeSeparator(strDecode, "lab/list/panel/"))));
            if (bundleM71890s1 != null) {
                Bundle bundle2 = new Bundle();
                bundle2.putBundle("DB", bundle);
                bundle2.putString("ParentId", bundleM71890s1.getString("id"));
                m71798N(EPOLabListActivity.class, EPOLabListActivityFragment.class, bundle2);
            }
            return true;
        }
        if (!strDecode.contains("rx/list/drug?select=")) {
            iMDLogger.m73554j("manageEpocrateURL", "Can't manage " + strDecode);
            return false;
        }
        Bundle bundleM71890s12 = m71890s1(m71819W(bundle, "select * from drug where name='" + m71893t1(StringUtils.splitByWholeSeparator(strDecode, "rx/list/drug?select=")).replace("*", "") + "'", "RX.sqlite"));
        if (bundleM71890s12 == null) {
            m71767x2(this.f87353d, "Sorry, Can't find it", 1);
            return true;
        }
        m71772A1(bundle, "rx-" + bundleM71890s12.getString("ID"), null, null);
        return true;
    }

    /* renamed from: O */
    public void m71801O(Class<?> cls, Class<?> cls2, Bundle bundle) {
        m71796M(cls, cls2, bundle, C5562R.id.detail_container);
        SlidingPaneLayout slidingPaneLayout = (SlidingPaneLayout) ((Activity) this.f87353d).findViewById(C5562R.id.sliding_layout);
        if (slidingPaneLayout != null) {
            slidingPaneLayout.m28119c();
        }
    }

    /* renamed from: O0 */
    public void m71802O0(char[] cArr, byte[] bArr, byte[] bArr2, FileInputStream fileInputStream, FileOutputStream fileOutputStream) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1").generateSecret(new PBEKeySpec(cArr, bArr, 19, 128)).getEncoded(), "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(bArr2);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        cipher.init(2, secretKeySpec, ivParameterSpec);
        try {
            CipherInputStream cipherInputStream = new CipherInputStream(fileInputStream, cipher);
            try {
                Source sourceM75785u = Okio.m75785u(cipherInputStream);
                try {
                    BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75780p(fileOutputStream));
                    try {
                        bufferedSinkM75768d.mo75508y1(sourceM75785u);
                        bufferedSinkM75768d.close();
                        if (sourceM75785u != null) {
                            sourceM75785u.close();
                        }
                        cipherInputStream.close();
                    } finally {
                    }
                } finally {
                }
            } finally {
            }
        } catch (IOException e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f(getClass().toString(), "Error in decrypting stream");
        }
    }

    /* renamed from: P */
    public void m71803P(String str) {
        this.f87353d.startActivity(new Intent("android.intent.action.VIEW").setData(Uri.parse(str)));
    }

    /* renamed from: P1 */
    public String m71804P1(File file) throws NoSuchAlgorithmException, IOException {
        try {
            if (!file.exists()) {
                return "";
            }
            MessageDigest.getInstance("MD5");
            FileInputStream fileInputStream = new FileInputStream(file);
            String strEncodeToString = Base64.encodeToString(DigestUtils.md5(fileInputStream), 0);
            fileInputStream.close();
            return strEncodeToString;
        } catch (Exception unused) {
            return "";
        }
    }

    /* renamed from: Q */
    public String m71805Q(String str) {
        return m71895u(str).equals(m71895u(m71797M1())) ? "Internal Storage" : "External Storage";
    }

    /* renamed from: R */
    public String m71806R() {
        String str = m71797M1() + "/psych.db";
        if (!new File(str).exists()) {
            try {
                SQLiteDatabase sQLiteDatabaseOpenOrCreateDatabase = SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null);
                sQLiteDatabaseOpenOrCreateDatabase.execSQL("CREATE TABLE tol (_id INTEGER PRIMARY KEY  AUTOINCREMENT  NOT NULL  UNIQUE , name TEXT, time integer, moves integer, dateadded TEXT)");
                sQLiteDatabaseOpenOrCreateDatabase.execSQL("CREATE TABLE igt (_id INTEGER PRIMARY KEY  AUTOINCREMENT  NOT NULL  UNIQUE , name TEXT, money integer, time integer, a integer,b integer,c integer, d integer,pos int,neg int, dateadded TEXT)");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
            }
        }
        return str;
    }

    /* renamed from: R0 */
    public void m71807R0(final Runnable runnable, final Runnable runnable2) {
        Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.28
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    runnable.run();
                    observableEmitter.onNext("asdfadf");
                } catch (Exception unused) {
                }
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59681e6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.29
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(String str) throws Throwable {
                try {
                    runnable2.run();
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.30
            @Override // io.reactivex.rxjava3.functions.Consumer
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public void accept(Throwable th) throws Throwable {
                try {
                    iMDLogger.m73550f("Error occured", th.getMessage());
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
            }
        });
    }

    /* renamed from: S */
    public Cursor m71808S(Bundle bundle, String str) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return m71814U(m71752f1(bundle), str);
    }

    /* renamed from: S0 */
    public void m71809S0(String str, String str2) throws IOException {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        TimeUnit timeUnit = TimeUnit.SECONDS;
        Response responseExecute = builder.m74699i(30L, timeUnit).m74682C(100L, timeUnit).m74689J(30L, timeUnit).m74694d().mo74340a(new Request.Builder().m74760q(str).m74745b()).execute();
        if (new File(str2).exists()) {
            new File(str2).delete();
        }
        if (responseExecute.m74781r()) {
            try {
                BufferedInputStream bufferedInputStream = new BufferedInputStream(responseExecute.m74770b().m74812b(), 8192);
                try {
                    FileOutputStream fileOutputStream = new FileOutputStream(str2);
                    try {
                        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream, 8192);
                        try {
                            byte[] bArr = new byte[1048576];
                            while (true) {
                                int i2 = bufferedInputStream.read(bArr);
                                if (i2 == -1) {
                                    break;
                                } else {
                                    bufferedOutputStream.write(bArr, 0, i2);
                                }
                            }
                            System.out.println(str + "File downloaded successfully");
                            bufferedOutputStream.close();
                            fileOutputStream.close();
                            bufferedInputStream.close();
                        } finally {
                        }
                    } finally {
                    }
                } finally {
                }
            } catch (IOException e2) {
                e2.printStackTrace();
                throw new IOException("Error during download", e2);
            }
        } else {
            System.out.println("No file to download. Server replied HTTP code: " + responseExecute.m74774f());
        }
        responseExecute.close();
    }

    /* renamed from: S1 */
    public String m71810S1(String str) {
        return str;
    }

    /* renamed from: T */
    public Cursor m71811T(Bundle bundle, String str, String str2) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return m71814U(m71753g1(bundle, str2), str);
    }

    /* renamed from: T0 */
    public Observable<String> m71812T0(final String str, final String str2) {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.2
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    CompressHelper.this.m71809S0(str, str2);
                    observableEmitter.onComplete();
                } catch (Exception e2) {
                    iMDLogger.m73550f("DownloadFile", "Error in downloading file " + e2);
                    observableEmitter.onError(e2);
                }
            }
        });
    }

    /* renamed from: T1 */
    public String m71813T1(String str) {
        return str;
    }

    /* renamed from: U */
    public Cursor m71814U(String str, String str2) {
        try {
            return this.f87353d.getContentResolver().query(Uri.parse("content://net.imedicaldoctor.imd/query"), null, str, null, str2);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            iMDLogger.m73550f("QueryDB " + str, "Error in Query DB , " + str + ", " + e2.getLocalizedMessage());
            return null;
        }
    }

    /* renamed from: U0 */
    public Observable<HttpURLConnection> m71815U0(final String str, final String str2, final Bundle bundle) {
        final String str3 = str2 + ".download";
        final String str4 = str2 + ".md5";
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        TimeUnit timeUnit = TimeUnit.SECONDS;
        final OkHttpClient okHttpClientM74694d = builder.m74699i(30L, timeUnit).m74689J(30L, timeUnit).m74682C(100L, timeUnit).m74694d();
        return Observable.m59451w1(new ObservableOnSubscribe<HttpURLConnection>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.3
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull final ObservableEmitter<HttpURLConnection> observableEmitter) throws Throwable {
                try {
                    final String strM71769z2 = CompressHelper.this.m71769z2(str);
                    if (CompressHelper.this.m71722A2(str2, str4, bundle)) {
                        observableEmitter.onComplete();
                        return;
                    }
                    final long jM71756k1 = CompressHelper.this.m71756k1(str3);
                    okHttpClientM74694d.mo74340a(new Request.Builder().m74760q(strM71769z2).m74744a(HttpHeaders.f62854I, "bytes=" + jM71756k1 + "-").m74745b()).mo74336e0(new Callback() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.3.1
                        /* JADX WARN: Code restructure failed: missing block: B:30:0x007b, code lost:
                        
                            r3.flush();
                         */
                        /* JADX WARN: Code restructure failed: missing block: B:31:0x0090, code lost:
                        
                            if (new java.io.File(r6).length() != (r0 + r4)) goto L33;
                         */
                        /* JADX WARN: Code restructure failed: missing block: B:32:0x0092, code lost:
                        
                            r0 = r8.f87417d;
                            r0.f87413g.m71723B2(r6, r3, r4, r5);
                         */
                        /* JADX WARN: Code restructure failed: missing block: B:33:0x00a1, code lost:
                        
                            r3.close();
                         */
                        /* JADX WARN: Code restructure failed: missing block: B:34:0x00a4, code lost:
                        
                            r10.close();
                         */
                        /* JADX WARN: Code restructure failed: missing block: B:35:0x00a7, code lost:
                        
                            r9.close();
                         */
                        /* JADX WARN: Code restructure failed: missing block: B:36:0x00aa, code lost:
                        
                            r2.onComplete();
                         */
                        /* JADX WARN: Code restructure failed: missing block: B:37:0x00af, code lost:
                        
                            return;
                         */
                        @Override // okhttp3.Callback
                        /* renamed from: a */
                        /*
                            Code decompiled incorrectly, please refer to instructions dump.
                            To view partially-correct add '--show-bad-code' argument
                        */
                        public void mo71920a(okhttp3.Call r9, okhttp3.Response r10) throws java.io.IOException {
                            /*
                                r8 = this;
                                boolean r9 = r10.m74781r()
                                if (r9 != 0) goto L22
                                io.reactivex.rxjava3.core.ObservableEmitter r9 = r2
                                java.io.IOException r0 = new java.io.IOException
                                java.lang.StringBuilder r1 = new java.lang.StringBuilder
                                r1.<init>()
                                java.lang.String r2 = "Unexpected code "
                                r1.append(r2)
                                r1.append(r10)
                                java.lang.String r10 = r1.toString()
                                r0.<init>(r10)
                                r9.onError(r0)
                                return
                            L22:
                                net.imedicaldoctor.imd.Data.CompressHelper$3 r9 = net.imedicaldoctor.imd.Data.CompressHelper.C46543.this
                                net.imedicaldoctor.imd.Data.CompressHelper r9 = net.imedicaldoctor.imd.Data.CompressHelper.this
                                java.lang.String r0 = r3
                                long r0 = net.imedicaldoctor.imd.Data.CompressHelper.m71765w0(r9, r0)
                                long r2 = r4
                                long r0 = r0 - r2
                                java.io.BufferedInputStream r9 = new java.io.BufferedInputStream     // Catch: java.io.IOException -> L6e
                                okhttp3.ResponseBody r10 = r10.m74770b()     // Catch: java.io.IOException -> L6e
                                java.io.InputStream r10 = r10.m74812b()     // Catch: java.io.IOException -> L6e
                                r2 = 16384(0x4000, float:2.2959E-41)
                                r9.<init>(r10, r2)     // Catch: java.io.IOException -> L6e
                                java.io.FileOutputStream r10 = new java.io.FileOutputStream     // Catch: java.lang.Throwable -> L70
                                net.imedicaldoctor.imd.Data.CompressHelper$3 r3 = net.imedicaldoctor.imd.Data.CompressHelper.C46543.this     // Catch: java.lang.Throwable -> L70
                                java.lang.String r3 = r6     // Catch: java.lang.Throwable -> L70
                                r4 = 1
                                r10.<init>(r3, r4)     // Catch: java.lang.Throwable -> L70
                                java.io.BufferedOutputStream r3 = new java.io.BufferedOutputStream     // Catch: java.lang.Throwable -> L72
                                r3.<init>(r10, r2)     // Catch: java.lang.Throwable -> L72
                                r2 = 1048576(0x100000, float:1.469368E-39)
                                byte[] r2 = new byte[r2]     // Catch: java.lang.Throwable -> L79
                            L51:
                                int r4 = r9.read(r2)     // Catch: java.lang.Throwable -> L79
                                r5 = -1
                                if (r4 == r5) goto L7b
                                net.imedicaldoctor.imd.Data.CompressHelper$3 r5 = net.imedicaldoctor.imd.Data.CompressHelper.C46543.this     // Catch: java.lang.Throwable -> L79
                                net.imedicaldoctor.imd.Data.CompressHelper r6 = net.imedicaldoctor.imd.Data.CompressHelper.this     // Catch: java.lang.Throwable -> L79
                                android.os.Bundle r5 = r5     // Catch: java.lang.Throwable -> L79
                                boolean r5 = net.imedicaldoctor.imd.Data.CompressHelper.m71766x0(r6, r5)     // Catch: java.lang.Throwable -> L79
                                if (r5 != 0) goto L74
                                r3.close()     // Catch: java.lang.Throwable -> L72
                                r10.close()     // Catch: java.lang.Throwable -> L70
                                r9.close()     // Catch: java.io.IOException -> L6e
                                return
                            L6e:
                                r9 = move-exception
                                goto Lcb
                            L70:
                                r10 = move-exception
                                goto Lc2
                            L72:
                                r0 = move-exception
                                goto Lb9
                            L74:
                                r5 = 0
                                r3.write(r2, r5, r4)     // Catch: java.lang.Throwable -> L79
                                goto L51
                            L79:
                                r0 = move-exception
                                goto Lb0
                            L7b:
                                r3.flush()     // Catch: java.lang.Throwable -> L79
                                java.io.File r2 = new java.io.File     // Catch: java.lang.Throwable -> L79
                                net.imedicaldoctor.imd.Data.CompressHelper$3 r4 = net.imedicaldoctor.imd.Data.CompressHelper.C46543.this     // Catch: java.lang.Throwable -> L79
                                java.lang.String r4 = r6     // Catch: java.lang.Throwable -> L79
                                r2.<init>(r4)     // Catch: java.lang.Throwable -> L79
                                long r4 = r2.length()     // Catch: java.lang.Throwable -> L79
                                long r6 = r4     // Catch: java.lang.Throwable -> L79
                                long r0 = r0 + r6
                                int r2 = (r4 > r0 ? 1 : (r4 == r0 ? 0 : -1))
                                if (r2 != 0) goto La1
                                net.imedicaldoctor.imd.Data.CompressHelper$3 r0 = net.imedicaldoctor.imd.Data.CompressHelper.C46543.this     // Catch: java.lang.Throwable -> L79
                                net.imedicaldoctor.imd.Data.CompressHelper r1 = net.imedicaldoctor.imd.Data.CompressHelper.this     // Catch: java.lang.Throwable -> L79
                                java.lang.String r2 = r6     // Catch: java.lang.Throwable -> L79
                                java.lang.String r4 = r3     // Catch: java.lang.Throwable -> L79
                                java.lang.String r5 = r4     // Catch: java.lang.Throwable -> L79
                                android.os.Bundle r0 = r5     // Catch: java.lang.Throwable -> L79
                                net.imedicaldoctor.imd.Data.CompressHelper.m71768y0(r1, r2, r4, r5, r0)     // Catch: java.lang.Throwable -> L79
                            La1:
                                r3.close()     // Catch: java.lang.Throwable -> L72
                                r10.close()     // Catch: java.lang.Throwable -> L70
                                r9.close()     // Catch: java.io.IOException -> L6e
                                io.reactivex.rxjava3.core.ObservableEmitter r9 = r2
                                r9.onComplete()
                                return
                            Lb0:
                                r3.close()     // Catch: java.lang.Throwable -> Lb4
                                goto Lb8
                            Lb4:
                                r1 = move-exception
                                r0.addSuppressed(r1)     // Catch: java.lang.Throwable -> L72
                            Lb8:
                                throw r0     // Catch: java.lang.Throwable -> L72
                            Lb9:
                                r10.close()     // Catch: java.lang.Throwable -> Lbd
                                goto Lc1
                            Lbd:
                                r10 = move-exception
                                r0.addSuppressed(r10)     // Catch: java.lang.Throwable -> L70
                            Lc1:
                                throw r0     // Catch: java.lang.Throwable -> L70
                            Lc2:
                                r9.close()     // Catch: java.lang.Throwable -> Lc6
                                goto Lca
                            Lc6:
                                r9 = move-exception
                                r10.addSuppressed(r9)     // Catch: java.io.IOException -> L6e
                            Lca:
                                throw r10     // Catch: java.io.IOException -> L6e
                            Lcb:
                                io.reactivex.rxjava3.core.ObservableEmitter r10 = r2
                                r10.onError(r9)
                                return
                            */
                            throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Data.CompressHelper.C46543.AnonymousClass1.mo71920a(okhttp3.Call, okhttp3.Response):void");
                        }

                        @Override // okhttp3.Callback
                        /* renamed from: b */
                        public void mo71921b(Call call, IOException iOException) {
                            observableEmitter.onError(iOException);
                        }
                    });
                } catch (Exception e2) {
                    observableEmitter.onError(e2);
                }
            }
        });
    }

    /* renamed from: U1 */
    public String m71816U1() {
        Context context = this.f87353d;
        if (context == null) {
            return "";
        }
        File externalFilesDir = context.getExternalFilesDir("");
        if (externalFilesDir == null) {
            externalFilesDir = this.f87353d.getFilesDir();
        }
        return externalFilesDir.toString();
    }

    /* renamed from: V */
    public ArrayList<Bundle> m71817V(Bundle bundle, String str) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return m71825Y(m71752f1(bundle), str);
    }

    /* renamed from: V0 */
    public void m71818V0() {
        if (m71906y2().booleanValue()) {
            return;
        }
        m71894t2(null);
        Process.killProcess(Process.myPid());
    }

    /* renamed from: W */
    public ArrayList<Bundle> m71819W(Bundle bundle, String str, String str2) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return m71825Y(m71753g1(bundle, str2), str);
    }

    /* renamed from: W0 */
    public void m71820W0() {
        m71785G1();
    }

    /* renamed from: W1 */
    public boolean m71821W1(boolean z) {
        String str;
        int i2;
        if (!m71903x1()) {
            ((Activity) this.f87353d).finish();
            ((Activity) this.f87353d).overridePendingTransition(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
            return false;
        }
        if (z) {
            str = f87347v;
            i2 = C5562R.id.rootcontainer;
        } else {
            str = f87348w;
            i2 = C5562R.id.detail_container;
        }
        return m71824X1(str, i2);
    }

    /* renamed from: X */
    public ArrayList<Bundle> m71822X(Bundle bundle, String str, String str2, boolean z) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        ArrayList<Bundle> arrayListM71819W = m71819W(bundle, str, str2);
        return (arrayListM71819W == null && z) ? new ArrayList<>() : arrayListM71819W;
    }

    /* renamed from: X0 */
    public String m71823X0() {
        String str = m71797M1() + "/favorites.db";
        if (!new File(str).exists()) {
            try {
                SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("CREATE TABLE favorites (_id INTEGER PRIMARY KEY  AUTOINCREMENT  NOT NULL  UNIQUE , dbName TEXT, dbTitle TEXT, dbAddress TEXT, dbDate TEXT, dbDocName TEXT);");
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                new File(str).delete();
                try {
                    SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("CREATE TABLE favorites (_id INTEGER PRIMARY KEY  AUTOINCREMENT  NOT NULL  UNIQUE , dbName TEXT, dbTitle TEXT, dbAddress TEXT, dbDate TEXT, dbDocName TEXT);");
                } catch (Exception unused) {
                }
            }
        }
        return str;
    }

    /* renamed from: X1 */
    public boolean m71824X1(String str, int i2) {
        try {
        } catch (Exception e2) {
            e2.printStackTrace();
        }
        if (f87344s.get(str).size() == 0) {
            return false;
        }
        if (f87344s.get(str).size() > 1) {
            Fragment fragmentElementAt = f87344s.get(str).elementAt(f87344s.get(str).size() - 2);
            f87344s.get(str).pop();
            FragmentTransaction fragmentTransactionM15664u = ((AppCompatActivity) this.f87353d).m15416k0().m15664u();
            fragmentTransactionM15664u.m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
            if (m71903x1() && str.equals(f87348w)) {
                fragmentTransactionM15664u.m15813M(C5562R.anim.fade_out, C5562R.anim.fade_in);
            }
            fragmentTransactionM15664u.m15803C(i2, fragmentElementAt);
            fragmentTransactionM15664u.mo15164r();
        } else {
            Fragment fragmentElementAt2 = f87344s.get(str).elementAt(f87344s.get(str).size() - 1);
            f87344s.get(str).pop();
            FragmentTransaction fragmentTransactionM15664u2 = ((AppCompatActivity) this.f87353d).m15416k0().m15664u();
            fragmentTransactionM15664u2.m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
            if (m71903x1() && str.equals(f87348w)) {
                fragmentTransactionM15664u2.m15813M(C5562R.anim.fade_out, C5562R.anim.fade_in);
            }
            fragmentTransactionM15664u2.mo15144B(fragmentElementAt2);
            fragmentTransactionM15664u2.mo15164r();
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.25
                @Override // java.lang.Runnable
                public void run() {
                    LocalBroadcastManager.m16410b(CompressHelper.this.f87353d).m16413d(new Intent("showLeftPane"));
                }
            }, 500L);
        }
        return true;
    }

    /* renamed from: Y */
    public ArrayList<Bundle> m71825Y(String str, String str2) {
        try {
            Context context = this.f87353d;
            if (context == null) {
                return null;
            }
            Cursor cursorQuery = context.getContentResolver().query(Uri.parse("content://net.imedicaldoctor.imd/query"), null, str, null, str2);
            ArrayList<Bundle> arrayListM71838c = m71838c(cursorQuery);
            if (cursorQuery == null) {
                return null;
            }
            cursorQuery.close();
            m71847g(str);
            return arrayListM71838c;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("QueryDBAsArray " + str, "Error in Query DB , " + str + ", " + e2.getLocalizedMessage());
            e2.printStackTrace();
            return null;
        }
    }

    /* renamed from: Y0 */
    public Bundle m71826Y0(final String str, final String str2) {
        ArrayList<Bundle> arrayList = ((iMD) this.f87353d.getApplicationContext()).f101678s;
        if ((arrayList.size() == 0) || (arrayList == null)) {
            return null;
        }
        ArrayList arrayList2 = new ArrayList(Collections2.m42365d(arrayList, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.11
            @Override // com.google.common.base.Predicate
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public boolean apply(Bundle bundle) {
                try {
                    return bundle.getString(str).equals(str2);
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    return false;
                }
            }
        }));
        if (arrayList2.size() == 0) {
            return null;
        }
        return (Bundle) arrayList2.get(0);
    }

    /* renamed from: Y1 */
    public void m71827Y1(String str, int i2) {
        try {
            if (f87344s.get(str).size() > 1) {
                while (f87344s.get(str).size() == 1) {
                    f87344s.get(str).pop();
                }
            }
            Fragment fragmentElementAt = f87344s.get(str).elementAt(f87344s.get(str).size() - 1);
            f87344s.get(str).pop();
            FragmentTransaction fragmentTransactionM15664u = ((AppCompatActivity) this.f87353d).m15416k0().m15664u();
            fragmentTransactionM15664u.m15813M(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
            if (m71903x1() && str.equals(f87348w)) {
                fragmentTransactionM15664u.m15813M(C5562R.anim.fade_out, C5562R.anim.fade_in);
            }
            fragmentTransactionM15664u.mo15144B(fragmentElementAt);
            fragmentTransactionM15664u.mo15164r();
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    /* renamed from: Z */
    public ArrayList<String> m71828Z(String str, String str2, String str3) {
        try {
            Cursor cursorQuery = this.f87353d.getContentResolver().query(Uri.parse("content://net.imedicaldoctor.imd/query"), null, str, null, str2);
            ArrayList<String> arrayListM71841d = m71841d(cursorQuery, str3);
            cursorQuery.close();
            m71847g(str);
            return arrayListM71841d;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("QueryDBAsArray " + str, "Error in Query DB , " + str + ", " + e2.getLocalizedMessage());
            e2.printStackTrace();
            return null;
        }
    }

    /* renamed from: Z0 */
    public ArrayList<Bundle> m71829Z0(final String str, final String str2) {
        return new ArrayList<>(Collections2.m42365d(((iMD) this.f87353d.getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.12
            @Override // com.google.common.base.Predicate
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public boolean apply(Bundle bundle) {
                try {
                    return bundle.getString(str).equals(str2);
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("Error in filtering", e2.getLocalizedMessage());
                    return false;
                }
            }
        }));
    }

    /* renamed from: Z1 */
    public void m71830Z1(boolean z) {
        String str;
        int i2;
        if (!m71903x1()) {
            Intent intent = new Intent(this.f87353d, (Class<?>) mainActivity.class);
            intent.addFlags(67108864);
            this.f87353d.startActivity(intent);
            ((Activity) this.f87353d).overridePendingTransition(C5562R.anim.to_fade_in, C5562R.anim.to_fade_out);
            return;
        }
        if (z) {
            str = f87347v;
            i2 = C5562R.id.rootcontainer;
        } else {
            str = f87348w;
            i2 = C5562R.id.detail_container;
        }
        m71827Y1(str, i2);
    }

    /* renamed from: a */
    public String m71831a() {
        StringBuilder sb;
        String str;
        VBHelper vBHelper = this.f87355f;
        String[] strArrSplit = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::");
        ArrayList arrayList = new ArrayList(Arrays.asList(TextUtils.split(strArrSplit[3], ",")));
        String str2 = strArrSplit[2];
        if (arrayList.contains(TtmlNode.f29738r0)) {
            if (str2.length() <= 0) {
                return "All";
            }
            sb = new StringBuilder();
            str = "Active|";
        } else {
            if (!arrayList.contains("expired")) {
                return "Simple";
            }
            sb = new StringBuilder();
            str = "Expired|";
        }
        sb.append(str);
        sb.append(str2);
        return sb.toString();
    }

    /* renamed from: a0 */
    public Observable<ArrayList<Bundle>> m71832a0(final Bundle bundle, final String str) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return Observable.m59451w1(new ObservableOnSubscribe<ArrayList<Bundle>>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.20
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<ArrayList<Bundle>> observableEmitter) throws Throwable {
                ArrayList<Bundle> arrayListM71817V = CompressHelper.this.m71817V(bundle, str);
                if (arrayListM71817V != null) {
                    observableEmitter.onNext(arrayListM71817V);
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: a1 */
    public String m71833a1(String str) {
        return str.replace("'", "''");
    }

    /* renamed from: a2 */
    public void m71834a2(String str, Fragment fragment, int i2, boolean z, boolean z2) {
        if (z2) {
            try {
                f87344s.get(str).push(fragment);
            } catch (Exception e2) {
                e2.printStackTrace();
                FirebaseCrashlytics.m48010d().m48016g(e2);
                return;
            }
        }
        if (str.equals(f87348w) && f87344s.get(str).size() > 3) {
            f87344s.get(str).removeElementAt(0);
        }
        FragmentTransaction fragmentTransactionM15664u = ((AppCompatActivity) this.f87353d).m15416k0().m15664u();
        if (z) {
            fragmentTransactionM15664u.m15813M(C5562R.anim.from_fade_in, C5562R.anim.from_fade_out);
        }
        if (m71903x1() && str.equals(f87348w)) {
            fragmentTransactionM15664u.m15813M(C5562R.anim.fade_in, C5562R.anim.fade_out);
        }
        fragmentTransactionM15664u.m15803C(i2, fragment);
        fragmentTransactionM15664u.mo15164r();
    }

    /* renamed from: b0 */
    public Observable<ArrayList<Bundle>> m71835b0(final Bundle bundle, final String str, final String str2) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return Observable.m59451w1(new ObservableOnSubscribe<ArrayList<Bundle>>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.22
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<ArrayList<Bundle>> observableEmitter) throws Throwable {
                ArrayList<Bundle> arrayListM71819W = CompressHelper.this.m71819W(bundle, str, str2);
                if (arrayListM71819W != null) {
                    observableEmitter.onNext(arrayListM71819W);
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: b1 */
    public String m71836b1(String str) {
        return str.replace("&amp;", "&");
    }

    /* renamed from: b2 */
    public long m71837b2() {
        long jLongValue;
        Bundle bundleM71890s1 = m71890s1(m71825Y(m71852h2(), "Select c from r where id=2"));
        Date date = new Date();
        if (bundleM71890s1 == null) {
            m71881q(m71852h2(), "INSERT OR IGNORE INTO r values (2," + date.getTime() + ")");
            jLongValue = date.getTime();
        } else {
            jLongValue = Long.valueOf(bundleM71890s1.getString("c")).longValue();
        }
        m71881q(m71852h2(), "update r set c=" + date.getTime() + " where id=2");
        return jLongValue;
    }

    /* renamed from: c */
    public ArrayList<Bundle> m71838c(Cursor cursor) {
        if (cursor == null || !cursor.moveToFirst()) {
            return null;
        }
        ArrayList<Bundle> arrayList = new ArrayList<>(cursor.getCount());
        int columnCount = cursor.getColumnCount();
        do {
            Bundle bundle = new Bundle();
            for (int i2 = 0; i2 < columnCount; i2++) {
                if (cursor.getType(i2) == 4) {
                    bundle.putByteArray(cursor.getColumnName(i2), cursor.getBlob(i2));
                } else {
                    String string = cursor.getString(i2);
                    if (string == null) {
                        string = "";
                    }
                    bundle.putString(cursor.getColumnName(i2), string);
                }
            }
            arrayList.add(bundle);
        } while (cursor.moveToNext());
        cursor.close();
        return arrayList;
    }

    /* renamed from: c0 */
    public Observable<ArrayList<Bundle>> m71839c0(final String str, final String str2) {
        return Observable.m59451w1(new ObservableOnSubscribe<ArrayList<Bundle>>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.21
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<ArrayList<Bundle>> observableEmitter) throws Throwable {
                ArrayList<Bundle> arrayListM71825Y = CompressHelper.this.m71825Y(str, str2);
                if (arrayListM71825Y != null) {
                    observableEmitter.onNext(arrayListM71825Y);
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: c2 */
    public byte[] m71840c2(String str) {
        try {
            BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(new File(str)));
            try {
                byte[] bArrMo75462b0 = bufferedSourceM75769e.mo75462b0();
                bufferedSourceM75769e.close();
                return bArrMo75462b0;
            } finally {
            }
        } catch (IOException e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f(getClass().toString(), "Error in Reading file: " + str);
            return null;
        }
    }

    /* renamed from: d */
    public ArrayList<String> m71841d(Cursor cursor, String str) {
        if (cursor == null || !cursor.moveToFirst()) {
            return null;
        }
        ArrayList<String> arrayList = new ArrayList<>(cursor.getCount());
        int columnIndex = cursor.getColumnIndex(str);
        do {
            new Bundle();
            arrayList.add(cursor.getString(columnIndex));
        } while (cursor.moveToNext());
        cursor.close();
        return arrayList;
    }

    /* renamed from: d0 */
    public ArrayList<Bundle> m71842d0(String str, String str2, int i2) {
        try {
            Cursor cursorQuery = this.f87353d.getContentResolver().query(Uri.parse("content://net.imedicaldoctor.imd/query"), null, str, null, str2);
            ArrayList<Bundle> arrayListM71843e = m71843e(cursorQuery, i2);
            cursorQuery.close();
            m71847g(str);
            return arrayListM71843e;
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("QueryDBAsArray " + str, "Error in Query DB , " + str + ", " + e2.getLocalizedMessage());
            e2.printStackTrace();
            return null;
        }
    }

    /* renamed from: e */
    public ArrayList<Bundle> m71843e(Cursor cursor, int i2) {
        if (cursor == null || !cursor.moveToFirst()) {
            return null;
        }
        ArrayList<Bundle> arrayList = new ArrayList<>(cursor.getCount());
        int columnCount = cursor.getColumnCount();
        int count = cursor.getCount();
        for (int i3 = 0; i3 < count; i3++) {
            Bundle bundle = new Bundle();
            if (i3 < i2) {
                for (int i4 = 0; i4 < columnCount; i4++) {
                    if (cursor.getType(i4) == 4) {
                        bundle.putByteArray(cursor.getColumnName(i4), cursor.getBlob(i4));
                    } else {
                        String string = cursor.getString(i4);
                        if (string == null) {
                            string = "";
                        }
                        bundle.putString(cursor.getColumnName(i4), string);
                    }
                }
                arrayList.add(bundle);
                cursor.moveToNext();
            }
        }
        cursor.close();
        return arrayList;
    }

    /* renamed from: e0 */
    public Bundle m71844e0(Bundle bundle, String str) {
        return m71871n1(m71817V(bundle, str));
    }

    /* renamed from: f0 */
    public Bundle m71845f0(Bundle bundle, String str, String str2) {
        return m71871n1(m71819W(bundle, str, str2));
    }

    /* renamed from: f2 */
    public String m71846f2(String str) {
        try {
            return Okio.m75769e(Okio.m75784t(new File(str))).mo75476g1(StandardCharsets.UTF_8);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            e2.printStackTrace();
            return "";
        }
    }

    /* renamed from: g */
    public void m71847g(String str) {
        this.f87353d.getContentResolver().query(Uri.parse("content://net.imedicaldoctor.imd/close"), null, str, null, null);
    }

    /* renamed from: g0 */
    public Observable<Cursor> m71848g0(final Bundle bundle, final String str) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return Observable.m59451w1(new ObservableOnSubscribe<Cursor>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.18
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<Cursor> observableEmitter) throws Throwable {
                Cursor cursorM71808S = CompressHelper.this.m71808S(bundle, str);
                if (cursorM71808S != null) {
                    observableEmitter.onNext(cursorM71808S);
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: g2 */
    public int m71849g2() {
        Bundle bundleM71890s1 = m71890s1(m71825Y(m71852h2(), "Select c from r where id=1"));
        if (bundleM71890s1 != null) {
            return Integer.valueOf(bundleM71890s1.getString("c")).intValue();
        }
        m71881q(m71852h2(), "INSERT OR IGNORE INTO r values (1,0)");
        return 0;
    }

    /* renamed from: h */
    public Cursor m71850h(ArrayList<Bundle> arrayList) {
        if (arrayList == null || arrayList.size() == 0) {
            return null;
        }
        String[] strArr = (String[]) FluentIterable.m42729D(arrayList.get(0).keySet()).m42746P(String.class);
        MatrixCursor matrixCursor = new MatrixCursor(strArr);
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            ArrayList arrayList2 = new ArrayList();
            for (String str : strArr) {
                arrayList2.add(next.getString(str));
            }
            matrixCursor.addRow(arrayList2);
        }
        return matrixCursor;
    }

    /* renamed from: h0 */
    public Observable<Cursor> m71851h0(final Bundle bundle, final String str, final String str2) {
        if (bundle.containsKey("Demo")) {
            str = m71780E0(str);
        }
        return Observable.m59451w1(new ObservableOnSubscribe<Cursor>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.17
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<Cursor> observableEmitter) throws Throwable {
                Cursor cursorM71811T = CompressHelper.this.m71811T(bundle, str, str2);
                if (cursorM71811T != null) {
                    observableEmitter.onNext(cursorM71811T);
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: h2 */
    public String m71852h2() {
        String str = m71797M1() + "/recent.db";
        try {
            if (!new File(str).exists()) {
                SQLiteDatabase sQLiteDatabaseOpenOrCreateDatabase = SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null);
                sQLiteDatabaseOpenOrCreateDatabase.execSQL("create table IF NOT EXISTS recent (id integer primary key autoincrement, dbName text, dbTitle text, dbAddress text, dbDate text, dbDocName text);");
                sQLiteDatabaseOpenOrCreateDatabase.execSQL("create table IF NOT EXISTS dbrecent (id integer primary key autoincrement, dbName text, dbTitle text, dbDate text,dbIcon text);");
                sQLiteDatabaseOpenOrCreateDatabase.execSQL("create table IF NOT EXISTS r (id integer primary key autoincrement, c int)");
            }
            if (m71890s1(m71825Y(str, "SELECT name FROM sqlite_master WHERE type='table' AND name='r'")) == null) {
                SQLiteDatabase.openOrCreateDatabase(str, (SQLiteDatabase.CursorFactory) null).execSQL("create table IF NOT EXISTS r (id integer primary key autoincrement, c int)");
            }
        } catch (Exception unused) {
        }
        return str;
    }

    /* renamed from: i */
    public Cursor m71853i(ArrayList<Bundle> arrayList, String[] strArr) {
        if (arrayList == null || arrayList.size() == 0) {
            return null;
        }
        arrayList.get(0);
        MatrixCursor matrixCursor = new MatrixCursor(strArr);
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            ArrayList arrayList2 = new ArrayList();
            for (String str : strArr) {
                arrayList2.add(next.getString(str));
            }
            matrixCursor.addRow(arrayList2);
        }
        return matrixCursor;
    }

    /* renamed from: i0 */
    public Observable<Cursor> m71854i0(final String str, final String str2) {
        return Observable.m59451w1(new ObservableOnSubscribe<Cursor>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.19
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<Cursor> observableEmitter) throws Throwable {
                Cursor cursorM71814U = CompressHelper.this.m71814U(str, str2);
                if (cursorM71814U != null) {
                    observableEmitter.onNext(cursorM71814U);
                }
                observableEmitter.onComplete();
            }
        });
    }

    /* renamed from: i1 */
    public Bundle m71855i1(final String str) {
        ArrayList arrayList = new ArrayList(Collections2.m42365d(((iMD) this.f87353d.getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.26
            @Override // com.google.common.base.Predicate
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public boolean apply(Bundle bundle) {
                return bundle.getString("Name").equals(str);
            }
        }));
        if (arrayList.size() == 0) {
            return null;
        }
        return (Bundle) arrayList.get(0);
    }

    /* renamed from: i2 */
    public void m71856i2(File[] fileArr) {
        if (fileArr != null) {
            int i2 = 0;
            while (i2 != fileArr.length) {
                String absolutePath = fileArr[i2].getAbsolutePath();
                if (absolutePath.contains(BuildConfig.f87202b) || absolutePath.contains("Documents/iMD") || absolutePath.contains("Documents/.iMD")) {
                    i2++;
                } else {
                    String str = fileArr[i2].getAbsolutePath() + "||" + fileArr[i2].length() + "||" + fileArr[i2].lastModified();
                    if (this.f87352c != null) {
                        str = this.f87352c + StringUtils.f103471LF + str;
                    }
                    this.f87352c = str;
                    if (fileArr[i2].isDirectory()) {
                        m71856i2(fileArr[i2].listFiles());
                    }
                    i2++;
                    iMDLogger.m73548d(i2 + "", absolutePath);
                }
            }
        }
    }

    /* renamed from: j */
    public void m71857j(String str) {
        try {
            str = str.replace("//", "/");
            System.gc();
            File file = new File(str);
            file.exists();
            file.setWritable(true, false);
            if (file.delete()) {
                Log.e("DeleteFile", "Delete Successfull . " + str);
            } else if (!file.getCanonicalFile().delete()) {
                file.deleteOnExit();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            Log.e("DeleteFile", "Failed to Delete " + str);
        }
    }

    /* renamed from: j0 */
    public ArrayList<Bundle> m71858j0(String str) {
        return m71825Y(m71823X0(), str);
    }

    /* renamed from: j1 */
    public Bundle m71859j1(final String str) {
        ArrayList arrayList = new ArrayList(Collections2.m42365d(((iMD) this.f87353d.getApplicationContext()).f101678s, new Predicate<Bundle>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.27
            @Override // com.google.common.base.Predicate
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public boolean apply(Bundle bundle) {
                return bundle.getString("Title").equals(str);
            }
        }));
        if (arrayList.size() == 0) {
            return null;
        }
        return (Bundle) arrayList.get(0);
    }

    /* renamed from: k */
    public void m71860k(File file) {
        if (file.isDirectory()) {
            for (File file2 : file.listFiles()) {
                m71860k(file2);
            }
        }
        file.delete();
    }

    /* renamed from: k0 */
    public void m71861k0() {
        File file;
        Bundle bundleM73442d;
        Log.e("Speed", "RefereshDatabaes Started");
        ArrayList<Bundle> arrayList = new ArrayList<>();
        VBHelper vBHelper = this.f87355f;
        if (vBHelper.m73439a(vBHelper.m73456r()) == null) {
            ((iMD) this.f87353d.getApplicationContext()).f101678s = null;
            return;
        }
        Iterator<String> it2 = m71875o1().iterator();
        while (it2.hasNext()) {
            File file2 = new File(it2.next());
            if (file2.listFiles() != null) {
                for (File file3 : file2.listFiles()) {
                    if (file3.isDirectory()) {
                        File file4 = new File(file3.getAbsolutePath() + "/info.vbe");
                        if (file4.exists()) {
                            Bundle bundleM73442d2 = this.f87355f.m73442d(file4);
                            if (bundleM73442d2 != null) {
                                Bundle bundleM71759q1 = m71759q1(arrayList, "Name", bundleM73442d2.getString("Name"));
                                if (bundleM71759q1 != null) {
                                    if (bundleM71759q1.getString("Version").compareTo(bundleM73442d2.getString("Version")) >= 0) {
                                        iMDLogger.m73550f("RefereshDatabases", "Delete Older Version");
                                        m71860k(file4.getParentFile());
                                    } else {
                                        iMDLogger.m73550f("RefereshDatabases", "There is an older version");
                                        String string = bundleM71759q1.getString("Path");
                                        arrayList.remove(bundleM71759q1);
                                        iMDLogger.m73550f("Deleting", string);
                                        m71860k(new File(string));
                                    }
                                }
                                bundleM73442d2.putString("Path", file3.getAbsolutePath());
                                arrayList.add(bundleM73442d2);
                                file = new File(file3.getAbsolutePath() + "/info2.vbe");
                                if (!file.exists()) {
                                }
                            }
                        } else {
                            file = new File(file3.getAbsolutePath() + "/info2.vbe");
                            if (!file.exists() && (bundleM73442d = this.f87355f.m73442d(file)) != null) {
                                bundleM73442d.putString("Path", file3.getAbsolutePath());
                                arrayList.add(bundleM73442d);
                            }
                        }
                    }
                }
            }
        }
        ((iMD) this.f87353d.getApplicationContext()).f101678s = arrayList;
        Log.e("Speed", "RefereshDatabaes Ended");
    }

    /* renamed from: k2 */
    public void m71862k2() {
        String strM71852h2;
        String str;
        if (m71890s1(m71825Y(m71852h2(), "Select c from r where id=1")) == null) {
            strM71852h2 = m71852h2();
            str = "INSERT OR IGNORE INTO r values (1,0)";
        } else {
            strM71852h2 = m71852h2();
            str = "update r set c=0 where id=1";
        }
        m71881q(strM71852h2, str);
    }

    /* renamed from: l */
    public Observable<String> m71863l(Fragment fragment) {
        iMDLogger.m73550f("DownloadDBs", "Starting");
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.1
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                if (CompressHelper.f87349x) {
                    iMDLogger.m73550f("DownloadDBs", "is Downloading , Return");
                    return;
                }
                while (CompressHelper.f87349x) {
                }
                CompressHelper.f87349x = true;
                String str = CompressHelper.this.m71816U1() + "/DBs.db";
                String str2 = CompressHelper.this.m71816U1() + "/DBs.z";
                if (new File(str).exists()) {
                    iMDLogger.m73550f("DownloadDBs", "DB Exist, no need to download");
                    CompressHelper.f87349x = false;
                    observableEmitter.onComplete();
                    return;
                }
                if (new File(str2).exists()) {
                    new File(str2).delete();
                }
                try {
                    iMDLogger.m73550f("DownloadDBs", "Downloading Zip File");
                    CompressHelper.this.m71809S0(CompressHelper.this.m71790J() + "/dbs.z", str2);
                    iMDLogger.m73550f("DownloadDBs", "Downloading Zip File Completed");
                    Decompress decompress = new Decompress(str2, CompressHelper.this.m71816U1() + "/", CompressHelper.this.f87353d);
                    iMDLogger.m73550f("CompressHelper", "Extract of dbs.z started");
                    boolean zM71945j = decompress.m71945j();
                    iMDLogger.m73550f("CompressHelper", "Extract of dbs.z ended");
                    CompressHelper.f87349x = false;
                    if (zM71945j) {
                        iMDLogger.m73550f("CompressHelper", "extract of dbs.z successful");
                        observableEmitter.onComplete();
                    } else {
                        iMDLogger.m73550f("CompressHelper", "extract of dbs.z failed");
                        observableEmitter.onError(null);
                    }
                } catch (Exception e2) {
                    CompressHelper.f87349x = false;
                    iMDLogger.m73550f("DownloadDBs", "Error in downloading file " + e2);
                    e2.printStackTrace();
                    observableEmitter.onError(e2);
                }
            }
        });
    }

    /* renamed from: l0 */
    public void m71864l0() {
        FirebaseMessaging.m49604y().m49606B().mo35608e(new OnCompleteListener<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.4
            @Override // com.google.android.gms.tasks.OnCompleteListener
            /* renamed from: a */
            public void mo33853a(@NonNull Task<String> task) {
                if (!task.mo35625v()) {
                    Log.w("RegisterToken", "Fetching FCM registration token failed", task.mo35620q());
                    return;
                }
                String strMo35621r = task.mo35621r();
                VBHelper vBHelper = new VBHelper(CompressHelper.this.f87353d);
                CompressHelper.this.m71874o0("PushRegistration|||||" + vBHelper.m73451m() + "|||||" + strMo35621r).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.4.1
                    @Override // io.reactivex.rxjava3.functions.Consumer
                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                    public void accept(String str) throws Throwable {
                        Log.e("RegisterToken", "Registration Successful");
                    }
                }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.4.2
                    @Override // io.reactivex.rxjava3.functions.Consumer
                    /* renamed from: a, reason: merged with bridge method [inline-methods] */
                    public void accept(Throwable th) throws Throwable {
                    }
                }, new Action() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.4.3
                    @Override // io.reactivex.rxjava3.functions.Action
                    public void run() throws Throwable {
                    }
                });
            }
        });
    }

    /* renamed from: l2 */
    public String m71865l2() {
        try {
            BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(new File(m71797M1() + "/exp.txt")));
            try {
                String strMo75460a2 = bufferedSourceM75769e.mo75460a2();
                bufferedSourceM75769e.close();
                return new VBHelper(this.f87353d).m73448j(strMo75460a2, "127");
            } finally {
            }
        } catch (Exception unused) {
            return "";
        }
    }

    /* renamed from: m */
    public void m71866m(Bundle bundle, String str) {
        try {
            this.f87353d.getContentResolver().update(Uri.parse("content://net.imedicaldoctor.imd/query"), null, m71752f1(bundle), new String[]{str});
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("ExecuteDB" + bundle.getString("Name"), "Error in Executing DB , " + m71752f1(bundle) + ", " + e2.getLocalizedMessage());
        }
    }

    /* renamed from: m0 */
    public void m71867m0(String str, String str2, String str3, String str4, Bundle bundle) {
        String str5;
        ArrayList arrayList = new ArrayList();
        String[] strArrSplitByWholeSeparatorPreserveAllTokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(str, ";;;");
        String[] strArrSplitByWholeSeparatorPreserveAllTokens2 = StringUtils.splitByWholeSeparatorPreserveAllTokens(str4, ",");
        int length = strArrSplitByWholeSeparatorPreserveAllTokens.length;
        int i2 = 0;
        while (i2 < length) {
            String[] strArrSplitByWholeSeparatorPreserveAllTokens3 = StringUtils.splitByWholeSeparatorPreserveAllTokens(strArrSplitByWholeSeparatorPreserveAllTokens[i2], ":::");
            ArrayList arrayList2 = new ArrayList();
            int length2 = strArrSplitByWholeSeparatorPreserveAllTokens3.length;
            int i3 = 0;
            int i4 = 0;
            while (i3 < length2) {
                String str6 = strArrSplitByWholeSeparatorPreserveAllTokens3[i3];
                String str7 = strArrSplitByWholeSeparatorPreserveAllTokens2[i4];
                String[] strArr = strArrSplitByWholeSeparatorPreserveAllTokens;
                if (bundle == null || !bundle.containsKey(str7)) {
                    str5 = "'" + str6.replace("'", "''") + "'";
                } else {
                    str5 = "'" + bundle.getString(str7).replace("'", "''") + "'";
                }
                arrayList2.add(str5);
                i4++;
                i3++;
                strArrSplitByWholeSeparatorPreserveAllTokens = strArr;
            }
            String[] strArr2 = strArrSplitByWholeSeparatorPreserveAllTokens;
            arrayList.add("Insert into " + str3 + " (" + str4 + ") values (" + StringUtils.join(arrayList2, ",") + ")");
            i2++;
            strArrSplitByWholeSeparatorPreserveAllTokens = strArr2;
        }
        m71884r(str2, (String[]) arrayList.toArray(new String[0]), 0);
    }

    /* renamed from: m2 */
    public Bundle m71868m2(Cursor cursor) {
        int columnCount = cursor.getColumnCount();
        Bundle bundle = new Bundle();
        for (int i2 = 0; i2 < columnCount; i2++) {
            String string = cursor.getString(i2);
            if (string == null) {
                string = "";
            }
            bundle.putString(cursor.getColumnName(i2), string);
        }
        return bundle;
    }

    /* renamed from: n */
    public void m71869n(Bundle bundle, String str, String str2) {
        try {
            this.f87353d.getContentResolver().update(Uri.parse("content://net.imedicaldoctor.imd/query"), null, m71753g1(bundle, str2), new String[]{str});
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("ExecuteDB" + bundle.getString("Name"), "Error in Executing DB , " + m71753g1(bundle, str2) + ", " + e2.getLocalizedMessage());
        }
    }

    /* renamed from: n0 */
    public String m71870n0(String str) {
        Response responseExecute;
        String strM74815r;
        String str2 = null;
        try {
            responseExecute = this.f87356g.mo74340a(new Request.Builder().m74760q(m71790J() + "/imd.php").m74755l(new FormBody.Builder().m74474a(HTML.Tag.f74375Y, this.f87355f.m73452n(str, "127")).m74476c()).m74745b()).execute();
        } catch (IOException e2) {
            iMDLogger.m73550f("SendCommand", "Error in " + e2);
        }
        try {
            if (responseExecute.m74781r()) {
                strM74815r = responseExecute.m74770b().m74815r();
            } else {
                BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75785u(responseExecute.m74770b().m74812b()));
                try {
                    iMDLogger.m73550f("SendCommand", bufferedSourceM75769e.mo75460a2());
                    bufferedSourceM75769e.close();
                    strM74815r = null;
                } finally {
                }
            }
            responseExecute.close();
            str2 = strM74815r;
            if (str2 == null) {
                m71820W0();
            } else {
                m71872n2(WorkQueueKt.f87084c);
                iMDLogger.m73554j("SendCommand result", str2);
            }
            return str2;
        } finally {
        }
    }

    /* renamed from: n1 */
    public Bundle m71871n1(ArrayList<Bundle> arrayList) {
        if (arrayList == null || arrayList.size() == 0) {
            return null;
        }
        return arrayList.get(0);
    }

    /* renamed from: n2 */
    public void m71872n2(int i2) {
        if (i2 == 127) {
            m71862k2();
        }
    }

    /* renamed from: o */
    public void m71873o(Bundle bundle, String str, String str2, int i2) {
        try {
            this.f87353d.getContentResolver().update(Uri.parse("content://net.imedicaldoctor.imd/query"), null, m71753g1(bundle, str2), new String[]{"SQLFile", str, String.valueOf(i2)});
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("ExecuteDB" + bundle.getString("Name"), "Error in Executing DB , " + m71753g1(bundle, str2) + ", " + e2.getLocalizedMessage());
        }
    }

    /* renamed from: o0 */
    public Observable<String> m71874o0(final String str) {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.7
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                String strM71870n0 = CompressHelper.this.m71870n0(str);
                if (strM71870n0 == null) {
                    observableEmitter.onError(new Throwable("Error Occured"));
                    return;
                }
                String strM73448j = CompressHelper.this.f87355f.m73448j(strM71870n0, "127");
                if (strM73448j == null) {
                    CompressHelper.this.m71820W0();
                    observableEmitter.onComplete();
                } else {
                    observableEmitter.onNext(strM73448j);
                }
                observableEmitter.onComplete();
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e());
    }

    /* renamed from: o1 */
    public HashSet<String> m71875o1() throws InterruptedException, IOException {
        if (((iMD) this.f87353d.getApplicationContext()).f101669Y != null) {
            return ((iMD) this.f87353d.getApplicationContext()).f101669Y;
        }
        HashSet<String> hashSetM71757l1 = m71757l1();
        HashSet<String> hashSet = new HashSet<>();
        hashSet.add(m71797M1());
        File[] externalFilesDirs = this.f87353d.getExternalFilesDirs(null);
        hashSetM71757l1.add("/storage/sdcard1");
        hashSetM71757l1.add(Environment.getExternalStorageDirectory().getAbsolutePath());
        Iterator<String> it2 = hashSetM71757l1.iterator();
        while (it2.hasNext()) {
            String str = it2.next() + "/Android/data/net.imedicaldoctor.imd/Documents";
            new File(str).mkdirs();
            if (new File(str).exists() && !hashSet.contains(str)) {
                hashSet.add(str);
            }
        }
        Iterator<String> it3 = hashSetM71757l1.iterator();
        while (it3.hasNext()) {
            String str2 = it3.next() + "/Android/data/net.imedicaldoctor.imd/.Documents";
            new File(str2).mkdirs();
            if (new File(str2).exists() && !hashSet.contains(str2)) {
                hashSet.add(str2);
            }
        }
        int i2 = 0;
        if (externalFilesDirs != null) {
            try {
                for (File file : externalFilesDirs) {
                    if (file != null) {
                        String str3 = file.getParentFile().getAbsolutePath() + "/Documents";
                        new File(str3).mkdirs();
                        if (new File(str3).exists() && !hashSet.contains(str3)) {
                            hashSet.add(str3);
                        }
                    }
                }
                for (File file2 : externalFilesDirs) {
                    if (file2 != null) {
                        String str4 = file2.getParentFile().getAbsolutePath() + "/.Documents";
                        new File(str4).mkdirs();
                        if (new File(str4).exists() && !hashSet.contains(str4)) {
                            hashSet.add(str4);
                        }
                    }
                }
            } catch (Exception unused) {
            }
        }
        HashSet hashSet2 = new HashSet();
        ArrayList arrayList = new ArrayList();
        Iterator<String> it4 = hashSet.iterator();
        while (it4.hasNext()) {
            arrayList.add(it4.next());
        }
        while (i2 < arrayList.size() - 1) {
            int i3 = i2 + 1;
            for (int i4 = i3; i4 < arrayList.size(); i4++) {
                try {
                    if (new File((String) arrayList.get(i2)).getCanonicalPath().equals(new File((String) arrayList.get(i4)).getCanonicalPath())) {
                        hashSet2.add((String) arrayList.get(i4));
                    }
                } catch (Exception unused2) {
                }
            }
            i2 = i3;
        }
        Iterator it5 = hashSet2.iterator();
        while (it5.hasNext()) {
            hashSet.remove((String) it5.next());
        }
        ((iMD) this.f87353d.getApplicationContext()).f101669Y = hashSet;
        return hashSet;
    }

    /* renamed from: o2 */
    public void m71876o2(ArrayList<Bundle> arrayList) throws JSONException {
        String str = m71797M1() + "/databases.json";
        ArrayList arrayList2 = new ArrayList();
        JSONArray jSONArray = new JSONArray();
        for (int i2 = 0; i2 < arrayList.size(); i2++) {
            Bundle bundle = arrayList.get(i2);
            ArrayList parcelableArrayList = bundle.getParcelableArrayList("items");
            ArrayList<? extends Parcelable> arrayList3 = new ArrayList<>();
            JSONArray jSONArray2 = new JSONArray();
            for (int i3 = 0; i3 < parcelableArrayList.size(); i3++) {
                Bundle bundle2 = (Bundle) parcelableArrayList.get(i3);
                String string = bundle2.getString("dontSearch", "0");
                Bundle bundle3 = new Bundle();
                bundle3.putString("Name", bundle2.getString("Name"));
                bundle3.putString("dontSearch", string);
                arrayList3.add(bundle3);
                try {
                    JSONObject jSONObject = new JSONObject();
                    jSONObject.put("Name", bundle2.getString("Name"));
                    jSONObject.put("dontSearch", string);
                    jSONArray2.put(jSONObject);
                } catch (Exception unused) {
                }
            }
            Bundle bundle4 = new Bundle();
            bundle4.putString("title", bundle.getString("title"));
            bundle4.putParcelableArrayList("items", arrayList3);
            arrayList2.add(bundle4);
            try {
                JSONObject jSONObject2 = new JSONObject();
                jSONObject2.put("title", bundle.getString("title"));
                jSONObject2.put("items", jSONArray2);
                jSONArray.put(jSONObject2);
            } catch (Exception unused2) {
            }
        }
        String string2 = jSONArray.toString();
        File file = new File(str);
        if (file.exists()) {
            file.delete();
        }
        try {
            BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
            try {
                bufferedSinkM75768d.mo75454W0(string2);
                bufferedSinkM75768d.close();
            } finally {
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in writing json", e2.getLocalizedMessage());
        }
    }

    /* renamed from: p */
    public void m71877p(Bundle bundle, String[] strArr, String str, int i2) {
        try {
            ArrayList arrayList = new ArrayList();
            arrayList.addAll(Arrays.asList(strArr).subList(i2, strArr.length));
            String[] strArr2 = new String[arrayList.size()];
            arrayList.toArray(strArr2);
            this.f87353d.getContentResolver().update(Uri.parse("content://net.imedicaldoctor.imd/query"), null, m71753g1(bundle, str), strArr2);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("ExecuteDB" + bundle.getString("Name"), "Error in Executing DB , " + m71753g1(bundle, str) + ", " + e2.getLocalizedMessage());
        }
    }

    /* renamed from: p0 */
    public Observable<String> m71878p0(final String str) {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.8
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                try {
                    Thread.sleep(4000L);
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                }
                String strM73448j = CompressHelper.this.f87355f.m73448j(CompressHelper.this.m71882q0(str), "127");
                if (strM73448j == null) {
                    CompressHelper.this.m71820W0();
                    observableEmitter.onComplete();
                } else {
                    observableEmitter.onNext(strM73448j);
                }
                observableEmitter.onComplete();
            }
        }).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e());
    }

    /* renamed from: p1 */
    public String m71879p1(String str, String str2) throws Exception {
        String strM71776C0 = m71776C0(str2, str2);
        if (strM71776C0 != null) {
            return strM71776C0;
        }
        HttpURLConnection httpURLConnection = (HttpURLConnection) new URL(str).openConnection();
        httpURLConnection.setConnectTimeout(10000);
        httpURLConnection.setReadTimeout(180000);
        int responseCode = httpURLConnection.getResponseCode();
        if (responseCode != 200) {
            throw new Exception("Error in contacting server, response code: " + responseCode);
        }
        httpURLConnection.getHeaderField(HttpHeaders.f62909a0);
        httpURLConnection.getContentType();
        httpURLConnection.getContentLength();
        try {
            InputStream inputStream = httpURLConnection.getInputStream();
            try {
                BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75785u(inputStream));
                try {
                    String strMo75460a2 = bufferedSourceM75769e.mo75460a2();
                    m71908z0(str2, strMo75460a2, str2);
                    bufferedSourceM75769e.close();
                    if (inputStream != null) {
                        inputStream.close();
                    }
                    httpURLConnection.disconnect();
                    return strMo75460a2;
                } finally {
                }
            } finally {
            }
        } catch (IOException e2) {
            throw new Exception("Error reading from input stream", e2);
        }
    }

    /* renamed from: p2 */
    public ArrayList<Bundle> m71880p2() throws JSONException {
        Log.e("Speed", "Sections Started");
        ArrayList<Bundle> arrayListM71795L1 = m71795L1();
        m71876o2(arrayListM71795L1);
        ArrayList<Bundle> arrayList = new ArrayList<>();
        Iterator<Bundle> it2 = arrayListM71795L1.iterator();
        while (it2.hasNext()) {
            Iterator it3 = it2.next().getParcelableArrayList("items").iterator();
            while (it3.hasNext()) {
                arrayList.add((Bundle) it3.next());
            }
        }
        f87345t = arrayList;
        Log.e("Speed", "Sections ended");
        return arrayListM71795L1;
    }

    /* renamed from: q */
    public void m71881q(String str, String str2) {
        try {
            this.f87353d.getContentResolver().update(Uri.parse("content://net.imedicaldoctor.imd/query"), null, str, new String[]{str2});
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("ExecuteDB", "Error in Executing DB , " + str + ", " + e2.getLocalizedMessage());
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:36:0x00f1  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x00f5  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x00fc  */
    /* JADX WARN: Removed duplicated region for block: B:42:0x0104  */
    /* JADX WARN: Removed duplicated region for block: B:56:0x0109 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* renamed from: q0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.String m71882q0(java.lang.String r9) throws java.lang.Throwable {
        /*
            Method dump skipped, instructions count: 289
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Data.CompressHelper.m71882q0(java.lang.String):java.lang.String");
    }

    /* renamed from: q2 */
    public ArrayList<Bundle> m71883q2(ArrayList<Bundle> arrayList) {
        return m71887r2(arrayList, "Section");
    }

    /* renamed from: r */
    public void m71884r(String str, String[] strArr, int i2) {
        try {
            ArrayList arrayList = new ArrayList();
            arrayList.addAll(Arrays.asList(strArr).subList(i2, strArr.length));
            String[] strArr2 = new String[arrayList.size()];
            arrayList.toArray(strArr2);
            this.f87353d.getContentResolver().update(Uri.parse("content://net.imedicaldoctor.imd/query"), null, str, strArr2);
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("ExecuteDB" + str, "Error in Executing DB , " + str + ", " + e2.getLocalizedMessage());
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:36:0x00f1  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x00f5  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x00fc  */
    /* JADX WARN: Removed duplicated region for block: B:42:0x0104  */
    /* JADX WARN: Removed duplicated region for block: B:56:0x0109 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* renamed from: r0 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.String m71885r0(byte[] r9) throws java.lang.Throwable {
        /*
            Method dump skipped, instructions count: 289
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Data.CompressHelper.m71885r0(byte[]):java.lang.String");
    }

    /* renamed from: r1 */
    public JSONObject m71886r1(JSONArray jSONArray, String str, String str2) throws JSONException {
        for (int i2 = 0; i2 < jSONArray.length(); i2++) {
            try {
                JSONObject jSONObject = jSONArray.getJSONObject(i2);
                if (jSONObject.getString(str).equals(str2)) {
                    return jSONObject;
                }
            } catch (Exception e2) {
                FirebaseCrashlytics.m48010d().m48016g(e2);
                iMDLogger.m73550f("Error in getJSONObjectFromArray", e2.getLocalizedMessage());
                e2.printStackTrace();
                return null;
            }
        }
        return null;
    }

    /* renamed from: r2 */
    public ArrayList<Bundle> m71887r2(ArrayList<Bundle> arrayList, String str) {
        ArrayList<Bundle> arrayList2 = new ArrayList<>();
        String str2 = null;
        if (arrayList == null) {
            return null;
        }
        ArrayList<? extends Parcelable> arrayList3 = new ArrayList<>();
        Iterator<Bundle> it2 = arrayList.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String string = next.getString(str);
            if (str2 == null) {
                arrayList3.add(next);
            } else if (str2.equals(string)) {
                arrayList3.add(next);
            } else {
                Bundle bundle = new Bundle();
                bundle.putString("title", str2);
                bundle.putParcelableArrayList("items", arrayList3);
                arrayList2.add(bundle);
                ArrayList<? extends Parcelable> arrayList4 = new ArrayList<>();
                arrayList4.add(next);
                arrayList3 = arrayList4;
            }
            str2 = string;
        }
        if (str2 != null) {
            Bundle bundle2 = new Bundle();
            bundle2.putString("title", str2);
            bundle2.putParcelableArrayList("items", arrayList3);
            arrayList2.add(bundle2);
        }
        return arrayList2;
    }

    /* renamed from: s */
    public void m71888s(String str) {
        try {
            this.f87353d.getContentResolver().update(Uri.parse("content://net.imedicaldoctor.imd/query"), null, m71823X0(), new String[]{str});
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("ExecuteDB", "Error in Executing DB , " + m71823X0() + ", " + e2.getLocalizedMessage());
        }
    }

    /* renamed from: s0 */
    public String m71889s0(String str, String str2, String str3, Bundle bundle) {
        ArrayList<Bundle> arrayListM71825Y = m71825Y(str, str2);
        if (arrayListM71825Y == null || arrayListM71825Y.isEmpty()) {
            return "";
        }
        String[] strArrSplitByWholeSeparatorPreserveAllTokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(str3, ",");
        StringBuilder sb = new StringBuilder();
        Iterator<Bundle> it2 = arrayListM71825Y.iterator();
        while (it2.hasNext()) {
            Bundle next = it2.next();
            StringBuilder sb2 = new StringBuilder();
            for (int i2 = 0; i2 < strArrSplitByWholeSeparatorPreserveAllTokens.length; i2++) {
                String str4 = strArrSplitByWholeSeparatorPreserveAllTokens[i2];
                String string = (bundle == null || !bundle.containsKey(str4)) ? next.getString(str4) : bundle.getString(str4);
                if (i2 > 0) {
                    sb2.append(":::");
                }
                sb2.append(string);
            }
            if (sb.length() > 0) {
                sb.append(";;;");
            }
            sb.append((CharSequence) sb2);
        }
        return sb.toString();
    }

    /* renamed from: s1 */
    public Bundle m71890s1(ArrayList<Bundle> arrayList) {
        return m71907z(arrayList);
    }

    /* renamed from: s2 */
    public ArrayList<Bundle> m71891s2(ArrayList<Bundle> arrayList, String str) {
        ArrayList<Bundle> arrayList2 = new ArrayList<>();
        if (arrayList == null) {
            return null;
        }
        ArrayList<? extends Parcelable> arrayList3 = new ArrayList<>();
        Iterator<Bundle> it2 = arrayList.iterator();
        String str2 = "";
        while (it2.hasNext()) {
            Bundle next = it2.next();
            String lowerCase = next.getString(str).substring(0, 1).toLowerCase();
            String upperCase = !"abcdefghijklmnopqrstuvwxyz".contains(lowerCase) ? "#" : lowerCase.toUpperCase();
            if (str2.length() != 0) {
                if (str2.equals(upperCase)) {
                    arrayList3.add(next);
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString("title", str2);
                    bundle.putParcelableArrayList("items", arrayList3);
                    arrayList2.add(bundle);
                    arrayList3 = new ArrayList<>();
                }
            }
            arrayList3.add(next);
            str2 = upperCase;
        }
        if (str2.length() > 0) {
            Bundle bundle2 = new Bundle();
            bundle2.putString("title", str2);
            bundle2.putParcelableArrayList("items", arrayList3);
            arrayList2.add(bundle2);
        }
        return arrayList2;
    }

    /* renamed from: t */
    public int m71892t(String str) {
        String[] strArrSplitByWholeSeparatorPreserveAllTokens = StringUtils.splitByWholeSeparatorPreserveAllTokens(str, ";;;");
        if (strArrSplitByWholeSeparatorPreserveAllTokens.length > 0) {
            return StringUtils.splitByWholeSeparatorPreserveAllTokens(strArrSplitByWholeSeparatorPreserveAllTokens[0], ":::").length;
        }
        return 0;
    }

    /* renamed from: t1 */
    public String m71893t1(String[] strArr) {
        return strArr[strArr.length - 1];
    }

    /* renamed from: t2 */
    public void m71894t2(String str) {
        (str == null ? this.f87353d.getSharedPreferences("default_preferences", 0).edit().remove("ActivationCode") : this.f87353d.getSharedPreferences("default_preferences", 0).edit().putString("ActivationCode", str)).commit();
    }

    /* renamed from: u */
    public String m71895u(String str) {
        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "/");
        return strArrSplitByWholeSeparator[0] + "/" + strArrSplitByWholeSeparator[1];
    }

    /* renamed from: u2 */
    public void m71896u2(String str, String str2) {
        Intent intent = new Intent("android.intent.action.SEND");
        File file = new File(str);
        if (file.exists()) {
            intent.setType(str2);
            Uri uriM6734h = FileProvider.m6734h(this.f87353d, this.f87353d.getApplicationContext().getPackageName() + ".provider", file);
            intent.putExtra("android.intent.extra.STREAM", uriM6734h);
            intent.addFlags(1);
            Intent intentCreateChooser = Intent.createChooser(intent, "Share File");
            Iterator<ResolveInfo> it2 = this.f87353d.getPackageManager().queryIntentActivities(intentCreateChooser, 65536).iterator();
            while (it2.hasNext()) {
                this.f87353d.grantUriPermission(it2.next().activityInfo.packageName, uriM6734h, 3);
            }
            this.f87353d.startActivity(intentCreateChooser);
        }
    }

    /* renamed from: v */
    public byte[] m71897v(String str, String str2, String str3) {
        if (!str3.equals("127")) {
            return null;
        }
        try {
            VBHelper vBHelper = this.f87355f;
            String str4 = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::")[1];
            byte[] bArrDecode = Base64.decode(str, 0);
            for (int length = str2.length(); length < 8; length++) {
                str2 = str2 + StringUtils.SPACE;
            }
            try {
                try {
                    return m71749e1(m71799N0(str4.toCharArray(), str2.getBytes(StandardCharsets.UTF_8), new byte[]{17, 115, 105, 102, 103, 104, 111, 107, 108, 122, 120, 119, 118, 98, 110, 109}, bArrDecode));
                } catch (Exception e2) {
                    iMDLogger.m73550f("CompressHelper _ GetData Decompressing", e2.toString());
                    return null;
                }
            } catch (Exception e3) {
                iMDLogger.m73550f("CompressHelper _ GetData Decryption", e3.toString());
                return null;
            }
        } catch (Exception e4) {
            FirebaseCrashlytics.m48010d().m48016g(e4);
            FirebaseCrashlytics.m48010d().m48016g(e4);
            return null;
        }
    }

    /* renamed from: v2 */
    public void m71898v2(String str) {
        new AlertDialog.Builder(this.f87353d, C5562R.style.alertDialogTheme).mo1102l(str).mo1106p("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.23
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
            }
        }).m1090I();
    }

    /* renamed from: w */
    public byte[] m71899w(byte[] bArr, String str, String str2) {
        if (!str2.equals("127")) {
            return null;
        }
        VBHelper vBHelper = this.f87355f;
        String str3 = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::")[1];
        for (int length = str.length(); length < 8; length++) {
            str = str + StringUtils.SPACE;
        }
        try {
            return m71799N0(str3.toCharArray(), str.getBytes(StandardCharsets.UTF_8), new byte[]{17, 115, 105, 102, 103, 104, 111, 107, 108, 122, 120, 119, 118, 98, 110, 109}, bArr);
        } catch (Exception e2) {
            iMDLogger.m73550f("CompressHelper _ GetData Decryption", e2.toString());
            return null;
        }
    }

    /* renamed from: w1 */
    public ArrayList<String> m71900w1() throws InterruptedException, IOException {
        HashSet<String> hashSetM71875o1 = m71875o1();
        Bundle bundle = new Bundle();
        ArrayList<String> arrayList = new ArrayList<>();
        Iterator<String> it2 = hashSetM71875o1.iterator();
        while (it2.hasNext()) {
            String next = it2.next();
            long usableSpace = new File(next).getUsableSpace();
            if (!bundle.containsKey(String.valueOf(usableSpace))) {
                String strReplace = next.replace("Android/data/net.imedicaldoctor.imd/Documents", "").replace("Android/data/net.imedicaldoctor.imd/.Documents", "").replace("Documents/iMD", "").replace("Documents/.iMD", "");
                bundle.putString(String.valueOf(usableSpace), strReplace);
                arrayList.add(strReplace);
            }
        }
        return arrayList;
    }

    /* renamed from: w2 */
    public void m71901w2(String str, final Runnable runnable) {
        new AlertDialog.Builder(this.f87353d, C5562R.style.alertDialogTheme).mo1102l(str).mo1106p("OK", new DialogInterface.OnClickListener() { // from class: net.imedicaldoctor.imd.Data.CompressHelper.24
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialogInterface, int i2) {
                runnable.run();
            }
        }).m1090I();
    }

    /* renamed from: x */
    public byte[] m71902x(byte[] bArr, String str, String str2) {
        if (!str2.equals("127")) {
            return null;
        }
        VBHelper vBHelper = this.f87355f;
        String str3 = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::")[1];
        for (int length = str.length(); length < 8; length++) {
            str = str + StringUtils.SPACE;
        }
        try {
            return m71799N0(str3.toCharArray(), str.getBytes(StandardCharsets.UTF_8), new byte[]{17, 115, 105, 102, 103, 104, 111, 107, 108, 122, 120, 119, 118, 98, 110, 109}, bArr);
        } catch (Exception e2) {
            iMDLogger.m73550f("CompressHelper _ GetData Decryption", e2.toString());
            return null;
        }
    }

    /* renamed from: x1 */
    public boolean m71903x1() {
        if (this.f87353d.getSharedPreferences("default_preferences", 0).getBoolean("mobile", false)) {
            return false;
        }
        return this.f87353d.getResources().getBoolean(C5562R.bool.isTablet);
    }

    /* renamed from: y */
    public String m71904y() {
        String strM71797M1;
        if (((iMD) this.f87353d.getApplicationContext()).f101671Z != null) {
            return ((iMD) this.f87353d.getApplicationContext()).f101671Z;
        }
        try {
            strM71797M1 = this.f87353d.getSharedPreferences("default_preferences", 0).getString("DownloadPath", "");
            if (strM71797M1.length() == 0 || !new File(strM71797M1).exists()) {
                strM71797M1 = m71797M1();
            }
            if (!m71875o1().contains(strM71797M1)) {
                strM71797M1 = m71797M1();
                this.f87353d.getSharedPreferences("default_preferences", 0).edit().remove("DownloadPath").commit();
                this.f87353d.getSharedPreferences("default_preferences", 0).edit().putString("DownloadPath", strM71797M1).commit();
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            FirebaseCrashlytics.m48010d().m48016g(e2);
            strM71797M1 = m71797M1();
        }
        ((iMD) this.f87353d.getApplicationContext()).f101671Z = strM71797M1;
        return strM71797M1;
    }

    /* renamed from: y1 */
    public String m71905y1() {
        VBHelper vBHelper = this.f87355f;
        String strM73462x = vBHelper.m73462x(vBHelper.m73451m());
        return strM73462x == null ? "Nousername" : TextUtils.split(strM73462x.replace("||", "::"), "::")[9];
    }

    /* renamed from: y2 */
    public Boolean m71906y2() throws NoSuchAlgorithmException {
        if (this.f87355f.m73456r().equals("")) {
            return Boolean.TRUE;
        }
        VBHelper vBHelper = this.f87355f;
        String[] strArrSplit = TextUtils.split(vBHelper.m73462x(vBHelper.m73451m()).replace("||", "::"), "::");
        if (strArrSplit.length < 12) {
            m71777C2("Parts less than 12 . parts length = " + strArrSplit.length);
            return Boolean.FALSE;
        }
        ArrayList arrayList = new ArrayList(Arrays.asList(TextUtils.split(strArrSplit[3], ",")));
        if (!m71731F1("NSDate" + m71731F1("NSDate" + strArrSplit[2] + strArrSplit[3] + strArrSplit[10] + strArrSplit[9] + "NSString")).equals(strArrSplit[8])) {
            m71777C2("Hash Validation Failed");
            return Boolean.FALSE;
        }
        Log.d("Validation", "Check hash passed");
        Date date = new Date(Long.parseLong(strArrSplit[10]) * 1000);
        long time = new Date().getTime() - date.getTime();
        TimeUnit timeUnit = TimeUnit.HOURS;
        TimeUnit timeUnit2 = TimeUnit.MILLISECONDS;
        long jConvert = timeUnit.convert(time, timeUnit2);
        if (jConvert > 240) {
            m71777C2("Server Date and System Date Mismatch . Server Date : " + date.getTime() + ", System Date : " + new Date().getTime());
            return Boolean.FALSE;
        }
        if (jConvert < -240) {
            m71777C2("Server Date and System Date Mismatch . Server Date : " + date.getTime() + ", System Date : " + new Date().getTime());
            return Boolean.FALSE;
        }
        Log.d("Validation", "Check server date passed");
        String str = strArrSplit[2];
        if (str.length() > 2) {
            long jConvert2 = TimeUnit.DAYS.convert(new Date(Long.parseLong(str) * 1000).getTime() - new Date().getTime(), timeUnit2);
            if (jConvert2 < 0) {
                if (!arrayList.contains("expired")) {
                    m71777C2("VIP Account Expired. Days Remaining : " + jConvert2);
                    return Boolean.FALSE;
                }
                if (arrayList.contains(TtmlNode.f29738r0)) {
                    m71777C2("VIP Account Expired. Days Remaining : " + jConvert2);
                    return Boolean.FALSE;
                }
            }
        }
        Log.d("Validation", "Check DOE passed");
        if (strArrSplit[11].equals("0")) {
            m71864l0();
        }
        int iM71849g2 = m71849g2();
        Log.d("Validation", "C = " + iM71849g2);
        if (iM71849g2 > 5000) {
            m71777C2("C Exceeded 5000. Counter : " + iM71849g2);
            m71872n2(WorkQueueKt.f87084c);
            return Boolean.FALSE;
        }
        long jConvert3 = timeUnit.convert(new Date().getTime() - m71837b2(), timeUnit2);
        if (jConvert3 >= -48) {
            Log.d("Validation", "D Succeed");
            return Boolean.TRUE;
        }
        m71777C2("D Failed. Hours : " + jConvert3);
        Log.d("Validation", "D failed . hours = " + jConvert3);
        return Boolean.FALSE;
    }

    /* renamed from: z */
    public Bundle m71907z(ArrayList<Bundle> arrayList) {
        if (arrayList == null || arrayList.size() == 0) {
            return null;
        }
        return arrayList.get(arrayList.size() - 1);
    }

    /* renamed from: z0 */
    public void m71908z0(String str, String str2, String str3) throws SQLException {
        String strM71833a1 = m71833a1(str);
        String strM71833a12 = m71833a1(str2);
        String strM71833a13 = m71833a1(str3);
        m71881q(m71778D0(), "INSERT OR REPLACE into cache (cachekey, cachecontent, cachevalidation) values ('" + strM71833a1 + "', '" + strM71833a12 + "', '" + strM71833a13 + "')");
    }

    /* renamed from: z1 */
    public void m71909z1(Bundle bundle) {
        Class<?> cls;
        Class<?> cls2;
        String string = bundle.getString("Type");
        String string2 = bundle.getString("Name");
        Bundle bundle2 = new Bundle();
        bundle2.putBundle("DB", bundle);
        if (!this.f87355f.m73463y(bundle.getString("Name"), bundle.getString("Version"), new StringBuilder()) && !bundle.containsKey("Demo")) {
            Toast.makeText(this.f87353d, "You don't own this database, please Buy before use", 1).show();
            return;
        }
        m71774B0(bundle.getString("Name"), bundle.getString("Title"), m71724C(bundle));
        if (string.equals("elsevier") || string.equals("elseviernew")) {
            cls = ELSChaptersActivity.class;
            cls2 = ELSChaptersActivity.ELSChaptersFragment.class;
        } else if (string.equals("uptodate")) {
            cls = UTDSearchActivity.class;
            cls2 = UTDSearchActivity.UTDSearchFragment.class;
        } else if (string.equals("nejm")) {
            cls = NEJMTOCActivity.class;
            cls2 = NEJMTOCActivity.NEJMTOCFragment.class;
        } else if (string.equals("utdadvanced")) {
            cls = UTDASearchActivity.class;
            cls2 = UTDASearchActivityFragment.class;
        } else if (string.equals("accessmedicine")) {
            bundle2.putString("ParentId", "0");
            cls = AMChaptersActivity.class;
            cls2 = AMChaptersActivity.AMChaptersFragment.class;
        } else if (string2.equals("interact.db")) {
            cls = LXInteractList.class;
            cls2 = LXInteractList.LXInteractListFragment.class;
        } else if (string2.equals("ivcompat.db")) {
            cls = LXIvInteract.class;
            cls2 = LXIvInteract.LXIvInteractFragment.class;
        } else if (string.equals("skyscape")) {
            cls = SSSearchActivity.class;
            cls2 = SSSearchActivity.SSSearchFragment.class;
        } else if (string.equals("ovid")) {
            cls = OvidChaptersActivity.class;
            cls2 = OvidChaptersActivity.OvidChaptersFragment.class;
        } else if (string.equals("irandarou")) {
            cls = IDSearchActivity.class;
            cls2 = IDSearchActivity.IDSearchFragment.class;
        } else if (string.equals("uptodateddx")) {
            cls = UTDDSearchActivity.class;
            cls2 = UTDDSearchActivity.UTDDSearchFragment.class;
        } else if (string.equals("visualdx")) {
            cls = VDSearchActivity.class;
            cls2 = VDSearchActivity.VDSearchFragment.class;
        } else if (string.equals("visualdxddx")) {
            cls = VDDxScenarioActivity.class;
            cls2 = VDDxScenarioActivity.VDDxScenarioFragment.class;
        } else if (string.equals("Dictionary")) {
            cls = CDicSearchActivity.class;
            cls2 = CDicSearchActivity.CDicSearchFragment.class;
        } else if (string.equals("medhand")) {
            cls = MHSearchActivity.class;
            cls2 = MHSearchActivity.MHSearchFragment.class;
        } else if (string.equals("epub")) {
            bundle2.putString("ParentId", "0");
            cls = EPUBChaptersActivity.class;
            cls2 = EPUBChaptersActivityFragment.class;
        } else if (string.equals("epocrate")) {
            cls = EPOMainActivity.class;
            cls2 = EPOMainActivityFragment.class;
        } else if (string.equals("martindale")) {
            cls = MDListActivity.class;
            cls2 = MDListActivityFragment.class;
        } else if (string.equals("amirsys")) {
            cls = ASListActivity.class;
            cls2 = ASListActivityFragment.class;
        } else if (string.equals("statdx")) {
            cls = SDListActivity.class;
            cls2 = SDListActivityFragment.class;
        } else if (string.equals("facts")) {
            cls = FTListActivity.class;
            cls2 = FTListActivityFragment.class;
        } else if (string.equals("micromedex-drug")) {
            cls = MMListActivity.class;
            cls2 = MMListActivityFragment.class;
        } else if (string.equals("micromedex-neofax")) {
            cls = MMNeoListActivity.class;
            cls2 = MMNeoListActivityFragment.class;
        } else if (string.equals("micromedex-interact")) {
            cls = MMInteractSelectActivity.class;
            cls2 = MMInteractSelectActivityFragment.class;
        } else if (string.equals("micromedex-iv")) {
            cls = MMIVSelectActivity.class;
            cls2 = MMIVSelectActivityFragment.class;
        } else if (string.equals("sanford")) {
            cls = SANTocActivity.class;
            cls2 = SANTocActivityFragment.class;
        } else if (string.equals("uworld")) {
            cls = UWMainActivity.class;
            cls2 = UWMainActivityFragment.class;
        } else if (string.equals("irqbank")) {
            cls = DREMainActivity.class;
            cls2 = DREMainActivityFragment.class;
        } else if (string.equals("noskhe")) {
            cls = NOSListActivity.class;
            cls2 = NOSListActivityFragment.class;
        } else if (string.equals("irandrugs")) {
            cls = IranGenericDrugsList.class;
            cls2 = IranGenericDrugsListFragment.class;
        } else {
            if (string.equals("mksap")) {
                m71772A1(bundle, "", null, null);
                return;
            }
            if (string.equals("stockley")) {
                cls = STListActivity.class;
                cls2 = STListActivityFragment.class;
            } else if (string.equals("lww")) {
                bundle2.putString("ParentId", "0");
                cls = LWWChapters.class;
                cls2 = LWWChaptersFragment.class;
            } else if (string.equals("cme") || string.equals("kaptest")) {
                bundle2.putString("ParentId", "0");
                m71798N(CMETOC.class, CMETOCFragment.class, bundle2);
                return;
            } else if (string.equals("tol")) {
                cls = PsychoListActivity.class;
                cls2 = PsychoListActivityFragment.class;
            } else {
                cls = LXItems.class;
                cls2 = LXItems.LXItemsFragment.class;
            }
        }
        m71798N(cls, cls2, bundle2);
    }
}

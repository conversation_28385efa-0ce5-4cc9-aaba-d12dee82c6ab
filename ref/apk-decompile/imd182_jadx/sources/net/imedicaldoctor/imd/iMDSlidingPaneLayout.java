package net.imedicaldoctor.imd;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import androidx.slidingpanelayout.widget.SlidingPaneLayout;

/* loaded from: classes3.dex */
public class iMDSlidingPaneLayout extends SlidingPaneLayout {

    /* renamed from: x3 */
    boolean f101684x3;

    public iMDSlidingPaneLayout(Context context) {
        super(context);
        this.f101684x3 = getContext().getSharedPreferences("default_preferences", 0).getBoolean("lockfull", true);
    }

    @Override // androidx.slidingpanelayout.widget.SlidingPaneLayout, android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        if (!this.f101684x3 || m28126l() || findViewById(C5562R.id.menu_button) == null) {
            return super.onInterceptTouchEvent(motionEvent);
        }
        return false;
    }

    public iMDSlidingPaneLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f101684x3 = getContext().getSharedPreferences("default_preferences", 0).getBoolean("lockfull", true);
    }

    public iMDSlidingPaneLayout(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        this.f101684x3 = getContext().getSharedPreferences("default_preferences", 0).getBoolean("lockfull", true);
    }
}

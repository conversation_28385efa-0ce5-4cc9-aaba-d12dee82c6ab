package net.imedicaldoctor.imd;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.itextpdf.text.Annotation;
import com.itextpdf.tool.xml.html.HTML;
import net.imedicaldoctor.imd.Data.CompressHelper;

/* loaded from: classes3.dex */
public class NotificationActivity extends AppCompatActivity implements View.OnClickListener {

    /* renamed from: A3 */
    Button f90919A3;

    /* renamed from: B3 */
    TextView f90920B3;

    /* renamed from: C3 */
    TextView f90921C3;

    /* renamed from: D3 */
    String f90922D3 = "StartCompaignDialogActivity";

    /* renamed from: y3 */
    Dialog f90923y3;

    /* renamed from: z3 */
    Button f90924z3;

    @Override // android.view.View.OnClickListener
    public void onClick(View view) {
        if (view.getId() == C5562R.id.moreinfo) {
            finish();
            this.f90923y3.dismiss();
            new Handler().postDelayed(new Runnable() { // from class: net.imedicaldoctor.imd.NotificationActivity.2
                @Override // java.lang.Runnable
                public void run() {
                    new CompressHelper(NotificationActivity.this).m71803P(NotificationActivity.this.getIntent().getStringExtra(HTML.Tag.f74331C));
                }
            }, 1000L);
        } else if (view.getId() == C5562R.id.cancel) {
            this.f90923y3.dismiss();
            finish();
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, android.view.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(C5562R.layout.nothing);
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View viewInflate = getLayoutInflater().inflate(C5562R.layout.activity_notification, (ViewGroup) null);
        builder.setView(viewInflate);
        AlertDialog alertDialogCreate = builder.create();
        this.f90923y3 = alertDialogCreate;
        alertDialogCreate.show();
        setFinishOnTouchOutside(false);
        this.f90923y3.setCanceledOnTouchOutside(false);
        this.f90924z3 = (Button) viewInflate.findViewById(C5562R.id.moreinfo);
        this.f90919A3 = (Button) viewInflate.findViewById(C5562R.id.cancel);
        this.f90920B3 = (TextView) viewInflate.findViewById(C5562R.id.notification_title);
        this.f90921C3 = (TextView) viewInflate.findViewById(C5562R.id.notification_content);
        this.f90920B3.setText(getIntent().getStringExtra("title"));
        this.f90921C3.setText(getIntent().getStringExtra(Annotation.f68283i3));
        this.f90924z3.setOnClickListener(this);
        this.f90919A3.setOnClickListener(this);
        if (getIntent().getStringExtra(HTML.Tag.f74331C).length() < 4) {
            this.f90924z3.setVisibility(8);
        }
        this.f90923y3.setOnDismissListener(new DialogInterface.OnDismissListener() { // from class: net.imedicaldoctor.imd.NotificationActivity.1
            @Override // android.content.DialogInterface.OnDismissListener
            public void onDismiss(DialogInterface dialogInterface) {
                NotificationActivity.this.finish();
            }
        });
    }
}

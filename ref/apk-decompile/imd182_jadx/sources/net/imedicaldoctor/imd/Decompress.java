package net.imedicaldoctor.imd;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import androidx.exifinterface.media.ExifInterface;
import com.google.android.material.timepicker.TimeModel;
import com.google.common.p009io.CountingInputStream;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableEmitter;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.io.SequenceInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.Vector;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Data.UnzipCompleted;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.progress.ProgressMonitor;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;

/* loaded from: classes3.dex */
public class Decompress {

    /* renamed from: a */
    private final String f87468a;

    /* renamed from: b */
    private final String f87469b;

    /* renamed from: c */
    Context f87470c;

    /* renamed from: d */
    CompressHelper f87471d;

    public Decompress(String str, String str2, Context context) {
        this.f87468a = str;
        this.f87469b = str2;
        this.f87470c = context;
        this.f87471d = new CompressHelper(context);
        m71936b("");
    }

    /* renamed from: b */
    private void m71936b(String str) {
        File file = new File(this.f87469b + str);
        if (file.isDirectory()) {
            return;
        }
        file.mkdirs();
    }

    /* renamed from: c */
    public static byte[] m71937c(String str, String str2) {
        ZipEntry nextEntry;
        try {
            BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75784t(new File(str)));
            try {
                ZipInputStream zipInputStream = new ZipInputStream(bufferedSourceM75769e.mo75509z());
                do {
                    try {
                        nextEntry = zipInputStream.getNextEntry();
                        if (nextEntry == null) {
                            zipInputStream.close();
                            bufferedSourceM75769e.close();
                            return null;
                        }
                    } finally {
                    }
                } while (!nextEntry.getName().equals(str2));
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] bArr = new byte[1024];
                while (true) {
                    int i2 = zipInputStream.read(bArr);
                    if (i2 == -1) {
                        byte[] byteArray = byteArrayOutputStream.toByteArray();
                        zipInputStream.close();
                        bufferedSourceM75769e.close();
                        return byteArray;
                    }
                    byteArrayOutputStream.write(bArr, 0, i2);
                }
            } finally {
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in unzip", e2.getLocalizedMessage());
            return null;
        }
    }

    /* renamed from: d */
    public static byte[] m71938d(String str, String str2, Bundle bundle) throws IOException {
        Date date = new Date();
        try {
            Vector vector = new Vector(10);
            for (int i2 = 1; i2 < 11; i2++) {
                vector.add(new FileInputStream(str + "." + i2));
            }
            SequenceInputStream sequenceInputStream = new SequenceInputStream(vector.elements());
            ZipInputStream zipInputStream = new ZipInputStream(new BufferedInputStream(sequenceInputStream));
            String name = "";
            while (true) {
                try {
                    ZipEntry nextEntry = zipInputStream.getNextEntry();
                    if (nextEntry == null) {
                        break;
                    }
                    if (!nextEntry.isDirectory()) {
                        nextEntry.getName().toLowerCase().endsWith(str2);
                    } else if (name.length() == 0) {
                        name = nextEntry.getName();
                    }
                } catch (Exception e2) {
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    return null;
                }
            }
            zipInputStream.close();
            sequenceInputStream.close();
            Iterator it2 = vector.iterator();
            while (it2.hasNext()) {
                try {
                    ((FileInputStream) it2.next()).close();
                } catch (Exception unused) {
                }
            }
            iMDLogger.m73550f("Found file", "In " + TimeUnit.MILLISECONDS.toSeconds(new Date().getTime() - date.getTime()) + " Seconds");
        } catch (Exception e3) {
            FirebaseCrashlytics.m48010d().m48016g(e3);
            iMDLogger.m73550f("Error in unzip", e3.getLocalizedMessage() + " in ");
            e3.printStackTrace();
        }
        return null;
    }

    /* renamed from: e */
    public static Observable<byte[]> m71939e(final String str, final String str2) {
        return Observable.m59451w1(new ObservableOnSubscribe<byte[]>() { // from class: net.imedicaldoctor.imd.Decompress.2
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<byte[]> observableEmitter) throws Throwable {
                ZipEntry nextEntry;
                try {
                    ZipInputStream zipInputStream = new ZipInputStream(Okio.m75769e(Okio.m75784t(new File(str))).mo75509z());
                    do {
                        nextEntry = zipInputStream.getNextEntry();
                        if (nextEntry == null) {
                            observableEmitter.onError(new FileNotFoundException("Resource not found: " + str2));
                            return;
                        }
                    } while (!nextEntry.getName().equals(str2));
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75780p(byteArrayOutputStream));
                    BufferedSource bufferedSourceM75769e = Okio.m75769e(Okio.m75785u(zipInputStream));
                    byte[] bArr = new byte[8192];
                    while (true) {
                        int i2 = bufferedSourceM75769e.read(bArr);
                        if (i2 == -1) {
                            bufferedSinkM75768d.flush();
                            observableEmitter.onNext(byteArrayOutputStream.toByteArray());
                            observableEmitter.onComplete();
                            return;
                        }
                        bufferedSinkM75768d.write(bArr, 0, i2);
                    }
                } catch (Exception e2) {
                    observableEmitter.onError(e2);
                    iMDLogger.m73550f("Error in unzip", e2.getLocalizedMessage());
                }
            }
        });
    }

    /* renamed from: f */
    public static void m71940f(String str, String str2, UnzipCompleted unzipCompleted) {
        try {
            ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(str));
            while (true) {
                try {
                    ZipEntry nextEntry = zipInputStream.getNextEntry();
                    if (nextEntry == null) {
                        unzipCompleted.mo71929a("Can't find file " + str2 + " in " + str);
                        break;
                    }
                    if (nextEntry.getName().equals(str2)) {
                        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                        byte[] bArr = new byte[1024];
                        while (true) {
                            int i2 = zipInputStream.read(bArr);
                            if (i2 == -1) {
                                break;
                            } else {
                                byteArrayOutputStream.write(bArr, 0, i2);
                            }
                        }
                        unzipCompleted.mo71930b(byteArrayOutputStream.toByteArray());
                    }
                } finally {
                }
            }
            zipInputStream.close();
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in unzip", e2.getLocalizedMessage());
            unzipCompleted.mo71929a(e2.getLocalizedMessage());
        }
    }

    /* renamed from: a */
    public void m71941a(File file) {
        try {
            file.setReadable(true, false);
        } catch (Exception unused) {
            Log.e("Error", "Error");
        }
    }

    /* renamed from: g */
    public String m71942g(ObservableEmitter<Bundle> observableEmitter, String str) {
        try {
            ZipFile zipFile = new ZipFile(this.f87468a);
            if (zipFile.m73568D()) {
                zipFile.m73581R("imedicaldoctor".toCharArray());
            }
            Bundle bundle = new Bundle();
            bundle.putString("progress", "");
            bundle.putString("labelText", str);
            zipFile.m73582S(true);
            ProgressMonitor progressMonitorM73566A = zipFile.m73566A();
            zipFile.m73595o(this.f87469b);
            Object obj = "";
            while (progressMonitorM73566A.m74068i() == ProgressMonitor.State.BUSY) {
                PrintStream printStream = System.out;
                printStream.println("Percent Done: " + progressMonitorM73566A.m74066g());
                printStream.println("File: " + progressMonitorM73566A.m74065f());
                String str2 = String.format(TimeModel.f59697b3, Integer.valueOf(progressMonitorM73566A.m74066g()));
                if (!str2.equals(obj)) {
                    iMDLogger.m73550f("Decompress", "Percent : " + str2);
                    bundle.remove("progress");
                    bundle.putString("progress", str2);
                    observableEmitter.onNext(bundle);
                    obj = str2;
                }
            }
            if (progressMonitorM73566A.m74067h() == ProgressMonitor.Result.SUCCESS) {
                return "0";
            }
            progressMonitorM73566A.m74064e().printStackTrace();
            return "0";
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in unzip", e2.getLocalizedMessage() + " in ");
            e2.printStackTrace();
            return e2.getLocalizedMessage() + " in ";
        }
    }

    /* JADX WARN: Can't wrap try/catch for region: R(14:177|60|(1:62)(1:64)|63|65|(3:175|66|67)|(6:197|68|69|181|70|71)|(8:164|72|73|(12:195|75|76|183|77|78|(9:162|80|81|193|82|83|(2:173|85)|88|89)(1:94)|168|95|96|(13:98|160|99|100|191|101|(3:103|189|104)(1:108)|105|109|110|199|111|210)(2:119|211)|112)(1:209)|179|142|129|207)|126|127|166|128|129|207) */
    /* JADX WARN: Code restructure failed: missing block: B:130:0x03c9, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0156, code lost:
    
        if (r7 == false) goto L43;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x0158, code lost:
    
        r3 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x015d, code lost:
    
        if (r3 >= r8.size()) goto L212;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x015f, code lost:
    
        r14 = r27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x016d, code lost:
    
        r36.f87471d.m71857j(((android.os.Bundle) r8.get(r3)).getString(r14));
        r3 = r3 + 1;
        r27 = r14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x017c, code lost:
    
        r3 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0185, code lost:
    
        if (r3 >= r24.size()) goto L213;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0187, code lost:
    
        r2 = r24;
        r36.f87471d.m71857j(((android.os.Bundle) r2.get(r3)).getString(r27));
        r3 = r3 + 1;
        r24 = r2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x019d, code lost:
    
        r12.close();
        net.imedicaldoctor.imd.iMDLogger.m73550f("Zip Completed", "In " + java.util.concurrent.TimeUnit.MILLISECONDS.toSeconds(new java.util.Date().getTime() - r18.getTime()) + " Seconds");
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x01d1, code lost:
    
        return "0";
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r12v13, types: [net.lingala.zip4j.io.inputstream.ZipInputStream] */
    /* JADX WARN: Type inference failed for: r12v3 */
    /* JADX WARN: Type inference failed for: r12v38 */
    /* JADX WARN: Type inference failed for: r12v39 */
    /* JADX WARN: Type inference failed for: r12v4, types: [net.lingala.zip4j.io.inputstream.ZipInputStream] */
    /* JADX WARN: Type inference failed for: r12v7 */
    /* JADX WARN: Type inference failed for: r7v25, types: [java.io.BufferedOutputStream] */
    /* JADX WARN: Type inference failed for: r7v47 */
    /* JADX WARN: Type inference failed for: r7v48 */
    /* renamed from: h */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.String m71943h(io.reactivex.rxjava3.core.ObservableEmitter<android.os.Bundle> r37, java.lang.String r38, android.app.Activity r39) throws java.io.IOException {
        /*
            Method dump skipped, instructions count: 1217
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: net.imedicaldoctor.imd.Decompress.m71943h(io.reactivex.rxjava3.core.ObservableEmitter, java.lang.String, android.app.Activity):java.lang.String");
    }

    /* renamed from: i */
    public boolean m71944i() throws IOException {
        try {
            ZipInputStream zipInputStream = new ZipInputStream(Okio.m75769e(Okio.m75784t(new File(this.f87468a))).mo75509z());
            while (true) {
                ZipEntry nextEntry = zipInputStream.getNextEntry();
                if (nextEntry == null) {
                    zipInputStream.close();
                    return true;
                }
                iMDLogger.m73554j("Decompress", "Unzipping " + nextEntry.getName());
                if (nextEntry.isDirectory()) {
                    m71936b(nextEntry.getName());
                } else {
                    File file = new File(this.f87469b, nextEntry.getName());
                    file.getParentFile().mkdirs();
                    BufferedSink bufferedSinkM75768d = Okio.m75768d(Okio.m75778n(file));
                    try {
                        byte[] bArr = new byte[8192];
                        while (true) {
                            int i2 = zipInputStream.read(bArr);
                            if (i2 == -1) {
                                break;
                            }
                            bufferedSinkM75768d.write(bArr, 0, i2);
                        }
                        if (bufferedSinkM75768d != null) {
                            bufferedSinkM75768d.close();
                        }
                        zipInputStream.closeEntry();
                    } finally {
                    }
                }
            }
        } catch (Exception e2) {
            iMDLogger.m73550f("Decompress", "unzip failed: " + e2);
            return false;
        }
    }

    /* renamed from: j */
    public boolean m71945j() {
        try {
            FileInputStream fileInputStream = new FileInputStream(this.f87468a);
            ZipInputStream zipInputStream = new ZipInputStream(new BufferedInputStream(fileInputStream));
            long length = new File(this.f87468a).length();
            long compressedSize = 0;
            while (true) {
                ZipEntry nextEntry = zipInputStream.getNextEntry();
                if (nextEntry == null) {
                    zipInputStream.close();
                    fileInputStream.close();
                    return true;
                }
                compressedSize += nextEntry.getCompressedSize();
                long j2 = compressedSize / length;
                if (nextEntry.isDirectory()) {
                    m71936b(nextEntry.getName());
                } else {
                    String str = this.f87469b + "/" + nextEntry.getName();
                    if (new File(str).exists()) {
                        this.f87471d.m71857j(str);
                    }
                    byte[] bArr = new byte[262144];
                    BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(this.f87469b + "/" + nextEntry.getName()), 262144);
                    while (true) {
                        int i2 = zipInputStream.read(bArr, 0, 262144);
                        if (i2 == -1) {
                            break;
                        }
                        bufferedOutputStream.write(bArr, 0, i2);
                    }
                    bufferedOutputStream.flush();
                    bufferedOutputStream.close();
                    m71941a(new File(str));
                }
            }
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            iMDLogger.m73550f("Error in unzip", e2.getLocalizedMessage());
            return false;
        }
    }

    /* renamed from: k */
    public Observable<String> m71946k() {
        return Observable.m59451w1(new ObservableOnSubscribe<String>() { // from class: net.imedicaldoctor.imd.Decompress.1
            @Override // io.reactivex.rxjava3.core.ObservableOnSubscribe
            /* renamed from: a */
            public void mo59827a(@NonNull ObservableEmitter<String> observableEmitter) throws Throwable {
                if (Decompress.this.m71945j()) {
                    observableEmitter.onComplete();
                } else {
                    observableEmitter.onError(null);
                }
            }
        });
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v23, types: [java.io.BufferedOutputStream, java.io.OutputStream] */
    /* JADX WARN: Type inference failed for: r15v1 */
    /* JADX WARN: Type inference failed for: r15v14, types: [int] */
    /* JADX WARN: Type inference failed for: r15v19 */
    /* JADX WARN: Type inference failed for: r15v2 */
    /* JADX WARN: Type inference failed for: r15v3 */
    /* JADX WARN: Type inference failed for: r15v4 */
    /* JADX WARN: Type inference failed for: r15v5 */
    /* JADX WARN: Type inference failed for: r15v7, types: [boolean] */
    /* renamed from: l */
    public String m71947l(ObservableEmitter<Bundle> observableEmitter, String str) throws IOException {
        ?? IsDirectory;
        ZipEntry nextEntry;
        long j2;
        Date date;
        String str2 = "/";
        String str3 = "";
        try {
            FileInputStream fileInputStream = new FileInputStream(this.f87468a);
            ZipInputStream zipInputStream = new ZipInputStream(new BufferedInputStream(fileInputStream));
            long length = new File(this.f87468a).length();
            Date date2 = new Date();
            Bundle bundle = new Bundle();
            bundle.putString("progress", "");
            bundle.putString("labelText", str);
            File file = new File(this.f87469b);
            long compressedSize = 0;
            String str4 = "";
            Date date3 = date2;
            while (true) {
                try {
                    nextEntry = zipInputStream.getNextEntry();
                } catch (Exception e2) {
                    String str5 = str2;
                    Date date4 = date3;
                    IsDirectory = str4;
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("Error", "Error in getNextEntry : " + e2.getLocalizedMessage());
                    e2.printStackTrace();
                    if (!e2.getLocalizedMessage().equals("CRC mismatch")) {
                        return e2.getLocalizedMessage();
                    }
                    date3 = date4;
                    str2 = str5;
                    str4 = IsDirectory;
                }
                if (nextEntry == null) {
                    zipInputStream.close();
                    fileInputStream.close();
                    return "0";
                }
                try {
                    compressedSize += nextEntry.getCompressedSize();
                    IsDirectory = nextEntry.isDirectory();
                    if (IsDirectory != 0) {
                        try {
                            m71936b(nextEntry.getName());
                        } catch (Exception e3) {
                            e = e3;
                            str3 = str4;
                            FirebaseCrashlytics.m48010d().m48016g(e);
                            iMDLogger.m73550f("Error in unzip", e.getLocalizedMessage() + " in " + str3);
                            e.printStackTrace();
                            return e.getLocalizedMessage() + " in " + str3;
                        }
                    } else {
                        String str6 = str2;
                        Date date5 = date3;
                        if (TimeUnit.MILLISECONDS.toSeconds(new Date().getTime() - date3.getTime()) > 1) {
                            IsDirectory = str4;
                            j2 = compressedSize;
                            try {
                                String str7 = String.format("%.2f", Double.valueOf((compressedSize / length) * 100.0d));
                                iMDLogger.m73550f("Decompress", "Percent : " + str7);
                                bundle.remove("progress");
                                bundle.putString("progress", str7);
                                observableEmitter.onNext(bundle);
                                date = new Date();
                            } catch (Exception e4) {
                                e = e4;
                                str3 = IsDirectory;
                                FirebaseCrashlytics.m48010d().m48016g(e);
                                iMDLogger.m73550f("Error in unzip", e.getLocalizedMessage() + " in " + str3);
                                e.printStackTrace();
                                return e.getLocalizedMessage() + " in " + str3;
                            }
                        } else {
                            j2 = compressedSize;
                            date = date5;
                        }
                        str4 = this.f87469b + str6 + nextEntry.getName();
                        if (new File(str4).exists()) {
                            iMDLogger.m73550f("Decompress", nextEntry.getName() + " Exists.");
                            this.f87471d.m71857j(str4);
                            iMDLogger.m73550f("Decompress", nextEntry.getName() + " Deleted.");
                        }
                        if (nextEntry.getSize() > file.getUsableSpace()) {
                            iMDLogger.m73550f("Decompress", "Not Enough space");
                            return ExifInterface.f16317Y4;
                        }
                        byte[] bArr = new byte[262144];
                        int i2 = 262144;
                        ?? bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(this.f87469b + str6 + nextEntry.getName()), 262144);
                        while (true) {
                            IsDirectory = zipInputStream.read(bArr, 0, i2);
                            if (IsDirectory == -1) {
                                break;
                            }
                            bufferedOutputStream.write(bArr, 0, IsDirectory);
                            i2 = 262144;
                        }
                        bufferedOutputStream.flush();
                        bufferedOutputStream.close();
                        m71941a(new File(str4));
                        date3 = date;
                        str2 = str6;
                        compressedSize = j2;
                    }
                } catch (Exception e5) {
                    e = e5;
                    IsDirectory = str4;
                    str3 = IsDirectory;
                    FirebaseCrashlytics.m48010d().m48016g(e);
                    iMDLogger.m73550f("Error in unzip", e.getLocalizedMessage() + " in " + str3);
                    e.printStackTrace();
                    return e.getLocalizedMessage() + " in " + str3;
                }
            }
        } catch (Exception e6) {
            e = e6;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v23, types: [java.io.BufferedOutputStream, java.io.OutputStream] */
    /* JADX WARN: Type inference failed for: r15v1 */
    /* JADX WARN: Type inference failed for: r15v14, types: [int] */
    /* JADX WARN: Type inference failed for: r15v15 */
    /* JADX WARN: Type inference failed for: r15v2 */
    /* JADX WARN: Type inference failed for: r15v3 */
    /* JADX WARN: Type inference failed for: r15v4 */
    /* JADX WARN: Type inference failed for: r15v5 */
    /* JADX WARN: Type inference failed for: r15v7, types: [boolean] */
    /* renamed from: m */
    public String m71948m(ObservableEmitter<Bundle> observableEmitter, String str) throws IOException {
        ?? IsDirectory;
        ZipEntry nextEntry;
        long j2;
        Date date;
        String str2 = "/";
        String str3 = "";
        try {
            FileInputStream fileInputStream = new FileInputStream(this.f87468a);
            ZipInputStream zipInputStream = new ZipInputStream(new BufferedInputStream(fileInputStream));
            long length = new File(this.f87468a).length();
            Date date2 = new Date();
            Bundle bundle = new Bundle();
            bundle.putString("progress", "");
            bundle.putString("labelText", str);
            File file = new File(this.f87469b);
            long compressedSize = 0;
            String str4 = "";
            Date date3 = date2;
            while (true) {
                try {
                    nextEntry = zipInputStream.getNextEntry();
                } catch (Exception e2) {
                    String str5 = str2;
                    Date date4 = date3;
                    IsDirectory = str4;
                    FirebaseCrashlytics.m48010d().m48016g(e2);
                    iMDLogger.m73550f("Error", "Error in getNextEntry : " + e2.getLocalizedMessage());
                    e2.printStackTrace();
                    if (!e2.getLocalizedMessage().equals("CRC mismatch")) {
                        return e2.getLocalizedMessage();
                    }
                    date3 = date4;
                    str2 = str5;
                    str4 = IsDirectory;
                }
                if (nextEntry == null) {
                    zipInputStream.close();
                    fileInputStream.close();
                    return "0";
                }
                try {
                    compressedSize += nextEntry.getCompressedSize();
                    IsDirectory = nextEntry.isDirectory();
                    if (IsDirectory != 0) {
                        try {
                            m71936b(nextEntry.getName());
                        } catch (Exception e3) {
                            e = e3;
                            str3 = str4;
                            FirebaseCrashlytics.m48010d().m48016g(e);
                            iMDLogger.m73550f("Error in unzip", e.getLocalizedMessage() + " in " + str3);
                            e.printStackTrace();
                            return e.getLocalizedMessage() + " in " + str3;
                        }
                    } else {
                        String str6 = str2;
                        Date date5 = date3;
                        if (TimeUnit.MILLISECONDS.toSeconds(new Date().getTime() - date3.getTime()) > 1) {
                            IsDirectory = str4;
                            j2 = compressedSize;
                            try {
                                String str7 = String.format("%.2f", Double.valueOf((compressedSize / length) * 100.0d));
                                iMDLogger.m73550f("Decompress", "Percent : " + str7);
                                bundle.remove("progress");
                                bundle.putString("progress", str7);
                                observableEmitter.onNext(bundle);
                                date = new Date();
                            } catch (Exception e4) {
                                e = e4;
                                str3 = IsDirectory;
                                FirebaseCrashlytics.m48010d().m48016g(e);
                                iMDLogger.m73550f("Error in unzip", e.getLocalizedMessage() + " in " + str3);
                                e.printStackTrace();
                                return e.getLocalizedMessage() + " in " + str3;
                            }
                        } else {
                            j2 = compressedSize;
                            date = date5;
                        }
                        str4 = this.f87469b + str6 + nextEntry.getName();
                        if (new File(str4).exists()) {
                            this.f87471d.m71857j(str4);
                        }
                        if (nextEntry.getSize() > file.getUsableSpace()) {
                            iMDLogger.m73550f("Decompress", "Not Enough space");
                            return ExifInterface.f16317Y4;
                        }
                        byte[] bArr = new byte[262144];
                        int i2 = 262144;
                        ?? bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(this.f87469b + str6 + nextEntry.getName()), 262144);
                        while (true) {
                            IsDirectory = zipInputStream.read(bArr, 0, i2);
                            if (IsDirectory == -1) {
                                break;
                            }
                            bufferedOutputStream.write(bArr, 0, IsDirectory);
                            i2 = 262144;
                        }
                        bufferedOutputStream.flush();
                        bufferedOutputStream.close();
                        m71941a(new File(str4));
                        date3 = date;
                        str2 = str6;
                        compressedSize = j2;
                    }
                } catch (Exception e5) {
                    e = e5;
                    IsDirectory = str4;
                    str3 = IsDirectory;
                    FirebaseCrashlytics.m48010d().m48016g(e);
                    iMDLogger.m73550f("Error in unzip", e.getLocalizedMessage() + " in " + str3);
                    e.printStackTrace();
                    return e.getLocalizedMessage() + " in " + str3;
                }
            }
        } catch (Exception e6) {
            e = e6;
        }
    }

    /* renamed from: n */
    public String m71949n(ObservableEmitter<Bundle> observableEmitter, String str, Activity activity) throws IOException {
        ArrayList arrayList;
        ArrayList arrayList2;
        Vector vector;
        long length;
        int i2;
        String str2;
        String str3;
        SequenceInputStream sequenceInputStream;
        BufferedInputStream bufferedInputStream;
        ArrayList arrayList3;
        String str4;
        String str5;
        String str6;
        String str7;
        ZipEntry nextEntry;
        Bundle bundle;
        String str8 = "progress";
        String str9 = "";
        try {
            arrayList = new ArrayList();
            arrayList2 = new ArrayList();
            vector = new Vector(10);
            length = 0;
            i2 = 1;
        } catch (Exception e2) {
            e = e2;
        }
        while (true) {
            str2 = TypedValues.CycleType.f5457R;
            str3 = "filePath";
            if (i2 >= 11) {
                break;
            }
            try {
                String str10 = this.f87468a + "." + i2;
                Bundle bundle2 = new Bundle();
                length += new File(str10).length();
                bundle2.putString("filePath", str10);
                bundle2.putLong(TypedValues.CycleType.f5457R, length);
                arrayList.add(bundle2);
                arrayList2.add(bundle2);
                vector.add(new FileInputStream(str10));
                i2++;
            } catch (Exception e3) {
                e = e3;
            }
            FirebaseCrashlytics.m48010d().m48016g(e);
            iMDLogger.m73550f("Error in unzip", e.getLocalizedMessage() + " in " + str9);
            e.printStackTrace();
            return e.getLocalizedMessage() + " in " + str9;
        }
        SequenceInputStream sequenceInputStream2 = new SequenceInputStream(vector.elements());
        BufferedInputStream bufferedInputStream2 = new BufferedInputStream(sequenceInputStream2, 131072);
        CountingInputStream countingInputStream = new CountingInputStream(bufferedInputStream2);
        String str11 = "/";
        ZipInputStream zipInputStream = new ZipInputStream(countingInputStream);
        Date date = new Date();
        Date date2 = new Date();
        Bundle bundle3 = new Bundle();
        bundle3.putString("progress", "");
        Date date3 = date;
        String str12 = "";
        try {
            bundle3.putString("labelText", str);
            new File(this.f87469b);
            Bundle bundle4 = bundle3;
            boolean z = activity.getSharedPreferences("default_preferences", 0).getBoolean("lessspace", false);
            long j2 = ((Bundle) arrayList.get(0)).getLong(TypedValues.CycleType.f5457R);
            while (true) {
                try {
                    nextEntry = zipInputStream.getNextEntry();
                } catch (Exception e4) {
                    sequenceInputStream = sequenceInputStream2;
                    bufferedInputStream = bufferedInputStream2;
                    arrayList3 = arrayList2;
                    str4 = str2;
                    str5 = str3;
                    str6 = str11;
                    str7 = str8;
                    FirebaseCrashlytics.m48010d().m48016g(e4);
                    iMDLogger.m73550f("Error", "Error in getNextEntry : " + e4.getLocalizedMessage());
                    e4.printStackTrace();
                    if (!e4.getLocalizedMessage().equals("CRC mismatch")) {
                        return e4.getLocalizedMessage();
                    }
                }
                if (nextEntry == null) {
                    int i3 = 0;
                    if (z) {
                        while (i3 < arrayList.size()) {
                            this.f87471d.m71857j(((Bundle) arrayList.get(i3)).getString(str3));
                            i3++;
                        }
                    } else {
                        while (i3 < arrayList2.size()) {
                            this.f87471d.m71857j(((Bundle) arrayList2.get(i3)).getString(str3));
                            i3++;
                        }
                    }
                    try {
                        Iterator it2 = vector.iterator();
                        while (it2.hasNext()) {
                            ((FileInputStream) it2.next()).close();
                        }
                        zipInputStream.close();
                        countingInputStream.close();
                        bufferedInputStream2.close();
                        sequenceInputStream2.close();
                    } catch (Exception unused) {
                    }
                    iMDLogger.m73550f("Zip Completed", "In " + TimeUnit.MILLISECONDS.toSeconds(new Date().getTime() - date2.getTime()) + " Seconds");
                    return "0";
                }
                if (countingInputStream.m45639b() > j2) {
                    sequenceInputStream = sequenceInputStream2;
                    String string = ((Bundle) arrayList.get(0)).getString(str3);
                    if (z) {
                        this.f87471d.m71857j(string);
                    }
                    arrayList.remove(0);
                    j2 = ((Bundle) arrayList.get(0)).getLong(str2);
                } else {
                    sequenceInputStream = sequenceInputStream2;
                }
                if (nextEntry.isDirectory()) {
                    m71936b(nextEntry.getName());
                    bufferedInputStream = bufferedInputStream2;
                    arrayList3 = arrayList2;
                    str4 = str2;
                    str5 = str3;
                    str6 = str11;
                    str7 = str8;
                } else {
                    bufferedInputStream = bufferedInputStream2;
                    arrayList3 = arrayList2;
                    String str13 = str2;
                    String str14 = str3;
                    if (TimeUnit.MILLISECONDS.toSeconds(new Date().getTime() - date3.getTime()) > 1) {
                        String str15 = String.format("%.2f", Double.valueOf((countingInputStream.m45639b() / length) * 100.0d));
                        iMDLogger.m73550f("Decompress", "Percent : " + str15);
                        bundle = bundle4;
                        bundle.remove(str8);
                        bundle.putString(str8, str15);
                        if (observableEmitter != null) {
                            observableEmitter.onNext(bundle);
                        }
                        date3 = new Date();
                    } else {
                        bundle = bundle4;
                    }
                    StringBuilder sb = new StringBuilder();
                    sb.append(this.f87469b);
                    str6 = str11;
                    sb.append(str6);
                    sb.append(nextEntry.getName());
                    String string2 = sb.toString();
                    try {
                        if (new File(string2).exists()) {
                            iMDLogger.m73550f("Decompress", nextEntry.getName() + " Exists");
                            iMDLogger.m73550f("Decompress", nextEntry.getName() + " Different Size. Deleteing old file");
                            this.f87471d.m71857j(string2);
                        }
                        byte[] bArr = new byte[131072];
                        try {
                            StringBuilder sb2 = new StringBuilder();
                            str7 = str8;
                            try {
                                sb2.append(this.f87469b);
                                sb2.append(str6);
                                sb2.append(nextEntry.getName());
                                FileOutputStream fileOutputStream = new FileOutputStream(sb2.toString());
                                int i4 = 131072;
                                BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream, 131072);
                                bundle4 = bundle;
                                int i5 = 0;
                                while (true) {
                                    try {
                                        int i6 = zipInputStream.read(bArr, i5, i4);
                                        if (i6 == -1) {
                                            break;
                                        }
                                        bufferedOutputStream.write(bArr, i5, i6);
                                        if (countingInputStream.m45639b() > j2) {
                                            Bundle bundle5 = (Bundle) arrayList.get(i5);
                                            str5 = str14;
                                            try {
                                                String string3 = bundle5.getString(str5);
                                                if (z) {
                                                    this.f87471d.m71857j(string3);
                                                }
                                                arrayList.remove(0);
                                                str4 = str13;
                                            } catch (Exception e5) {
                                                e = e5;
                                                str4 = str13;
                                            }
                                            try {
                                                j2 = ((Bundle) arrayList.get(0)).getLong(str4);
                                                str14 = str5;
                                                str13 = str4;
                                            } catch (Exception e6) {
                                                e = e6;
                                                FirebaseCrashlytics.m48010d().m48016g(e);
                                                iMDLogger.m73550f("Decompress Error", "Can't write . " + nextEntry.getName());
                                                str12 = string2;
                                                str2 = str4;
                                                str8 = str7;
                                                arrayList2 = arrayList3;
                                                bufferedInputStream2 = bufferedInputStream;
                                                str11 = str6;
                                                str3 = str5;
                                                sequenceInputStream2 = sequenceInputStream;
                                            }
                                        }
                                        i5 = 0;
                                        i4 = 131072;
                                    } catch (Exception e7) {
                                        e = e7;
                                        str4 = str13;
                                        str5 = str14;
                                        FirebaseCrashlytics.m48010d().m48016g(e);
                                        iMDLogger.m73550f("Decompress Error", "Can't write . " + nextEntry.getName());
                                        str12 = string2;
                                        str2 = str4;
                                        str8 = str7;
                                        arrayList2 = arrayList3;
                                        bufferedInputStream2 = bufferedInputStream;
                                        str11 = str6;
                                        str3 = str5;
                                        sequenceInputStream2 = sequenceInputStream;
                                    }
                                }
                                str4 = str13;
                                str5 = str14;
                                bufferedOutputStream.flush();
                                bufferedOutputStream.close();
                                m71941a(new File(string2));
                            } catch (Exception e8) {
                                e = e8;
                                bundle4 = bundle;
                                str4 = str13;
                                str5 = str14;
                                FirebaseCrashlytics.m48010d().m48016g(e);
                                iMDLogger.m73550f("Decompress Error", "Can't write . " + nextEntry.getName());
                                str12 = string2;
                                str2 = str4;
                                str8 = str7;
                                arrayList2 = arrayList3;
                                bufferedInputStream2 = bufferedInputStream;
                                str11 = str6;
                                str3 = str5;
                                sequenceInputStream2 = sequenceInputStream;
                            }
                        } catch (Exception e9) {
                            e = e9;
                            str7 = str8;
                        }
                        str12 = string2;
                    } catch (Exception e10) {
                        e = e10;
                        str9 = string2;
                    }
                }
                str2 = str4;
                str8 = str7;
                arrayList2 = arrayList3;
                bufferedInputStream2 = bufferedInputStream;
                str11 = str6;
                str3 = str5;
                sequenceInputStream2 = sequenceInputStream;
            }
        } catch (Exception e11) {
            e = e11;
            str9 = str12;
        }
    }
}

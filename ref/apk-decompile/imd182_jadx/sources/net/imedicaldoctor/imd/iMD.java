package net.imedicaldoctor.imd;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.StrictMode;
import android.text.TextUtils;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.media3.extractor.metadata.icy.IcyHeaders;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Action;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Locale;
import java.util.Random;
import java.util.Timer;
import java.util.TimerTask;
import net.imedicaldoctor.imd.Data.CompressHelper;
import net.imedicaldoctor.imd.Fragments.downloadFragment;
import org.apache.commons.lang3.StringUtils;

/* loaded from: classes3.dex */
public class iMD extends Application {

    /* renamed from: f3 */
    private static Application f101666f3;

    /* renamed from: X */
    public LocalServer f101667X;

    /* renamed from: X2 */
    public String f101668X2;

    /* renamed from: Y */
    public HashSet<String> f101669Y;

    /* renamed from: Y2 */
    public int f101670Y2;

    /* renamed from: Z */
    public String f101671Z;

    /* renamed from: Z2 */
    public int f101672Z2;

    /* renamed from: a3 */
    public CompressHelper f101673a3;

    /* renamed from: b3 */
    public VBHelper f101674b3;

    /* renamed from: c3 */
    public downloadFragment f101675c3;

    /* renamed from: d3 */
    private Timer f101676d3;

    /* renamed from: e3 */
    public Handler f101677e3 = new Handler() { // from class: net.imedicaldoctor.imd.iMD.1
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            iMD.this.m73538b();
        }
    };

    /* renamed from: s */
    public ArrayList<Bundle> f101678s;

    /* renamed from: a */
    public static boolean m73534a() {
        return false;
    }

    /* renamed from: c */
    public static Application m73535c() {
        return f101666f3;
    }

    /* renamed from: d */
    public static Context m73536d() {
        return m73535c().getApplicationContext();
    }

    /* renamed from: g */
    private void m73537g() {
        try {
            Locale locale = new Locale("en");
            Locale.setDefault(locale);
            Configuration configuration = new Configuration();
            configuration.locale = locale;
            if (Build.VERSION.SDK_INT >= 24) {
                configuration.setLocale(locale);
            } else {
                configuration.locale = locale;
            }
            getResources().updateConfiguration(configuration, getResources().getDisplayMetrics());
        } catch (Exception unused) {
        }
    }

    @Override // android.content.ContextWrapper
    protected void attachBaseContext(Context context) {
        super.attachBaseContext(context);
    }

    /* renamed from: b */
    public void m73538b() {
        try {
            iMDLogger.m73554j("iMD", "Checking Activation Code");
            if (this.f101673a3 == null) {
                this.f101673a3 = new CompressHelper(this);
            }
            if (this.f101674b3 == null) {
                this.f101674b3 = new VBHelper(this);
            }
            int i2 = 0;
            try {
                i2 = getPackageManager().getPackageInfo(getPackageName(), 0).versionCode;
            } catch (Exception unused) {
            }
            this.f101673a3.m71874o0("ActivationCode|||||" + this.f101674b3.m73451m() + "|||||android-" + i2).m59701h6(Schedulers.m61579e()).m59775s4(AndroidSchedulers.m58440e()).m59688f6(new Consumer<String>() { // from class: net.imedicaldoctor.imd.iMD.2
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(String str) throws Throwable {
                    String str2;
                    CompressHelper compressHelper;
                    try {
                        String[] strArrSplitByWholeSeparator = StringUtils.splitByWholeSeparator(str, "|||||");
                        if (strArrSplitByWholeSeparator[0].equals(IcyHeaders.f28171a3)) {
                            String str3 = strArrSplitByWholeSeparator[1];
                            if (iMD.this.m73540f(str3)) {
                                LocalBroadcastManager.m16410b(iMD.this).m16413d(new Intent("reload"));
                                compressHelper = iMD.this.f101673a3;
                            } else {
                                compressHelper = iMD.this.f101673a3;
                            }
                            compressHelper.m71894t2(str3);
                            str2 = "Setted Activation Code";
                        } else {
                            if (!strArrSplitByWholeSeparator[0].equals("0")) {
                                return;
                            }
                            VBHelper vBHelper = iMD.this.f101674b3;
                            if (vBHelper.m73439a(vBHelper.m73456r()) == null) {
                                return;
                            }
                            iMD.this.f101673a3.m71894t2(null);
                            iMDLogger.m73550f("SystemExitCAlled", "CheckActivationCode : " + str);
                            System.exit(0);
                            str2 = "nulled Activation Code";
                        }
                        iMDLogger.m73554j("iMD", str2);
                    } catch (Exception e2) {
                        FirebaseCrashlytics.m48010d().m48016g(e2);
                    }
                }
            }, new Consumer<Throwable>() { // from class: net.imedicaldoctor.imd.iMD.3
                @Override // io.reactivex.rxjava3.functions.Consumer
                /* renamed from: a, reason: merged with bridge method [inline-methods] */
                public void accept(Throwable th) throws Throwable {
                    try {
                        iMDLogger.m73550f("CheckActivationCode", "failed");
                    } catch (Exception unused2) {
                    }
                }
            }, new Action() { // from class: net.imedicaldoctor.imd.iMD.4
                @Override // io.reactivex.rxjava3.functions.Action
                public void run() throws Throwable {
                }
            });
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
        }
        this.f101673a3.m71818V0();
    }

    /* renamed from: e */
    public Bundle m73539e(ArrayList<Bundle> arrayList) {
        try {
            return arrayList.get(new Random().nextInt(arrayList.size()));
        } catch (Exception e2) {
            FirebaseCrashlytics.m48010d().m48016g(e2);
            return null;
        }
    }

    /* renamed from: f */
    public boolean m73540f(String str) {
        String string = getSharedPreferences("default_preferences", 0).getString("ActivationCode", "");
        VBHelper vBHelper = this.f101674b3;
        String[] strArrSplit = TextUtils.split(vBHelper.m73440b(string, vBHelper.m73451m()).replace("||", "::"), "::");
        VBHelper vBHelper2 = this.f101674b3;
        String[] strArrSplit2 = TextUtils.split(vBHelper2.m73440b(str, vBHelper2.m73451m()).replace("||", "::"), "::");
        int[] iArr = {0, 1, 2, 3, 4, 5, 6, 7, 9};
        for (int i2 = 0; i2 < 9; i2++) {
            int i3 = iArr[i2];
            if (!strArrSplit[i3].equals(strArrSplit2[i3])) {
                return true;
            }
        }
        return false;
    }

    @Override // android.app.Application
    public void onCreate() {
        try {
            if (m73536d().getSharedPreferences("default_preferences", 0).getBoolean("dark", false)) {
                AppCompatDelegate.m1154c0(2);
            } else {
                AppCompatDelegate.m1154c0(1);
            }
        } catch (Exception unused) {
        }
        if (m73534a()) {
            StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder().detectNetwork().detectCustomSlowCalls().penaltyLog().build());
            StrictMode.setVmPolicy(new StrictMode.VmPolicy.Builder().detectLeakedSqlLiteObjects().detectLeakedClosableObjects().penaltyLog().build());
        }
        super.onCreate();
        FirebaseCrashlytics.m48010d().m48014c();
        Timer timer = new Timer();
        this.f101676d3 = timer;
        timer.scheduleAtFixedRate(new TimerTask() { // from class: net.imedicaldoctor.imd.iMD.5
            @Override // java.util.TimerTask, java.lang.Runnable
            public void run() {
                iMD.this.f101677e3.obtainMessage(1).sendToTarget();
            }
        }, 30000L, 600000L);
        m73537g();
        f101666f3 = this;
    }
}
